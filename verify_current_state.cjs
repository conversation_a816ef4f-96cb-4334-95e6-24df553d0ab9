const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  console.log('Testing current app state...');
  
  // Login
  await page.goto('http://localhost:5001/auth');
  await page.type('input[id="email"]', '<EMAIL>');
  await page.type('input[id="password"]', 'J4913836j');
  await page.click('button[type="submit"]');
  await page.waitForNavigation();
  
  // Check dashboard
  console.log('\n=== DASHBOARD STATUS ===');
  const bodyText = await page.evaluate(() => document.body.innerText);
  console.log('Content length:', bodyText.length);
  console.log('Contains "Loading":', bodyText.includes('Loading'));
  console.log('First 200 chars:', bodyText.substring(0, 200));
  
  await page.screenshot({ path: 'current_dashboard_test.png', fullPage: true });
  
  await browser.close();
})();
