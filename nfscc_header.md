# No False Success Claim Checklist for Header.tsx

## Task: Refine Header.tsx Styling

### Flaw #1: Hardcoded and Inconsistent <PERSON><PERSON> Styling

**Success Criteria:**

- [ ] **Checklist 1:** A new, unified, and reusable button style variant has been created in `index.css` for all header buttons.
- [ ] **Checklist 2:** The new style enforces a single, elegant, solid hover effect, removing cheap `bg-muted` and inconsistent text color changes.
- [ ] **Checklist 3:** The `className` logic in `Header.tsx` has been simplified, replacing the mess of utility classes with a clean, semantic approach using the new CSS variant and `data-active` attributes for state.
- [ ] **Checklist 4:** The solution is not a bypass or a hack. It is a root-cause fix that establishes a maintainable and scalable pattern.
- [ ] **Checklist 5:** The changes have been applied to the original `Header.tsx` file and `index.css`.
- [ ] **Checklist 6:** I have visually confirmed the changes in the preview and they are pixel-perfect.
