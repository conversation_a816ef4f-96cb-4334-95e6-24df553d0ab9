# Puppeteer MCP 101: A Guide to Flawless Web Automation

## Introduction

This guide provides a clear, simple, and robust set of instructions for using the Puppeteer MCP to automate web applications. The primary lesson learned from previous failures is that modern web apps (built with <PERSON>act, Vue, Angular, etc.) are not static HTML documents. They have an internal state managed by JavaScript. Interacting with them requires techniques that respect this state management. Failure to do so will result in automation that appears to work but ultimately fails.

## The Golden Rule: Respect the Application's State

The most common point of failure is interacting with form inputs. In a framework like React, an input is often a "controlled component." This means <PERSON><PERSON>'s state, not the DOM, is the single source of truth for the input's value. 

**The fatal mistake is changing the input's value in the DOM without telling <PERSON><PERSON> about it.** This is why simple `fill` commands failed repeatedly. The input *looked* filled, but <PERSON><PERSON>'s state was still empty, so an empty form was submitted.

--- 

## 1. Navigation (`mcp1_puppeteer_navigate`)

This is the simplest operation. It is used to load a specific URL.

- **Correct Usage:** Provide the full URL of the page you want to visit.
- **Key Lesson:** Always start a critical sequence of actions (like login) with a `navigate` step. This ensures you are starting from a clean, predictable state and avoids issues from previous, failed interactions.

**Example:**
```json
<mcp1_puppeteer_navigate>
{"url":"http://localhost:5175/auth"}
</mcp1_puppeteer_navigate>
```

--- 

## 2. Interacting with Forms (The Right Way vs. The Wrong Way)

This is the most critical and failure-prone area of web automation.

### The Wrong Way: Simple `fill` or `click` -> `fill`

This approach fails because it does not trigger the `onChange` or `onInput` event listeners that React uses to update its state.

**Code Example (WRONG AND UNRELIABLE):**
```json
// Step 1: Fill email (fails to update React state)
<mcp1_puppeteer_fill>
{"selector":"input[id=\"email\"]","value":"<EMAIL>"}
</mcp1_puppeteer_fill>

// Step 2: Fill password (fails to update React state)
<mcp1_puppeteer_fill>
{"selector":"input[id=\"password\"]","value":"password123"}
</mcp1_puppeteer_fill>

// Step 3: Click submit (submits an empty form according to React)
<mcp1_puppeteer_click>
{"selector":"button[type=\"submit\"]"}
</mcp1_puppeteer_click>
```

### The Right Way: Atomic, React-Aware Evaluation (`mcp1_puppeteer_evaluate`)

This is the most robust and reliable method. It uses a single JavaScript snippet to perform the entire interaction in a way the application understands.

**The key steps within the script are:**
1.  Programmatically find the input elements and the form.
2.  Use `Object.getOwnPropertyDescriptor` to get the native `value` setter. This bypasses React's control and allows direct manipulation.
3.  **CRUCIALLY**, after setting the value, manually dispatch a new `input` event. This is the step that tells React to update its state.
4.  Use `form.requestSubmit()` to reliably submit the form.

**Code Example (CORRECT AND ROBUST):**
```json
<mcp1_puppeteer_evaluate>
{"script":"
    const emailInput = document.querySelector('input[id=\"email\"]');
    const passwordInput = document.querySelector('input[id=\"password\"]');
    const form = emailInput.closest('form');

    if (emailInput && passwordInput && form) {
      const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, 'value').set;
      
      nativeInputValueSetter.call(emailInput, '<EMAIL>');
      emailInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      nativeInputValueSetter.call(passwordInput, 'J4913836j');
      passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      form.requestSubmit();
    } else {
      throw new Error('Could not find one or more required login elements');
    }
  "}
</mcp1_puppeteer_evaluate>
```

--- 

## 3. Visual Verification (`mcp1_puppeteer_screenshot`)

- **Key Lesson:** Never assume a command worked. The tool may report success, but the UI may be in an unexpected state. A screenshot is undeniable proof. It is the most important debugging tool.
- **When to Use:** After navigation, after a form interaction, and after any critical step to confirm the result before proceeding.

**Example:**
```json
<mcp1_puppeteer_screenshot>
{"name":"verification_of_login_success"}
</mcp1_puppeteer_screenshot>
```

--- 

## 4. Other Interactions

### Clicking (`mcp1_puppeteer_click`)
- **Usage:** Best for simple, non-form elements like navigation links, buttons that toggle UI, or tabs.
- **Lesson:** Always use the most specific selector possible. An `id` is best. Avoid generic class names.

### Hovering (`mcp1_puppeteer_hover`)
- **Usage:** To trigger UI elements that appear on mouseover, like dropdown menus or tooltips.
- **Lesson:** After a `hover`, you must chain another action, like a `click` on the element that appeared or a `screenshot` to verify its appearance.

### Selecting from Dropdowns (`mcp1_puppeteer_select`)
- **Usage:** For standard `<select>` HTML elements.
- **Lesson:** If the dropdown is a custom component or a controlled React component, this may fail for the same reasons `fill` fails. The robust `evaluate` method may be required to also dispatch a `change` event.

## Final Summary of Key Lessons

1.  **`evaluate` is King:** For any complex form, default to using `mcp1_puppeteer_evaluate` with the event-dispatching technique. It is the most reliable method.
2.  **Always Dispatch Events:** If you programmatically change an input's value, you MUST dispatch an `input` or `change` event.
3.  **Verify with Screenshots:** Do not proceed blindly. A screenshot is the only source of truth.
4.  **Start Clean:** Use `navigate` to reset the state before starting a sequence of actions.
