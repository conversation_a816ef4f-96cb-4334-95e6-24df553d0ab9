// This is a minimal VS Code extension that watches for trigger text and inserts predefined text
// File: extension.ts

import * as vscode from 'vscode';

export function activate(context: vscode.ExtensionContext) {
    console.log('Text Trigger extension is now active');

    // Store the last known text to detect changes
    let lastText = '';

    // Define your trigger text and response - Updated for Cursor auto-continue scenarios
    const triggerResponses: { [key: string]: string } = {
        "I've reached my tool call limit of 25": "continue",
        "I need to pause execution": "continue",
        "To proceed with the remaining": "continue",
        "Would you like me to continue": "Yes, please continue",
        "By default, we stop the agent after 25 tool calls": "continue",
        "You can resume the conversation": "continue",
        "I've hit the limit": "continue",
        "I'll need to pause": "continue",
        "Should I continue with": "Yes, continue",
        "Let me know if you'd like me to continue": "Yes, continue"
    };

    // Create a function that checks the editor content
    function checkForTriggers() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) return;

        const document = editor.document;
        const text = document.getText();

        // Only process if text has changed
        if (text === lastText) return;
        lastText = text;

        // Check for each trigger
        for (const trigger in triggerResponses) {
            if (text.includes(trigger)) {
                // Get cursor position
                const position = editor.selection.active;

                // Replace the trigger with the response
                editor.edit(editBuilder => {
                    // Find the trigger position
                    const triggerPos = text.lastIndexOf(trigger);
                    const startPos = document.positionAt(triggerPos);
                    const endPos = document.positionAt(triggerPos + trigger.length);

                    // Replace the trigger with the response
                    editBuilder.replace(new vscode.Range(startPos, endPos), triggerResponses[trigger]);
                });

                // Only handle one trigger at a time
                break;
            }
        }
    }

    // Set up a timer to check for triggers periodically
    const interval = setInterval(checkForTriggers, 500);

    // Clean up when the extension is deactivated
    context.subscriptions.push({
        dispose: () => clearInterval(interval)
    });
}

export function deactivate() {
    console.log('Text Trigger extension is now deactivated');
}
