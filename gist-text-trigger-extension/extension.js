"use strict";
// This is a minimal VS Code extension that watches for trigger text and inserts predefined text
// File: extension.ts
exports.__esModule = true;
exports.deactivate = exports.activate = void 0;
var vscode = require("vscode");
function activate(context) {
    console.log('Text Trigger extension is now active');
    // Store the last known text to detect changes
    var lastText = '';
    // Define your trigger text and response - Updated for Cursor auto-continue scenarios
    var triggerResponses = {
        "I've reached my tool call limit of 25": "continue",
        "I need to pause execution": "continue",
        "To proceed with the remaining": "continue",
        "Would you like me to continue": "Yes, please continue",
        "By default, we stop the agent after 25 tool calls": "continue",
        "You can resume the conversation": "continue",
        "I've hit the limit": "continue",
        "I'll need to pause": "continue",
        "Should I continue with": "Yes, continue",
        "Let me know if you'd like me to continue": "Yes, continue"
    };
    // Create a function that checks the editor content
    function checkForTriggers() {
        var editor = vscode.window.activeTextEditor;
        if (!editor)
            return;
        var document = editor.document;
        var text = document.getText();
        // Only process if text has changed
        if (text === lastText)
            return;
        lastText = text;
        var _loop_1 = function (trigger) {
            if (text.includes(trigger)) {
                // Get cursor position
                var position = editor.selection.active;
                // Replace the trigger with the response
                editor.edit(function (editBuilder) {
                    // Find the trigger position
                    var triggerPos = text.lastIndexOf(trigger);
                    var startPos = document.positionAt(triggerPos);
                    var endPos = document.positionAt(triggerPos + trigger.length);
                    // Replace the trigger with the response
                    editBuilder.replace(new vscode.Range(startPos, endPos), triggerResponses[trigger]);
                });
                return "break";
            }
        };
        // Check for each trigger
        for (var trigger in triggerResponses) {
            var state_1 = _loop_1(trigger);
            if (state_1 === "break")
                break;
        }
    }
    // Set up a timer to check for triggers periodically
    var interval = setInterval(checkForTriggers, 500);
    // Clean up when the extension is deactivated
    context.subscriptions.push({
        dispose: function () { return clearInterval(interval); }
    });
}
exports.activate = activate;
function deactivate() {
    console.log('Text Trigger extension is now deactivated');
}
exports.deactivate = deactivate;
