{"name": "text-trigger", "displayName": "Text Trigger", "description": "Watches for trigger text and replaces it with predefined content", "version": "0.0.1", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./extension.js", "contributes": {"commands": []}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/vscode": "^1.60.0", "@types/node": "^16.11.7", "typescript": "^4.5.5"}}