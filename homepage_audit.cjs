const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: {width: 1200, height: 800}
  });
  
  const page = await browser.newPage();
  
  // Listen for console errors
  page.on('console', msg => {
    if (msg.type() === 'error') {
      console.log('❌ CONSOLE ERROR:', msg.text());
    }
  });
  
  page.on('pageerror', error => {
    console.log('❌ PAGE ERROR:', error.message);
  });
  
  await page.goto('http://localhost:5001');
  await page.waitForSelector('body', {timeout: 5000});
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Get page title
  const title = await page.title();
  console.log('📄 Page Title:', title);
  
  // Check for interactive elements
  const buttons = await page.$$('button');
  const forms = await page.$$('form');
  const inputs = await page.$$('input');
  const links = await page.$$('a');
  
  console.log('\n🎯 INTERACTIVE ELEMENTS:');
  console.log('Buttons found:', buttons.length);
  console.log('Forms found:', forms.length);
  console.log('Inputs found:', inputs.length);
  console.log('Links found:', links.length);
  
  // Check for basic structure
  const header = await page.$('header');
  const nav = await page.$('nav');
  const main = await page.$('main');
  const footer = await page.$('footer');
  
  console.log('\n🏗️ STRUCTURE CHECK:');
  console.log('Header exists:', header !== null);
  console.log('Nav exists:', nav !== null);
  console.log('Main exists:', main !== null);
  console.log('Footer exists:', footer !== null);
  
  // Check for images
  const images = await page.$$('img');
  console.log('\n🖼️ IMAGES:');
  console.log('Images found:', images.length);
  
  // Check for logo
  const logo = await page.$('[alt*="logo"], [src*="logo"], .logo');
  console.log('Logo found:', logo !== null);
  
  // Test clicking first button if exists
  if (buttons.length > 0) {
    console.log('\n🔘 TESTING FIRST BUTTON...');
    try {
      await buttons[0].click();
      console.log('✅ First button clicked successfully');
    } catch (error) {
      console.log('❌ Button click failed:', error.message);
    }
  }
  
  // Take final screenshot
  await page.screenshot({path: 'homepage_final_audit.png', fullPage: true});
  console.log('\n📸 Final screenshot saved');
  
  await browser.close();
  console.log('\n✅ Homepage audit completed!');
})();
