const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  console.log('Testing after AppLayout fix...');
  
  // Login
  await page.goto('http://localhost:5001/auth');
  await page.type('input[id="email"]', '<EMAIL>');
  await page.type('input[id="password"]', 'J4913836j');
  await page.click('button[type="submit"]');
  await page.waitForNavigation();
  
  // Check dashboard
  console.log('\n=== DASHBOARD AFTER FIX ===');
  const bodyText = await page.evaluate(() => document.body.innerText);
  console.log('Content length:', bodyText.length);
  console.log('Contains "Loading your workspace":', bodyText.includes('Loading your workspace'));
  console.log('Contains "Smoke-Free Days":', bodyText.includes('Smoke-Free Days'));
  console.log('First 300 chars:');
  console.log(bodyText.substring(0, 300));
  
  await page.screenshot({ path: 'dashboard_fixed.png', fullPage: true });
  
  await browser.close();
})();
