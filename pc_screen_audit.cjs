const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: {width: 1920, height: 1080}, // PC screen size
    args: ['--start-maximized']
  });
  
  const page = await browser.newPage();
  await page.setViewport({width: 1920, height: 1080});
  
  // Listen for console errors
  page.on('console', msg => {
    if (msg.type() === 'error') {
      console.log('❌ CONSOLE ERROR:', msg.text());
    }
  });
  
  page.on('pageerror', error => {
    console.log('❌ PAGE ERROR:', error.message);
  });
  
  await page.goto('http://localhost:5001');
  await page.waitForSelector('body', {timeout: 10000});
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  console.log('🖥️ PC SCREEN SIZE AUDIT - 1920x1080');
  
  // Check for logo specifically
  const logoSelectors = [
    '[alt*="logo"]', '[src*="logo"]', '.logo', 
    '[alt*="Mission"]', '[alt*="Fresh"]', 
    'svg[class*="logo"]', 'img[class*="logo"]',
    'header img', 'nav img'
  ];
  
  let logoFound = false;
  for (const selector of logoSelectors) {
    const element = await page.$(selector);
    if (element) {
      console.log('✅ LOGO FOUND with selector:', selector);
      logoFound = true;
      break;
    }
  }
  
  if (!logoFound) {
    console.log('❌ CRITICAL: LOGO NOT FOUND WITH ANY SELECTOR!');
  }
  
  // Check images
  const images = await page.$$('img');
  console.log('🖼️ Images found:', images.length);
  
  if (images.length > 0) {
    for (let i = 0; i < images.length; i++) {
      const alt = await images[i].evaluate(el => el.alt);
      const src = await images[i].evaluate(el => el.src);
      console.log(`Image ${i+1}: alt="${alt}", src="${src.substring(0, 50)}..."`);
    }
  }
  
  // Check for SVG icons/logos
  const svgs = await page.$$('svg');
  console.log('🎨 SVG elements found:', svgs.length);
  
  // Take full page screenshot
  await page.screenshot({
    path: 'pc_screen_homepage_audit.png', 
    fullPage: true
  });
  
  console.log('📸 PC screen audit screenshot saved');
  
  await browser.close();
})();
