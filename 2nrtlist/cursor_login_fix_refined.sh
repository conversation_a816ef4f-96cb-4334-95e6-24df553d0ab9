#!/bin/bash

echo "🎯 REFINED Cursor Login Fix (Safe Version)"
echo "=========================================="

# Only clear authentication-specific data, not essential app files
echo "1. 🧹 Clearing ONLY authentication data..."

# Clear authentication tokens and session data (but keep app functionality)
find ~/Library/Application\ Support/Cursor -name "*auth*" -type f -delete 2>/dev/null || true
find ~/Library/Application\ Support/Cursor -name "*token*" -type f -delete 2>/dev/null || true
find ~/Library/Application\ Support/Cursor -name "*session*" -type f -delete 2>/dev/null || true
find ~/Library/Application\ Support/Cursor -name "*login*" -type f -delete 2>/dev/null || true

# Clear specific authentication storage keys
echo "2. 🔑 Clearing authentication storage..."
if [ -f ~/Library/Application\ Support/Cursor/User/globalStorage/storage.json ]; then
    # Backup and clean auth keys from storage
    cp ~/Library/Application\ Support/Cursor/User/globalStorage/storage.json ~/Library/Application\ Support/Cursor/User/globalStorage/storage.json.backup
    # Remove auth-related keys but keep other settings
    python3 -c "
import json
import os
storage_file = os.path.expanduser('~/Library/Application Support/Cursor/User/globalStorage/storage.json')
try:
    with open(storage_file, 'r') as f:
        data = json.load(f)
    # Remove authentication keys
    auth_keys = [k for k in data.keys() if any(term in k.lower() for term in ['auth', 'token', 'login', 'session', 'user', 'account'])]
    for key in auth_keys:
        del data[key]
    with open(storage_file, 'w') as f:
        json.dump(data, f, indent=2)
    print(f'Cleaned {len(auth_keys)} authentication keys')
except:
    pass
" 2>/dev/null || true
fi

# Clear browser authentication cache (but not all browser data)
echo "3. 🌐 Clearing browser auth cache..."
find ~/Library/Application\ Support/Google/Chrome/Default/Local\ Storage/leveldb/ -name "*cursor*" -delete 2>/dev/null || true
find ~/Library/Safari/LocalStorage/ -name "*cursor*" -delete 2>/dev/null || true

# Reset only Launch Services for deep links (not the entire database)
echo "4. 🔗 Refreshing deep link handling..."
/System/Library/Frameworks/CoreServices.framework/Frameworks/LaunchServices.framework/Support/lsregister -f "/Applications/Cursor.app" 2>/dev/null || true

# Test if Cursor is responsive
echo "5. 🧪 Testing Cursor responsiveness..."
if pgrep -f "Cursor" > /dev/null; then
    echo "   ✅ Cursor is running"
    # Send a gentle refresh signal
    osascript -e 'tell application "Cursor" to activate' 2>/dev/null || true
else
    echo "   🚀 Starting Cursor..."
    open -a "/Applications/Cursor.app" 2>/dev/null || open -a "/Users/<USER>/Applications/Cursor 0.47 Plus.app" 2>/dev/null || true
fi

echo ""
echo "🎉 REFINED FIX COMPLETE!"
echo "======================="
echo ""
echo "✅ What was done (safely):"
echo "   • Cleared ONLY authentication data (not app functionality)"
echo "   • Removed auth tokens and session data"
echo "   • Cleaned authentication keys from storage"
echo "   • Cleared browser auth cache for cursor.com"
echo "   • Refreshed deep link handling"
echo "   • Preserved all UI settings and app functionality"
echo ""
echo "🔥 NOW TRY:"
echo "1. Click the 'Sign In' button in Cursor"
echo "2. Complete login in browser"
echo "3. If deep link fails, use: ~/cursor_emergency_login.sh"
echo ""
echo "💡 This fix is much safer and preserves app functionality!"
