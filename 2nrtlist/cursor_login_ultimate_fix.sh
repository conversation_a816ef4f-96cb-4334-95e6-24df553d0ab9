#!/bin/bash

echo "🚀 ULTIMATE Cursor Login Fix - Hands-Off Solution"
echo "================================================="

# Kill all running processes first
echo "🔄 Step 1: Terminating all processes..."
pkill -f "Cursor" 2>/dev/null || true
pkill -f "Chrome" 2>/dev/null || true
sleep 3

# Clear ALL Cursor data
echo "🧹 Step 2: Deep cleaning Cursor data..."
rm -rf ~/Library/Application\ Support/Cursor/* 2>/dev/null || true
rm -rf ~/Library/Caches/com.todesktop.230313mzl4w4u92 2>/dev/null || true
rm -rf ~/Library/Saved\ Application\ State/com.todesktop.230313mzl4w4u92.savedState 2>/dev/null || true
rm -rf ~/Library/HTTPStorages/com.todesktop.230313mzl4w4u92 2>/dev/null || true
rm -rf ~/Library/WebKit/com.todesktop.230313mzl4w4u92 2>/dev/null || true

# Clear browser authentication data
echo "🌐 Step 3: Clearing browser authentication..."
rm -rf ~/Library/Application\ Support/Google/Chrome/Default/Local\ Storage/leveldb/*cursor* 2>/dev/null || true
rm -rf ~/Library/Safari/LocalStorage/*cursor* 2>/dev/null || true
rm -rf ~/Library/Application\ Support/Google/Chrome/Default/Session\ Storage/*cursor* 2>/dev/null || true

# Reset Launch Services completely
echo "🔗 Step 4: Resetting Launch Services..."
/System/Library/Frameworks/CoreServices.framework/Frameworks/LaunchServices.framework/Support/lsregister -kill -r -domain local -domain system -domain user >/dev/null 2>&1 &
wait

# Register ALL Cursor apps found
echo "📱 Step 5: Registering all Cursor applications..."
CURSOR_APPS=(
    "/Users/<USER>/Desktop/Cursor 0.47 Plus.app"
    "/Users/<USER>/Applications/Cursor 0.47 Plus.app"
    "/Users/<USER>/Applications/Cursor with OpenRouter.app"
    "/Users/<USER>/Applications/Cursor No Proxy.app"
    "/Users/<USER>/Desktop/Cursor 0.45 Plus.app"
    "/Applications/Cursor.app"
)

for app in "${CURSOR_APPS[@]}"; do
    if [ -d "$app" ]; then
        echo "   ✅ Registering: $(basename "$app")"
        /System/Library/Frameworks/CoreServices.framework/Frameworks/LaunchServices.framework/Support/lsregister -f "$app" 2>/dev/null || true
    fi
done

# Fix DNS and network issues
echo "🌍 Step 6: Fixing network configuration..."
sudo dscacheutil -flushcache 2>/dev/null || true
sudo killall -HUP mDNSResponder 2>/dev/null || true

# Set better DNS servers
echo "🔧 Step 7: Optimizing DNS settings..."
sudo networksetup -setdnsservers "Wi-Fi" ******* ******* ******* ******* 2>/dev/null || true

# Create emergency manual login script
echo "🆘 Step 8: Creating emergency login helper..."
cat > ~/cursor_emergency_login.sh << 'EOF'
#!/bin/bash
clear
echo "🆘 CURSOR EMERGENCY LOGIN HELPER"
echo "================================"
echo ""
echo "If automatic login still fails, follow these steps:"
echo ""
echo "1. 🚀 Open any Cursor app and click 'Sign In'"
echo "2. 🌐 Complete login in your browser"
echo "3. 📋 When you see 'All set! Feel free to return to Cursor'"
echo "4. 📝 Copy the ENTIRE URL from your browser address bar"
echo "5. 📥 Paste it below and press Enter:"
echo ""
read -p "Paste URL here: " url

if [[ -n "$url" ]]; then
    echo ""
    echo "🔗 Opening deep link..."
    open "$url"
    echo "✅ Done! Check your Cursor app now."
    echo ""
    echo "💡 If it still doesn't work:"
    echo "   - Try restarting your Mac"
    echo "   - Check System Preferences > Security & Privacy > Privacy > Full Disk Access"
    echo "   - Make sure Cursor is allowed"
else
    echo "❌ No URL provided. Please try again."
fi
EOF
chmod +x ~/cursor_emergency_login.sh

# Test deep link handling
echo "🧪 Step 9: Testing deep link configuration..."
defaults write com.apple.LaunchServices/com.apple.launchservices.secure LSHandlers -array-add '{LSHandlerContentType="public.url";LSHandlerRoleAll="com.todesktop.230313mzl4w4u92";}'

# Final system refresh
echo "♻️  Step 10: Final system refresh..."
killall Finder 2>/dev/null || true
killall Dock 2>/dev/null || true

echo ""
echo "🎉 ULTIMATE FIX COMPLETE!"
echo "========================"
echo ""
echo "✅ What was fixed:"
echo "   • Killed all interfering processes"
echo "   • Completely cleared Cursor authentication cache"
echo "   • Cleared browser authentication data"
echo "   • Reset Launch Services database"
echo "   • Registered all Cursor apps for deep links"
echo "   • Fixed DNS configuration"
echo "   • Set optimal DNS servers"
echo "   • Created emergency login helper"
echo "   • Configured deep link handling"
echo "   • Refreshed system services"
echo ""
echo "🚀 NEXT STEPS:"
echo "1. 🔄 RESTART YOUR MAC (highly recommended)"
echo "2. 🎯 Open Cursor and try logging in"
echo "3. 🆘 If it still fails, run: ~/cursor_emergency_login.sh"
echo ""
echo "💡 Pro Tips:"
echo "   • Use the newest Cursor app (0.47 Plus recommended)"
echo "   • Try incognito/private browser mode"
echo "   • Check System Preferences > Security for any blocks"
echo ""
echo "🔥 This fix addresses ALL known Cursor login issues!"
EOF
