-- Create vendors table for Online Vendors page functionality
-- RULE 0001 COMPLIANT: Real database table creation only
-- Based on TypeScript Vendor interface found in src/lib/supabase.ts

CREATE TABLE IF NOT EXISTS public.vendors (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    website TEXT NOT NULL,
    logo_url TEXT,
    description TEXT NOT NULL,
    rating DECIMAL(3,2) DEFAULT 0.0,
    review_count INTEGER DEFAULT 0,
    shipping_info TEXT NOT NULL,
    min_order DECIMAL(10,2) DEFAULT 0.00,
    delivery_time TEXT NOT NULL,
    coverage_areas TEXT[] DEFAULT '{}',
    specialties TEXT[] DEFAULT '{}',
    verified BOOLEA<PERSON> DEFAULT false,
    affiliate_commission DECIMAL(5,4) DEFAULT 0.0000,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_vendors_active ON public.vendors(active);
CREATE INDEX IF NOT EXISTS idx_vendors_verified ON public.vendors(verified);
CREATE INDEX IF NOT EXISTS idx_vendors_rating ON public.vendors(rating DESC);
CREATE INDEX IF NOT EXISTS idx_vendors_name ON public.vendors(name);

-- Add RLS (Row Level Security) policy for public read access
ALTER TABLE public.vendors ENABLE ROW LEVEL SECURITY;

-- Allow public read access to vendors (no auth required)
CREATE POLICY IF NOT EXISTS "Enable read access for all users" ON public.vendors
    FOR SELECT USING (true);

-- Allow authenticated users to insert/update vendors (for admin functionality)
CREATE POLICY IF NOT EXISTS "Enable insert for authenticated users only" ON public.vendors
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY IF NOT EXISTS "Enable update for authenticated users only" ON public.vendors
    FOR UPDATE USING (auth.role() = 'authenticated');

COMMENT ON TABLE public.vendors IS 'Online NRT vendors for price comparison and affiliate revenue';
COMMENT ON COLUMN public.vendors.id IS 'Unique vendor identifier';
COMMENT ON COLUMN public.vendors.name IS 'Vendor business name';
COMMENT ON COLUMN public.vendors.website IS 'Vendor website URL';
COMMENT ON COLUMN public.vendors.logo_url IS 'Vendor logo image URL';
COMMENT ON COLUMN public.vendors.description IS 'Vendor description and specialties';
COMMENT ON COLUMN public.vendors.rating IS 'Average customer rating (0.0-5.0)';
COMMENT ON COLUMN public.vendors.review_count IS 'Total number of customer reviews';
COMMENT ON COLUMN public.vendors.shipping_info IS 'Shipping options and policies';
COMMENT ON COLUMN public.vendors.min_order IS 'Minimum order amount in USD';
COMMENT ON COLUMN public.vendors.delivery_time IS 'Typical delivery timeframe';
COMMENT ON COLUMN public.vendors.coverage_areas IS 'Countries/regions served';
COMMENT ON COLUMN public.vendors.specialties IS 'Product specialties and categories';
COMMENT ON COLUMN public.vendors.verified IS 'Vendor verification status';
COMMENT ON COLUMN public.vendors.affiliate_commission IS 'Commission rate for affiliate program';
COMMENT ON COLUMN public.vendors.active IS 'Whether vendor is currently active';
