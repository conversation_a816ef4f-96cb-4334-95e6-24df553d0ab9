#!/bin/bash

echo "🔧 Comprehensive Cursor Login Fix Script"
echo "========================================"

# Step 1: Kill all Cursor processes
echo "1. Killing all Cursor processes..."
pkill -f "Cursor" 2>/dev/null || true
sleep 2

# Step 2: Clear all Cursor data and cache
echo "2. Clearing Cursor cache and authentication data..."
rm -rf ~/Library/Application\ Support/Cursor/User/globalStorage 2>/dev/null || true
rm -rf ~/Library/Application\ Support/Cursor/logs 2>/dev/null || true
rm -rf ~/Library/Application\ Support/Cursor/CachedData 2>/dev/null || true
rm -rf ~/Library/Application\ Support/Cursor/User/workspaceStorage 2>/dev/null || true
rm -rf ~/Library/Caches/com.todesktop.230313mzl4w4u92 2>/dev/null || true
rm -rf ~/Library/Saved\ Application\ State/com.todesktop.230313mzl4w4u92.savedState 2>/dev/null || true

# Step 3: Reset Launch Services (fixes deep link handling)
echo "3. Resetting Launch Services database..."
/System/Library/Frameworks/CoreServices.framework/Frameworks/LaunchServices.framework/Support/lsregister -kill -r -domain local -domain system -domain user >/dev/null 2>&1

# Step 4: Clear browser data that might interfere
echo "4. Clearing browser authentication cache..."
# Clear Chrome data for cursor.com
rm -rf ~/Library/Application\ Support/Google/Chrome/Default/Local\ Storage/leveldb/*cursor* 2>/dev/null || true
# Clear Safari data
rm -rf ~/Library/Safari/LocalStorage/*cursor* 2>/dev/null || true

# Step 5: Fix DNS issues
echo "5. Flushing DNS cache..."
sudo dscacheutil -flushcache 2>/dev/null || true
sudo killall -HUP mDNSResponder 2>/dev/null || true

# Step 6: Reset network settings that might block deep links
echo "6. Checking network configuration..."
# Check if any proxy is blocking
PROXY_STATUS=$(networksetup -getwebproxy "Wi-Fi" | grep "Enabled: Yes" || echo "No proxy")
if [[ "$PROXY_STATUS" != "No proxy" ]]; then
    echo "   Warning: Proxy detected. This might interfere with authentication."
fi

# Step 7: Create manual deep link handler
echo "7. Creating manual deep link handler..."
cat > ~/cursor_manual_login.sh << 'EOF'
#!/bin/bash
echo "Manual Cursor Login Helper"
echo "========================="
echo "1. Open Cursor and click 'Sign In'"
echo "2. Complete login in browser"
echo "3. When you see 'All set! Feel free to return to Cursor'"
echo "4. Copy the URL from your browser address bar"
echo "5. Paste it here and press Enter:"
read -r url
echo "Opening deep link..."
open "$url"
echo "Done! Check Cursor now."
EOF
chmod +x ~/cursor_manual_login.sh

# Step 8: Test deep link handling
echo "8. Testing deep link registration..."
# Re-register Cursor app
if [ -d "/Applications/Cursor.app" ]; then
    /System/Library/Frameworks/CoreServices.framework/Frameworks/LaunchServices.framework/Support/lsregister -f /Applications/Cursor.app
    echo "   ✅ Cursor app re-registered"
else
    echo "   ⚠️  Cursor app not found in /Applications/"
fi

echo ""
echo "🎉 Fix complete! Here's what was done:"
echo "   ✅ Killed all Cursor processes"
echo "   ✅ Cleared all authentication cache"
echo "   ✅ Reset Launch Services database"
echo "   ✅ Cleared browser cache for cursor.com"
echo "   ✅ Flushed DNS cache"
echo "   ✅ Re-registered Cursor app"
echo "   ✅ Created manual login helper at ~/cursor_manual_login.sh"
echo ""
echo "🚀 Next steps:"
echo "1. Restart your Mac (recommended) OR just restart Cursor"
echo "2. Try logging in to Cursor normally"
echo "3. If it still doesn't work, run: ~/cursor_manual_login.sh"
echo ""
echo "💡 Pro tip: If you still have issues, the problem might be:"
echo "   - Firewall blocking deep links (check System Preferences > Security)"
echo "   - Corporate network restrictions"
echo "   - Cursor server issues (try again later)"
