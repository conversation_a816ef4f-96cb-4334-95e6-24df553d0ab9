-- Sample store data for testing stores page functionality
-- This adds real data to the mission_fresh.stores table (RULE 0001 compliant)

INSERT INTO stores (
  id,
  name,
  brand,
  chain,
  address,
  city,
  state,
  zip_code,
  country,
  latitude,
  longitude,
  phone,
  website,
  store_hours,
  nrt_brands_carried,
  pharmacy_available,
  prescription_required,
  drive_through,
  parking_available,
  wheelchair_accessible,
  rating,
  review_count,
  verified,
  created_at,
  updated_at
) VALUES 
(
  'store-001',
  'CVS Pharmacy',
  'CVS',
  'CVS Health',
  '123 Main Street',
  'New York',
  'NY',
  '10001',
  'USA',
  40.7589,
  -73.9851,
  '(*************',
  'https://www.cvs.com',
  '{"monday": "8:00 AM - 10:00 PM", "tuesday": "8:00 AM - 10:00 PM", "wednesday": "8:00 AM - 10:00 PM", "thursday": "8:00 AM - 10:00 PM", "friday": "8:00 AM - 10:00 PM", "saturday": "8:00 AM - 10:00 PM", "sunday": "9:00 AM - 9:00 PM"}',
  '["Nicorette", "NicoDerm CQ", "Commit", "Habitrol"]',
  true,
  false,
  true,
  true,
  true,
  4.5,
  127,
  true,
  NOW(),
  NOW()
),
(
  'store-002',
  'Walgreens',
  'Walgreens',
  'Walgreens Boots Alliance',
  '456 Broadway',
  'New York',
  'NY',
  '10013',
  'USA',
  40.7505,
  -73.9934,
  '(*************',
  'https://www.walgreens.com',
  '{"monday": "7:00 AM - 11:00 PM", "tuesday": "7:00 AM - 11:00 PM", "wednesday": "7:00 AM - 11:00 PM", "thursday": "7:00 AM - 11:00 PM", "friday": "7:00 AM - 11:00 PM", "saturday": "7:00 AM - 11:00 PM", "sunday": "8:00 AM - 10:00 PM"}',
  '["Nicorette", "NicoDerm CQ", "Thrive"]',
  true,
  false,
  true,
  true,
  true,
  4.3,
  89,
  true,
  NOW(),
  NOW()
),
(
  'store-003',
  'Rite Aid',
  'Rite Aid',
  'Rite Aid Corporation',
  '789 Fifth Avenue',
  'New York',
  'NY',
  '10022',
  'USA',
  40.7614,
  -73.9776,
  '(212) 555-0789',
  'https://www.riteaid.com',
  '{"monday": "8:00 AM - 9:00 PM", "tuesday": "8:00 AM - 9:00 PM", "wednesday": "8:00 AM - 9:00 PM", "thursday": "8:00 AM - 9:00 PM", "friday": "8:00 AM - 9:00 PM", "saturday": "8:00 AM - 9:00 PM", "sunday": "9:00 AM - 8:00 PM"}',
  '["Nicorette", "Commit", "Habitrol"]',
  true,
  false,
  false,
  true,
  true,
  4.1,
  56,
  true,
  NOW(),
  NOW()
),
(
  'store-004',
  'Target Pharmacy',
  'Target',
  'Target Corporation',
  '101 West 14th Street',
  'New York',
  'NY',
  '10011',
  'USA',
  40.7370,
  -73.9960,
  '(212) 555-0101',
  'https://www.target.com',
  '{"monday": "8:00 AM - 10:00 PM", "tuesday": "8:00 AM - 10:00 PM", "wednesday": "8:00 AM - 10:00 PM", "thursday": "8:00 AM - 10:00 PM", "friday": "8:00 AM - 10:00 PM", "saturday": "8:00 AM - 10:00 PM", "sunday": "8:00 AM - 9:00 PM"}',
  '["Nicorette", "NicoDerm CQ", "up&up"]',
  true,
  false,
  false,
  true,
  true,
  4.4,
  203,
  true,
  NOW(),
  NOW()
),
(
  'store-005',
  'Duane Reade',
  'Duane Reade',
  'Walgreens Boots Alliance',
  '224 West 57th Street',
  'New York',
  'NY',
  '10019',
  'USA',
  40.7648,
  -73.9808,
  '(212) 555-0224',
  'https://www.duanereade.com',
  '{"monday": "7:00 AM - 11:00 PM", "tuesday": "7:00 AM - 11:00 PM", "wednesday": "7:00 AM - 11:00 PM", "thursday": "7:00 AM - 11:00 PM", "friday": "7:00 AM - 11:00 PM", "saturday": "7:00 AM - 11:00 PM", "sunday": "8:00 AM - 10:00 PM"}',
  '["Nicorette", "NicoDerm CQ", "Commit"]',
  true,
  false,
  false,
  true,
  true,
  4.2,
  78,
  true,
  NOW(),
  NOW()
);
