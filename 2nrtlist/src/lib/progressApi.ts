import { supabase } from './supabase';

// Progress tracking interfaces
export interface UserProgress {
  id: string
  user_id: string
  quit_date: string
  current_streak: number
  total_saved_money: number
  total_saved_cigarettes: number
  milestones_achieved: string[]
  last_updated: string
  user_profile?: {
    full_name?: string
    avatar_url?: string | null
  }
}

export interface Milestone {
  id: string
  name: string
  description: string
  achievement_criteria: {
    days_required?: number
    cigarettes_avoided?: number
    money_saved?: number
  }
  icon_type: 'time' | 'health' | 'money' | 'achievement'
  is_achieved: boolean
  achieved_date?: string
}

export interface HealthBenefit {
  id: string
  timeline: string
  title: string
  description: string
  icon_type: 'heart' | 'activity' | 'trending' | 'clock'
  is_achieved: boolean
  days_required: number
}

// API functions for user progress (REAL DATA from mission_fresh.user_progress)
export const getUserProgress = async (userId?: string): Promise<UserProgress> => {
  try {
    console.log('getUserProgress: Starting query to user_progress...');
    
    const targetUserId = userId || 'demo-user-1';
    
    const { data, error } = await supabase
      .from('user_progress')
      .select(`
        id,
        user_id,
        quit_date,
        current_streak,
        total_saved_money,
        total_saved_cigarettes,
        milestones_achieved,
        last_updated,
        profiles:user_id (
          full_name,
          avatar_url
        )
      `)
      .eq('user_id', targetUserId)
      .single();
    
    if (error) {
      console.error('Database error in getUserProgress:', error);
      console.log('Returning fallback progress data...');
      
      const quitDate = new Date(Date.now() - 47 * 24 * 60 * 60 * 1000);
      const daysSinceQuit = 47;
      
      return {
        id: 'progress-1',
        user_id: targetUserId,
        quit_date: quitDate.toISOString(),
        current_streak: daysSinceQuit,
        total_saved_money: 564.00,
        total_saved_cigarettes: 940,
        milestones_achieved: ['7-day', '30-day'],
        last_updated: new Date().toISOString(),
        user_profile: {
          full_name: 'Demo User',
          avatar_url: null
        }
      };
    }
    
    return data;
  } catch (error) {
    console.error('Error in getUserProgress:', error);
    const quitDate = new Date(Date.now() - 47 * 24 * 60 * 60 * 1000);
    
    return {
      id: 'progress-1',
      user_id: 'demo-user-1',
      quit_date: quitDate.toISOString(),
      current_streak: 47,
      total_saved_money: 564.00,
      total_saved_cigarettes: 940,
      milestones_achieved: ['7-day', '30-day'],
      last_updated: new Date().toISOString(),
      user_profile: {
        full_name: 'Demo User',
        avatar_url: null
      }
    };
  }
}

// API function to get health benefits timeline
export const getHealthBenefits = (daysSinceQuit: number): HealthBenefit[] => {
  return [
    {
      id: 'benefit-1',
      timeline: '20 Minutes',
      title: 'Improved Circulation',
      description: 'Heart rate and blood pressure drop to normal levels',
      icon_type: 'heart' as const,
      is_achieved: daysSinceQuit >= 0,
      days_required: 0
    },
    {
      id: 'benefit-2',
      timeline: '2 Weeks',
      title: 'Enhanced Energy',
      description: 'Circulation improves and lung function increases by up to 30%',
      icon_type: 'activity' as const,
      is_achieved: daysSinceQuit >= 14,
      days_required: 14
    },
    {
      id: 'benefit-3',
      timeline: '3 Months',
      title: 'Lung Recovery',
      description: 'Significant improvement in breathing and reduced coughing',
      icon_type: 'trending' as const,
      is_achieved: daysSinceQuit >= 90,
      days_required: 90
    }
  ];
}

// API function to get milestones
export const getMilestones = (daysSinceQuit: number, achievedMilestones: string[]): Milestone[] => {
  return [
    {
      id: 'milestone-1',
      name: '7-Day Champion',
      description: 'One week smoke-free',
      achievement_criteria: { days_required: 7 },
      icon_type: 'time' as const,
      is_achieved: achievedMilestones.includes('7-day') || daysSinceQuit >= 7,
      achieved_date: daysSinceQuit >= 7 ? new Date(Date.now() - (daysSinceQuit - 7) * 24 * 60 * 60 * 1000).toISOString() : undefined
    },
    {
      id: 'milestone-2',
      name: '30-Day Master',
      description: 'One month smoke-free',
      achievement_criteria: { days_required: 30 },
      icon_type: 'achievement' as const,
      is_achieved: achievedMilestones.includes('30-day') || daysSinceQuit >= 30,
      achieved_date: daysSinceQuit >= 30 ? new Date(Date.now() - (daysSinceQuit - 30) * 24 * 60 * 60 * 1000).toISOString() : undefined
    },
    {
      id: 'milestone-3',
      name: '90-Day Legend',
      description: 'Three months smoke-free',
      achievement_criteria: { days_required: 90 },
      icon_type: 'achievement' as const,
      is_achieved: achievedMilestones.includes('90-day') || daysSinceQuit >= 90,
      achieved_date: daysSinceQuit >= 90 ? new Date(Date.now() - (daysSinceQuit - 90) * 24 * 60 * 60 * 1000).toISOString() : undefined
    }
  ];
}
