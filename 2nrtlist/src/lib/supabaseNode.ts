// Node.js compatible Supabase configuration for seed scripts
// RULE 0001 COMPLIANT: Real database connection only
import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables for Node.js context
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://yekarqanirdkdckimpna.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc';

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabaseNode = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

console.log('🚨 supabaseNode: Initialized with URL:', supabaseUrl.substring(0, 30) + '...');
