import React, { createContext, useContext, useEffect, useState } from 'react'
import { supabase, getCurrentUser, signIn, signUp, signOut, onAuthStateChange } from '../lib/supabase'
import type { User } from '@supabase/supabase-js'

// User role types for sophisticated multi-user platform
export type UserRole = 'user' | 'vendor' | 'admin'

// Enhanced user profile interface
export interface UserProfile {
  id: string
  email: string
  full_name?: string
  role: UserRole
  vendor_id?: string
  business_name?: string
  verified_vendor?: boolean
  created_at: string
  updated_at: string
}

interface AuthContextType {
  user: User | null
  userProfile: UserProfile | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, userData?: { full_name?: string; role?: UserRole }) => Promise<void>
  signOut: () => Promise<void>
  isAuthenticated: boolean
  userRole: UserRole | null
  isVendor: boolean
  isUser: boolean
  isAdmin: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)

  // Fetch user profile with role information
  const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single()
      
      if (error) {
        console.error('Error fetching user profile:', error)
        return null
      }
      
      return data as UserProfile
    } catch (error) {
      console.error('Error in fetchUserProfile:', error)
      return null
    }
  }

  useEffect(() => {
    // Get initial user and profile
    getCurrentUser().then(async (user) => {
      setUser(user)
      if (user) {
        const profile = await fetchUserProfile(user.id)
        setUserProfile(profile)
      }
      setLoading(false)
    })

    // Listen for auth changes
    const { data: { subscription } } = onAuthStateChange(async (event, session) => {
      const user = session?.user ?? null
      setUser(user)
      
      if (user) {
        const profile = await fetchUserProfile(user.id)
        setUserProfile(profile)
      } else {
        setUserProfile(null)
      }
      
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const handleSignIn = async (email: string, password: string) => {
    setLoading(true)
    try {
      await signIn(email, password)
    } catch (error) {
      throw error
    } finally {
      setLoading(false)
    }
  }

  const handleSignUp = async (email: string, password: string, userData?: { full_name?: string; role?: UserRole }) => {
    setLoading(true)
    try {
      await signUp(email, password, userData)
    } catch (error) {
      throw error
    } finally {
      setLoading(false)
    }
  }

  const handleSignOut = async () => {
    setLoading(true)
    try {
      await signOut()
    } catch (error) {
      throw error
    } finally {
      setLoading(false)
    }
  }

  const value = {
    user,
    userProfile,
    loading,
    signIn: handleSignIn,
    signUp: handleSignUp,
    signOut: handleSignOut,
    isAuthenticated: !!user,
    userRole: userProfile?.role || null,
    isVendor: userProfile?.role === 'vendor',
    isUser: userProfile?.role === 'user',
    isAdmin: userProfile?.role === 'admin'
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export default AuthProvider
