import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

// Universal Layout Component
import Layout from './components/Layout';

// Landing Page
import LandingPage from './App';

// All SEO-Optimized Pages for Maximum Revenue
import ProductDetailPage from './pages/ProductDetailPage';
import ProductsPage from './pages/ProductsPage';
import CategoryPage from './pages/CategoryPage';
import BrandPage from './pages/BrandPage';
import StoreDetailPage from './pages/StoreDetailPage';
import VendorDetailPage from './pages/VendorDetailPage';
import VendorListPage from './pages/VendorListPage';
import SearchResultsPage from './pages/SearchResultsPage';
import ReviewsPage from './pages/ReviewsPage';
import DealsPage from './pages/DealsPage';
import VendorsPage from './pages/VendorsPage';
import DiscoverPage from './pages/DiscoverPage';
import StoreLocator from './components/StoreLocator';
import UserProfilePage from './pages/UserProfilePage';
import RetailersPage from './pages/RetailersPage';
import CommunityPage from './pages/CommunityPage';
import ProgressPage from './pages/ProgressPage';
import SmokelessPage from './pages/SmokelessPage';
import PriceComparePage from './pages/PriceComparePage';

import { AuthProvider } from './contexts/AuthContext';

const AppRouter: React.FC = () => {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Landing Page - Has its own header/footer */}
          <Route path="/" element={<LandingPage />} />
          
          {/* All other pages wrapped in universal Layout */}
          <Route path="/products" element={<Layout><ProductsPage /></Layout>} />
          <Route path="/nrt-directory" element={<Layout><ProductsPage /></Layout>} />
          <Route path="/product/:id" element={<Layout><ProductDetailPage /></Layout>} />
          <Route path="/product/:id/:slug" element={<Layout><ProductDetailPage /></Layout>} />
          
          {/* SEO-Optimized Category Pages */}
          <Route path="/category/:category" element={<Layout><CategoryPage /></Layout>} />
          <Route path="/category/:category/:subcategory" element={<Layout><CategoryPage /></Layout>} />
          
          {/* SEO-Optimized Brand Pages */}
          <Route path="/brand/:brand" element={<Layout><BrandPage /></Layout>} />
          <Route path="/brand/:brand/products" element={<Layout><BrandPage /></Layout>} />
          
          {/* SEO-Optimized Store Pages */}
          <Route path="/store/:id" element={<Layout><StoreDetailPage /></Layout>} />
          <Route path="/store/:id/:slug" element={<Layout><StoreDetailPage /></Layout>} />
          <Route path="/store-locator" element={<Layout><StoreLocator /></Layout>} />
          
          {/* SEO-Optimized Vendor Pages */}
          <Route path="/vendor/:id" element={<Layout><VendorDetailPage /></Layout>} />
          <Route path="/vendor/:id/:slug" element={<Layout><VendorDetailPage /></Layout>} />
          <Route path="/online-vendors" element={<Layout><VendorsPage /></Layout>} />
          
          {/* SEO-Optimized Search & Discovery */}
          <Route path="/search" element={<Layout><SearchResultsPage /></Layout>} />
          <Route path="/discover" element={<Layout><DiscoverPage /></Layout>} />
          <Route path="/marketplace" element={<Layout><SearchResultsPage /></Layout>} />
          
          {/* SEO-Optimized Content Pages */}
          <Route path="/reviews" element={<Layout><ReviewsPage /></Layout>} />
          <Route path="/deals" element={<Layout><DealsPage /></Layout>} />
          <Route path="/price-compare" element={<Layout><PriceComparePage /></Layout>} />
          <Route path="/smokeless-alternatives" element={<Layout><SmokelessPage /></Layout>} />
          
          {/* Main Navigation Pages */}
          <Route path="/retailers" element={<Layout><RetailersPage /></Layout>} />
          <Route path="/community" element={<Layout><CommunityPage /></Layout>} />
          <Route path="/progress" element={<Layout><ProgressPage /></Layout>} />
          
          {/* User Pages */}
          <Route path="/profile" element={<Layout><UserProfilePage /></Layout>} />
          <Route path="/my-journey" element={<Layout><UserProfilePage /></Layout>} />
          
          {/* Fallback */}
          <Route path="*" element={<LandingPage />} />
        </Routes>
      </Router>
    </AuthProvider>
  );
};

export default AppRouter;
