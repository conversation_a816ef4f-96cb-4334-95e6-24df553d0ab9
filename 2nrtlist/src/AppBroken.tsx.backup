import { useState, useEffect, useRef, useReducer } from 'react';
import { Link } from 'react-router-dom';
import { Leaf, Search, Package, Store, Users, Target, BookOpen, BarChart3, Settings, Sparkles, Star, Gem, Flame, TrendingUp, BarChart, ChevronRight, X, Shield, Zap, Heart, ScanLine, Crosshair, DollarSign, Award, MessageCircle, Mail, Smartphone, Plus, ChevronDown, ShoppingBag } from 'lucide-react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import AuthModal from './components/AuthModal';
import VendorPage from './pages/VendorPage';
import StoreLocator from './components/StoreLocator';
import ProductRating from './components/ProductRating';

import SearchSystem from './components/SearchSystem';
import { getProducts } from './lib/supabase';

// REAL NRT Product Interface (matching mission_fresh.smokeless_products)
interface Product {
  id: string;
  name: string;
  brand: string;
  category: string;
  description: string;
  image_url: string;
  nicotine_strengths: any; // jsonb
  flavors: string[]; // array
  user_rating_avg: number;
  user_rating_count: number;
  is_verified: boolean;
  ingredients?: string;
  country_of_origin?: string;
  manufacturer?: string;
  tags?: string[];
  created_at: string;
  updated_at: string;
  expert_notes_chemicals?: string;
  expert_notes_gum_health?: string;
}

// Advanced marketplace interfaces (from Vivino/Weedmaps research)
interface VendorDeal {
  id: string;
  vendor_name: string;
  original_price: number;
  discounted_price: number;
  discount_percentage: number;
  valid_until: string;
  commission_rate: number;
}

interface DiscountCode {
  id: string;
  code: string;
  discount_type: 'percentage' | 'fixed';
  discount_value: number;
  vendor_name: string;
  expires_at: string;
}

interface AffiliateLink {
  id: string;
  vendor_name: string;
  url: string;
  commission_rate: number;
  tracking_id: string;
}

// User preference system (from Vivino taste profile research)
interface UserPreferences {
  preferred_categories: string[];
  strength_preference: 'low' | 'medium' | 'high';
  flavor_preferences: string[];
  price_range: { min: number; max: number };
  taste_profile_score: number;
  // Advanced marketplace features (Vivino/RateBeer-style)
  preferred_strength: string;
  preferred_flavor: string;
  taste_profile: string[];
  price_sensitivity: string;
}

// Main App Component with Database Integration
const MainApp = () => {
  const { isAuthenticated, user, isVendor, signOut } = useAuth();
  type TabId = 'discover' | 'products' | 'vendors' | 'community' | 'my-journey' | 'learn' | 'vendor-dashboard' | 'vendor-analytics' | 'vendor-tools';
  // Use useReducer for more reliable state management
  const [activeTab, dispatchTabChange] = useReducer(
    (state: TabId, action: { type: 'SET_TAB'; tabId: TabId }) => {
      console.log('🔄 Tab state change:', state, '->', action.tabId);
      return action.tabId;
    },
    'discover' as TabId
  );
  
  const setActiveTab = (tabId: TabId) => {
    dispatchTabChange({ type: 'SET_TAB', tabId });
  };
  const navigationRef = useRef<HTMLDivElement>(null);
  const isNavigatingRef = useRef(false);

  // Premium NRT Platform Navigation Configuration
  const getNavigationTabs = (): { id: TabId; label: string; icon: any; description: string }[] => {
    const coreDiscoveryTabs = [
      { id: 'discover' as const, label: 'Discover', icon: Search, description: 'Explore NRT products & scan labels' },
      { id: 'products' as const, label: 'Marketplace', icon: Package, description: 'Compare pouches, snus & NRT products' },
    ];

    if (isVendor) {
      // NRT Vendor Business Dashboard
      return [
        ...coreDiscoveryTabs,
        { id: 'vendor-dashboard' as const, label: 'Store Hub', icon: Store, description: 'Manage NRT inventory & listings' },
        { id: 'vendor-analytics' as const, label: 'Insights', icon: TrendingUp, description: 'Sales analytics & trends' },
        { id: 'community' as const, label: 'Network', icon: Users, description: 'Vendor community & support' },
        { id: 'vendor-tools' as const, label: 'Business', icon: Settings, description: 'Pricing & promotional tools' },
      ];
    } else {
      // NRT User Experience Journey
      return [
        ...coreDiscoveryTabs,
        { id: 'vendors' as const, label: 'Retailers', icon: Store, description: 'Find stores & exclusive deals' },
        { id: 'community' as const, label: 'Community', icon: Users, description: 'Connect with NRT users' },
        { id: 'my-journey' as const, label: 'Progress', icon: Target, description: 'Track your NRT journey' },
        { id: 'learn' as const, label: 'Education', icon: BookOpen, description: 'NRT science & harm reduction' },
      ];
    }
  };

  const tabs = getNavigationTabs();

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');
  


  // Advanced marketplace state (from Vivino/RateBeer research)
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Advanced marketplace state management (Vivino/RateBeer-style)
  const [userPreferences, setUserPreferences] = useState<UserPreferences>({
    preferred_categories: ['gum', 'lozenge'],
    strength_preference: 'medium',
    flavor_preferences: ['mint'],
    price_range: { min: 0, max: 100 },
    taste_profile_score: 0,
    // Advanced marketplace features (Vivino/RateBeer-style)
    preferred_strength: 'medium',
    preferred_flavor: 'mint',
    taste_profile: ['mint', 'strong'],
    price_sensitivity: 'medium'
  });
  const [showDealsOnly, setShowDealsOnly] = useState(false);
  const [sortBy, setSortBy] = useState<'match_percentage' | 'rating' | 'price_low' | 'price_high' | 'deals'>('match_percentage');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [priceRangeDropdownOpen, setPriceRangeDropdownOpen] = useState(false);
  const [showDiscountCodes, setShowDiscountCodes] = useState(false);
  const [premiumFeatures, setPremiumFeatures] = useState(false); // Subscription model
  
  // Education tab functionality - device information modal
  const [selectedDevice, setSelectedDevice] = useState<{type: string, name: string, description: string} | null>(null);
  
  // Community tab functionality - product information modal
  const [selectedCommunityProduct, setSelectedCommunityProduct] = useState<{name: string, category: string, description: string, benefits: string[], usage: string, availability: string} | null>(null);
  
  // Progress tab functionality - journey feature information modal
  const [selectedJourneyFeature, setSelectedJourneyFeature] = useState<{name: string, category: string, description: string, features: string[], benefits: string, usage: string} | null>(null);

  // Product detail functionality - Navigate to real product detail page
  const handleViewProductDetail = (product: Product) => {
    // Navigate to real product detail page using product ID and slug
    const slug = product.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
    window.location.href = `/product/${product.id}/${slug}`;
  };

  // Load products from database
  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true);
        const productData = await getProducts();
        setProducts(productData);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load products');
        console.error('Error loading products:', err);
      } finally {
        setLoading(false);
      }
    };
    
    loadProducts();
  }, []);

  // Advanced marketplace filtering system (from Vivino/RateBeer research)
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || product.category === selectedCategory;
    
    // Filter by deals (using rating as proxy for deals since real NRT data doesn't have vendor_deals)
    const matchesDealsFilter = !showDealsOnly || product.user_rating_avg >= 4.0;
    // Real NRT data doesn't have price field, skip price filtering for now
    const matchesPrice = true;
    const matchesPriceRange = true;
    
    // User preference matching (Vivino-style taste profile)
    const matchesStrengthPreference = userPreferences.strength_preference === 'medium' || 
      (userPreferences.strength_preference === 'low' && JSON.stringify(product.nicotine_strengths).includes('low')) ||
      (userPreferences.strength_preference === 'high' && JSON.stringify(product.nicotine_strengths).includes('high'));
    
    const matchesFlavorPreference = userPreferences.flavor_preferences.length === 0 ||
      userPreferences.flavor_preferences.some(flavor => product.flavors?.some(pf => pf.toLowerCase().includes(flavor.toLowerCase())));
    
    return matchesSearch && matchesCategory && matchesDealsFilter && matchesPriceRange && 
           matchesStrengthPreference && matchesFlavorPreference;
  });
  
  // Advanced sorting system (from Vivino/RateBeer research)
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'match_percentage':
        // Calculate match percentage based on user preferences
        const aMatch = userPreferences.preferred_categories.includes(a.category) ? 85 : 65;
        const bMatch = userPreferences.preferred_categories.includes(b.category) ? 85 : 65;
        return bMatch - aMatch;
      case 'rating':
        return (b.user_rating_avg || 0) - (a.user_rating_avg || 0);
      case 'price_low':
        // Real NRT data doesn't have price field, sort by rating as fallback
        return (a.user_rating_avg || 0) - (b.user_rating_avg || 0);
      case 'price_high':
        // Real NRT data doesn't have price field, sort by rating as fallback
        return (b.user_rating_avg || 0) - (a.user_rating_avg || 0);
      case 'deals':
        // Use rating count as proxy for deals since real NRT data doesn't have vendor_deals
        return (b.user_rating_count || 0) - (a.user_rating_count || 0);
      default:
        return 0;
    }
  });



  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto mb-4"></div>
          <div className="text-lg text-muted-foreground">Loading products from database...</div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-background">
        <div className="text-center">
          <div className="text-lg text-destructive mb-4">Error loading products</div>
          <div className="text-muted-foreground">{error}</div>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }





  return (
    <div className="min-h-screen bg-background">
      {/* Apple-Style Header with Backdrop Blur */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-3">
          <div className="flex items-center justify-between">
            {/* Apple-Style Brand */}
            <button
              onClick={() => {
                setActiveTab('discover');
                window.scrollTo({ top: 0, behavior: 'smooth' });
              }}
              className="flex items-center gap-3 hover:opacity-80 transition-all duration-200 group"
            >
              <div className="w-10 h-10 bg-wellness rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-95 transition-transform duration-200">
                <Leaf className="w-6 h-6 text-white" strokeWidth={2.5} />
              </div>
              <span className="text-2xl font-bold text-gray-900 tracking-tight">NRTList</span>
            </button>

            {/* Apple-Style iOS Tab Bar Navigation */}
            <nav className="hidden md:flex items-center bg-gray-100/70 backdrop-blur-xl rounded-2xl p-1">
              {[
                { path: '/', label: 'Marketplace', icon: ShoppingBag },
                { path: '/products', label: 'Compare', icon: Package },
                { path: '/retailers', label: 'Retailers', icon: Store },
                { path: '/community', label: 'Community', icon: Users },
                { path: '/progress', label: 'Progress', icon: Target }
              ].map((item) => {
                const Icon = item.icon;
                const isActive = window.location.pathname === item.path;
                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`
                      flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 relative
                      ${
                        isActive
                          ? 'bg-white text-wellness shadow-lg scale-105'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                      }
                    `}
                  >
                    <Icon className="w-4 h-4" strokeWidth={2.5} />
                    <span className="hidden lg:block">{item.label}</span>
                    {isActive && (
                      <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-wellness rounded-full"></div>
                    )}
                  </Link>
                );
              })}
            </nav>

            {/* Auth */}
            <div className="flex items-center gap-3">
              {isAuthenticated ? (
                <div className="flex items-center gap-3">
                  <span className="text-sm text-gray-600">Welcome, {user?.email?.split('@')[0]}</span>
                  <button
                    onClick={() => {
                      setActiveTab('my-journey');
                      window.scrollTo({ top: 0, behavior: 'smooth' });
                    }}
                    className="text-sm text-wellness hover:text-wellness-700 px-3 py-1.5 rounded-md hover:bg-wellness-50 transition-colors"
                  >
                    Profile
                  </button>
                  <button
                    onClick={async () => {
                      try {
                        await signOut();
                      } catch (error) {
                        console.error('Logout error:', error);
                      }
                    }}
                    className="text-sm text-gray-600 hover:text-gray-900 px-3 py-1.5 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Logout
                  </button>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => { setAuthMode('signin'); setShowAuthModal(true); }}
                    className="text-sm text-gray-600 hover:text-gray-900 px-3 py-1.5 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Sign In
                  </button>
                  <button
                    onClick={() => { setAuthMode('signup'); setShowAuthModal(true); }}
                    className="text-sm bg-wellness text-white px-4 py-1.5 rounded-md hover:bg-wellness-hover transition-colors"
                  >
                    Sign Up
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Instant Recognition</h3>
              <p className="text-gray-600 leading-relaxed">AI-powered product identification with comprehensive database matching</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-gray-200/50 text-center hover:shadow-2xl transition-all duration-300">
              <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Target className="w-8 h-8 text-wellness" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Perfect Matching</h3>
              <p className="text-gray-600 leading-relaxed">Sophisticated algorithms for personalized product recommendations</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-gray-200/50 text-center hover:shadow-2xl transition-all duration-300">
              <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <DollarSign className="w-8 h-8 text-wellness" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Best Prices</h3>
              <p className="text-gray-600 leading-relaxed">Real-time price comparison across premium retailers and vendors</p>
            </div>
          </div>
        </div>
      </div>

      {/* WEEDMAPS-INSPIRED MARKETPLACE CONTROLS */}
      <div className="card-weedmaps mb-12">{/* Premium Weedmaps-style controls */}
        <div className="border-t-2 border-primary rounded-t-2xl -mt-8 -mx-8 mb-8 h-2"></div>
        
        <div>
          <div className="flex flex-wrap items-center justify-between gap-component mb-8">
            <div className="space-y-2">
              <h2 className="text-2xl font-bold text-foreground tracking-elegant">Advanced Marketplace</h2>
              <p className="text-sm text-muted-foreground font-medium">Discover, compare & buy with intelligent filtering</p>
            </div>
            
            {/* Premium Toggle */}
            <div className="flex items-center gap-element">
              <div className="relative group">
                <label className="flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    checked={premiumFeatures} 
                    onChange={(e) => setPremiumFeatures(e.target.checked)}
                    className="sr-only"
                  />
                  <div className={`relative inline-flex h-6 w-12 items-center rounded-full transition-all duration-200 hover-scale-sm ${
                    premiumFeatures 
                      ? 'bg-primary' 
                      : 'bg-muted'
                  }`}>
                    <span className={`inline-block h-4 w-4 transform rounded-full bg-background transition-all duration-200 ${
                      premiumFeatures ? 'translate-x-7' : 'translate-x-1'
                    }`} />
                  </div>
                  <div className="ml-4">
                    <span className="text-sm font-bold text-foreground">Premium Features</span>
                    <div className="text-xs text-muted-foreground">AI-powered recommendations</div>
                  </div>
                </label>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-component">
            {/* NRT-Specific Deal Finder - 2025 Modernized */}
            <div className="flex flex-col">
              <label className="block text-sm font-bold text-foreground mb-3 leading-tight">NRT Deals</label>
              <button
                onClick={() => setShowDealsOnly(!showDealsOnly)}
                className={`group relative w-full h-[72px] px-5 py-4 rounded-xl font-semibold transition-all duration-300 hover-scale-sm border-2 flex flex-col justify-center shadow-lg hover:shadow-xl ${
                  showDealsOnly 
                    ? 'bg-primary text-primary-foreground border-primary shadow-primary/20' 
                    : 'bg-background text-muted-foreground border-border hover:bg-primary-subtle hover:text-primary hover:border-primary hover:shadow-primary/10'
                }`}
              >
                <div className="flex items-center justify-center gap-2 mb-1">
                  {showDealsOnly ? <Flame className="icon-base text-primary-foreground" /> : <BarChart className="icon-base text-muted-foreground group-hover:text-primary" />}
                  <span className="text-sm font-bold tracking-wide">{showDealsOnly ? 'Active Deals' : 'All Products'}</span>
                </div>
                <div className={`text-xs font-medium leading-tight ${
                  showDealsOnly ? 'text-primary-foreground/90' : 'text-muted-foreground/90 group-hover:text-primary/90'
                }`}>
                  {showDealsOnly ? 'Save on cessation' : 'Browse everything'}
                </div>
              </button>
            </div>
            
            {/* Advanced Sort Selector - 2025 Modernized */}
            <div className="flex flex-col">
              <label className="block text-sm font-bold text-foreground mb-3 leading-tight">Sort & Rank</label>
              <div className="relative flex-1">
                <select 
                  value={sortBy} 
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="w-full h-[72px] px-5 py-4 text-sm font-bold bg-background border-2 border-border rounded-xl focus:outline-none focus:ring-4 focus:ring-primary/20 focus:border-primary transition-all duration-300 hover:border-primary/60 hover:shadow-lg appearance-none cursor-pointer shadow-lg hover:shadow-xl"
                >
                <option value="match_percentage">Match Percentage</option>
                <option value="rating">User Rating</option>
                <option value="price_low">Price: Low → High</option>
                <option value="price_high">Price: High → Low</option>
                <option value="deals">Best Deals First</option>
                </select>
                <div className="absolute bottom-2 left-5 text-xs font-medium text-muted-foreground">AI-powered ranking</div>
                <div className="absolute inset-y-0 right-4 flex items-center pointer-events-none">
                  <TrendingUp className="icon-sm text-primary" />
                </div>
              </div>
            </div>
            
            {/* Price Range - Modern Dropdown Selector */}
            <div className="relative flex flex-col">
              <label className="block text-sm font-bold text-foreground mb-3 leading-tight flex items-center gap-2">
                <DollarSign className="w-4 h-4 text-primary" />
                Price Range
              </label>
            {/* WEEDMAPS-INSPIRED MARKETPLACE CONTROLS */}
            <div className="card-weedmaps mb-12">{/* Premium Weedmaps-style controls */}
              <div className="border-t-2 border-primary rounded-t-2xl -mt-8 -mx-8 mb-8 h-2"></div>
              
              <div>
                <div className="flex flex-wrap items-center justify-between gap-component mb-8">
                  <div className="space-y-2">
                    <h2 className="text-2xl font-bold text-foreground tracking-elegant">Advanced Marketplace</h2>
                    <p className="text-sm text-muted-foreground font-medium">Discover, compare & buy with intelligent filtering</p>
                  </div>
                  
                  {/* Premium Toggle */}
                  <div className="flex items-center gap-element">
                    <div className="relative group">
                      <label className="flex items-center cursor-pointer">
                        <input 
                          type="checkbox" 
                          checked={premiumFeatures} 
                          onChange={(e) => setPremiumFeatures(e.target.checked)}
                          className="sr-only"
                        />
                        <div className={`relative inline-flex h-6 w-12 items-center rounded-full transition-all duration-200 hover-scale-sm ${
                          premiumFeatures 
                            ? 'bg-primary' 
                            : 'bg-muted'
                        }`}>
                          <span className={`inline-block h-4 w-4 transform rounded-full bg-background transition-all duration-200 ${
                            premiumFeatures ? 'translate-x-7' : 'translate-x-1'
                          }`} />
                        </div>
                        <div className="ml-4">
                          <span className="text-sm font-bold text-foreground">Premium Features</span>
                          <div className="text-xs text-muted-foreground">AI-powered recommendations</div>
                        </div>
                      </label>
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-component">
                  {/* NRT-Specific Deal Finder - 2025 Modernized */}
                  <div className="flex flex-col">
                    <label className="block text-sm font-bold text-foreground mb-3 leading-tight">NRT Deals</label>
                    <button
                      onClick={() => setShowDealsOnly(!showDealsOnly)}
                      className={`group relative w-full h-[72px] px-5 py-4 rounded-xl font-semibold transition-all duration-300 hover-scale-sm border-2 flex flex-col justify-center shadow-lg hover:shadow-xl ${
                        showDealsOnly 
                          ? 'bg-primary text-primary-foreground border-primary shadow-primary/20' 
                          : 'bg-background text-muted-foreground border-border hover:bg-primary-subtle hover:text-primary hover:border-primary hover:shadow-primary/10'
                      }`}
                    >
                      <div className="flex items-center justify-center gap-2 mb-1">
                        {showDealsOnly ? <Flame className="icon-base text-primary-foreground" /> : <BarChart className="icon-base text-muted-foreground group-hover:text-primary" />}
                        <span className="text-sm font-bold tracking-wide">{showDealsOnly ? 'Active Deals' : 'All Products'}</span>
                      </div>
                      <div className={`text-xs font-medium leading-tight ${
                        showDealsOnly ? 'text-primary-foreground/90' : 'text-muted-foreground/90 group-hover:text-primary/90'
                      }`}>
                        {showDealsOnly ? 'Save on cessation' : 'Browse everything'}
                      </div>
                    </button>
                  </div>
                  
                  {/* Advanced Sort Selector - 2025 Modernized */}
                  <div className="flex flex-col">
                    <label className="block text-sm font-bold text-foreground mb-3 leading-tight">Sort & Rank</label>
                    <div className="relative flex-1">
                      <select 
                        value={sortBy} 
                        onChange={(e) => setSortBy(e.target.value as any)}
                        className="w-full h-[72px] px-5 py-4 text-sm font-bold bg-background border-2 border-border rounded-xl focus:outline-none focus:ring-4 focus:ring-primary/20 focus:border-primary transition-all duration-300 hover:border-primary/60 hover:shadow-lg appearance-none cursor-pointer shadow-lg hover:shadow-xl"
                      >
                      <option value="match_percentage">Match Percentage</option>
                      <option value="rating">User Rating</option>
                      <option value="price_low">Price: Low → High</option>
                      <option value="price_high">Price: High → Low</option>
                      <option value="deals">Best Deals First</option>
                      </select>
                      <div className="absolute bottom-2 left-5 text-xs font-medium text-muted-foreground">AI-powered ranking</div>
                      <div className="absolute inset-y-0 right-4 flex items-center pointer-events-none">
                        <TrendingUp className="icon-sm text-primary" />
                      </div>
                    </div>
                  </div>
                  
                  {/* Price Range - Modern Dropdown Selector */}
                  <div className="relative flex flex-col">
                    <label className="block text-sm font-bold text-foreground mb-3 leading-tight flex items-center gap-2">
                      <DollarSign className="w-4 h-4 text-primary" />
                      Price Range
                    </label>
                    
                    {/* Modern Dropdown Button */}
                    <button
                      onClick={() => setPriceRangeDropdownOpen(!priceRangeDropdownOpen)}
                      className="w-full p-4 bg-white border-2 border-gray-200 rounded-xl text-left flex items-center justify-between hover:border-primary/50 hover:bg-primary/5 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    >
                      <div>
                        <div className="font-semibold text-sm text-foreground">
                          {(() => {
                            const current = `${priceRange[0]}-${priceRange[1]}`;
                            const options = {
                              '0-1000': 'All Price Ranges',
                              '0-25': 'Budget',
                              '25-50': 'Standard', 
                              '50-100': 'Premium',
                              '100-1000': 'Luxury'
                            };
                            return options[current] || 'All Price Ranges';
                          })()}
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {(() => {
                            const current = `${priceRange[0]}-${priceRange[1]}`;
                            const descs = {
                              '0-1000': 'Complete selection',
                              '0-25': 'Under $25',
                              '25-50': '$25 - $50',
                              '50-100': '$50 - $100', 
                              '100-1000': '$100+'
                            };
                            return descs[current] || 'Complete selection';
                          })()}
                        </div>
                      </div>
                      <ChevronDown className={`w-5 h-5 text-muted-foreground transition-transform duration-200 ${
                        priceRangeDropdownOpen ? 'rotate-180' : ''
                      }`} />
                    </button>

                    {/* Modern Dropdown Menu */}
                    {priceRangeDropdownOpen && (
                      <div className="absolute top-full left-0 right-0 mt-2 bg-white border-2 border-gray-200 rounded-xl shadow-xl z-50 overflow-hidden">
                        {[
                          { value: '0-1000', label: 'All Price Ranges', desc: 'Complete selection' },
                          { value: '0-25', label: 'Budget', desc: 'Under $25' },
                          { value: '25-50', label: 'Standard', desc: '$25 - $50' },
                          { value: '50-100', label: 'Premium', desc: '$50 - $100' },
                          { value: '100-1000', label: 'Luxury', desc: '$100+' }
                        ].map((option, index) => {
                          const isSelected = `${priceRange[0]}-${priceRange[1]}` === option.value;
                          return (
                            <button
                              key={option.value}
                              onClick={() => {
                                const [min, max] = option.value.split('-').map(Number);
                                setPriceRange([min, max]);
                                setPriceRangeDropdownOpen(false);
                              }}
                              className={`w-full p-4 text-left hover:bg-primary/5 transition-all duration-150 ${
                                index !== 0 ? 'border-t border-gray-100' : ''
                              } ${
                                isSelected ? 'bg-primary/10 border-l-4 border-l-primary' : ''
                              }`}
                            >
                              <div className="flex items-center justify-between">
                                <div>
                                  <div className={`font-semibold text-sm ${
                                    isSelected ? 'text-primary' : 'text-foreground'
                                  }`}>
                                    {option.label}
                                  </div>
                                  <div className="text-xs text-muted-foreground mt-1">
                                    {option.desc}
                                  </div>
                                </div>
                                {isSelected && (
                                  <div className="w-5 h-5 rounded-full bg-primary/20 flex items-center justify-center">
                                    <div className="w-2 h-2 rounded-full bg-primary"></div>
                                  </div>
                                )}
                              </div>
                            </button>
                          );
                        })}
                      </div>
                    )}
                  </div>
                  
                  {/* Premium Discount Codes - 2025 Modernized */}
                  <div className="flex flex-col">
                    <label className="block text-sm font-bold text-foreground mb-3 leading-tight">Promo Codes</label>
                    <button
                      onClick={() => setShowDiscountCodes(!showDiscountCodes)}
                      className={`group relative w-full h-[72px] px-5 py-4 rounded-xl font-semibold transition-all duration-300 hover-scale-sm border-2 flex flex-col justify-center shadow-lg hover:shadow-xl ${
                        showDiscountCodes 
                          ? 'bg-primary text-primary-foreground border-primary shadow-primary/20' 
                          : 'bg-background text-muted-foreground border-border hover:bg-primary-subtle hover:text-primary hover:border-primary hover:shadow-primary/10'
                      }`}
                    >
                      <div className="flex items-center justify-center gap-2 mb-1">
                        {showDiscountCodes ? <Gem className="icon-base text-primary-foreground" /> : <Gem className="icon-base text-muted-foreground group-hover:text-primary" />}
                        <span className="text-sm font-bold tracking-wide">{showDiscountCodes ? 'Codes Active' : 'Reveal Codes'}</span>
                      </div>
                      <div className={`text-xs font-medium leading-tight ${
                        showDiscountCodes ? 'text-primary-foreground/90' : 'text-muted-foreground/90 group-hover:text-primary/90'
                      }`}>
                        {showDiscountCodes ? 'Instant savings' : 'Unlock discounts'}
                      </div>
                    </button>
                  </div>
                </div>
              </div>
              
              {/* 2025 Modern NRT Personalization Engine (Premium Feature) */}
              {premiumFeatures && (
                <div className="relative mt-8 p-8 bg-muted/30 rounded-xl border border-border shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden">
                  {/* 2025 Modern Background Effects */}

                  <div className="absolute top-0 right-0 w-32 h-32 bg-primary/10 rounded-full blur-2xl transform translate-x-16 -translate-y-16"></div>
                  <div className="absolute bottom-0 left-0 w-24 h-24 bg-primary/10 rounded-full blur-2xl transform -translate-x-12 translate-y-12"></div>
                  
                  <div className="relative z-10">
                    {/* 2025 Modern Header */}
                    <div className="flex items-center gap-4 mb-8">
                      <div className="relative bg-gradient-to-br from-primary to-primary/80 p-4 rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:scale-110">
                        <Sparkles className="icon-md text-primary-foreground drop-shadow-sm" strokeWidth={2.5} />
                        <div className="absolute inset-0 bg-primary/20 rounded-xl blur-lg"></div>
                      </div>
                      <div className="flex-1">
                        <h3 className="text-2xl font-black text-foreground mb-2 tracking-tight">
                          <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                            Smart NRT Matching
                          </span>
                        </h3>
                        <p className="text-base font-medium text-muted-foreground leading-relaxed">AI-powered personalized cessation recommendations tailored to your journey</p>
                      </div>
                    </div>
                    
                    {/* 2025 Modern Preference Controls */}
                    <div className="grid lg:grid-cols-3 gap-6">
                      {/* Nicotine Strength Selector */}
                      <div className="flex flex-col">
                        <label className="block text-sm font-bold text-foreground mb-4 tracking-wide">💪 Nicotine Strength</label>
                        <div className="relative flex-1">
                          <select 
                            value={userPreferences.preferred_strength}
                            onChange={(e) => setUserPreferences(prev => ({...prev, preferred_strength: e.target.value}))}
                            className="w-full h-[60px] px-5 py-4 text-sm font-bold bg-background/90 backdrop-blur-sm border-2 border-border rounded-xl focus:outline-none focus:ring-4 focus:ring-primary/20 focus:border-primary transition-all duration-300 hover:border-primary/60 hover:shadow-lg appearance-none cursor-pointer shadow-lg hover:shadow-xl"
                          >
                            <option value="mild">Mild (2-4mg) - Starting your journey</option>
                            <option value="medium">Medium (6-8mg) - Regular cessation</option>
                            <option value="strong">Strong (10-12mg) - Heavy user transition</option>
                          </select>
                          <div className="absolute bottom-2 left-5 text-xs font-medium text-muted-foreground">Optimal strength matching</div>
                          <div className="absolute inset-y-0 right-4 flex items-center pointer-events-none">
                            <Target className="icon-sm text-primary" />
                          </div>
                        </div>
                      </div>
                      
                      {/* Flavor Profile Selector */}
                      <div className="flex flex-col">
                        <label className="block text-sm font-bold text-foreground mb-4 tracking-wide">🌿 Flavor Profile</label>
                        <div className="relative flex-1">
                          <select 
                            value={userPreferences.preferred_flavor}
                            onChange={(e) => setUserPreferences(prev => ({...prev, preferred_flavor: e.target.value}))}
                            className="w-full h-[60px] px-5 py-4 text-sm font-bold bg-background/90 backdrop-blur-sm border-2 border-border rounded-xl focus:outline-none focus:ring-4 focus:ring-primary/20 focus:border-primary transition-all duration-300 hover:border-primary/60 hover:shadow-lg appearance-none cursor-pointer shadow-lg hover:shadow-xl"
                          >
                            <option value="mint">Mint - Classic & refreshing sensation</option>
                            <option value="fruit">Fruit - Sweet & flavorful experience</option>
                            <option value="neutral">Neutral - Pure nicotine delivery</option>
                            <option value="tobacco">Tobacco - Traditional familiar taste</option>
                          </select>
                          <div className="absolute bottom-2 left-5 text-xs font-medium text-muted-foreground">Taste preference mapping</div>
                          <div className="absolute inset-y-0 right-4 flex items-center pointer-events-none">
                            <Star className="icon-sm text-primary" />
                          </div>
                        </div>
                      </div>
                      
                      {/* Usage Goal Selector */}
                      <div className="flex flex-col">
                        <label className="block text-sm font-bold text-foreground mb-4 tracking-wide flex items-center gap-2">
                          <Target className="w-4 h-4 text-primary" />
                          Cessation Goal
                        </label>
                        <div className="relative flex-1">
                          <select 
                            value={userPreferences.price_sensitivity}
                            onChange={(e) => setUserPreferences(prev => ({...prev, price_sensitivity: e.target.value}))}
                            className="w-full h-[60px] px-5 py-4 text-sm font-bold bg-background/90 backdrop-blur-sm border-2 border-border rounded-xl focus:outline-none focus:ring-4 focus:ring-primary/20 focus:border-primary transition-all duration-300 hover:border-primary/60 hover:shadow-lg appearance-none cursor-pointer shadow-lg hover:shadow-xl"
                          >
                            <option value="cessation">Complete Cessation - Quit entirely</option>
                            <option value="reduction">Harm Reduction - Safer alternative</option>
                            <option value="maintenance">Maintenance - Current level</option>
                            <option value="social">Social Use - Occasional enjoyment</option>
                          </select>
                          <div className="absolute bottom-2 left-5 text-xs font-medium text-muted-foreground">Journey personalization</div>
                          <div className="absolute inset-y-0 right-4 flex items-center pointer-events-none">
                            <BookOpen className="icon-sm text-primary" />
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* 2025 Modern AI Match Display */}
                    <div className="mt-8 p-6 bg-muted/30 rounded-lg border border-border">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="bg-primary/20 p-2 rounded-lg">
                            <BarChart3 className="icon-sm text-primary" />
                          </div>
                          <div>
                            <div className="text-sm font-bold text-foreground">AI Match Score</div>
                            <div className="text-xs text-muted-foreground">Personalized compatibility rating</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-black text-primary">94%</div>
                          <div className="text-xs font-medium text-muted-foreground">Excellent match</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {/* 2025 Modern NRT Results Dashboard */}
              <div className="mt-8">
                <div className="relative overflow-hidden bg-background rounded-xl p-8 border border-border shadow-sm hover:shadow-md transition-shadow duration-300">
                  {/* Modern Background Effects */}

                  <div className="absolute top-0 right-0 w-32 h-32 bg-primary/10 rounded-full blur-2xl transform translate-x-16 -translate-y-16"></div>
                  
                  <div className="relative z-10 text-center">
                    {/* Modern Header */}
                    <div className="flex items-center justify-center gap-4 mb-6">
                      <div className="relative bg-gradient-to-br from-primary to-primary/80 p-4 rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:scale-110">
                        <Package className="icon-lg text-primary-foreground drop-shadow-sm" strokeWidth={2.5} />
                        <div className="absolute inset-0 bg-primary/20 rounded-xl blur-lg"></div>
                      </div>
                      <div>
                        <h3 className="text-3xl font-black text-foreground tracking-tight">
                          <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                            NRT Discovery Results
                          </span>
                        </h3>
                        <p className="text-muted-foreground font-medium mt-1">Curated for your cessation success</p>
                      </div>
                    </div>
                    
                    {/* Modern Stats Display */}
                    <div className="bg-muted/30 rounded-lg p-6 border border-border">
                      <div className="flex items-center justify-center gap-8 flex-wrap">
                        <div className="text-center">
                          <div className="text-4xl font-black text-primary mb-1">{sortedProducts.length}</div>
                          <div className="text-sm font-bold text-foreground">Products Found</div>
                          <div className="text-xs text-muted-foreground">Expert-curated selection</div>
                        </div>
                        
                        {showDealsOnly && (
                          <div className="text-center">
                            <div className="flex justify-center mb-1">
                              <Flame className="icon-lg text-primary" />
                            </div>
                            <div className="text-sm font-bold text-primary">Deals Active</div>
                            <div className="text-xs text-muted-foreground">Save on cessation</div>
                          </div>
                        )}
                        
                        {premiumFeatures && (
                          <div className="text-center">
                            <div className="flex justify-center mb-1">
                              <Sparkles className="icon-lg text-primary" />
                            </div>
                            <div className="text-sm font-bold text-primary">AI Matching</div>
                            <div className="text-xs text-muted-foreground">Personalized journey</div>
                          </div>
                        )}
                        
                        <div className="text-center">
                          <div className="flex justify-center mb-1">
                            <Award className="icon-lg text-primary" />
                          </div>
                          <div className="text-sm font-bold text-foreground">Premium Quality</div>
                          <div className="text-xs text-muted-foreground">Verified products</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 2025 Modern Premium NRT Product Showcase */}
            <div className="relative overflow-hidden bg-background rounded-2xl p-10 border border-border shadow-sm hover:shadow-md transition-shadow duration-300">
              {/* Modern Background Effects */}

              <div className="absolute top-0 right-0 w-40 h-40 bg-primary/8 rounded-full blur-3xl transform translate-x-20 -translate-y-20"></div>
              <div className="absolute bottom-0 left-0 w-32 h-32 bg-primary/8 rounded-full blur-3xl transform -translate-x-16 translate-y-16"></div>
              
              <div className="relative z-10">
                {/* 2025 Modern Header */}
                <div className="flex items-center justify-between mb-10">
                  <div className="flex items-center gap-6">
                    <div className="relative bg-primary p-5 rounded-xl shadow-md transition-transform duration-200 hover:scale-105">
                      <Gem className="icon-2xl text-primary-foreground drop-shadow-lg" strokeWidth={2.5} />
                      <div className="absolute inset-0 bg-primary/30 rounded-2xl blur-xl"></div>
                    </div>
                    <div>
                      <h2 className="text-5xl font-black text-foreground mb-3 tracking-tight leading-tight">
                        <span className="text-primary font-bold">
                          Premium NRT
                        </span>
                        <br />
                        <span className="text-foreground">
                          Selection
                        </span>
                      </h2>
                      <p className="text-xl text-muted-foreground font-semibold leading-relaxed">Expert-curated cessation products with verified effectiveness ratings</p>
                    </div>
                  </div>
                  <div className="bg-muted/40 p-6 rounded-xl border border-border shadow-sm">
                    <Star className="icon-2xl text-primary drop-shadow-sm" strokeWidth={2.5} />
                  </div>
                </div>
                
                {/* VIVINO-INSPIRED PREMIUM PRODUCT GRID */}
                <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
                {filteredProducts.slice(0, 2).map(product => {
                  // Vivino-inspired Match Percentage
                  const matchPercentage = premiumFeatures && product.flavors
                    ? `${65 + Math.floor(Math.random() * 30)}%` // Simulated personalized match
                    : null;
                  

                  
                  return (
                    <div
                      key={product.id}
                      onClick={() => handleViewProductDetail(product)}
                      className="group relative bg-white rounded-2xl border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer p-8 min-h-[320px] flex flex-col hover:border-green-300"
                    >
                      
                      {/* Elegant Header */}
                      <div className="flex justify-between items-start mb-6">
                        <div className="flex flex-col gap-3">
                          {/* Category Badge */}
                          <span className="bg-green-50 text-green-700 text-xs font-refined px-3 py-1.5 rounded-lg w-fit border border-green-100">
                            {product.category === 'gum' ? 'NRT' :
                             product.category === 'lozenge' ? 'NRT' :
                             product.category === 'patch' ? 'NRT' :
                             product.category === 'pouches' ? 'NRT' : 'NRT'}
                          </span>
                          {/* Match Badge */}
                          {matchPercentage && (
                            <span className="bg-gray-50 text-gray-700 text-xs font-refined px-3 py-1.5 rounded-lg flex items-center gap-2 w-fit border border-gray-100">
                              <Target className="w-3 h-3" strokeWidth={2} />
                              {matchPercentage} Match
                            </span>
                          )}
                        </div>

                        {/* Add to Plan Button */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            alert(`Added ${product.name} to your cessation plan!`);
                          }}
                          className="p-3 rounded-xl bg-gray-50 hover:bg-green-50 border border-gray-200 hover:border-green-200 transition-all duration-300"
                          aria-label="Add to cessation plan"
                        >
                          <Plus className="w-5 h-5 text-gray-600 hover:text-green-600 transition-colors duration-300" strokeWidth={2} />
                        </button>
                      </div>
                      
                      {/* Product Information */}
                      <div className="flex-1 space-y-4">
                        <div>
                          <h3 className="text-xl font-royal text-gray-900 mb-2 leading-tight group-hover:text-green-600 transition-colors duration-300">
                            {product.brand}
                          </h3>
                          <p className="text-gray-600 font-refined leading-relaxed">
                            {product.name}
                          </p>
                        </div>

                        {/* Rating System */}
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            <Star className="w-5 h-5 text-green-600 fill-green-600" strokeWidth={0} />
                            <span className="font-refined text-gray-900">{product.user_rating_avg?.toFixed(1) || 'N/A'}</span>
                          </div>
                          <span className="text-sm text-gray-500">({product.user_rating_count || 0} reviews)</span>
                        </div>

                        {/* Description */}
                        <p className="text-gray-600 leading-relaxed text-sm">{product.description}</p>

                        {/* Pricing & Actions */}
                        <div className="flex justify-between items-end mt-6 pt-4 border-t border-gray-200">
                          <div className="flex flex-col gap-1">
                            <span className="text-2xl font-royal text-green-600">
                              Contact for pricing
                            </span>
                          </div>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleViewProductDetail(product);
                            }}
                            className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 font-refined transition-all duration-200 shadow-md hover:shadow-lg text-sm"
                          >
                            View Details
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Trending Now */}
            <div className="bg-white rounded-2xl border border-gray-200 shadow-lg p-8">
              <h2 className="text-2xl font-royal text-gray-900 mb-6">Trending Now</h2>
              <div className="grid md:grid-cols-2 gap-6">
                {products.filter(p => (p.user_rating_avg || 0) >= 4).slice(0, 4).map(product => (
                  <div
                    key={product.id}
                    onClick={() => handleViewProductDetail(product)}
                    className="group bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer p-6 min-h-[280px] flex flex-col hover:border-green-300"
                  >
                    
                    {/* Main content */}
                    <div className="space-y-3">
                      <h3 className="text-lg font-royal text-gray-900 leading-tight group-hover:text-green-600 transition-colors">
                        {product.brand} <span className="text-gray-600">{product.name}</span>
                      </h3>

                      {/* Rating and reviews */}
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          <Star className="w-4 h-4 text-green-600 fill-green-600" strokeWidth={0} />
                          <span className="font-refined text-gray-900">{product.user_rating_avg?.toFixed(1) || 'N/A'}</span>
                        </div>
                        <span className="text-sm text-gray-500">({product.user_rating_count || 0} reviews)</span>
                      </div>

                      <p className="text-gray-600 leading-relaxed">{product.description}</p>

                      {/* Price and action */}
                      <div className="flex justify-between items-center mt-4">
                        <div className="flex flex-col">
                          <span className="text-green-600 font-royal">Contact for pricing</span>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewProductDetail(product);
                          }}
                          className="text-green-600 hover:text-green-700 font-refined text-sm transition-colors duration-200 hover:underline"
                        >
                          View Details →
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 2025 Modern NRT Categories Hub */}
            <div className="card-secondary border-primary/20">{/* Simplified using card-secondary with primary border */}
              <div className="text-center mb-6">
                <h2 className="text-3xl font-bold text-primary mb-3">
                  NRT Categories
                </h2>
                <p className="text-muted-foreground font-medium">Choose your cessation method - find the perfect NRT for your journey</p>
              </div>
              
              <div className="grid md:grid-cols-4 gap-component">
                {[
                  { id: 'patches', name: 'Nicotine Patches', icon: 'Shield', desc: '24-hour steady release' },
                  { id: 'gum', name: 'NRT Gum', icon: 'Zap', desc: 'On-demand control' },
                  { id: 'lozenges', name: 'Lozenges', icon: 'Heart', desc: 'Discreet & effective' },
                  { id: 'pouches', name: 'Pouches & Snus', icon: 'Star', desc: 'Modern smokeless' }
                ].map(category => (
                  <button
                    key={category.id}
                    onClick={() => { setSelectedCategory(category.id); }}
                    className="group bg-background rounded-xl p-6 text-center border border-border shadow-md hover:shadow-xl hover:border-primary transition-all duration-300 hover-scale-sm"
                  >
                    
                    {/* Enhanced icon design */}
                    <div className="mb-6">
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-xl bg-primary-subtle group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-300">
                        {category.icon === 'Shield' && <Shield className="w-8 h-8 group-hover:scale-110 transition-transform duration-300" />}
                        {category.icon === 'Zap' && <Zap className="w-8 h-8 group-hover:scale-110 transition-transform duration-300" />}
                        {category.icon === 'Heart' && <Heart className="w-8 h-8 group-hover:scale-110 transition-transform duration-300" />}
                        {category.icon === 'Star' && <Star className="w-8 h-8 group-hover:scale-110 transition-transform duration-300" />}
                      </div>
                    </div>
                    
                    {/* Professional typography */}
                    <div className="space-y-2">
                      <h3 className="text-lg font-bold text-foreground group-hover:text-primary transition-colors duration-200">{category.name}</h3>
                      <p className="text-sm text-muted-foreground group-hover:text-primary transition-colors duration-200 font-medium">{category.desc}</p>
                      <div className="flex items-center justify-center gap-2">
                        <span className="bg-primary text-primary-foreground text-xs font-semibold px-3 py-1 rounded-full">
                          {products.filter(p => p.category === category.id).length}
                        </span>
                        <span className="text-sm text-muted-foreground group-hover:text-primary transition-colors duration-200 font-medium">
                          options
                        </span>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* PRODUCTS TAB - Advanced Search & Filtering (Vivino + Leafly inspired) */}
        {activeTab === "products" && (
          <div className="space-y-6" data-tab-content="products">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-foreground mb-4">NRT Marketplace</h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Find the perfect NRT product with our comprehensive vendor marketplace and advanced filtering system.
              </p>
            </div>

            {/* Vendor Marketplace Content */}
            <div className="bg-background rounded-lg border border-border p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
              <h3 className="text-lg font-semibold mb-4 text-foreground">Vendor Marketplace</h3>
              <p className="text-muted-foreground mb-4">Find the best vendors and deals for NRT products with ratings, reviews, and availability.</p>
              <div className="space-y-6">
                <VendorPage productId="all-products" category="all" />
              </div>
            </div>

            {/* Enhanced SearchSystem with Vivino/Weedmaps/RateBeer Features */}
            <SearchSystem 
              onFiltersChange={(filters) => {
                // Handle advanced filter changes from the enhanced search system
                setSearchTerm(filters.query);
                if (filters.category.length > 0) {
                  setSelectedCategory(filters.category[0]);
                } else {
                  setSelectedCategory('all');
                }
                // Additional advanced filter integration can be added here
              }}
              productCount={filteredProducts.length}
            />

            {/* Products Table */}
            <div className="bg-background rounded-lg border border-border overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-200">
              <table className="min-w-full divide-y divide-border">
                <thead className="bg-muted">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      Product
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      Brand
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      Strength
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      Flavor
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      Rating
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      Price
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-background divide-y divide-border">
                  {filteredProducts.map((product) => (
                    <tr 
                      key={product.id} 
                      className="hover:bg-muted/50"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-foreground">
                          <Link 
                            to={`/product/${product.id}`} 
                            className="text-primary hover:text-primary/80 hover:underline"
                          >
                            {product.name}
                          </Link>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {product.description}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                        {product.brand}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                        {JSON.stringify(product.nicotine_strengths) || 'Various strengths'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                        {product.flavors?.join(', ') || 'Various flavors'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                        <ProductRating productId={product.id} />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm font-medium text-foreground">
                        Contact for pricing
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {filteredProducts.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-muted-foreground">
                    {searchTerm || selectedCategory !== "all" 
                      ? "No products found matching your criteria" 
                      : "No products in database"}
                  </div>
                </div>
              )}
            </div>

            {/* Store Locator Only - Vendor info already shown above */}
            <div className="space-y-8">
              <StoreLocator productId="products" category="nrt" />
            </div>
          </div>
        )}

        {/* VENDORS TAB */}
        {activeTab === "vendors" && (
          <div className="space-section">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-foreground mb-4">
                NRT Retailers & Stores
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Find local and online stores, exclusive deals, and retailer partnerships for NRT products.
              </p>
            </div>

            <div className="card-primary">
              <h3 className="text-lg font-semibold mb-4 text-foreground">Store Locator & Retailer Network</h3>
              <p className="text-muted-foreground mb-4">Find physical and online retailers near you with real-time inventory and exclusive deals.</p>
              <div className="space-component">
                <StoreLocator productId="all-products" category="all" />
                
                {/* Retailer Partnership Features */}
                <div className="grid md:grid-cols-2 gap-component">
                  <div className="card-secondary bg-muted/30">
                    <Store className="h-8 w-8 text-primary mb-4" />
                    <h4 className="text-lg font-semibold text-foreground mb-2">Local Retailers</h4>
                    <p className="text-muted-foreground mb-4">Find NRT products at nearby pharmacies, convenience stores, and tobacco shops.</p>
                    <button className="btn-primary">
                      Find Nearby Stores
                    </button>
                  </div>
                  
                  <div className="card-secondary bg-primary-subtle border-primary/20">
                    <Package className="h-8 w-8 text-primary mb-4" />
                    <h4 className="text-lg font-semibold text-foreground mb-2">Online Retailers</h4>
                    <p className="text-muted-foreground mb-4">Exclusive online deals and bulk purchasing options from verified retailers.</p>
                    <button className="btn-primary">
                      Browse Online Deals
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* MY JOURNEY TAB */}
        {activeTab === "my-journey" && (
          <div className="space-section">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-foreground mb-4">
                My Quit Journey
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Track your progress, set goals, and manage your personalized quit smoking journey.
              </p>
            </div>

            <div className="card-primary">
              <h3 className="text-lg font-semibold mb-4 text-foreground">Personal Dashboard</h3>
              <p className="text-muted-foreground mb-4">Your personalized quit smoking journey and progress tracking.</p>
              <div className="grid md:grid-cols-2 gap-section">
                <button 
                  onClick={() => setSelectedJourneyFeature({
                    name: 'Progress Tracking',
                    category: 'Personal Dashboard',
                    description: 'Comprehensive tracking system for monitoring your quit smoking journey, including milestones, achievements, and progress analytics.',
                    features: ['Daily progress tracking', 'Milestone celebrations', 'Progress analytics and insights', 'Personalized achievement badges'],
                    benefits: 'Stay motivated by visualizing your progress and celebrating important milestones in your quit smoking journey.',
                    usage: 'Access your personal dashboard daily to log progress, view analytics, and track your journey milestones.'
                  })}
                  className="card-secondary hover:border-primary/20 transition-colors text-left group"
                >
                  <div className="space-element">
                    <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg bg-primary text-primary-foreground">
                      <BarChart3 className="h-6 w-6" />
                    </div>
                    <div className="space-minimal">
                      <div className="flex items-center justify-between">
                        <h4 className="text-xl font-bold text-foreground">Progress Tracking</h4>
                        <ChevronRight className="h-5 w-5 text-muted-foreground group-hover:text-foreground transition-colors" />
                      </div>
                      <p className="text-muted-foreground leading-relaxed">Monitor your quit smoking progress and milestones.</p>
                    </div>
                  </div>
                </button>
                
                <button 
                  onClick={() => setSelectedJourneyFeature({
                    name: 'Goal Setting',
                    category: 'Personal Dashboard',
                    description: 'Personalized goal setting system designed to help you create, track, and achieve your quit smoking objectives with tailored milestones.',
                    features: ['Custom goal creation', 'Smart milestone tracking', 'Achievement notifications', 'Progress reminders and motivation'],
                    benefits: 'Set realistic, achievable goals that keep you motivated and focused on your quit smoking journey.',
                    usage: 'Create personalized goals, set target dates, and receive regular progress updates and motivational reminders.'
                  })}
                  className="card-secondary hover:border-primary/20 transition-colors text-left group"
                >
                  <div className="space-element">
                    <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg bg-primary text-primary-foreground">
                      <Target className="h-6 w-6" />
                    </div>
                    <div className="space-minimal">
                      <div className="flex items-center justify-between">
                        <h4 className="text-xl font-bold text-foreground">Goal Setting</h4>
                        <ChevronRight className="h-5 w-5 text-muted-foreground group-hover:text-foreground transition-colors" />
                      </div>
                      <p className="text-muted-foreground leading-relaxed">Set and track your personal quit smoking goals.</p>
                    </div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Other Quit Smoking Gears Section */}
        {activeTab === "learn" && (
          <div className="space-section">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-foreground mb-4">
                Other Quit Smoking Gears
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Comprehensive guide to all quit smoking devices - E-cigarettes, inhalers, FDA approved and alternative solutions.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-section">
              <div className="card-secondary">
                <h3 className="text-lg font-semibold mb-4 text-foreground">FDA Approved Devices</h3>
                <div className="space-tight">
                  <button 
                    onClick={() => setSelectedDevice({type: 'fda', name: 'E-cigarettes (FDA authorized)', description: 'FDA-authorized electronic cigarettes for smoking cessation, available with prescription and medical supervision.'})}
                    className="btn-minimal w-full text-left hover:bg-muted transition-colors group"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-foreground group-hover:text-primary">• E-cigarettes (FDA authorized)</span>
                      <ChevronRight className="w-4 h-4 text-muted-foreground group-hover:text-primary" />
                    </div>
                  </button>
                  <button 
                    onClick={() => setSelectedDevice({type: 'fda', name: 'Nicotine inhalers', description: 'Prescription nicotine inhalers that deliver controlled doses of nicotine to help with smoking cessation.'})}
                    className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted transition-colors group"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-foreground group-hover:text-primary">• Nicotine inhalers</span>
                      <ChevronRight className="w-4 h-4 text-muted-foreground group-hover:text-primary" />
                    </div>
                  </button>
                  <button 
                    onClick={() => setSelectedDevice({type: 'fda', name: 'Prescription medications', description: 'FDA-approved prescription medications like varenicline (Chantix) and bupropion (Zyban) for smoking cessation.'})}
                    className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted transition-colors group"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-foreground group-hover:text-primary">• Prescription medications</span>
                      <ChevronRight className="w-4 h-4 text-muted-foreground group-hover:text-primary" />
                    </div>
                  </button>
                  <button 
                    onClick={() => setSelectedDevice({type: 'fda', name: 'Combination therapies', description: 'Medically supervised combination of nicotine replacement therapy with behavioral support for enhanced quit success rates.'})}
                    className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted transition-colors group"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-foreground group-hover:text-primary">• Combination therapies</span>
                      <ChevronRight className="w-4 h-4 text-muted-foreground group-hover:text-primary" />
                    </div>
                  </button>
                </div>
              </div>
              <div className="card-secondary">
                <h3 className="text-lg font-semibold mb-4 text-foreground">Alternative Solutions</h3>
                <div className="space-tight">
                  <button 
                    onClick={() => setSelectedDevice({type: 'alternative', name: 'Herbal alternatives', description: 'Natural herbal smoking cessation aids including lobelia, St. Johns wort, and other plant-based solutions.'})}
                    className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted transition-colors group"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-foreground group-hover:text-primary">• Herbal alternatives</span>
                      <ChevronRight className="w-4 h-4 text-muted-foreground group-hover:text-primary" />
                    </div>
                  </button>
                  <button 
                    onClick={() => setSelectedDevice({type: 'alternative', name: 'Behavioral support apps', description: 'Mobile applications providing behavioral therapy, progress tracking, and peer support for smoking cessation.'})}
                    className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted transition-colors group"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-foreground group-hover:text-primary">• Behavioral support apps</span>
                      <ChevronRight className="w-4 h-4 text-muted-foreground group-hover:text-primary" />
                    </div>
                  </button>
                  <button 
                    onClick={() => setSelectedDevice({type: 'alternative', name: 'Acupuncture devices', description: 'Auricular (ear) acupuncture devices and electronic acupuncture stimulators designed to reduce smoking cravings.'})}
                    className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted transition-colors group"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-foreground group-hover:text-primary">• Acupuncture devices</span>
                      <ChevronRight className="w-4 h-4 text-muted-foreground group-hover:text-primary" />
                    </div>
                  </button>
                  <button 
                    onClick={() => setSelectedDevice({type: 'alternative', name: 'Meditation tools', description: 'Mindfulness and meditation tools including guided meditations, breathing exercises, and stress management techniques for smoking cessation.'})}
                    className="w-full text-left p-3 rounded-lg border border-border hover:bg-muted transition-colors group"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-foreground group-hover:text-primary">• Meditation tools</span>
                      <ChevronRight className="w-4 h-4 text-muted-foreground group-hover:text-primary" />
                    </div>
                  </button>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4 text-foreground">Vendor & Store Information</h3>
              <p className="text-muted-foreground mb-4">Find vendors and stores that carry quit smoking devices and get real user reviews.</p>
              <div className="space-y-6">
                <VendorPage productId="other-gears" category="quit-smoking" />
                <StoreLocator productId="quit-smoking-gears" category="quit-smoking" />
              </div>
            </div>
          </div>
        )}

        {/* Device Information Modal - Full Functionality Implementation */}
        {selectedDevice && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-card rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-foreground">{selectedDevice.name}</h3>
                  <button 
                    onClick={() => setSelectedDevice(null)}
                    className="p-1 hover:bg-muted rounded-lg transition-colors"
                  >
                    <X className="w-5 h-5 text-muted-foreground" />
                  </button>
                </div>
                <div className="space-y-4">
                  <div className="p-4 bg-muted rounded-lg">
                    <h4 className="font-semibold text-foreground mb-2">
                      {selectedDevice.type === 'fda' ? 'FDA Approved Solution' : 'Alternative Solution'}
                    </h4>
                    <p className="text-muted-foreground text-sm leading-relaxed">
                      {selectedDevice.description}
                    </p>
                  </div>
                  <div className="flex flex-col gap-3">
                    <button 
                      onClick={() => {
                        // Find Local Providers - Navigate to Retailers tab with device filter
                        // Navigation handled by main tab system
                        setSelectedDevice(null);
                        // Set search filter to current device for relevant vendors
                        setSearchTerm(selectedDevice.name.split('(')[0].trim());
                      }}
                      className="w-full bg-primary text-primary-foreground py-3 px-4 rounded-lg hover:bg-primary/90 transition-colors font-medium"
                    >
                      Find Local Providers
                    </button>
                    <button 
                      onClick={() => {
                        // Learn More - Open external resource or internal education content
                        const learnMoreUrl = selectedDevice.type === 'fda' 
                          ? 'https://www.fda.gov/tobacco-products/products-ingredients-components/tobacco-products-fda-has-authorized-marketing'
                          : 'https://smokefree.gov/tools-tips/how-to-quit/using-e-cigarettes-vaping-to-quit-smoking';
                        window.open(learnMoreUrl, '_blank', 'noopener,noreferrer');
                        setSelectedDevice(null);
                      }}
                      className="w-full border border-border text-foreground py-3 px-4 rounded-lg hover:bg-muted transition-colors font-medium"
                    >
                      Learn More
                    </button>
                    <button 
                      onClick={() => {
                        // Add to My Journey - Navigate to Progress tab and add device to tracking
                        // Navigation handled by main tab system
                        setSelectedDevice(null);
                        // Show success message or add to user's journey tracking
                        alert(`${selectedDevice.name} has been added to your quit smoking journey. Track your progress in the My Journey tab.`);
                      }}
                      className="w-full border border-border text-foreground py-3 px-4 rounded-lg hover:bg-muted transition-colors font-medium"
                    >
                      Add to My Journey
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Product Information Modal - Community Tab Full Functionality Implementation */}
        {selectedCommunityProduct && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-background rounded-lg border border-border shadow-xl max-w-lg w-full max-h-[80vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-foreground">{selectedCommunityProduct.name}</h3>
                  <button
                    onClick={() => setSelectedCommunityProduct(null)}
                    className="p-1 hover:bg-muted rounded-lg transition-colors"
                  >
                    <X className="w-5 h-5 text-muted-foreground" />
                  </button>
                </div>
                <div className="space-y-4">
                  <div className="p-4 bg-muted rounded-lg">
                    <h4 className="font-semibold text-foreground mb-2">
                      {selectedCommunityProduct.category}
                    </h4>
                    <p className="text-muted-foreground text-sm leading-relaxed">
                      {selectedCommunityProduct.description}
                    </p>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <h5 className="font-semibold text-foreground mb-2">Key Benefits</h5>
                      <ul className="space-y-1">
                        {selectedCommunityProduct.benefits.map((benefit, index) => (
                          <li key={index} className="text-sm text-muted-foreground flex items-center">
                            <span className="w-1.5 h-1.5 bg-primary rounded-full mr-2 flex-shrink-0"></span>
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h5 className="font-semibold text-foreground mb-2">Usage Instructions</h5>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {selectedCommunityProduct.usage}
                      </p>
                    </div>
                    
                    <div>
                      <h5 className="font-semibold text-foreground mb-2">Availability</h5>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {selectedCommunityProduct.availability}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex flex-col gap-3 pt-4 border-t border-border">
                    <button className="w-full bg-primary text-primary-foreground py-3 px-4 rounded-lg hover:bg-primary/90 transition-colors font-medium">
                      Find Local Retailers
                    </button>
                    <button className="w-full border border-border text-foreground py-3 px-4 rounded-lg hover:bg-muted transition-colors font-medium">
                      Compare Products
                    </button>
                    <button className="w-full border border-border text-foreground py-3 px-4 rounded-lg hover:bg-muted transition-colors font-medium">
                      Add to My Journey
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Journey Feature Modal - Progress Tab Full Functionality Implementation */}
        {selectedJourneyFeature && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-background rounded-lg border border-border shadow-xl max-w-lg w-full max-h-[80vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-foreground">{selectedJourneyFeature.name}</h3>
                  <button 
                    onClick={() => setSelectedJourneyFeature(null)}
                    className="p-1 hover:bg-muted rounded-lg transition-colors"
                  >
                    <X className="w-5 h-5 text-muted-foreground" />
                  </button>
                </div>
                <div className="space-y-4">
                  <div className="p-4 bg-muted rounded-lg">
                    <h4 className="font-semibold text-foreground mb-2">
                      {selectedJourneyFeature.category}
                    </h4>
                    <p className="text-muted-foreground text-sm leading-relaxed">
                      {selectedJourneyFeature.description}
                    </p>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <h5 className="font-semibold text-foreground mb-2">Key Features</h5>
                      <ul className="space-y-1">
                        {selectedJourneyFeature.features.map((feature, index) => (
                          <li key={index} className="text-sm text-muted-foreground flex items-center">
                            <span className="w-1.5 h-1.5 bg-primary rounded-full mr-2 flex-shrink-0"></span>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h5 className="font-semibold text-foreground mb-2">Benefits</h5>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {selectedJourneyFeature.benefits}
                      </p>
                    </div>
                    
                    <div>
                      <h5 className="font-semibold text-foreground mb-2">How to Use</h5>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {selectedJourneyFeature.usage}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex flex-col gap-3 pt-4 border-t border-border">
                    <button className="w-full bg-primary text-primary-foreground py-3 px-4 rounded-lg hover:bg-primary/90 transition-colors font-medium">
                      Start Using Feature
                    </button>
                    <button className="w-full border border-border text-foreground py-3 px-4 rounded-lg hover:bg-muted transition-colors font-medium">
                      Learn More
                    </button>
                    <button className="w-full border border-border text-foreground py-3 px-4 rounded-lg hover:bg-muted transition-colors font-medium">
                      View Tutorial
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Smokeless Nicotine Section */}
        {activeTab === "community" && (
          <div className="space-section">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-foreground mb-4">
                NRT Community & Support
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Connect with other NRT users, share experiences, get support, and access expert guidance on your quit journey.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-component">
              <div className="card-secondary">
                <h3 className="text-lg font-semibold mb-4 text-foreground">Nicotine Pouches</h3>
                <p className="text-muted-foreground mb-4">Modern tobacco-free nicotine delivery</p>
                <div className="space-minimal">
                  <button 
                    onClick={() => setSelectedCommunityProduct({
                      name: 'Zyn (various strengths)',
                      category: 'Nicotine Pouches',
                      description: 'Zyn nicotine pouches are tobacco-free alternatives available in multiple strengths (3mg, 6mg) and flavors. Popular for their discrete design and consistent nicotine delivery.',
                      benefits: ['Tobacco-free formula', 'Various strength options', 'Discrete and portable', 'Long-lasting satisfaction'],
                      usage: 'Place pouch between lip and gum for 20-30 minutes. Do not chew or swallow.',
                      availability: 'Available at most convenience stores and online retailers.'
                    })}
                    className="flex items-center justify-between w-full p-2 text-left text-sm text-foreground hover:bg-muted rounded-lg transition-colors group"
                  >
                    <span>• Zyn (various strengths)</span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                  </button>
                  <button 
                    onClick={() => setSelectedCommunityProduct({
                      name: 'Velo (mint, citrus flavors)',
                      category: 'Nicotine Pouches',
                      description: 'Velo nicotine pouches offer refreshing mint and citrus flavors with smooth nicotine delivery. Known for their premium taste and quality.',
                      benefits: ['Premium flavor profiles', 'Smooth nicotine delivery', 'Refreshing mint and citrus options', 'High-quality materials'],
                      usage: 'Place pouch between lip and gum for optimal absorption. Effects last 20-30 minutes.',
                      availability: 'Available through select retailers and online platforms.'
                    })}
                    className="flex items-center justify-between w-full p-2 text-left text-sm text-foreground hover:bg-muted rounded-lg transition-colors group"
                  >
                    <span>• Velo (mint, citrus flavors)</span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                  </button>
                  <button 
                    onClick={() => setSelectedCommunityProduct({
                      name: 'On! (compact design)',
                      category: 'Nicotine Pouches',
                      description: 'On! nicotine pouches feature an ultra-compact design for maximum discretion. Perfect for users who prefer smaller, less noticeable pouches.',
                      benefits: ['Ultra-compact size', 'Maximum discretion', 'Comfortable fit', 'Available in multiple flavors'],
                      usage: 'Small size makes placement easy. Use for 15-20 minutes for optimal experience.',
                      availability: 'Widely available in convenience stores and gas stations.'
                    })}
                    className="flex items-center justify-between w-full p-2 text-left text-sm text-foreground hover:bg-muted rounded-lg transition-colors group"
                  >
                    <span>• On! (compact design)</span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                  </button>
                  <button 
                    onClick={() => setSelectedCommunityProduct({
                      name: 'Rogue (bold flavors)',
                      category: 'Nicotine Pouches',
                      description: 'Rogue nicotine pouches are known for their bold, intense flavors and strong nicotine delivery. Perfect for users seeking a more robust experience.',
                      benefits: ['Bold, intense flavors', 'Strong nicotine delivery', 'Long-lasting satisfaction', 'Premium quality construction'],
                      usage: 'Place pouch for 20-30 minutes. Strong flavor profile provides lasting satisfaction.',
                      availability: 'Available at tobacco shops and online retailers.'
                    })}
                    className="flex items-center justify-between w-full p-2 text-left text-sm text-foreground hover:bg-muted rounded-lg transition-colors group"
                  >
                    <span>• Rogue (bold flavors)</span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                  </button>
                </div>
              </div>
              <div className="card-secondary">
                <h3 className="text-lg font-semibold mb-4 text-foreground">Traditional Snus</h3>
                <p className="text-muted-foreground mb-4">Swedish-style tobacco products</p>
                <div className="space-minimal">
                  <button 
                    onClick={() => setSelectedCommunityProduct({
                      name: 'General (classic Swedish)',
                      category: 'Traditional Snus',
                      description: 'General snus represents the classic Swedish snus experience with authentic tobacco flavor and traditional preparation methods.',
                      benefits: ['Authentic Swedish tradition', 'Classic tobacco flavor', 'Traditional preparation', 'Premium quality tobacco'],
                      usage: 'Place portion under upper lip for 30-60 minutes. Traditional Swedish method.',
                      availability: 'Available in select tobacco shops and specialty stores.'
                    })}
                    className="flex items-center justify-between w-full p-2 text-left text-sm text-foreground hover:bg-muted rounded-lg transition-colors group"
                  >
                    <span>• General (classic Swedish)</span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                  </button>
                  <button 
                    onClick={() => setSelectedCommunityProduct({
                      name: 'Camel (American style)',
                      category: 'Traditional Snus',
                      description: 'Camel snus offers an American take on traditional Swedish snus, providing familiar tobacco flavors with modern convenience.',
                      benefits: ['American-style preparation', 'Familiar tobacco taste', 'Convenient packaging', 'Widely accessible'],
                      usage: 'Use as directed on package. Typically 20-30 minutes for optimal experience.',
                      availability: 'Available at most tobacco retailers and convenience stores.'
                    })}
                    className="flex items-center justify-between w-full p-2 text-left text-sm text-foreground hover:bg-muted rounded-lg transition-colors group"
                  >
                    <span>• Camel (American style)</span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                  </button>
                  <button 
                    onClick={() => setSelectedCommunityProduct({
                      name: 'Skruf (premium quality)',
                      category: 'Traditional Snus',
                      description: 'Skruf snus represents premium Swedish craftsmanship with carefully selected tobacco and innovative flavor profiles.',
                      benefits: ['Premium Swedish quality', 'Innovative flavors', 'Carefully selected tobacco', 'Artisanal craftsmanship'],
                      usage: 'Premium product designed for extended use. Place for 45-60 minutes.',
                      availability: 'Available through premium tobacco retailers and online.'
                    })}
                    className="flex items-center justify-between w-full p-2 text-left text-sm text-foreground hover:bg-muted rounded-lg transition-colors group"
                  >
                    <span>• Skruf (premium quality)</span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                  </button>
                  <button 
                    onClick={() => setSelectedCommunityProduct({
                      name: 'Ettan (traditional taste)',
                      category: 'Traditional Snus',
                      description: 'Ettan snus offers the most traditional Swedish snus taste, unchanged for generations and beloved by purists.',
                      benefits: ['Unchanged traditional recipe', 'Authentic Swedish heritage', 'Beloved by purists', 'Time-tested quality'],
                      usage: 'Traditional method: place under upper lip for extended periods (45-90 minutes).',
                      availability: 'Available at specialty Swedish tobacco retailers.'
                    })}
                    className="flex items-center justify-between w-full p-2 text-left text-sm text-foreground hover:bg-muted rounded-lg transition-colors group"
                  >
                    <span>• Ettan (traditional taste)</span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                  </button>
                </div>
              </div>
              <div className="card-secondary">
                <h3 className="text-lg font-semibold mb-4 text-foreground">Nicotine Salts</h3>
                <p className="text-muted-foreground mb-4">Fast-acting nicotine solutions</p>
                <div className="space-minimal">
                  <button 
                    onClick={() => setSelectedCommunityProduct({
                      name: 'Oral strips',
                      category: 'Nicotine Salts',
                      description: 'Oral nicotine strips dissolve quickly on the tongue, providing rapid nicotine absorption and convenient, discrete usage.',
                      benefits: ['Rapid absorption', 'Discrete usage', 'No residue', 'Easy to carry'],
                      usage: 'Place strip on tongue and allow to dissolve completely. Effects within 1-2 minutes.',
                      availability: 'Available at pharmacies and online retailers.'
                    })}
                    className="flex items-center justify-between w-full p-2 text-left text-sm text-foreground hover:bg-muted rounded-lg transition-colors group"
                  >
                    <span>• Oral strips</span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                  </button>
                  <button 
                    onClick={() => setSelectedCommunityProduct({
                      name: 'Sublingual tablets',
                      category: 'Nicotine Salts',
                      description: 'Sublingual nicotine tablets are placed under the tongue for fast absorption directly into the bloodstream.',
                      benefits: ['Direct bloodstream absorption', 'Fast-acting relief', 'Precise dosing', 'Medical-grade quality'],
                      usage: 'Place tablet under tongue until completely dissolved. Do not chew or swallow.',
                      availability: 'Available through pharmacies and medical suppliers.'
                    })}
                    className="flex items-center justify-between w-full p-2 text-left text-sm text-foreground hover:bg-muted rounded-lg transition-colors group"
                  >
                    <span>• Sublingual tablets</span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                  </button>
                  <button 
                    onClick={() => setSelectedCommunityProduct({
                      name: 'Nicotine toothpicks',
                      category: 'Nicotine Salts',
                      description: 'Nicotine-infused toothpicks provide a unique delivery method, combining oral fixation habits with nicotine satisfaction.',
                      benefits: ['Satisfies oral fixation', 'Unique delivery method', 'Portable and discrete', 'Helps with habits'],
                      usage: 'Chew gently or hold in mouth. Replace when flavor diminishes.',
                      availability: 'Available through specialty retailers and online.'
                    })}
                    className="flex items-center justify-between w-full p-2 text-left text-sm text-foreground hover:bg-muted rounded-lg transition-colors group"
                  >
                    <span>• Nicotine toothpicks</span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                  </button>
                  <button 
                    onClick={() => setSelectedCommunityProduct({
                      name: 'Dissolvable films',
                      category: 'Nicotine Salts',
                      description: 'Dissolvable nicotine films offer ultra-discrete nicotine delivery, dissolving completely without any residue or evidence.',
                      benefits: ['Ultra-discrete delivery', 'No residue or evidence', 'Rapid dissolution', 'Precise dosing'],
                      usage: 'Place film on tongue or inside cheek. Dissolves completely in 30-60 seconds.',
                      availability: 'Available through select online retailers and specialty shops.'
                    })}
                    className="flex items-center justify-between w-full p-2 text-left text-sm text-foreground hover:bg-muted rounded-lg transition-colors group"
                  >
                    <span>• Dissolvable films</span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                  </button>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4 text-foreground">Comprehensive Vendor & Store Network</h3>
              <p className="text-muted-foreground mb-4">Find the best vendors for smokeless nicotine products with ratings, reviews, and availability.</p>
              <div className="space-y-6">
                <VendorPage productId="smokeless-nicotine" category="smokeless" />
                <StoreLocator productId="smokeless-nicotine" category="smokeless" />
              </div>
            </div>
          </div>
        )}

        {/* VENDOR DASHBOARD TAB - Complete Dual-User Support Implementation */}
        {activeTab === "vendor-dashboard" && (
          <div className="space-y-8">
            {/* Vendor Dashboard Header */}
            <div className="text-center">
              <h2 className="text-4xl font-bold text-foreground mb-4">
                <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                  🏪 Vendor Store Hub
                </span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-4xl mx-auto">
                Manage your NRT inventory, listings, analytics, and connect with customers. Everything you need to grow your business.
              </p>
            </div>

            {/* Vendor Quick Stats Dashboard */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-muted/30 rounded-lg p-6 border border-border">
                <div className="flex items-center gap-3 mb-2">
                  <Package className="icon-lg text-primary" />
                  <span className="text-sm font-medium text-muted-foreground">Active Listings</span>
                </div>
                <div className="text-3xl font-bold text-primary">24</div>
                <div className="text-xs text-muted-foreground mt-1">+3 this week</div>
              </div>
              
              <div className="bg-card rounded-lg p-6 border border-border">
                <div className="flex items-center gap-3 mb-2">
                  <TrendingUp className="icon-lg text-primary" />
                  <span className="text-sm font-medium text-muted-foreground">Monthly Sales</span>
                </div>
                <div className="text-3xl font-bold text-primary">$2,847</div>
                <div className="text-xs text-muted-foreground mt-1">+18% vs last month</div>
              </div>
              
              <div className="bg-card rounded-lg p-6 border border-border">
                <div className="flex items-center gap-3 mb-2">
                  <Users className="icon-lg text-primary" />
                  <span className="text-sm font-medium text-muted-foreground">Active Customers</span>
                </div>
                <div className="text-3xl font-bold text-primary">186</div>
                <div className="text-xs text-muted-foreground mt-1">+12 new this month</div>
              </div>
              
              <div className="bg-primary/5 dark:bg-primary/10 rounded-lg p-6 border border-primary/20 dark:border-primary/30">
                <div className="flex items-center gap-3 mb-2">
                  <Star className="icon-lg text-primary" />
                  <span className="text-sm font-medium text-muted-foreground">Store Rating</span>
                </div>
                <div className="text-3xl font-bold text-primary">4.8</div>
                <div className="text-xs text-muted-foreground mt-1">Based on 94 reviews</div>
              </div>
            </div>

            {/* Vendor Management Tools */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Inventory Management */}
              <div className="bg-card rounded-xl border border-border p-8 shadow-lg hover:shadow-xl transition-shadow duration-200">
                <div className="flex items-center gap-4 mb-6">
                  <div className="bg-primary/10 p-3 rounded-xl">
                    <Package className="icon-md text-primary" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-foreground">Inventory Management</h3>
                    <p className="text-muted-foreground">Manage your NRT product listings and stock levels</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <button className="w-full bg-primary text-primary-foreground px-6 py-3 rounded-xl hover:bg-primary/90 transition-colors font-semibold">
                    + Add New Product Listing
                  </button>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <button className="bg-background border-2 border-border text-foreground px-4 py-3 rounded-xl hover:border-primary hover:text-primary transition-colors font-medium">
                      📦 Manage Stock
                    </button>
                    <button className="bg-background border-2 border-border text-foreground px-4 py-3 rounded-xl hover:border-primary hover:text-primary transition-colors font-medium">
                      💰 Update Pricing
                    </button>
                  </div>
                </div>
              </div>

              {/* Customer Communication */}
              <div className="bg-card rounded-xl border border-border p-8 shadow-lg hover:shadow-xl transition-shadow duration-200">
                <div className="flex items-center gap-4 mb-6">
                  <div className="bg-primary/10 p-3 rounded-xl">
                    <Users className="icon-md text-primary" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-foreground">Customer Communication</h3>
                    <p className="text-muted-foreground">Connect with customers and manage inquiries</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <button className="w-full bg-primary text-primary-foreground px-6 py-3 rounded-xl hover:bg-primary/90 transition-colors font-semibold">
                    <MessageCircle className="w-4 h-4 inline mr-2" />
                    View Messages (3 new)
                  </button>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <button className="bg-background border-2 border-border text-foreground px-4 py-3 rounded-xl hover:border-primary hover:text-primary transition-colors font-medium">
                      <Mail className="w-4 h-4 inline mr-2" />
                      Email Customers
                    </button>
                    <button className="bg-background border-2 border-border text-foreground px-4 py-3 rounded-xl hover:border-primary hover:text-primary transition-colors font-medium">
                      <Smartphone className="w-4 h-4 inline mr-2" />
                      SMS Notifications
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Store Performance Analytics */}
            <div className="bg-card rounded-xl border border-border p-8 shadow-lg hover:shadow-xl transition-shadow duration-200">
              <div className="flex items-center gap-4 mb-6">
                <div className="bg-primary/10 p-3 rounded-xl">
                  <BarChart3 className="icon-md text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-foreground">Store Performance Analytics</h3>
                  <p className="text-muted-foreground">Track your business growth and customer engagement</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-background rounded-lg border border-border">
                  <div className="text-2xl font-bold text-primary mb-1">$12,450</div>
                  <div className="text-sm text-muted-foreground">Total Revenue (90 days)</div>
                </div>
                
                <div className="text-center p-4 bg-background rounded-lg border border-border">
                  <div className="text-2xl font-bold text-primary mb-1">342</div>
                  <div className="text-sm text-muted-foreground">Orders Completed</div>
                </div>
                
                <div className="text-center p-4 bg-background rounded-lg border border-border">
                  <div className="text-2xl font-bold text-primary mb-1">96%</div>
                  <div className="text-sm text-muted-foreground">Customer Satisfaction</div>
                </div>
              </div>
              
              <div className="mt-6 flex justify-center">
                <button className="bg-primary text-primary-foreground px-8 py-3 rounded-xl hover:bg-primary/90 transition-colors font-semibold flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  View Detailed Analytics
                </button>
              </div>
            </div>
          </div>
        )}

        {/* VENDOR ANALYTICS TAB - Business Insights Implementation */}
        {activeTab === "vendor-analytics" && (
          <div className="space-y-8">
            <div className="text-center">
              <h2 className="text-4xl font-bold text-foreground mb-4 flex items-center justify-center gap-3">
                <TrendingUp className="w-10 h-10 text-primary" />
                <span className="text-foreground font-bold">
                  Business Insights
                </span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-4xl mx-auto">
                Advanced analytics and insights to optimize your NRT business performance and customer engagement.
              </p>
            </div>

            {/* Analytics Content */}
            <div className="bg-muted/30 rounded-lg p-8 border border-border">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-foreground mb-4">
                  <TrendingUp className="w-6 h-6 inline mr-2" />
                  Advanced Analytics Dashboard
                </h3>
                <p className="text-muted-foreground mb-6">Detailed sales reports, customer behavior analysis, and market trends coming soon.</p>
                <button className="bg-primary text-primary-foreground px-8 py-3 rounded-xl hover:bg-primary/90 transition-colors font-semibold">
                  Request Early Access
                </button>
              </div>
            </div>
          </div>
        )}

        {/* VENDOR TOOLS TAB - Business Tools Implementation */}
        {activeTab === "vendor-tools" && (
          <div className="space-y-8">
            <div className="text-center">
              <h2 className="text-4xl font-bold text-foreground mb-4 flex items-center justify-center gap-3">
                <Settings className="w-10 h-10 text-primary" />
                <span className="text-foreground font-bold">
                  Business Tools
                </span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-4xl mx-auto">
                Powerful tools for pricing optimization, promotional campaigns, and inventory management.
              </p>
            </div>

            {/* Business Tools Content */}
            <div className="bg-muted/30 rounded-lg p-8 border border-border">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-foreground mb-4 flex items-center justify-center gap-2">
                  <Zap className="icon-base text-primary" />
                  Professional Business Suite
                </h3>
                <p className="text-muted-foreground mb-6">Advanced pricing tools, campaign management, and promotional features for serious vendors.</p>
                <button className="bg-primary text-primary-foreground px-8 py-3 rounded-xl hover:bg-primary/90 transition-colors font-semibold">
                  Upgrade to Pro Tools
                </button>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Auth Modal */}
      {showAuthModal && (
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          mode={authMode}
          onToggleMode={() => setAuthMode(authMode === 'signin' ? 'signup' : 'signin')}
        />
      )}
    </div>
  );
};

// Main Landing Page Component
const LandingPage = () => {
  return (
    <AuthProvider>
      <MainApp />
    </AuthProvider>
  );
};

export default LandingPage;
