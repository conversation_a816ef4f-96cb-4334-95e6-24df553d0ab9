import { supabase } from '../lib/supabase';

// Sample smokeless products data for testing smokeless page functionality
// This adds real data to the mission_fresh.smokeless_products table (RULE 0001 compliant)
export const seedSampleSmokelessProducts = async () => {
  try {
    console.log('🚨 seedSampleSmokelessProducts: Adding sample smokeless products...');
    
    const sampleSmokelessProducts = [
      {
        id: 'smokeless-001',
        name: 'Zyn Nicotine Pouches - Cool Mint',
        brand: 'Zyn',
        category: 'nicotine_pouches',
        subcategory: 'tobacco_free',
        nicotine_strength: '3mg',
        flavor: 'Cool Mint',
        format: 'pouch',
        package_size: '15 pouches',
        description: 'Tobacco-free nicotine pouches with refreshing cool mint flavor. Discreet and convenient for on-the-go use.',
        ingredients: ['Nicotine', 'Plant-based fibers', 'Natural flavors', 'Sweeteners'],
        usage_instructions: 'Place one pouch between your lip and gum. Keep for up to 60 minutes. Do not swallow.',
        warnings: ['Contains nicotine', 'Not for use by minors', 'May cause mouth irritation'],
        price_range_min: 4.99,
        price_range_max: 6.99,
        availability: 'widely_available',
        rating_avg: 4.3,
        rating_count: 1247,
        harm_reduction_score: 8.5,
        satisfaction_rating: 4.2,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'smokeless-002',
        name: 'On! Nicotine Pouches - Wintergreen',
        brand: 'On!',
        category: 'nicotine_pouches',
        subcategory: 'tobacco_free',
        nicotine_strength: '4mg',
        flavor: 'Wintergreen',
        format: 'pouch',
        package_size: '20 pouches',
        description: 'Premium tobacco-free nicotine pouches with bold wintergreen flavor. Small, discreet, and long-lasting.',
        ingredients: ['Nicotine', 'Cellulose', 'Natural wintergreen flavor', 'Sodium carbonate'],
        usage_instructions: 'Place pouch between lip and gum. Use for 20-30 minutes. Dispose responsibly.',
        warnings: ['Contains nicotine', 'Addictive substance', 'Keep away from children'],
        price_range_min: 5.49,
        price_range_max: 7.99,
        availability: 'widely_available',
        rating_avg: 4.1,
        rating_count: 892,
        harm_reduction_score: 8.7,
        satisfaction_rating: 4.0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'smokeless-003',
        name: 'Velo Nicotine Pouches - Citrus Burst',
        brand: 'Velo',
        category: 'nicotine_pouches',
        subcategory: 'tobacco_free',
        nicotine_strength: '2mg',
        flavor: 'Citrus Burst',
        format: 'pouch',
        package_size: '20 pouches',
        description: 'Light nicotine pouches with vibrant citrus flavor. Perfect for beginners or those preferring lower nicotine.',
        ingredients: ['Nicotine', 'Plant fibers', 'Citrus flavoring', 'pH adjusters'],
        usage_instructions: 'Place between upper lip and gum. Enjoy for up to 30 minutes. Do not chew or swallow.',
        warnings: ['Contains nicotine', 'Not a smoking cessation product', 'Consult doctor if pregnant'],
        price_range_min: 4.49,
        price_range_max: 6.49,
        availability: 'widely_available',
        rating_avg: 4.4,
        rating_count: 1156,
        harm_reduction_score: 8.8,
        satisfaction_rating: 4.3,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'smokeless-004',
        name: 'Rogue Nicotine Pouches - Honey Lemon',
        brand: 'Rogue',
        category: 'nicotine_pouches',
        subcategory: 'tobacco_free',
        nicotine_strength: '6mg',
        flavor: 'Honey Lemon',
        format: 'pouch',
        package_size: '20 pouches',
        description: 'Strong nicotine pouches with unique honey lemon flavor. For experienced users seeking higher nicotine satisfaction.',
        ingredients: ['Nicotine', 'Microcrystalline cellulose', 'Natural honey flavor', 'Lemon extract'],
        usage_instructions: 'Place one pouch under upper lip. Keep for 45-60 minutes. Remove and dispose properly.',
        warnings: ['High nicotine content', 'Not for beginners', 'May cause dizziness'],
        price_range_min: 5.99,
        price_range_max: 8.49,
        availability: 'limited_availability',
        rating_avg: 4.0,
        rating_count: 634,
        harm_reduction_score: 8.3,
        satisfaction_rating: 4.1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'smokeless-005',
        name: 'Lucy Nicotine Gum - Pomegranate',
        brand: 'Lucy',
        category: 'nicotine_gum',
        subcategory: 'flavored_gum',
        nicotine_strength: '4mg',
        flavor: 'Pomegranate',
        format: 'gum',
        package_size: '10 pieces',
        description: 'Premium nicotine gum with exotic pomegranate flavor. Made with natural ingredients and no artificial colors.',
        ingredients: ['Nicotine polacrilex', 'Gum base', 'Pomegranate flavor', 'Natural sweeteners'],
        usage_instructions: 'Chew slowly until tingling sensation. Park between cheek and gum. Repeat as needed.',
        warnings: ['Contains nicotine', 'Do not exceed 24 pieces per day', 'Not for non-smokers'],
        price_range_min: 6.99,
        price_range_max: 9.99,
        availability: 'online_only',
        rating_avg: 4.5,
        rating_count: 423,
        harm_reduction_score: 9.0,
        satisfaction_rating: 4.4,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    // Insert smokeless products using upsert to avoid duplicates
    const { data, error } = await supabase
      .from('smokeless_products')
      .upsert(sampleSmokelessProducts, { onConflict: 'id' })
      .select();

    if (error) {
      console.error('🚨 seedSampleSmokelessProducts: Error inserting smokeless products:', error);
      throw error;
    }

    console.log('🚨 seedSampleSmokelessProducts: Successfully added', data?.length || 0, 'smokeless products');
    return data;
  } catch (error) {
    console.error('🚨 seedSampleSmokelessProducts: Error:', error);
    throw error;
  }
};
