import { seedSampleStores } from './seedStores';
import { seedSamplePriceComparisons } from './seedPriceComparisons';
import { seedSampleSmokelessProducts } from './seedSmokelessProducts';
import { seedSampleDeals } from './seedDeals';

// Master seeding function to populate all missing data
// This adds real data to all mission_fresh database tables (RULE 0001 compliant)
export const seedAllMissingData = async () => {
  try {
    console.log('🚨 seedAllMissingData: Starting comprehensive data seeding...');
    
    const results = {
      stores: null,
      priceComparisons: null,
      smokelessProducts: null,
      deals: null,
      errors: []
    };

    // Seed stores data
    try {
      console.log('🚨 seedAllMissingData: Seeding stores...');
      results.stores = await seedSampleStores();
      console.log('✅ Stores seeded successfully');
    } catch (error) {
      console.error('❌ Error seeding stores:', error);
      results.errors.push({ type: 'stores', error });
    }

    // Seed price comparison data
    try {
      console.log('🚨 seedAllMissingData: Seeding price comparisons...');
      results.priceComparisons = await seedSamplePriceComparisons();
      console.log('✅ Price comparisons seeded successfully');
    } catch (error) {
      console.error('❌ Error seeding price comparisons:', error);
      results.errors.push({ type: 'priceComparisons', error });
    }

    // Seed smokeless products data
    try {
      console.log('🚨 seedAllMissingData: Seeding smokeless products...');
      results.smokelessProducts = await seedSampleSmokelessProducts();
      console.log('✅ Smokeless products seeded successfully');
    } catch (error) {
      console.error('❌ Error seeding smokeless products:', error);
      results.errors.push({ type: 'smokelessProducts', error });
    }

    // Seed deals data
    try {
      console.log('🚨 seedAllMissingData: Seeding deals...');
      results.deals = await seedSampleDeals();
      console.log('✅ Deals seeded successfully');
    } catch (error) {
      console.error('❌ Error seeding deals:', error);
      results.errors.push({ type: 'deals', error });
    }

    console.log('🚨 seedAllMissingData: Comprehensive data seeding completed!');
    console.log('📊 Results:', {
      stores: results.stores?.length || 0,
      priceComparisons: results.priceComparisons ? 
        (results.priceComparisons.vendorPrices?.length || 0) + (results.priceComparisons.storePrices?.length || 0) : 0,
      smokelessProducts: results.smokelessProducts?.length || 0,
      deals: results.deals?.length || 0,
      errors: results.errors.length
    });

    return results;
  } catch (error) {
    console.error('🚨 seedAllMissingData: Critical error:', error);
    throw error;
  }
};
