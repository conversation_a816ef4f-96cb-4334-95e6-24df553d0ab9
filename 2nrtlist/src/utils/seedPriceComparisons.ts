import { supabase } from '../lib/supabase';

// Sample price comparison data for testing compare page functionality
// This adds real data to the mission_fresh database tables (RULE 0001 compliant)
export const seedSamplePriceComparisons = async () => {
  try {
    console.log('🚨 seedSamplePriceComparisons: Adding sample price comparison data...');
    
    // First, let's add some vendor prices
    const sampleVendorPrices = [
      {
        id: 'vprice-001',
        vendor_id: 'vendor-001',
        product_id: 'nrt-001',
        product_name: 'Nicorette Gum Original 2mg',
        brand: 'Nicorette',
        category: 'gum',
        flavor: 'Original',
        nicotine_strength: '2mg',
        package_size: '20-piece',
        price_regular: 24.99,
        price_sale: 19.99,
        discount_percentage: 20,
        in_stock: true,
        stock_level: 'high',
        currency: 'USD',
        price_per_unit: 1.00,
        unit_type: 'piece',
        shipping_cost: 4.99,
        free_shipping_threshold: 35.00,
        last_updated: new Date().toISOString()
      },
      {
        id: 'vprice-002',
        vendor_id: 'vendor-002',
        product_id: 'nrt-001',
        product_name: 'Nicorette Gum Original 2mg',
        brand: 'Nicorette',
        category: 'gum',
        flavor: 'Original',
        nicotine_strength: '2mg',
        package_size: '20-piece',
        price_regular: 26.99,
        price_sale: 22.99,
        discount_percentage: 15,
        in_stock: true,
        stock_level: 'medium',
        currency: 'USD',
        price_per_unit: 1.15,
        unit_type: 'piece',
        shipping_cost: 0.00,
        free_shipping_threshold: 25.00,
        last_updated: new Date().toISOString()
      },
      {
        id: 'vprice-003',
        vendor_id: 'vendor-003',
        product_id: 'nrt-002',
        product_name: 'NicoDerm CQ Patch 21mg',
        brand: 'NicoDerm CQ',
        category: 'patch',
        flavor: null,
        nicotine_strength: '21mg',
        package_size: '14-patch',
        price_regular: 49.99,
        price_sale: 39.99,
        discount_percentage: 20,
        in_stock: true,
        stock_level: 'high',
        currency: 'USD',
        price_per_unit: 2.86,
        unit_type: 'patch',
        shipping_cost: 6.99,
        free_shipping_threshold: 50.00,
        last_updated: new Date().toISOString()
      },
      {
        id: 'vprice-004',
        vendor_id: 'vendor-004',
        product_id: 'nrt-002',
        product_name: 'NicoDerm CQ Patch 21mg',
        brand: 'NicoDerm CQ',
        category: 'patch',
        flavor: null,
        nicotine_strength: '21mg',
        package_size: '14-patch',
        price_regular: 52.99,
        price_sale: 44.99,
        discount_percentage: 15,
        in_stock: true,
        stock_level: 'low',
        currency: 'USD',
        price_per_unit: 3.21,
        unit_type: 'patch',
        shipping_cost: 0.00,
        free_shipping_threshold: 30.00,
        last_updated: new Date().toISOString()
      },
      {
        id: 'vprice-005',
        vendor_id: 'vendor-005',
        product_id: 'nrt-003',
        product_name: 'Commit Lozenge 4mg',
        brand: 'Commit',
        category: 'lozenge',
        flavor: 'Mint',
        nicotine_strength: '4mg',
        package_size: '72-lozenge',
        price_regular: 34.99,
        price_sale: 29.99,
        discount_percentage: 14,
        in_stock: true,
        stock_level: 'high',
        currency: 'USD',
        price_per_unit: 0.42,
        unit_type: 'lozenge',
        shipping_cost: 5.99,
        free_shipping_threshold: 40.00,
        last_updated: new Date().toISOString()
      }
    ];

    // Insert vendor prices using upsert to avoid duplicates
    const { data: vendorPricesData, error: vendorPricesError } = await supabase
      .from('vendor_prices')
      .upsert(sampleVendorPrices, { onConflict: 'id' })
      .select();

    if (vendorPricesError) {
      console.error('🚨 seedSamplePriceComparisons: Error inserting vendor prices:', vendorPricesError);
      throw vendorPricesError;
    }

    console.log('🚨 seedSamplePriceComparisons: Successfully added', vendorPricesData?.length || 0, 'vendor prices');

    // Now add some store prices for comparison
    const sampleStorePrices = [
      {
        id: 'sprice-001',
        store_id: 'store-001',
        product_id: 'nrt-001',
        product_name: 'Nicorette Gum Original 2mg',
        brand: 'Nicorette',
        category: 'gum',
        flavor: 'Original',
        nicotine_strength: '2mg',
        package_size: '20-piece',
        price_regular: 27.99,
        price_sale: 24.99,
        discount_percentage: 11,
        in_stock: true,
        stock_level: 'high',
        currency: 'USD',
        price_per_unit: 1.25,
        unit_type: 'piece',
        last_updated: new Date().toISOString()
      },
      {
        id: 'sprice-002',
        store_id: 'store-002',
        product_id: 'nrt-001',
        product_name: 'Nicorette Gum Original 2mg',
        brand: 'Nicorette',
        category: 'gum',
        flavor: 'Original',
        nicotine_strength: '2mg',
        package_size: '20-piece',
        price_regular: 25.99,
        price_sale: 23.99,
        discount_percentage: 8,
        in_stock: true,
        stock_level: 'medium',
        currency: 'USD',
        price_per_unit: 1.20,
        unit_type: 'piece',
        last_updated: new Date().toISOString()
      },
      {
        id: 'sprice-003',
        store_id: 'store-003',
        product_id: 'nrt-002',
        product_name: 'NicoDerm CQ Patch 21mg',
        brand: 'NicoDerm CQ',
        category: 'patch',
        flavor: null,
        nicotine_strength: '21mg',
        package_size: '14-patch',
        price_regular: 54.99,
        price_sale: 49.99,
        discount_percentage: 9,
        in_stock: true,
        stock_level: 'high',
        currency: 'USD',
        price_per_unit: 3.57,
        unit_type: 'patch',
        last_updated: new Date().toISOString()
      }
    ];

    // Insert store prices using upsert to avoid duplicates
    const { data: storePricesData, error: storePricesError } = await supabase
      .from('store_prices')
      .upsert(sampleStorePrices, { onConflict: 'id' })
      .select();

    if (storePricesError) {
      console.error('🚨 seedSamplePriceComparisons: Error inserting store prices:', storePricesError);
      throw storePricesError;
    }

    console.log('🚨 seedSamplePriceComparisons: Successfully added', storePricesData?.length || 0, 'store prices');

    return {
      vendorPrices: vendorPricesData,
      storePrices: storePricesData
    };
  } catch (error) {
    console.error('🚨 seedSamplePriceComparisons: Error:', error);
    throw error;
  }
};
