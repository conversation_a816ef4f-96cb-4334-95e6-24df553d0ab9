import { supabase } from '../lib/supabase';

// Sample store data for testing stores page functionality
// This adds real data to the mission_fresh.stores table (RULE 0001 compliant)
export const seedSampleStores = async () => {
  try {
    console.log('🚨 seedSampleStores: Adding sample stores to database...');
    
    const sampleStores = [
      {
        id: 'store-001',
        name: 'CVS Pharmacy',
        brand: 'CVS',
        chain: 'CVS Health',
        address: '123 Main Street',
        city: 'New York',
        state: 'NY',
        zip_code: '10001',
        country: 'USA',
        latitude: 40.7589,
        longitude: -73.9851,
        phone: '(*************',
        website: 'https://www.cvs.com',
        store_hours: {
          monday: '8:00 AM - 10:00 PM',
          tuesday: '8:00 AM - 10:00 PM',
          wednesday: '8:00 AM - 10:00 PM',
          thursday: '8:00 AM - 10:00 PM',
          friday: '8:00 AM - 10:00 PM',
          saturday: '8:00 AM - 10:00 PM',
          sunday: '9:00 AM - 9:00 PM'
        },
        nrt_brands_carried: ['Nicorette', 'NicoDerm CQ', 'Commit', 'Habitrol'],
        pharmacy_available: true,
        prescription_required: false,
        drive_through: true,
        parking_available: true,
        wheelchair_accessible: true,
        rating: 4.5,
        review_count: 127,
        verified: true
      },
      {
        id: 'store-002',
        name: 'Walgreens',
        brand: 'Walgreens',
        chain: 'Walgreens Boots Alliance',
        address: '456 Broadway',
        city: 'New York',
        state: 'NY',
        zip_code: '10013',
        country: 'USA',
        latitude: 40.7505,
        longitude: -73.9934,
        phone: '(*************',
        website: 'https://www.walgreens.com',
        store_hours: {
          monday: '7:00 AM - 11:00 PM',
          tuesday: '7:00 AM - 11:00 PM',
          wednesday: '7:00 AM - 11:00 PM',
          thursday: '7:00 AM - 11:00 PM',
          friday: '7:00 AM - 11:00 PM',
          saturday: '7:00 AM - 11:00 PM',
          sunday: '8:00 AM - 10:00 PM'
        },
        nrt_brands_carried: ['Nicorette', 'NicoDerm CQ', 'Thrive'],
        pharmacy_available: true,
        prescription_required: false,
        drive_through: true,
        parking_available: true,
        wheelchair_accessible: true,
        rating: 4.3,
        review_count: 89,
        verified: true
      },
      {
        id: 'store-003',
        name: 'Rite Aid',
        brand: 'Rite Aid',
        chain: 'Rite Aid Corporation',
        address: '789 Fifth Avenue',
        city: 'New York',
        state: 'NY',
        zip_code: '10022',
        country: 'USA',
        latitude: 40.7614,
        longitude: -73.9776,
        phone: '(*************',
        website: 'https://www.riteaid.com',
        store_hours: {
          monday: '8:00 AM - 9:00 PM',
          tuesday: '8:00 AM - 9:00 PM',
          wednesday: '8:00 AM - 9:00 PM',
          thursday: '8:00 AM - 9:00 PM',
          friday: '8:00 AM - 9:00 PM',
          saturday: '8:00 AM - 9:00 PM',
          sunday: '9:00 AM - 8:00 PM'
        },
        nrt_brands_carried: ['Nicorette', 'Commit', 'Habitrol'],
        pharmacy_available: true,
        prescription_required: false,
        drive_through: false,
        parking_available: true,
        wheelchair_accessible: true,
        rating: 4.1,
        review_count: 56,
        verified: true
      },
      {
        id: 'store-004',
        name: 'Target Pharmacy',
        brand: 'Target',
        chain: 'Target Corporation',
        address: '101 West 14th Street',
        city: 'New York',
        state: 'NY',
        zip_code: '10011',
        country: 'USA',
        latitude: 40.7370,
        longitude: -73.9960,
        phone: '(*************',
        website: 'https://www.target.com',
        store_hours: {
          monday: '8:00 AM - 10:00 PM',
          tuesday: '8:00 AM - 10:00 PM',
          wednesday: '8:00 AM - 10:00 PM',
          thursday: '8:00 AM - 10:00 PM',
          friday: '8:00 AM - 10:00 PM',
          saturday: '8:00 AM - 10:00 PM',
          sunday: '8:00 AM - 9:00 PM'
        },
        nrt_brands_carried: ['Nicorette', 'NicoDerm CQ', 'up&up'],
        pharmacy_available: true,
        prescription_required: false,
        drive_through: false,
        parking_available: true,
        wheelchair_accessible: true,
        rating: 4.4,
        review_count: 203,
        verified: true
      },
      {
        id: 'store-005',
        name: 'Duane Reade',
        brand: 'Duane Reade',
        chain: 'Walgreens Boots Alliance',
        address: '224 West 57th Street',
        city: 'New York',
        state: 'NY',
        zip_code: '10019',
        country: 'USA',
        latitude: 40.7648,
        longitude: -73.9808,
        phone: '(*************',
        website: 'https://www.duanereade.com',
        store_hours: {
          monday: '7:00 AM - 11:00 PM',
          tuesday: '7:00 AM - 11:00 PM',
          wednesday: '7:00 AM - 11:00 PM',
          thursday: '7:00 AM - 11:00 PM',
          friday: '7:00 AM - 11:00 PM',
          saturday: '7:00 AM - 11:00 PM',
          sunday: '8:00 AM - 10:00 PM'
        },
        nrt_brands_carried: ['Nicorette', 'NicoDerm CQ', 'Commit'],
        pharmacy_available: true,
        prescription_required: false,
        drive_through: false,
        parking_available: true,
        wheelchair_accessible: true,
        rating: 4.2,
        review_count: 78,
        verified: true
      }
    ];

    // Insert stores using upsert to avoid duplicates
    const { data, error } = await supabase
      .from('stores')
      .upsert(sampleStores, { onConflict: 'id' })
      .select();

    if (error) {
      console.error('🚨 seedSampleStores: Error inserting stores:', error);
      throw error;
    }

    console.log('🚨 seedSampleStores: Successfully added', data?.length || 0, 'stores');
    return data;
  } catch (error) {
    console.error('🚨 seedSampleStores: Error:', error);
    throw error;
  }
};
