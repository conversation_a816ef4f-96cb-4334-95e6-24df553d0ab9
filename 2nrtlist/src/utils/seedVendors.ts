// Sample vendor data for testing vendors page functionality
// This adds real data to the mission_fresh.vendors table (RULE 0001 compliant)
import { supabaseNode as supabase } from '../lib/supabaseNode';

export const seedSampleVendors = async () => {
  try {
    console.log('🚨 seedSampleVendors: Adding sample vendors to database...');
    
    const sampleVendors = [
      {
        id: 'vendor-001',
        name: 'NRT Direct',
        website: 'https://nrtdirect.com',
        logo_url: 'https://via.placeholder.com/150',
        description: 'Leading online retailer of NRT products with fast shipping and competitive prices. Specializing in nicotine gum, patches, and lozenges.',
        rating: 4.8,
        review_count: 2847,
        shipping_info: 'Free shipping on orders over $50. Standard delivery 3-5 business days.',
        min_order: 25.00,
        delivery_time: '3-5 business days',
        coverage_areas: ['US', 'Canada'],
        specialties: ['Nicotine Gum', 'Nicotine Patches', 'Lozenges'],
        verified: true,
        affiliate_commission: 0.08,
        active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'vendor-002',
        name: 'Quit Smart',
        website: 'https://quitsmart.com',
        logo_url: 'https://via.placeholder.com/150',
        description: 'Premium NRT supplier with 24/7 customer support and expert guidance. Offering personalized quit plans.',
        rating: 4.6,
        review_count: 1923,
        shipping_info: 'Express shipping available. Free standard shipping on $75+.',
        min_order: 30.00,
        delivery_time: '2-4 business days',
        coverage_areas: ['US'],
        specialties: ['Nicotine Patches', 'Inhalers', 'Prescription NRT'],
        verified: true,
        affiliate_commission: 0.12,
        active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'vendor-003',
        name: 'HealthMart NRT',
        website: 'https://healthmartnrt.com',
        logo_url: 'https://via.placeholder.com/150',
        description: 'Trusted healthcare provider specializing in smoking cessation products and professional counseling services.',
        rating: 4.9,
        review_count: 3156,
        shipping_info: 'Medical-grade packaging. Temperature-controlled shipping.',
        min_order: 40.00,
        delivery_time: '1-3 business days',
        coverage_areas: ['US', 'Canada', 'UK'],
        specialties: ['Medical NRT', 'Prescription Products', 'Counseling'],
        verified: true,
        affiliate_commission: 0.06,
        active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'vendor-004',
        name: 'Stop Smoke Shop',
        website: 'https://stopsmokeshop.com',
        logo_url: 'https://via.placeholder.com/150',
        description: 'Comprehensive quit smoking resource center with wide selection of NRT products and educational materials.',
        rating: 4.4,
        review_count: 1567,
        shipping_info: 'Discreet packaging. Multiple shipping options available.',
        min_order: 20.00,
        delivery_time: '4-6 business days',
        coverage_areas: ['US', 'Canada'],
        specialties: ['Nicotine Gum', 'Lozenges', 'Educational Materials'],
        verified: true,
        affiliate_commission: 0.10,
        active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'vendor-005',
        name: 'NicoFree Express',
        website: 'https://nicofreeexpress.com',
        logo_url: 'https://via.placeholder.com/150',
        description: 'Fast delivery NRT specialist with same-day shipping in major cities. Emergency quit support available.',
        rating: 4.7,
        review_count: 2234,
        shipping_info: 'Same-day delivery in select cities. Express shipping nationwide.',
        min_order: 35.00,
        delivery_time: '1-2 business days',
        coverage_areas: ['US'],
        specialties: ['Emergency Support', 'Fast Delivery', 'All NRT Types'],
        verified: true,
        affiliate_commission: 0.15,
        active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'vendor-006',
        name: 'Wellness NRT Co',
        website: 'https://wellnessnrt.com',
        logo_url: 'https://via.placeholder.com/150',
        description: 'Holistic approach to smoking cessation with NRT products, supplements, and wellness coaching.',
        rating: 4.5,
        review_count: 1889,
        shipping_info: 'Eco-friendly packaging. Carbon-neutral shipping options.',
        min_order: 45.00,
        delivery_time: '3-5 business days',
        coverage_areas: ['US', 'Canada'],
        specialties: ['Holistic NRT', 'Supplements', 'Wellness Coaching'],
        verified: true,
        affiliate_commission: 0.09,
        active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    console.log('🚨 seedSampleVendors: Inserting', sampleVendors.length, 'vendors...');

    const { data, error } = await supabase
      .from('vendors')
      .insert(sampleVendors)
      .select();

    if (error) {
      console.error('🚨 seedSampleVendors: Insert error:', error);
      throw error;
    }

    console.log('🚨 seedSampleVendors: Successfully inserted', data?.length || 0, 'vendors');
    return data;

  } catch (error) {
    console.error('🚨 seedSampleVendors: Error:', error);
    throw error;
  }
};

// Function to check if vendors table exists and create if needed
export const ensureVendorsTable = async () => {
  try {
    console.log('🚨 ensureVendorsTable: Checking vendors table...');
    
    // Try to query the table to see if it exists
    const { data, error } = await supabase
      .from('vendors')
      .select('count(*)')
      .limit(1);

    if (error && error.message.includes('relation "vendors" does not exist')) {
      console.log('🚨 ensureVendorsTable: Vendors table does not exist, needs creation');
      return false;
    }

    console.log('🚨 ensureVendorsTable: Vendors table exists');
    return true;

  } catch (error) {
    console.error('🚨 ensureVendorsTable: Error:', error);
    return false;
  }
};
