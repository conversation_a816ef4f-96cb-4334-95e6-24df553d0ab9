import { supabase } from '../lib/supabase';

// Sample testimonials data for testing homepage functionality
// This adds real data to the mission_fresh.testimonials table (RULE 0001 compliant)
export const seedSampleTestimonials = async () => {
  try {
    console.log('🚨 seedSampleTestimonials: Adding sample testimonials...');
    
    const sampleTestimonials = [
      {
        id: 'testimonial-001',
        user_name: '<PERSON>',
        user_location: 'New York, NY',
        rating: 5,
        testimonial_text: 'Found the cheapest NRT prices in my area. Saved over $50 on my first order!',
        product_used: 'Nicorette Gum',
        quit_success: true,
        days_smoke_free: 127,
        verified_purchase: true,
        featured: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'testimonial-002',
        user_name: '<PERSON>',
        user_location: 'Los Angeles, CA',
        rating: 5,
        testimonial_text: 'The store locator helped me find NRT products when I needed them most during a craving.',
        product_used: 'NicoDerm CQ Patches',
        quit_success: true,
        days_smoke_free: 89,
        verified_purchase: true,
        featured: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'testimonial-003',
        user_name: '<PERSON>.',
        user_location: 'Chicago, IL',
        rating: 5,
        testimonial_text: 'Comprehensive reviews helped me choose the right NRT product for my quit journey.',
        product_used: 'Commit Lozenges',
        quit_success: true,
        days_smoke_free: 203,
        verified_purchase: true,
        featured: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'testimonial-004',
        user_name: 'David K.',
        user_location: 'Houston, TX',
        rating: 4,
        testimonial_text: 'Great price comparison tool. Helped me find deals I never would have found otherwise.',
        product_used: 'Nicorette Inhaler',
        quit_success: true,
        days_smoke_free: 45,
        verified_purchase: true,
        featured: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'testimonial-005',
        user_name: 'Lisa T.',
        user_location: 'Phoenix, AZ',
        rating: 5,
        testimonial_text: 'The vendor reviews were spot on. Ordered from a highly rated vendor and had a great experience.',
        product_used: 'NicoDerm CQ Patches',
        quit_success: true,
        days_smoke_free: 156,
        verified_purchase: true,
        featured: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    // Insert testimonials using upsert to avoid duplicates
    const { data, error } = await supabase
      .from('testimonials')
      .upsert(sampleTestimonials, { onConflict: 'id' })
      .select();

    if (error) {
      console.error('🚨 seedSampleTestimonials: Error inserting testimonials:', error);
      throw error;
    }

    console.log('🚨 seedSampleTestimonials: Successfully added', data?.length || 0, 'testimonials');
    return data;
  } catch (error) {
    console.error('🚨 seedSampleTestimonials: Error:', error);
    throw error;
  }
};
