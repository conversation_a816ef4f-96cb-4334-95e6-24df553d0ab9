import { supabase } from '../lib/supabase';

// Sample deals data for testing deals page functionality
// This adds real data to the mission_fresh.deals table (RULE 0001 compliant)
export const seedSampleDeals = async () => {
  try {
    console.log('🚨 seedSampleDeals: Adding sample deals...');
    
    const sampleDeals = [
      {
        id: 'deal-001',
        vendor_id: 'vendor-001',
        product_id: 'nrt-001',
        title: '25% Off Nicorette Gum - Limited Time',
        description: 'Save big on America\'s #1 nicotine gum. All flavors and strengths included in this special promotion.',
        deal_type: 'percentage_discount',
        discount_value: 25,
        original_price: 24.99,
        sale_price: 18.74,
        savings_amount: 6.25,
        promo_code: 'QUIT25',
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        active: true,
        featured: true,
        terms_conditions: 'Valid on all Nicorette gum products. Cannot be combined with other offers. Limit one per customer.',
        affiliate_link: 'https://example.com/nicorette-deal',
        click_count: 1247,
        conversion_count: 89,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'deal-002',
        vendor_id: 'vendor-002',
        product_id: 'nrt-002',
        title: 'Buy 2 Get 1 Free - NicoDerm CQ Patches',
        description: 'Stock up and save with this incredible buy 2 get 1 free offer on NicoDerm CQ patches.',
        deal_type: 'buy_x_get_y',
        discount_value: 33,
        original_price: 149.97,
        sale_price: 99.98,
        savings_amount: 49.99,
        promo_code: 'B2G1FREE',
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
        active: true,
        featured: true,
        terms_conditions: 'Must add 3 items to cart. Lowest priced item will be free. Valid on 14-day and 21-day patches.',
        affiliate_link: 'https://example.com/nicoderm-deal',
        click_count: 892,
        conversion_count: 67,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'deal-003',
        vendor_id: 'vendor-003',
        product_id: 'nrt-003',
        title: 'Free Shipping + 20% Off Commit Lozenges',
        description: 'Get free shipping plus 20% off all Commit nicotine lozenges. Perfect for discreet nicotine replacement.',
        deal_type: 'percentage_discount',
        discount_value: 20,
        original_price: 34.99,
        sale_price: 27.99,
        savings_amount: 7.00,
        promo_code: 'FREESHIP20',
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days from now
        active: true,
        featured: false,
        terms_conditions: 'Free shipping on orders over $25. 20% discount applied automatically at checkout.',
        affiliate_link: 'https://example.com/commit-deal',
        click_count: 634,
        conversion_count: 45,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'deal-004',
        vendor_id: 'vendor-004',
        product_id: 'nrt-004',
        title: 'Starter Kit Special - 50% Off First Order',
        description: 'New to NRT? Get 50% off your first order with our comprehensive starter kit including gum, patches, and lozenges.',
        deal_type: 'percentage_discount',
        discount_value: 50,
        original_price: 89.99,
        sale_price: 44.99,
        savings_amount: 45.00,
        promo_code: 'NEWUSER50',
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        active: true,
        featured: true,
        terms_conditions: 'Valid for new customers only. One-time use. Includes variety pack of NRT products.',
        affiliate_link: 'https://example.com/starter-kit-deal',
        click_count: 1456,
        conversion_count: 123,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'deal-005',
        vendor_id: 'vendor-005',
        product_id: 'nrt-005',
        title: 'Subscribe & Save - 15% Off Monthly Delivery',
        description: 'Never run out of your NRT products. Subscribe for monthly delivery and save 15% on every order.',
        deal_type: 'subscription_discount',
        discount_value: 15,
        original_price: 29.99,
        sale_price: 25.49,
        savings_amount: 4.50,
        promo_code: 'SUBSCRIBE15',
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year from now
        active: true,
        featured: false,
        terms_conditions: 'Subscription can be cancelled anytime. 15% discount applies to all subscription orders.',
        affiliate_link: 'https://example.com/subscription-deal',
        click_count: 789,
        conversion_count: 156,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    // Insert deals using upsert to avoid duplicates
    const { data, error } = await supabase
      .from('deals')
      .upsert(sampleDeals, { onConflict: 'id' })
      .select();

    if (error) {
      console.error('🚨 seedSampleDeals: Error inserting deals:', error);
      throw error;
    }

    console.log('🚨 seedSampleDeals: Successfully added', data?.length || 0, 'deals');
    return data;
  } catch (error) {
    console.error('🚨 seedSampleDeals: Error:', error);
    throw error;
  }
};
