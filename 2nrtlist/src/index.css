/*
  ======================================================================================
  NRT LIST - ROYAL ELEGANCE DESIGN SYSTEM
  COLORS, FONTS, AND CHARACTER STYLES ONLY
  All sizing, spacing, and layout styles are hard-coded in components
  Worthy of the Queen of England, approved by <PERSON>
  ======================================================================================
*/

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* ===================================================================== */
    /* SINGLE LOGO GREEN - ROYAL ELEGANCE                                   */
    /* One shade only - sophisticated, professional, zen-like               */
    /* ===================================================================== */

    /* Base Colors - Pure Elegance */
    --background: 0 0% 100%;              /* Pure white background */
    --foreground: 220 13% 18%;            /* Sophisticated dark charcoal */

    /* Primary Brand - THE ONLY GREEN - WELLNESS ELEGANCE */
    --wellness-green: 142 55% 58%;        /* Vibrant energetic wellness green - THE ONLY GREEN */
    --logo-green: 142 55% 58%;            /* Same as wellness green */
    --primary: 142 55% 58%;               /* Same as wellness green */
    --primary-foreground: 0 0% 100%;      /* Pure white text on green */

    /* Secondary Colors - Minimal Palette */
    --secondary: 0 0% 100%;               /* Pure white */
    --secondary-foreground: 220 13% 18%;  /* Sophisticated charcoal */

    /* Muted Colors - Refined Grays */
    --muted: 0 0% 99%;                    /* Subtle off-white for cards */
    --muted-foreground: 220 9% 46%;       /* Elegant medium gray */

    /* Accent Colors - Clean & Minimal */
    --accent: 0 0% 98%;                   /* Very subtle gray for hover states */
    --accent-foreground: 220 13% 18%;     /* Sophisticated charcoal */
    
    /* Status Colors - Refined & Purposeful */
    --destructive: 0 65% 51%;             /* Refined red */
    --destructive-foreground: 0 0% 100%;
    --success: 142 55% 58%;               /* Same wellness green */
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;                /* Refined amber */
    --warning-foreground: 0 0% 100%;
    --info: 142 55% 58%;                  /* Same wellness green */
    --info-foreground: 0 0% 100%;
    
    /* Sophisticated Rating App Colors - Premium Quality */
    --rating-gold: 38 75% 58%;            /* Premium gold for ratings & awards */
    --alert-red: 0 65% 56%;               /* Clean red for alerts & warnings */
    
    /* UI Element Colors - Sophisticated */
    --border: 220 13% 91%;                /* Elegant light gray border */
    --input: 0 0% 100%;                   /* Pure white inputs */
    --ring: 152 48% 58%;                  /* Focus ring - logo green */
    --card: 0 0% 100%;                    /* Pure white cards */
    --card-foreground: 220 13% 18%;       /* Sophisticated text */
    --popover: 0 0% 100%;                 /* Pure white popovers */
    --popover-foreground: 220 13% 18%;    /* Sophisticated text */

    /* Chart Colors - Single Green Elegance */
    --chart-1: 152 48% 58%;
    --chart-2: 152 48% 58%;
    --chart-3: 152 48% 58%;
    --chart-4: 152 48% 58%;
    --chart-5: 152 48% 58%;
  }

  /* Sophisticated Footer - Not Blackout */
  .footer-dark {
    --background: 220 13% 15%;            /* Sophisticated dark gray, not black */
    --foreground: 220 13% 91%;            /* Elegant light text */
    --primary: 142 55% 58%;               /* Same wellness green */
    --primary-foreground: 220 13% 15%;    /* Dark text on green */
    --secondary: 220 13% 18%;             /* Subtle dark */
    --secondary-foreground: 220 13% 91%;  /* Light text */
    --muted: 220 13% 18%;                 /* Muted dark */
    --muted-foreground: 220 9% 70%;       /* Muted light */
    --accent: 220 13% 20%;                /* Accent dark */
    --accent-foreground: 220 13% 91%;     /* Light text */
    --border: 220 13% 25%;                /* Visible border */
    --input: 220 13% 18%;                 /* Dark input */
    --card: 220 13% 17%;                  /* Dark card */
    --card-foreground: 220 13% 91%;       /* Light text */
    --popover: 220 13% 15%;               /* Dark popover */
    --popover-foreground: 220 13% 91%;    /* Light text */
  }

  /* ===================================================================== */
  /* ROYAL TYPOGRAPHY - SOPHISTICATED FONT SYSTEM                         */
  /* ===================================================================== */

  * {
    border-color: hsl(var(--border));
  }

  html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-optical-sizing: auto;
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11", "ss01", "ss03";
    font-variant-numeric: proportional-nums;
    font-optical-sizing: auto;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    letter-spacing: -0.008em;
  }

  /* Typography Hierarchy - Royal Elegance */
  h1, h2, h3, h4, h5, h6 {
    color: hsl(var(--foreground));
    font-feature-settings: "cv02", "cv03", "cv04", "cv11", "ss01", "ss03";
    font-variant-numeric: lining-nums;
    text-rendering: optimizeLegibility;
    font-optical-sizing: auto;

  }

  h1 { 
    font-weight: 700; 
    letter-spacing: -0.035em;
  }
  h2 { 
    font-weight: 650; 
    letter-spacing: -0.03em;
  }
  h3 { 
    font-weight: 625; 
    letter-spacing: -0.025em;
  }
  h4 { 
    font-weight: 600; 
    letter-spacing: -0.02em;
  }
  h5 { 
    font-weight: 575; 
    letter-spacing: -0.015em;
  }
  h6 { 
    font-weight: 550; 
    letter-spacing: -0.01em;
  }

  /* Selection - Elegant Highlight */
  ::selection {
    background: hsl(var(--primary) / 0.1);
    color: hsl(var(--foreground));
  }

  /* Focus States - Royal Elegance */
  :focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }
}

/* ===================================================================== */
/* ROYAL TYPOGRAPHY UTILITIES - CHARACTER STYLES ONLY                  */
/* ===================================================================== */

@layer components {
  /* Royal Typography Classes - Character Styles Only */
  .tracking-royal { letter-spacing: -0.025em; }
  .tracking-elegant { letter-spacing: -0.015em; }
  .tracking-refined { letter-spacing: -0.01em; }
  .tracking-sophisticated { letter-spacing: -0.005em; }
  
  /* Font Weight Classes - Royal Hierarchy */
  .font-royal { font-weight: 700; }
  .font-elegant { font-weight: 650; }
  .font-refined { font-weight: 600; }
  .font-sophisticated { font-weight: 575; }
  .font-graceful { font-weight: 550; }
  .font-noble { font-weight: 500; }
  
  /* ===================================================================== */
  /* SOPHISTICATED RATING APP COLOR SYSTEM - MINIMAL & ELEGANT             */
  /* Inspired by premium wine/beer/cigar rating apps - ONE shade per color */
  /* ===================================================================== */
  
  /* PRIMARY - Wellness Green (Main Brand) */
  .bg-wellness { background-color: hsl(var(--wellness-green)); }
  .bg-wellness-50 { background-color: hsl(var(--wellness-green) / 0.05); }
  .bg-wellness-100 { background-color: hsl(var(--wellness-green) / 0.1); }
  .bg-wellness-200 { background-color: hsl(var(--wellness-green) / 0.2); }
  .text-wellness { color: hsl(var(--wellness-green)); }
  .text-wellness-600 { color: hsl(var(--wellness-green) / 0.8); }
  .text-wellness-700 { color: hsl(var(--wellness-green) / 0.9); }
  .border-wellness { border-color: hsl(var(--wellness-green)); }
  .border-wellness-200 { border-color: hsl(var(--wellness-green) / 0.2); }
  .border-wellness-300 { border-color: hsl(var(--wellness-green) / 0.3); }
  
  /* RATING GOLD - Premium Awards & Stars (ONE shade only) */
  .bg-rating-gold { background-color: hsl(var(--rating-gold)); }
  .bg-rating-gold-50 { background-color: hsl(var(--rating-gold) / 0.1); }
  .text-rating-gold { color: hsl(var(--rating-gold)); }
  .border-rating-gold { border-color: hsl(var(--rating-gold)); }
  
  /* ALERT RED - Warnings & Critical Actions (ONE shade only) */
  .bg-alert-red { background-color: hsl(var(--alert-red)); }
  .bg-alert-red-50 { background-color: hsl(var(--alert-red) / 0.1); }
  .text-alert-red { color: hsl(var(--alert-red)); }
  .border-alert-red { border-color: hsl(var(--alert-red)); }
  
  /* Hover States - Consistent Opacity */
  .hover\:bg-wellness:hover { background-color: hsl(var(--wellness-green)); }
  .hover\:bg-wellness-hover:hover { background-color: hsl(var(--wellness-green) / 0.9); }
  .hover\:bg-wellness-50:hover { background-color: hsl(var(--wellness-green) / 0.05); }
  .hover\:text-wellness:hover { color: hsl(var(--wellness-green)); }
  .hover\:text-wellness-700:hover { color: hsl(var(--wellness-green) / 0.9); }
  .hover\:border-wellness-300:hover { border-color: hsl(var(--wellness-green) / 0.3); }
  .hover\:text-rating-gold:hover { color: hsl(var(--rating-gold)); }
}

@layer utilities {
  /* Royal Scrollbar - Visual Only */
  ::-webkit-scrollbar-track {
    background: hsl(var(--background));
  }
  ::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
  }
  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary));
  }
}
