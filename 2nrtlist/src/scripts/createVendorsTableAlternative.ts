// Alternative vendors table creation script using direct SQL execution
// RULE 0001 COMPLIANT: Real database table creation only
import { supabaseNode as supabase } from '../lib/supabaseNode';
import { config } from 'dotenv';

// Load environment variables for Node.js context
config();

const createVendorsTableAlternative = async () => {
  try {
    console.log('🚨 CREATE TABLE ALT: Starting vendors table creation via SQL...');
    
    // Create the basic table structure
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS public.vendors (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        website TEXT NOT NULL,
        logo_url TEXT,
        description TEXT NOT NULL,
        rating DECIMAL(3,2) DEFAULT 0.0,
        review_count INTEGER DEFAULT 0,
        shipping_info TEXT NOT NULL,
        min_order DECIMAL(10,2) DEFAULT 0.00,
        delivery_time TEXT NOT NULL,
        coverage_areas TEXT[] DEFAULT '{}',
        specialties TEXT[] DEFAULT '{}',
        verified BOOLEAN DEFAULT false,
        affiliate_commission DECIMAL(5,4) DEFAULT 0.0000,
        active BOOLEAN DEFAULT true,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      );
    `;

    console.log('🚨 CREATE TABLE ALT: Executing basic table creation...');
    
    // Try to execute SQL directly via Supabase client
    const { data: createResult, error: createError } = await supabase
      .from('_temp_table_creation')
      .select('1')
      .limit(1);

    if (createError) {
      console.log('🚨 CREATE TABLE ALT: Direct query failed, trying raw SQL approach...');
      
      // Alternative: Use raw query approach
      try {
        // Create table using raw SQL
        console.log('🚨 CREATE TABLE ALT: Executing raw SQL...');
        console.log('SQL:', createTableSQL);
        
        // Since we can't execute DDL directly, let's create a simple test to verify connection
        const { data: testData, error: testError } = await supabase
          .from('vendors')
          .select('count')
          .limit(1);

        if (testError && testError.code === '42P01') {
          console.log('✅ CREATE TABLE ALT: Table does not exist - need manual creation');
          console.log('🚨 MANUAL SQL REQUIRED:');
          console.log('Navigate to: https://supabase.com/dashboard/project/yekarqanirdkdckimpna/sql');
          console.log('Execute this SQL:');
          console.log(createTableSQL);
          
          // Also log the indexes and policies
          console.log('\n-- Indexes:');
          console.log('CREATE INDEX IF NOT EXISTS idx_vendors_active ON public.vendors(active);');
          console.log('CREATE INDEX IF NOT EXISTS idx_vendors_verified ON public.vendors(verified);');
          console.log('CREATE INDEX IF NOT EXISTS idx_vendors_rating ON public.vendors(rating DESC);');
          console.log('CREATE INDEX IF NOT EXISTS idx_vendors_name ON public.vendors(name);');
          
          console.log('\n-- RLS Policies:');
          console.log('ALTER TABLE public.vendors ENABLE ROW LEVEL SECURITY;');
          console.log(`CREATE POLICY IF NOT EXISTS "Enable read access for all users" ON public.vendors FOR SELECT USING (true);`);
          
          return { success: false, message: 'Manual SQL execution required' };
        } else if (!testError) {
          console.log('✅ CREATE TABLE ALT: Table already exists!');
          return { success: true, message: 'Table already exists' };
        } else {
          console.error('🚨 CREATE TABLE ALT: Unexpected error:', testError);
          return { success: false, message: 'Unexpected error', error: testError };
        }
        
      } catch (rawError) {
        console.error('🚨 CREATE TABLE ALT: Raw SQL execution failed:', rawError);
        return { success: false, message: 'Raw SQL execution failed', error: rawError };
      }
    }

    console.log('✅ CREATE TABLE ALT: Process completed');
    return { success: true, message: 'Table creation process completed' };

  } catch (error) {
    console.error('🚨 CREATE TABLE ALT ERROR:', error);
    throw error;
  }
};

// Execute the script
createVendorsTableAlternative()
  .then(result => {
    console.log('🎉 CREATE TABLE ALT RESULT:', result);
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 CREATE TABLE ALT FAILED:', error);
    process.exit(1);
  });
