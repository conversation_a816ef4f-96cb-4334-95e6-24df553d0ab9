// <PERSON>ript to create vendors table in Supabase database
// RULE 0001 COMPLIANT: Real database table creation only
import { supabaseNode as supabase } from '../lib/supabaseNode';
import { config } from 'dotenv';
import { readFileSync } from 'fs';
import { join } from 'path';

// Load environment variables for Node.js context
config();

const createVendorsTable = async () => {
  try {
    console.log('🚨 CREATE TABLE: Starting vendors table creation...');
    
    // Read the SQL schema file
    const sqlPath = join(process.cwd(), 'sql', 'create_vendors_table.sql');
    const createTableSQL = readFileSync(sqlPath, 'utf8');
    
    console.log('🚨 CREATE TABLE: Executing SQL schema...');
    console.log('🚨 CREATE TABLE: SQL preview:', createTableSQL.substring(0, 200) + '...');
    
    // Execute the SQL to create the table
    const { data, error } = await supabase.rpc('exec_sql', { 
      sql_query: createTableSQL 
    });

    if (error) {
      console.error('🚨 CREATE TABLE ERROR:', JSON.stringify(error, null, 2));
      
      // Try alternative approach using individual SQL statements
      console.log('🚨 CREATE TABLE: Trying alternative approach...');
      
      const createTableBasic = `
        CREATE TABLE IF NOT EXISTS public.vendors (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          website TEXT NOT NULL,
          logo_url TEXT,
          description TEXT NOT NULL,
          rating DECIMAL(3,2) DEFAULT 0.0,
          review_count INTEGER DEFAULT 0,
          shipping_info TEXT NOT NULL,
          min_order DECIMAL(10,2) DEFAULT 0.00,
          delivery_time TEXT NOT NULL,
          coverage_areas TEXT[] DEFAULT '{}',
          specialties TEXT[] DEFAULT '{}',
          verified BOOLEAN DEFAULT false,
          affiliate_commission DECIMAL(5,4) DEFAULT 0.0000,
          active BOOLEAN DEFAULT true,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
      `;
      
      const { data: altData, error: altError } = await supabase.rpc('exec_sql', {
        sql_query: createTableBasic
      });
      
      if (altError) {
        console.error('🚨 CREATE TABLE ALT ERROR:', JSON.stringify(altError, null, 2));
        throw altError;
      }
      
      console.log('✅ CREATE TABLE: Alternative approach succeeded!');
      return altData;
    }
    
    console.log('✅ CREATE TABLE: Successfully created vendors table!');
    console.log('✅ CREATE TABLE: Response:', JSON.stringify(data, null, 2));
    
    // Verify table was created
    console.log('🚨 CREATE TABLE: Verifying table creation...');
    const { data: verifyData, error: verifyError } = await supabase
      .from('vendors')
      .select('count(*)')
      .limit(1);

    if (verifyError) {
      console.error('🚨 VERIFY ERROR:', JSON.stringify(verifyError, null, 2));
      throw verifyError;
    }
    
    console.log('✅ VERIFY: Vendors table confirmed created and accessible!');
    console.log('✅ VERIFY: Ready for data population');
    
  } catch (error) {
    console.error('❌ CREATE TABLE SCRIPT ERROR:', error);
    throw error;
  }
};

// Execute the table creation script
createVendorsTable();
