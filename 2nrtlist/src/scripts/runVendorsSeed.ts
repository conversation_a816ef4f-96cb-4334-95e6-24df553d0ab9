// Script to run vendors seed data population
// RULE 0001 COMPLIANT: Populates real database data only
import { seedSampleVendors, ensureVendorsTable } from '../utils/seedVendors';
import { config } from 'dotenv';

// Load environment variables for Node.js context
config();

const runVendorsSeed = async () => {
  try {
    console.log('🚨 VENDORS SEED: Starting vendors table population...');
    
    // Check if vendors table exists
    const tableExists = await ensureVendorsTable();
    
    if (!tableExists) {
      console.log('🚨 VENDORS SEED: Vendors table does not exist - cannot proceed');
      console.log('🚨 VENDORS SEED: Please create vendors table in Supabase first');
      return;
    }
    
    console.log('🚨 VENDORS SEED: Vendors table exists, proceeding with data population...');
    
    // Populate vendors data
    const result = await seedSampleVendors();
    
    console.log('✅ VENDORS SEED: Successfully populated vendors table!');
    console.log('✅ VENDORS SEED: Inserted', result?.length || 0, 'vendors');
    console.log('✅ VENDORS SEED: Online Vendors page should now display real data');
    
  } catch (error) {
    console.error('❌ VENDORS SEED: Failed to populate vendors table:', error);
    
    if (error instanceof Error && error.message.includes('relation "vendors" does not exist')) {
      console.log('🚨 VENDORS SEED: Table creation required first');
    }
  }
};

// Execute the seed script
runVendorsSeed();
