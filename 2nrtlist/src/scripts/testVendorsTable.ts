// Debug script to test vendors table structure and permissions
// RULE 0001 COMPLIANT: Real database testing only
import { supabaseNode as supabase } from '../lib/supabaseNode';
import { config } from 'dotenv';

// Load environment variables for Node.js context
config();

const testVendorsTable = async () => {
  try {
    console.log('🚨 TEST: Starting vendors table debugging...');
    
    // Test 1: Check if table exists and get structure
    console.log('🚨 TEST 1: Checking table structure...');
    const { data: tableInfo, error: tableError } = await supabase
      .from('vendors')
      .select('*')
      .limit(1);

    if (tableError) {
      console.error('🚨 TEST 1 ERROR:', JSON.stringify(tableError, null, 2));
      return;
    }
    
    console.log('🚨 TEST 1 SUCCESS: Table exists, current data count:', tableInfo?.length || 0);
    
    // Test 2: Try inserting a minimal record
    console.log('🚨 TEST 2: Attempting minimal insert...');
    const testVendor = {
      id: 'test-vendor-001',
      name: 'Test Vendor',
      website: 'https://test.com',
      description: 'Test vendor for debugging',
      rating: 4.5,
      review_count: 100,
      shipping_info: 'Test shipping',
      min_order: 25.00,
      delivery_time: '3-5 days',
      coverage_areas: ['US'],
      specialties: ['Test'],
      verified: true,
      affiliate_commission: 0.05,
      active: true
    };

    const { data: insertData, error: insertError } = await supabase
      .from('vendors')
      .insert([testVendor])
      .select();

    if (insertError) {
      console.error('🚨 TEST 2 ERROR:', JSON.stringify(insertError, null, 2));
      console.log('🚨 TEST 2: Checking detailed error properties...');
      console.log('🚨 TEST 2: Error code:', insertError.code);
      console.log('🚨 TEST 2: Error message:', insertError.message);
      console.log('🚨 TEST 2: Error details:', insertError.details);
      console.log('🚨 TEST 2: Error hint:', insertError.hint);
      return;
    }
    
    console.log('🚨 TEST 2 SUCCESS: Insert worked!');
    console.log('🚨 TEST 2: Inserted data:', JSON.stringify(insertData, null, 2));
    
    // Test 3: Clean up test data
    console.log('🚨 TEST 3: Cleaning up test data...');
    const { error: deleteError } = await supabase
      .from('vendors')
      .delete()
      .eq('id', 'test-vendor-001');

    if (deleteError) {
      console.error('🚨 TEST 3 ERROR:', JSON.stringify(deleteError, null, 2));
    } else {
      console.log('🚨 TEST 3 SUCCESS: Test data cleaned up');
    }
    
    console.log('✅ DEBUGGING COMPLETE: Table structure and permissions verified');
    
  } catch (error) {
    console.error('❌ TEST SCRIPT ERROR:', error);
  }
};

// Execute the test script
testVendorsTable();
