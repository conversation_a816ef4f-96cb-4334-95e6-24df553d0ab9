import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Star, MapPin, Phone, Globe, Truck, Clock, Tag, Shield, Award, Users, TrendingUp, ShoppingCart, Heart, Share2 } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';

interface Vendor {
  id: string;
  name: string;
  type: 'online' | 'physical' | 'both';
  rating: number;
  reviews: number;
  verified: boolean;
  location?: {
    address: string;
    city: string;
    state: string;
    zip: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  contact: {
    phone?: string;
    website?: string;
    email?: string;
  };
  delivery: {
    available: boolean;
    methods: string[];
    freeThreshold?: number;
    estimatedTime: string;
    coverage: string[];
  };
  discounts: {
    active: boolean;
    code?: string;
    description?: string;
    percentage?: number;
    minOrder?: number;
    expiresAt?: string;
  }[];
  inventory: {
    productId: string;
    productName: string;
    brand: string;
    price: number;
    originalPrice?: number;
    stock: number;
    lastUpdated: string;
    category: string;
  }[];
  badges: string[];
  hours: {
    day: string;
    open: string;
    close: string;
    isOpen?: boolean;
  }[];
  images: string[];
  description: string;
  specialties: string[];
  stats: {
    totalOrders: number;
    avgDeliveryTime: string;
    returnRate: number;
    customerSatisfaction: number;
  };
  socialProof: {
    featuredReviews: {
      id: string;
      username: string;
      rating: number;
      comment: string;
      date: string;
      verified: boolean;
    }[];
    certifications: string[];
    awards: string[];
  };
}

const VendorDetailPage: React.FC = () => {
  const { vendorId } = useParams<{ vendorId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [vendor, setVendor] = useState<Vendor | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isFavorite, setIsFavorite] = useState(false);
  const [cartItems, setCartItems] = useState<Set<string>>(new Set());
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const loadVendor = async () => {
      if (!vendorId) return;
      
      try {
        setLoading(true);
        
        // 🚨 RULE 0001 COMPLIANCE: REAL DATABASE VENDOR DATA ONLY
        const { data: vendorData, error: vendorError } = await supabase
          .from('vendors')
          .select(`
            id,
            name,
            website,
            logo_url,
            description,
            rating,
            review_count,
            verified,
            phone,
            email,
            shipping_info,
            minimum_order_amount,
            specialties,
            coverage_areas,
            affiliate_commission_rate,
            created_at,
            updated_at
          `)
          .eq('id', vendorId)
          .single();

        if (vendorError || !vendorData) {
          console.error('🚨 VendorDetailPage: Vendor not found in database:', vendorError);
          setError('Vendor not found');
          setLoading(false);
          return;
        }

        // Transform database vendor data to component format
        const realVendor: Vendor = {
          id: vendorData.id,
          name: vendorData.name,
          type: 'online', // Default type
          rating: parseFloat(vendorData.rating || '0'),
          reviews: vendorData.review_count || 0,
          verified: vendorData.verified || false,
          contact: {
            phone: vendorData.phone || '',
            website: vendorData.website || '',
            email: vendorData.email || ''
          },
          delivery: {
            available: true,
            methods: ['Standard Shipping'], // Default methods
            freeThreshold: parseFloat(vendorData.minimum_order_amount || '0'),
            estimatedTime: vendorData.shipping_info?.delivery_time || '3-5 business days',
            coverage: vendorData.coverage_areas || ['US']
          },
          discounts: [], // Real discounts would come from deals table
          inventory: [], // Real inventory would come from vendor_prices table
          badges: vendorData.specialties || [],
          hours: [], // Real hours would come from vendor hours table
          images: vendorData.logo_url ? [vendorData.logo_url] : [],
          description: vendorData.description || '',
          specialties: vendorData.specialties || [],
          stats: {
            totalOrders: 0, // Real stats would come from orders table
            avgDeliveryTime: vendorData.shipping_info?.delivery_time || '3-5 days',
            returnRate: 0,
            customerSatisfaction: vendorData.rating || 0
          },
          socialProof: {
            featuredReviews: [], // Real reviews would come from vendor_reviews table
            certifications: vendorData.verified ? ['Verified Merchant'] : [],
            awards: []
          }
        };

        setVendor(realVendor);
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load vendor');
      } finally {
        setLoading(false);
      }
    };

    loadVendor();
  }, [vendorId]);

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: vendor?.name,
          text: vendor?.description,
          url: window.location.href,
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      await navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
    // TODO: Save to user favorites in database
  };

  const toggleCart = (productId: string) => {
    setCartItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(productId)) {
        newSet.delete(productId);
      } else {
        newSet.add(productId);
      }
      return newSet;
    });
  };

  const filteredInventory = vendor?.inventory.filter(item => {
    const matchesSearch = item.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.brand.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  }) || [];

  const categories = [...new Set(vendor?.inventory.map(item => item.category) || [])];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="flex justify-center items-center min-h-[400px]">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !vendor) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Vendor Not Found</h1>
            <p className="text-gray-600 mb-4">{error || 'The vendor you\'re looking for doesn\'t exist.'}</p>
            <button 
              onClick={() => navigate('/vendors')}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              Browse All Vendors
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/vendors')}
                className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="h-5 w-5 mr-1" />
                Back to Vendors
              </button>
              <div className="flex items-center">
                <Shield className="h-8 w-8 text-green-600 mr-2" />
                <span className="text-xl font-bold text-gray-900">NRTList</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={toggleFavorite}
                className={`p-2 rounded-full ${
                  isFavorite ? 'bg-red-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <Heart className="h-5 w-5" />
              </button>
              <button
                onClick={handleShare}
                className="p-2 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200"
              >
                <Share2 className="h-5 w-5" />
              </button>
              <div className="relative">
                <ShoppingCart className="h-6 w-6 text-gray-600" />
                {cartItems.size > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {cartItems.size}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Vendor Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid md:grid-cols-3 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-3 mb-3">
                {vendor.verified && (
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium">
                    ✓ Verified Vendor
                  </span>
                )}
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm capitalize">
                  {vendor.type} Store
                </span>
              </div>
              
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{vendor.name}</h1>
              <p className="text-gray-600 mb-4">{vendor.description}</p>
              
              <div className="flex items-center space-x-6 mb-4">
                <div className="flex items-center space-x-1">
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                  <span className="text-xl font-semibold">{vendor.rating}</span>
                  <span className="text-gray-500">({vendor.reviews} reviews)</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Users className="h-5 w-5 text-gray-500" />
                  <span className="text-gray-600">{vendor.stats.totalOrders.toLocaleString()} orders</span>
                </div>
                <div className="flex items-center space-x-1">
                  <TrendingUp className="h-5 w-5 text-gray-500" />
                  <span className="text-gray-600">{vendor.stats.customerSatisfaction}% satisfaction</span>
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                {vendor.badges.map((badge, index) => (
                  <span 
                    key={index}
                    className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm"
                  >
                    {badge}
                  </span>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold mb-3">Contact Information</h3>
                <div className="space-y-2">
                  {vendor.contact.phone && (
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{vendor.contact.phone}</span>
                    </div>
                  )}
                  {vendor.contact.website && (
                    <div className="flex items-center space-x-2">
                      <Globe className="h-4 w-4 text-gray-500" />
                      <a href={vendor.contact.website} className="text-sm text-green-600 hover:underline">
                        Visit Website
                      </a>
                    </div>
                  )}
                  {vendor.location && (
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{vendor.location.city}, {vendor.location.state}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold mb-3">Delivery Info</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Truck className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{vendor.delivery.estimatedTime}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">Avg: {vendor.stats.avgDeliveryTime}</span>
                  </div>
                  {vendor.delivery.freeThreshold && (
                    <div className="text-sm text-green-600">
                      Free shipping over ${vendor.delivery.freeThreshold}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Active Discounts */}
      {vendor.discounts.some(d => d.active) && (
        <div className="bg-yellow-50 border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <h3 className="font-semibold text-yellow-800 mb-2">Active Discounts</h3>
            <div className="grid md:grid-cols-2 gap-4">
              {vendor.discounts.filter(d => d.active).map((discount, index) => (
                <div key={index} className="bg-white rounded-lg p-4 border border-yellow-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="font-mono bg-yellow-100 px-2 py-1 rounded text-sm">
                        {discount.code}
                      </span>
                      <p className="text-sm text-gray-600 mt-1">{discount.description}</p>
                    </div>
                    <div className="text-right">
                      <span className="text-lg font-bold text-yellow-600">{discount.percentage}% OFF</span>
                      {discount.minOrder && (
                        <p className="text-xs text-gray-500">Min order: ${discount.minOrder}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'products', label: `Products (${vendor.inventory.length})` },
              { id: 'reviews', label: `Reviews (${vendor.reviews})` },
              { id: 'info', label: 'Store Info' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Products Tab */}
        {activeTab === 'products' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Available Products</h2>
              <div className="flex items-center space-x-4">
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-600 focus:border-transparent"
                />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-600 focus:border-transparent"
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category} className="capitalize">
                      {category}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredInventory.map(item => (
                <div key={item.productId} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <Link 
                          to={`/product/${item.productId}`}
                          className="text-lg font-semibold text-gray-900 hover:text-green-600"
                        >
                          {item.productName}
                        </Link>
                        <p className="text-sm text-gray-600">{item.brand}</p>
                      </div>
                      <button
                        onClick={() => toggleCart(item.productId)}
                        className={`p-2 rounded-full ${
                          cartItems.has(item.productId) 
                            ? 'bg-green-500 text-white' 
                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                        }`}
                      >
                        <ShoppingCart className="h-4 w-4" />
                      </button>
                    </div>
                    
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg font-bold text-green-600">${item.price}</span>
                        {item.originalPrice && (
                          <span className="text-sm text-gray-500 line-through">${item.originalPrice}</span>
                        )}
                      </div>
                      <span className={`text-sm px-2 py-1 rounded ${
                        item.stock > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {item.stock > 0 ? `${item.stock} in stock` : 'Out of stock'}
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">
                        Updated: {new Date(item.lastUpdated).toLocaleDateString()}
                      </span>
                      <Link
                        to={`/product/${item.productId}`}
                        className="text-sm text-green-600 hover:underline"
                      >
                        View Details
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Other tabs content would go here */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4">Key Statistics</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Orders</span>
                    <span className="font-semibold">{vendor.stats.totalOrders.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Average Delivery</span>
                    <span className="font-semibold">{vendor.stats.avgDeliveryTime}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Return Rate</span>
                    <span className="font-semibold">{vendor.stats.returnRate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Customer Satisfaction</span>
                    <span className="font-semibold">{vendor.stats.customerSatisfaction}%</span>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4">Specialties</h3>
                <div className="flex flex-wrap gap-2">
                  {vendor.specialties.map((specialty, index) => (
                    <span 
                      key={index}
                      className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                    >
                      {specialty}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default VendorDetailPage;
