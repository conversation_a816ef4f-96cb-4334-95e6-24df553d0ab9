import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Star, Award, Users, Sparkles, Grid, List, Filter, Menu, X, Package } from 'lucide-react';
import { getProducts } from '../lib/supabase';
import Rating from '../components/Rating';

interface Product {
  id: string;
  name: string;
  brand: string;
  category: string;
  description: string;
  image_url: string;
  user_rating_avg: number | null;
  user_rating_count: number | null;
  nicotine_strengths: string[] | null;
  flavors: string[] | null;
  is_verified: boolean | null;
  created_at: string;
  updated_at: string;
}

const BrandPage: React.FC = () => {
  const { brand } = useParams<{ brand: string }>();
  const navigate = useNavigate();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState('rating');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Decode brand name from URL
  const brandName = brand ? decodeURIComponent(brand).replace(/[+]/g, ' ') : '';

  useEffect(() => {
    const loadBrandProducts = async () => {
      try {
        const allProducts = await getProducts();
        const brandProducts = allProducts?.filter(product => 
          product.brand.toLowerCase() === brandName.toLowerCase()
        ) || [];
        setProducts(brandProducts);
        setError(null);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to load brand products');
      } finally {
        setLoading(false);
      }
    };
    if (brandName) {
      loadBrandProducts();
    }
  }, [brandName]);

  // Sophisticated sorting
  const sortedProducts = [...products].sort((a, b) => {
    switch (sortBy) {
      case 'rating':
        return (b.user_rating_avg || 0) - (a.user_rating_avg || 0);
      case 'name':
        return a.name.localeCompare(b.name);
      case 'reviews':
        return (b.user_rating_count || 0) - (a.user_rating_count || 0);
      case 'newest':
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      default:
        return 0;
    }
  });

  // Calculate brand statistics
  const avgRating = products.length > 0 
    ? products.reduce((sum, p) => sum + (p.user_rating_avg || 0), 0) / products.length 
    : 0;
  const totalReviews = products.reduce((sum, p) => sum + (p.user_rating_count || 0), 0);
  const topRatedCount = products.filter(p => (p.user_rating_avg || 0) >= 4.5).length;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-20 h-20 bg-wellness rounded-3xl flex items-center justify-center animate-pulse mx-auto mb-6">
            <Sparkles className="w-10 h-10 text-white" />
          </div>
          <p className="text-wellness font-medium text-lg">Loading {brandName} collection...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-3xl p-10 shadow-2xl border border-gray-100 max-w-md text-center">
          <div className="w-20 h-20 bg-red-50 rounded-3xl flex items-center justify-center mx-auto mb-6">
            <Award className="w-10 h-10 text-red-500" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Unable to Load Brand</h2>
          <p className="text-gray-600 mb-8">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="bg-wellness text-white px-8 py-4 rounded-2xl hover:bg-wellness-hover transition-all duration-200 font-semibold shadow-lg"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Apple-style Main Navigation */}
      <nav className="sticky top-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center gap-3">
              <div className="w-10 h-10 bg-wellness rounded-2xl flex items-center justify-center">
                <Package className="w-6 h-6 text-white" strokeWidth={2} />
              </div>
              <span className="text-xl font-bold text-gray-900">NRTList</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-8">
              <Link to="/" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Marketplace
              </Link>
              <Link to="/products" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Compare
              </Link>
              <Link to="/retailers" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Retailers
              </Link>
              <Link to="/community" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Community
              </Link>
              <Link to="/progress" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Progress
              </Link>
            </div>

            {/* Auth Buttons */}
            <div className="hidden md:flex items-center gap-4">
              <button className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Sign In
              </button>
              <button className="bg-wellness text-white px-6 py-2 rounded-xl hover:bg-wellness-hover font-medium transition-all duration-200">
                Sign Up
              </button>
            </div>

            {/* Mobile Menu Button */}
            <button 
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-t border-gray-200">
            <div className="px-4 py-4 space-y-4">
              <Link to="/" className="block text-gray-700 font-medium">Marketplace</Link>
              <Link to="/products" className="block text-gray-700 font-medium">Compare</Link>
              <Link to="/retailers" className="block text-gray-700 font-medium">Retailers</Link>
              <Link to="/community" className="block text-gray-700 font-medium">Community</Link>
              <Link to="/progress" className="block text-gray-700 font-medium">Progress</Link>
              <div className="pt-4 border-t border-gray-200 space-y-3">
                <button className="block w-full text-left text-gray-700 font-medium">Sign In</button>
                <button className="block w-full bg-wellness text-white px-4 py-2 rounded-xl font-medium">Sign Up</button>
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Secondary Navigation Header */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 sticky top-16 z-40">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-6">
              <button
                onClick={() => navigate('/products')}
                className="flex items-center text-gray-600 hover:text-wellness transition-colors duration-200 group"
              >
                <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-200" />
                <span className="font-medium">Products</span>
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-bold text-gray-900">{brandName}</h1>
            </div>
            <div className="flex items-center gap-3">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-2 border border-gray-200 rounded-xl bg-white/70 backdrop-blur focus:ring-2 focus:ring-wellness focus:border-transparent text-sm font-medium"
              >
                <option value="rating">Highest Rated</option>
                <option value="name">A to Z</option>
                <option value="reviews">Most Reviewed</option>
                <option value="newest">Newest</option>
              </select>
              <button
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="p-2 rounded-xl bg-gray-100/70 hover:bg-gray-200/70 transition-colors backdrop-blur"
              >
                {viewMode === 'grid' ? <List className="w-5 h-5" /> : <Grid className="w-5 h-5" />}
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Apple-style Hero Section */}
      <div className="bg-gradient-to-br from-white via-gray-50 to-gray-100">
        <div className="max-w-7xl mx-auto px-6 py-16">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-3 bg-wellness-50 px-6 py-3 rounded-full mb-6">
              <Sparkles className="w-5 h-5 text-wellness" />
              <span className="text-wellness font-semibold">Premium Brand</span>
            </div>
            <h1 className="text-5xl font-bold text-gray-900 mb-6 tracking-tight">
              {brandName}
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Discover the complete collection of premium {brandName} products, 
              rated and reviewed by our sophisticated community.
            </p>
          </div>

          {/* Apple-style Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-gray-200/50 text-center">
              <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-wellness" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">
                {avgRating > 0 ? avgRating.toFixed(1) : 'N/A'}
              </div>
              <div className="text-gray-600 font-medium">Average Rating</div>
            </div>
            
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-gray-200/50 text-center">
              <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-wellness" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">
                {totalReviews.toLocaleString()}
              </div>
              <div className="text-gray-600 font-medium">Total Reviews</div>
            </div>
            
            <div className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-gray-200/50 text-center">
              <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Award className="w-8 h-8 text-wellness" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">
                {topRatedCount}
              </div>
              <div className="text-gray-600 font-medium">Top Rated Products</div>
            </div>
          </div>
        </div>
      </div>

      {/* Apple-style Product Grid */}
      <div className="max-w-7xl mx-auto px-6 pb-16">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-gray-900">
            All {brandName} Products
            <span className="ml-3 text-wellness font-normal">({products.length})</span>
          </h2>
        </div>

        {products.length > 0 ? (
          <div className={viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
            : 'space-y-6'
          }>
            {sortedProducts.map(product => (
              <Link
                key={product.id}
                to={`/product/${product.id}/${product.name.toLowerCase().replace(/\s+/g, '-')}`}
                className={`group bg-white rounded-3xl shadow-lg border border-gray-200/50 hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 overflow-hidden ${
                  viewMode === 'list' ? 'flex items-center p-8' : 'p-8'
                }`}
              >
                {viewMode === 'grid' ? (
                  <div>
                    <div className="flex items-start justify-between mb-6">
                      <div className="flex-1">
                        <h3 className="font-bold text-gray-900 text-xl mb-2 group-hover:text-wellness transition-colors">
                          {product.name}
                        </h3>
                        <p className="text-wellness font-semibold text-lg">{product.brand}</p>
                        <div className="inline-flex items-center gap-2 bg-gray-100 px-3 py-1 rounded-full mt-2">
                          <span className="text-sm font-medium text-gray-700">{product.category}</span>
                        </div>
                      </div>
                      {product.is_verified && (
                        <div className="bg-wellness-50 p-3 rounded-2xl">
                          <Award className="w-5 h-5 text-wellness" />
                        </div>
                      )}
                    </div>
                    
                    <p className="text-gray-600 mb-6 line-clamp-3 leading-relaxed">{product.description}</p>
                    
                    {product.user_rating_avg && (
                      <Rating 
                        product={product}
                        size="compact"
                      />
                    )}
                  </div>
                ) : (
                  <div className="flex items-center gap-8 w-full">
                    <div className="flex-1">
                      <h3 className="font-bold text-gray-900 text-xl mb-2 group-hover:text-wellness transition-colors">
                        {product.name}
                      </h3>
                      <p className="text-wellness font-semibold">{product.brand} • {product.category}</p>
                    </div>
                    {product.user_rating_avg && (
                      <Rating 
                        product={product}
                        size="compact"
                      />
                    )}
                  </div>
                )}
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-20">
            <div className="w-32 h-32 bg-gray-100 rounded-3xl flex items-center justify-center mx-auto mb-8">
              <Filter className="w-16 h-16 text-gray-400" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">No {brandName} Products Found</h3>
            <p className="text-gray-500 mb-8 max-w-md mx-auto">
              This brand doesn't have any products in our database yet. Check back soon for updates.
            </p>
            <Link
              to="/products"
              className="inline-flex items-center bg-wellness text-white px-8 py-4 rounded-2xl hover:bg-wellness-hover transition-all duration-200 font-semibold shadow-lg"
            >
              Browse All Products
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default BrandPage;
