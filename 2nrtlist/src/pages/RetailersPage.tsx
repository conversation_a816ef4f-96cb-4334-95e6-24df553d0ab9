import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Package, Store, Star, MapPin, Phone, Globe, ExternalLink, Menu, X } from 'lucide-react';
import { getRetailers, Retailer } from '../lib/supabase';

const RetailersPage: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [retailers, setRetailers] = useState<Retailer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadRetailers = async () => {
      try {
        setLoading(true);
        const retailersData = await getRetailers();
        setRetailers(retailersData || []);
        setError(null);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to load retailers');
      } finally {
        setLoading(false);
      }
    };
    loadRetailers();
  }, []);
  return (
    <div className="min-h-screen bg-white">
      {/* Apple-style Navigation Header */}
      <nav className="sticky top-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center gap-3">
              <div className="w-10 h-10 bg-wellness rounded-2xl flex items-center justify-center">
                <Package className="w-6 h-6 text-white" strokeWidth={2} />
              </div>
              <span className="text-xl font-bold text-gray-900">NRTList</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-8">
              <Link to="/" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Directory
              </Link>
              <Link to="/products" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Products
              </Link>
              <Link to="/stores" className="text-wellness font-medium">
                Find Stores
              </Link>
              <Link to="/deals" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Best Deals
              </Link>
              <Link to="/reviews" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Reviews
              </Link>
            </div>

            {/* Auth Buttons */}
            <div className="hidden md:flex items-center gap-4">
              <button className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Sign In
              </button>
              <button className="bg-wellness text-white px-6 py-2 rounded-xl hover:bg-wellness-hover font-medium transition-all duration-200">
                Sign Up
              </button>
            </div>

            {/* Mobile Menu Button */}
            <button 
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-t border-gray-200">
            <div className="px-4 py-4 space-y-4">
              <Link to="/" className="block text-gray-700 font-medium">Directory</Link>
              <Link to="/products" className="block text-gray-700 font-medium">Products</Link>
              <Link to="/stores" className="block text-wellness font-medium">Find Stores</Link>
              <Link to="/deals" className="block text-gray-700 font-medium">Best Deals</Link>
              <Link to="/reviews" className="block text-gray-700 font-medium">Reviews</Link>
              <div className="pt-4 border-t border-gray-200 space-y-3">
                <button className="block w-full text-left text-gray-700 font-medium">Sign In</button>
                <button className="block w-full bg-wellness text-white px-4 py-2 rounded-xl font-medium">Sign Up</button>
              </div>
            </div>
          </div>
        )}
      </nav>

      <div className="bg-gray-50">
      {/* Apple-style Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="inline-flex items-center gap-3 bg-wellness-50 px-6 py-3 rounded-full mb-6">
              <Store className="w-5 h-5 text-wellness" />
              <span className="text-wellness font-semibold">Verified Retailers</span>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Trusted Retail Partners
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Shop with confidence from our curated network of verified NRT retailers
            </p>
          </div>
        </div>
      </header>

      {/* Retailers Grid */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-16">
            <div className="w-8 h-8 border-4 border-wellness border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-3 text-gray-600">Loading retailers...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-2xl p-6 mb-8">
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">!</span>
              </div>
              <p className="text-red-700 font-medium">{error}</p>
            </div>
          </div>
        )}

        {/* Retailers Data */}
        {!loading && !error && (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {retailers.map((retailer) => (
              <div key={retailer.id} className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 bg-wellness-50 rounded-2xl flex items-center justify-center">
                    <Store className="w-8 h-8 text-wellness" strokeWidth={2} />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900">{retailer.name}</h3>
                    {retailer.rating_avg && (
                      <div className="flex items-center gap-2">
                        <Star className="w-5 h-5 text-yellow-400 fill-current" />
                        <span className="text-lg font-semibold text-gray-700">{retailer.rating_avg}</span>
                        <span className="text-gray-500">({retailer.rating_count?.toLocaleString() || 0} reviews)</span>
                      </div>
                    )}
                  </div>
                </div>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {retailer.description || 'Verified NRT retailer providing quality products and services.'}
                </p>
                <div className="space-y-3 mb-8">
                  {retailer.locations_count && (
                    <div className="flex items-center gap-3 text-gray-600">
                      <MapPin className="w-5 h-5 text-wellness" />
                      <span>{retailer.locations_count.toLocaleString()}+ locations nationwide</span>
                    </div>
                  )}
                  {retailer.phone && (
                    <div className="flex items-center gap-3 text-gray-600">
                      {retailer.phone.includes('http') ? (
                        <Globe className="w-5 h-5 text-wellness" />
                      ) : (
                        <Phone className="w-5 h-5 text-wellness" />
                      )}
                      <span>{retailer.phone}</span>
                    </div>
                  )}
                </div>
                <button 
                  onClick={() => retailer.website_url && window.open(retailer.website_url, '_blank')}
                  className="w-full bg-wellness text-white py-4 px-6 rounded-2xl hover:bg-wellness-hover transition-all duration-200 font-semibold flex items-center justify-center gap-2"
                >
                  <ExternalLink className="w-5 h-5" />
                  Visit Store
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Benefits Section */}
        <section className="mt-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Why Shop with Our Partners?
            </h2>
            <p className="text-lg text-gray-600">
              All retail partners are carefully vetted for quality and reliability
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-wellness" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Verified Quality</h3>
              <p className="text-gray-600">
                All products are authentic and sourced directly from manufacturers
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Store className="w-8 h-8 text-wellness" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Expert Support</h3>
              <p className="text-gray-600">
                Licensed pharmacists available for consultation and guidance
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <MapPin className="w-8 h-8 text-wellness" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Convenient Access</h3>
              <p className="text-gray-600">
                Multiple locations and delivery options for easy accessibility
              </p>
            </div>
          </div>
        </section>
      </main>
      </div>
    </div>
  );
};

export default RetailersPage;
