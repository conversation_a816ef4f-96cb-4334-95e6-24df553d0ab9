import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Star, Heart, Share2, ShoppingCart, MapPin, Truck, Tag, Award, Clock, Shield, Users, TrendingUp, AlertTriangle } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { getProductById } from '../lib/supabase';
import { supabase } from '../lib/supabase';
import ReviewSystem from '../components/ReviewSystem';
import VendorList from '../components/VendorList';
import StoreLocator from '../components/StoreLocator';
import PriceComparisonSystem from '../components/PriceComparisonSystem';
import DealsSystem from '../components/DealsSystem';
import SocialSystem from '../components/SocialSystem';

interface Product {
  id: string;
  name: string;
  description: string;
  image_url?: string;
  brand: string;
  category: string;
  nicotine_strengths?: any; // jsonb field from database
  flavors?: string[];
  user_rating_avg?: number;
  user_rating_count?: number;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
}

interface Review {
  id: string;
  user_id: string;
  username: string;
  rating: number;
  review: string;
  created_at: string;
  helpful_count: number;
  verified_purchase: boolean;
  parameters?: {
    effectiveness: number;
    sideEffects: number;
    easeOfUse: number;
    taste: number;
    adherence: number;
    value: number;
    nicotineDelivery: number;
  };
}

const ProductDetailPage: React.FC = () => {
  const { id: productId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [product, setProduct] = useState<Product | null>(null);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isFavorite, setIsFavorite] = useState(false);
  const [userRating, setUserRating] = useState<number | null>(null);

  useEffect(() => {
    const loadProduct = async () => {
      if (!productId) {
        console.error('ProductDetailPage: No product ID provided');
        return;
      }
      
      console.log('ProductDetailPage: Loading product with ID:', productId);
      
      try {
        setLoading(true);
        console.log('ProductDetailPage: Calling getProductById...');
        const productData = await getProductById(productId);
        console.log('ProductDetailPage: Product data received:', productData);
        setProduct(productData);
        
        // Load real reviews from database
        const { data: reviewsData, error: reviewsError } = await supabase
          .from('nrt_product_reviews')
          .select('*')
          .eq('product_id', productId)
          .order('created_at', { ascending: false });

        if (reviewsError) {
          console.error('Error loading reviews:', reviewsError);
          setReviews([]);
        } else {
          setReviews(reviewsData || []);
        }
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load product');
      } finally {
        setLoading(false);
      }
    };

    loadProduct();
  }, [productId]);

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product?.name,
          text: product?.description,
          url: window.location.href,
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      // Fallback: copy to clipboard
      await navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
    // TODO: Save to user favorites in database
  };

  const ratingParameters = [
    { key: 'effectiveness', label: 'Effectiveness', description: 'How well does it reduce cravings?' },
    { key: 'sideEffects', label: 'Side Effects', description: 'Minimal side effects?' },
    { key: 'easeOfUse', label: 'Ease of Use', description: 'How easy is it to use?' },
    { key: 'taste', label: 'Taste/Flavor', description: 'How does it taste?' },
    { key: 'adherence', label: 'Adherence', description: 'Easy to stick with?' },
    { key: 'value', label: 'Value', description: 'Good value for money?' },
    { key: 'nicotineDelivery', label: 'Nicotine Delivery', description: 'Consistent nicotine delivery?' }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="flex flex-col justify-center items-center min-h-[400px]">
            <div className="w-16 h-16 bg-wellness rounded-2xl flex items-center justify-center animate-pulse">
              <Shield className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
            <p className="text-gray-600 mb-4">
              {error && typeof error === 'string' ? error : 'The product you\'re looking for doesn\'t exist.'}
            </p>
            <div className="text-sm text-gray-500 mb-4">
              Debug info: Product ID = {productId || 'undefined'}
            </div>
            <button 
              onClick={() => navigate('/')}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              Go Back Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Product loaded with real NRT data structure - no safety check needed

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/')}
                className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="h-5 w-5 mr-1" />
                Back to Products
              </button>
              <div className="flex items-center">
                <Shield className="h-8 w-8 text-green-600 mr-2" />
                <span className="text-xl font-bold text-gray-900">NRTList</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={toggleFavorite}
                className={`p-2 rounded-full ${
                  isFavorite ? 'bg-red-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <Heart className="h-5 w-5" />
              </button>
              <button
                onClick={handleShare}
                className="p-2 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200"
              >
                <Share2 className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Product Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                  {product.category}
                </span>
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                  {product.brand}
                </span>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
              <p className="text-gray-600 mb-4">{product.description}</p>
              
              <div className="flex items-center space-x-4 mb-4">
                <div className="flex items-center space-x-1">
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                  <span className="text-xl font-semibold">{product.user_rating_avg || 0}</span>
                  <span className="text-gray-500">({product.user_rating_count || 0} reviews)</span>
                </div>
                <div className="text-2xl font-bold text-green-600">
                  Contact for pricing
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-6">
                <div>
                  <span className="text-sm text-gray-500">Strength</span>
                  <p className="font-semibold">
                    {(() => {
                      if (!product.nicotine_strengths) return 'Various strengths';

                      if (Array.isArray(product.nicotine_strengths)) {
                        return product.nicotine_strengths
                          .map(s => {
                            if (typeof s === 'object' && s.value && s.unit) {
                              return `${s.value}${s.unit}`;
                            }
                            return s ? s.toString() : '';
                          })
                          .filter(s => s)
                          .join(', ') || 'Various strengths';
                      }

                      if (typeof product.nicotine_strengths === 'object') {
                        if (product.nicotine_strengths.value && product.nicotine_strengths.unit) {
                          return `${product.nicotine_strengths.value}${product.nicotine_strengths.unit}`;
                        }
                        const values = Object.values(product.nicotine_strengths)
                          .filter(v => v && typeof v !== 'object')
                          .map(v => (v as string | number).toString())
                          .filter(v => v && v !== 'null' && v !== 'undefined');
                        return values.length > 0 ? values.join(', ') : 'Various strengths';
                      }

                      return product.nicotine_strengths.toString();
                    })()}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500">Flavor</span>
                  <p className="font-semibold">{product.flavors?.join(', ') || 'Multiple flavors'}</p>
                </div>
              </div>
            </div>

            <div>
              <div className="bg-gray-50 rounded-2xl p-6 h-96 flex items-center justify-center overflow-hidden">
                {product.image_url ? (
                  <img
                    src={product.image_url}
                    alt={product.name}
                    className="w-full h-full object-cover rounded-xl"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling.style.display = 'flex';
                    }}
                  />
                ) : null}
                <div
                  className="w-full h-full flex items-center justify-center"
                  style={{ display: product.image_url ? 'none' : 'flex' }}
                >
                  <div className="text-center">
                    <Shield className="h-20 w-20 text-wellness mx-auto mb-4" />
                    <p className="text-gray-600 font-medium">{product.name}</p>
                    <p className="text-gray-500 text-sm">Product Image</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'reviews', label: `Reviews (${reviews.length})` },
              { id: 'prices', label: 'Price Comparison' },
              { id: 'deals', label: 'Deals & Discounts' },
              { id: 'vendors', label: 'Where to Buy' },
              { id: 'stores', label: 'Store Locator' },
              { id: 'social', label: 'Community' },
              { id: 'compare', label: 'Compare' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Product Rating Breakdown</h2>
              <div className="grid md:grid-cols-2 gap-6">
                {ratingParameters.map(param => (
                  <div key={param.key} className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-700">{param.label}</span>
                      <span className="text-sm font-semibold">
                        N/A
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ 
                          width: `0%` 
                        }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500">{param.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Reviews Tab */}
        {activeTab === 'reviews' && (
          <div className="space-y-6">
            <ReviewSystem 
              productId={product.id} 
              productName={product.name} 
              productType={product.category}
            />
          </div>
        )}

        {/* Price Comparison Tab - Advanced affiliate monetization like Vivino */}
        {activeTab === 'prices' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Price Comparison</h2>
                  <p className="text-gray-600 mt-1">Find the best deals from verified vendors</p>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Shield className="h-4 w-4" />
                  <span>Affiliate partnerships disclosed</span>
                </div>
              </div>
              <PriceComparisonSystem 
                productId={product.id}
                category={product.category}
              />
            </div>
          </div>
        )}

        {/* Deals Tab - Advanced discount system like Weedmaps */}
        {activeTab === 'deals' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Deals & Discounts</h2>
                  <p className="text-gray-600 mt-1">Exclusive offers and promo codes for {product.name}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Tag className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium text-green-600">Live Deals Available</span>
                </div>
              </div>
              <DealsSystem 
                category={product.category}
                productId={product.id}
              />
            </div>
          </div>
        )}

        {/* Vendors Tab */}
        {activeTab === 'vendors' && (
          <div className="space-y-6">
            <VendorList 
              productId={product.id} 
              category={product.category}
            />
          </div>
        )}

        {/* Stores Tab */}
        {activeTab === 'stores' && (
          <div className="space-y-6">
            <StoreLocator 
              productId={product.id} 
              category={product.category}
            />
          </div>
        )}

        {/* Social Tab - Advanced community features like Untappd */}
        {activeTab === 'social' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Community & Social</h2>
                  <p className="text-gray-600 mt-1">Check-ins, achievements, and community insights for {product.name}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-600">Join the Community</span>
                </div>
              </div>
              <SocialSystem 
                userId={user?.id}
                productId={product.id}
              />
            </div>
          </div>
        )}

        {/* Compare Tab */}
        {activeTab === 'compare' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Compare Similar Products</h2>
              <p className="text-gray-600 mb-4">
                Compare this product with similar {product.category} products.
              </p>
              <Link 
                to={`/compare?products=${product.id}`}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                Start Comparison
              </Link>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default ProductDetailPage;
