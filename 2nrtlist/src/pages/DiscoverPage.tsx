import React from 'react';
import { <PERSON>, Zap, <PERSON>an<PERSON><PERSON>, <PERSON>hai<PERSON> } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

// Self-contained SEO-optimized DiscoverPage
const DiscoverPage: React.FC = () => {
  const navigate = useNavigate();
  return (
    <div className="space-y-12" data-tab-content="discover">
      {/* Royal Hero Section - Elegant & Sophisticated */}
      <div className="bg-white rounded-2xl border border-gray-200 shadow-lg p-16 text-center">
        <div className="max-w-4xl mx-auto">
          {/* Elegant Search Icon */}
          <div className="mx-auto w-20 h-20 bg-green-50 rounded-full flex items-center justify-center mb-8">
            <Search className="w-10 h-10 text-primary" />
          </div>

          {/* Royal Typography */}
          <h1 className="text-5xl font-bold text-gray-900 mb-6 leading-tight tracking-tight">
            Smart NRT Discovery
          </h1>
          
          <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed font-refined">
            Scan any nicotine pouch, snus, or NRT product for instant access to strength ratings,
            flavor profiles, vendor prices, and community reviews. Find your perfect cessation companion.
          </p>

          {/* Elegant Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button
              onClick={() => {
                // SEO-optimized navigation to marketplace
                navigate('/marketplace');
              }}
              className="bg-primary hover:bg-primary/90 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-3"
            >
              <ScanLine className="w-5 h-5" />
              Scan Product Label
            </button>
            
            <button
              onClick={() => {
                // SEO-optimized navigation to search/catalog
                navigate('/search');
              }}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-8 py-4 rounded-xl font-semibold transition-all duration-200 border border-gray-200 hover:border-gray-300 flex items-center gap-3"
            >
              <Search className="w-5 h-5" />
              Browse Catalog
            </button>
          </div>
        </div>
      </div>

      {/* Key Features - Royal Elegance */}
      <div className="grid md:grid-cols-3 gap-8">
        <div className="text-center group">
          <div className="w-16 h-16 bg-green-50 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-green-100 transition-colors duration-200">
            <Zap className="w-8 h-8 text-primary" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-4">Instant Recognition</h3>
          <p className="text-gray-600 leading-relaxed">
            Advanced AI-powered product identification for immediate access to comprehensive product data and community insights.
          </p>
        </div>

        <div className="text-center group">
          <div className="w-16 h-16 bg-green-50 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-green-100 transition-colors duration-200">
            <Search className="w-8 h-8 text-primary" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-4">Smart Discovery</h3>
          <p className="text-gray-600 leading-relaxed">
            Intelligent recommendation engine that learns your preferences to suggest the most suitable NRT products for your journey.
          </p>
        </div>

        <div className="text-center group">
          <div className="w-16 h-16 bg-green-50 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-green-100 transition-colors duration-200">
            <Crosshair className="w-8 h-8 text-primary" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-4">Precision Matching</h3>
          <p className="text-gray-600 leading-relaxed">
            Sophisticated matching algorithm considering strength, flavor, format, and effectiveness to find your ideal cessation companion.
          </p>
        </div>
      </div>
    </div>
  );
};

export default DiscoverPage;
