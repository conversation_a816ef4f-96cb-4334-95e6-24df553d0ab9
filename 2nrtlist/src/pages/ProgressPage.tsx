import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Target, Calendar, TrendingUp, Award, Clock, Activity, Heart, DollarSign, Package, Menu, X } from 'lucide-react';
import { getUserProgress, getHealthBenefits, getMilestones, UserProgress, HealthBenefit, Milestone } from '../lib/progressApi';

const ProgressPage: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [userProgress, setUserProgress] = useState<UserProgress | null>(null);
  const [healthBenefits, setHealthBenefits] = useState<HealthBenefit[]>([]);
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProgressData = async () => {
      try {
        setLoading(true);
        const progressData = await getUserProgress();
        setUserProgress(progressData);
        
        // Calculate days since quit
        const quitDate = new Date(progressData.quit_date);
        const daysSinceQuit = Math.floor((Date.now() - quitDate.getTime()) / (1000 * 60 * 60 * 24));
        
        // Get health benefits and milestones based on progress
        const benefits = getHealthBenefits(daysSinceQuit);
        const userMilestones = getMilestones(daysSinceQuit, progressData.milestones_achieved);
        
        setHealthBenefits(benefits);
        setMilestones(userMilestones);
        setError(null);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to load progress data');
      } finally {
        setLoading(false);
      }
    };
    loadProgressData();
  }, []);
  return (
    <div className="min-h-screen bg-white">
      {/* Apple-style Navigation Header */}
      <nav className="sticky top-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center gap-3">
              <div className="w-10 h-10 bg-wellness rounded-2xl flex items-center justify-center">
                <Package className="w-6 h-6 text-white" strokeWidth={2} />
              </div>
              <span className="text-xl font-bold text-gray-900">NRTList</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-8">
              <Link to="/" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Marketplace
              </Link>
              <Link to="/products" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Compare
              </Link>
              <Link to="/retailers" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Retailers
              </Link>
              <Link to="/community" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Community
              </Link>
              <Link to="/progress" className="text-wellness font-medium">
                Progress
              </Link>
            </div>

            {/* Auth Buttons */}
            <div className="hidden md:flex items-center gap-4">
              <button className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Sign In
              </button>
              <button className="bg-wellness text-white px-6 py-2 rounded-xl hover:bg-wellness-hover font-medium transition-all duration-200">
                Sign Up
              </button>
            </div>

            {/* Mobile Menu Button */}
            <button 
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-t border-gray-200">
            <div className="px-4 py-4 space-y-4">
              <Link to="/" className="block text-gray-700 font-medium">Marketplace</Link>
              <Link to="/products" className="block text-gray-700 font-medium">Compare</Link>
              <Link to="/retailers" className="block text-gray-700 font-medium">Retailers</Link>
              <Link to="/community" className="block text-gray-700 font-medium">Community</Link>
              <Link to="/progress" className="block text-wellness font-medium">Progress</Link>
              <div className="pt-4 border-t border-gray-200 space-y-3">
                <button className="block w-full text-left text-gray-700 font-medium">Sign In</button>
                <button className="block w-full bg-wellness text-white px-4 py-2 rounded-xl font-medium">Sign Up</button>
              </div>
            </div>
          </div>
        )}
      </nav>

      <div className="bg-gray-50">
      {/* Apple-style Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="inline-flex items-center gap-3 bg-wellness-50 px-6 py-3 rounded-full mb-6">
              <Target className="w-5 h-5 text-wellness" />
              <span className="text-wellness font-semibold">Progress Tracking</span>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Track Your Journey
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Monitor your cessation progress with comprehensive analytics and milestone tracking
            </p>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        
        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-16">
            <div className="w-8 h-8 border-4 border-wellness border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-3 text-gray-600">Loading your progress...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-2xl p-6 mb-8">
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">!</span>
              </div>
              <p className="text-red-700 font-medium">{error}</p>
            </div>
          </div>
        )}

        {/* Progress Data */}
        {!loading && !error && userProgress && (
          <>
            {/* Progress Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-wellness-50 rounded-xl flex items-center justify-center">
                    <Calendar className="w-6 h-6 text-wellness" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900">{userProgress.current_streak}</div>
                    <div className="text-sm text-gray-600">Days Smoke-Free</div>
                  </div>
                </div>
                <div className="text-sm text-wellness font-semibold">+1 day from yesterday</div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-wellness-50 rounded-xl flex items-center justify-center">
                    <DollarSign className="w-6 h-6 text-wellness" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900">${userProgress.total_saved_money.toFixed(2)}</div>
                    <div className="text-sm text-gray-600">Money Saved</div>
                  </div>
                </div>
                <div className="text-sm text-wellness font-semibold">Keep it up!</div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-wellness-50 rounded-xl flex items-center justify-center">
                    <Heart className="w-6 h-6 text-wellness" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900">{userProgress.total_saved_cigarettes}</div>
                    <div className="text-sm text-gray-600">Cigarettes Avoided</div>
                  </div>
                </div>
                <div className="text-sm text-wellness font-semibold">Excellent progress</div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-wellness-50 rounded-xl flex items-center justify-center">
                    <Award className="w-6 h-6 text-wellness" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900">{userProgress.milestones_achieved.length}</div>
                    <div className="text-sm text-gray-600">Milestones</div>
                  </div>
                </div>
                <div className="text-sm text-wellness font-semibold">Amazing journey</div>
              </div>
            </div>
          </>
        )}

        {/* Progress Chart Section */}
        <section className="mb-12">
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Daily Progress Chart</h2>
            
            {/* Mock Chart Area */}
            <div className="h-80 bg-gradient-to-br from-wellness-50 to-wellness-100 rounded-xl flex items-center justify-center">
              <div className="text-center">
                <TrendingUp className="w-16 h-16 text-wellness mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Interactive Progress Chart</h3>
                <p className="text-gray-600">
                  Visual representation of your cessation journey with detailed analytics
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Milestones Section */}
        {!loading && !error && userProgress && (
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Achievement Milestones</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {milestones.map((milestone) => {
                const quitDate = new Date(userProgress.quit_date);
                const daysSinceQuit = Math.floor((Date.now() - quitDate.getTime()) / (1000 * 60 * 60 * 24));
                const daysUntilMilestone = milestone.achievement_criteria.days_required ? milestone.achievement_criteria.days_required - daysSinceQuit : 0;
                
                return (
                  <div key={milestone.id} className={`bg-white rounded-2xl p-6 shadow-lg border border-gray-200 relative ${
                    !milestone.is_achieved ? 'opacity-75' : ''
                  }`}>
                    <div className="absolute top-4 right-4">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        milestone.is_achieved 
                          ? 'bg-wellness'
                          : 'bg-gray-200'
                      }`}>
                        {milestone.is_achieved ? (
                          <Award className="w-5 h-5 text-white" />
                        ) : (
                          <Clock className="w-5 h-5 text-gray-400" />
                        )}
                      </div>
                    </div>
                    <div className="mb-4">
                      <h3 className="text-lg font-bold text-gray-900">{milestone.name}</h3>
                      <p className="text-sm text-gray-600">{milestone.description}</p>
                    </div>
                    <div className={`text-sm font-semibold ${
                      milestone.is_achieved ? 'text-wellness' : 'text-gray-500'
                    }`}>
                      {milestone.is_achieved 
                        ? 'Completed!'
                        : `${daysUntilMilestone} days remaining`
                      }
                    </div>
                  </div>
                );
              })}
            </div>
          </section>
        )}

        {/* Health Benefits Timeline */}
        {!loading && !error && userProgress && (
          <section className="mb-12">
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900 mb-8">Health Benefits Timeline</h2>
              
              <div className="space-y-6">
                {healthBenefits.map((benefit) => {
                  const IconComponent = benefit.icon === 'Heart' ? Heart : 
                                      benefit.icon === 'Activity' ? Activity : 
                                      benefit.icon === 'TrendingUp' ? TrendingUp : Heart;
                  
                  return (
                    <div key={benefit.id} className="flex items-start gap-4">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center mt-1 ${
                        benefit.is_achieved ? 'bg-wellness' : 'bg-gray-200'
                      }`}>
                        <IconComponent className={`w-6 h-6 ${
                          benefit.is_achieved ? 'text-white' : 'text-gray-400'
                        }`} />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-bold text-gray-900">{benefit.name}</h3>
                        <p className="text-gray-600 mb-2">
                          {benefit.description}
                        </p>
                        <div className={`text-sm font-semibold ${
                          benefit.is_achieved ? 'text-wellness' : 'text-gray-500'
                        }`}>
                          {benefit.is_achieved 
                            ? '✓ Achieved' 
                            : `Coming in ${benefit.days_required - Math.floor((Date.now() - new Date(userProgress.quit_date).getTime()) / (1000 * 60 * 60 * 24))} days`
                          }
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </section>
        )}

        {/* Motivation Section */}
        <section className="bg-gradient-to-br from-wellness-50 to-wellness-100 rounded-3xl p-12 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Keep Going Strong!
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            You're making incredible progress. Every day smoke-free is a victory worth celebrating.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-wellness text-white px-8 py-4 rounded-2xl hover:bg-wellness-hover font-semibold transition-all duration-200">
              Share Progress
            </button>
            <button className="bg-white text-gray-900 px-8 py-4 rounded-2xl hover:bg-gray-50 font-semibold transition-all duration-200 border border-gray-200">
              View Full Report
            </button>
          </div>
        </section>
      </main>
      </div>
    </div>
  );
};

export default ProgressPage;
