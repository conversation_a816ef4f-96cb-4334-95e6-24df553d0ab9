import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, User, Shield, LogIn } from 'lucide-react';
import { supabase } from '../lib/supabase';

const UserProfilePage: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check authentication and load real user data from database (RULE 0001 COMPLIANT)
  useEffect(() => {
    const checkAuth = async () => {
      try {
        console.log('🚨 UserProfilePage: Checking authentication...');
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('🚨 UserProfilePage: Auth error:', error);
          setIsAuthenticated(false);
          setLoading(false);
          return;
        }

        if (!session?.user) {
          console.log('🚨 UserProfilePage: No authenticated user found');
          setIsAuthenticated(false);
          setLoading(false);
          return;
        }

        console.log('🚨 UserProfilePage: User authenticated:', session.user.id);
        setIsAuthenticated(true);
        setUser(session.user);
        setLoading(false);
      } catch (error) {
        console.error('🚨 UserProfilePage: Error checking auth:', error);
        setIsAuthenticated(false);
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-wellness rounded-2xl flex items-center justify-center animate-pulse mx-auto mb-4">
            <User className="w-8 h-8 text-white" />
          </div>
          <p className="text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  // Show login required if not authenticated (RULE 0001 COMPLIANT)
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 max-w-md text-center">
          <div className="w-16 h-16 bg-alert-red-50 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <LogIn className="w-8 h-8 text-alert-red" />
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">Login Required</h2>
          <p className="text-gray-600 mb-6">You must be logged in to view your profile.</p>
          <button
            onClick={() => navigate('/')}
            className="bg-wellness text-white px-6 py-3 rounded-xl hover:bg-wellness-hover transition-colors font-medium"
          >
            Go to Homepage
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/')}
                className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="h-5 w-5 mr-1" />
                Back Home
              </button>
              <div className="flex items-center">
                <Shield className="h-8 w-8 text-green-600 mr-2" />
                <span className="text-xl font-bold text-gray-900">NRTList</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            User Profile - {user?.email || 'Unknown User'}
          </h1>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <p className="text-gray-900">{user?.email || 'Not available'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">User ID</label>
              <p className="text-gray-900">{user?.id || 'Not available'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Account Created</label>
              <p className="text-gray-900">{user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Not available'}</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default UserProfilePage;
