import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Shield, Tag, Percent, ExternalLink } from 'lucide-react';
import { getDeals } from '../lib/supabase';

interface Deal {
  id: string;
  title: string;
  description: string;
  deal_type: string;
  discount_value: number;
  promo_code?: string;
  vendor_name: string;
  vendor_website: string;
  affiliate_url: string;
  featured: boolean;
}

const DealsPage: React.FC = () => {
  const navigate = useNavigate();
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDeals = async () => {
      try {
        const dealsData = await getDeals();
        setDeals(dealsData);
      } catch (error) {
        console.error('Error fetching deals:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDeals();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/')}
                className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <ArrowLeft className="h-5 w-5 mr-1" />
                Back Home
              </button>
              <div className="flex items-center">
                <Shield className="h-8 w-8 text-green-600 mr-2" />
                <span className="text-xl font-bold text-gray-900">NRTList</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center mb-4">
            <Tag className="h-8 w-8 text-green-600 mr-3" />
            <h1 className="text-2xl font-bold text-gray-900">
              Deals & Discounts
            </h1>
          </div>
          <p className="text-gray-600 mb-6">
            SEO-friendly deals page with discount codes, special offers, and vendor promotions - perfect for monetization.
          </p>
          
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
              <span className="ml-3 text-gray-600">Loading deals...</span>
            </div>
          ) : deals.length > 0 ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {deals.map((deal) => (
                <div key={deal.id} className={`border rounded-lg p-6 transition-all hover:shadow-lg ${
                  deal.featured ? 'bg-emerald-50 border-emerald-200' : 'bg-white border-gray-200'
                }`}>
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <Tag className="h-5 w-5 text-emerald-600 mr-2" />
                      <span className={`font-semibold ${
                        deal.featured ? 'text-emerald-800' : 'text-gray-800'
                      }`}>
                        {deal.deal_type === 'percentage' ? `${deal.discount_value}% OFF` :
                         deal.deal_type === 'fixed_amount' ? `$${deal.discount_value} OFF` :
                         deal.deal_type === 'free_shipping' ? 'Free Shipping' :
                         deal.deal_type === 'buy_one_get_one' ? 'BOGO Deal' :
                         `${deal.discount_value}% OFF`}
                      </span>
                    </div>
                    {deal.featured && (
                      <span className="bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded-full font-medium">
                        Featured
                      </span>
                    )}
                  </div>
                  
                  <h3 className="font-semibold text-gray-900 mb-2">{deal.title}</h3>
                  <p className="text-sm text-gray-600 mb-3">{deal.description}</p>
                  
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm text-gray-500">by {deal.vendor_name}</span>
                    {deal.promo_code && (
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-gray-500">Code:</span>
                        <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded font-mono cursor-pointer hover:bg-gray-200 transition-colors"
                              onClick={() => navigator.clipboard.writeText(deal.promo_code || '')}
                              title="Click to copy">
                          {deal.promo_code}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Deal Expiry and Terms */}
                  <div className="mb-3 text-xs text-gray-500">
                    <p>• Valid until supplies last</p>
                    <p>• Cannot be combined with other offers</p>
                    <p>• Terms and conditions apply</p>
                  </div>

                  <div className="flex gap-2">
                    <a
                      href={deal.affiliate_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 bg-emerald-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-emerald-700 transition-colors flex items-center justify-center text-sm"
                    >
                      Claim Deal
                      <ExternalLink className="h-4 w-4 ml-2" />
                    </a>
                    <button
                      className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                      title="Save deal"
                    >
                      ♡
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Tag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No active deals</h3>
              <p className="text-gray-600">Check back soon for new promotions and discounts!</p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default DealsPage;
