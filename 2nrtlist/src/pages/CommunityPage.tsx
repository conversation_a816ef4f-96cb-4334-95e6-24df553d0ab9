import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Package, Users, MessageCircle, TrendingUp, Menu, X, Award, ThumbsUp, Calendar } from 'lucide-react';
import { getCommunityPosts, CommunityPost } from '../lib/supabase';

const CommunityPage: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [posts, setPosts] = useState<CommunityPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPosts = async () => {
      try {
        setLoading(true);
        const postsData = await getCommunityPosts();
        setPosts(postsData || []);
        setError(null);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to load community posts');
      } finally {
        setLoading(false);
      }
    };
    loadPosts();
  }, []);
  return (
    <div className="min-h-screen bg-white">
      {/* Apple-style Navigation Header */}
      <nav className="sticky top-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center gap-3">
              <div className="w-10 h-10 bg-wellness rounded-2xl flex items-center justify-center">
                <Package className="w-6 h-6 text-white" strokeWidth={2} />
              </div>
              <span className="text-xl font-bold text-gray-900">NRTList</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-8">
              <Link to="/" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Directory
              </Link>
              <Link to="/products" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Products
              </Link>
              <Link to="/stores" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Find Stores
              </Link>
              <Link to="/deals" className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Best Deals
              </Link>
              <Link to="/reviews" className="text-wellness font-medium">
                Reviews
              </Link>
            </div>

            {/* Auth Buttons */}
            <div className="hidden md:flex items-center gap-4">
              <button className="text-gray-700 hover:text-wellness font-medium transition-colors">
                Sign In
              </button>
              <button className="bg-wellness text-white px-6 py-2 rounded-xl hover:bg-wellness-hover font-medium transition-all duration-200">
                Sign Up
              </button>
            </div>

            {/* Mobile Menu Button */}
            <button 
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-t border-gray-200">
            <div className="px-4 py-4 space-y-4">
              <Link to="/" className="block text-gray-700 font-medium">Directory</Link>
              <Link to="/products" className="block text-gray-700 font-medium">Products</Link>
              <Link to="/stores" className="block text-gray-700 font-medium">Find Stores</Link>
              <Link to="/deals" className="block text-gray-700 font-medium">Best Deals</Link>
              <Link to="/reviews" className="block text-wellness font-medium">Reviews</Link>
              <div className="pt-4 border-t border-gray-200 space-y-3">
                <button className="block w-full text-left text-gray-700 font-medium">Sign In</button>
                <button className="block w-full bg-wellness text-white px-4 py-2 rounded-xl font-medium">Sign Up</button>
              </div>
            </div>
          </div>
        )}
      </nav>

      <div className="bg-gray-50">
      {/* Apple-style Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="inline-flex items-center gap-3 bg-wellness-50 px-6 py-3 rounded-full mb-6">
              <Award className="w-5 h-5 text-wellness" />
              <span className="text-wellness font-semibold">Expert Reviews</span>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              NRT Product Reviews & Ratings
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Professional ratings on flavor, strength, and effectiveness from certified users and experts
            </p>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Review Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 text-center">
            <div className="w-12 h-12 bg-wellness-100 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Award className="w-6 h-6 text-wellness" />
            </div>
            <div className="text-2xl font-bold text-gray-900">4,280</div>
            <div className="text-sm text-gray-600">Expert Reviews</div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 text-center">
            <div className="w-12 h-12 bg-wellness-100 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Users className="w-6 h-6 text-wellness" />
            </div>
            <div className="text-2xl font-bold text-gray-900">12,350</div>
            <div className="text-sm text-gray-600">Verified Reviewers</div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 text-center">
            <div className="w-12 h-12 bg-wellness-100 rounded-xl flex items-center justify-center mx-auto mb-3">
              <ThumbsUp className="w-6 h-6 text-wellness" />
            </div>
            <div className="text-2xl font-bold text-gray-900">4.7</div>
            <div className="text-sm text-gray-600">Avg Rating</div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 text-center">
            <div className="w-12 h-12 bg-wellness-100 rounded-xl flex items-center justify-center mx-auto mb-3">
              <TrendingUp className="w-6 h-6 text-wellness" />
            </div>
            <div className="text-2xl font-bold text-gray-900">156</div>
            <div className="text-sm text-gray-600">Products Rated</div>
          </div>
        </div>

        {/* Recent Discussions */}
        <section className="mb-12">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-gray-900">Recent Discussions</h2>
            <Link 
              to="/community?action=create" 
              className="bg-wellness text-white px-6 py-3 rounded-xl hover:bg-wellness-hover font-semibold transition-all duration-200 flex items-center gap-2"
            >
              <MessageCircle className="w-4 h-4" />
              Start Discussion
            </Link>
          </div>
          
          {/* Loading State */}
          {loading && (
            <div className="flex items-center justify-center py-16">
              <div className="w-8 h-8 border-4 border-wellness border-t-transparent rounded-full animate-spin"></div>
              <span className="ml-3 text-gray-600">Loading discussions...</span>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-2xl p-6 mb-8">
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">!</span>
                </div>
                <p className="text-red-700 font-medium">{error}</p>
              </div>
            </div>
          )}

          {/* Community Posts Data */}
          {!loading && !error && (
            <div className="space-y-6">
              {posts.length > 0 ? (
                posts.map((post) => (
                  <div key={post.id} className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-wellness-50 rounded-full flex items-center justify-center">
                        <span className="text-wellness font-bold text-sm">
                          {post.user_profile?.full_name ? 
                            post.user_profile.full_name.split(' ').map(n => n[0]).join('').slice(0, 2).toUpperCase() :
                            '??'
                          }
                        </span>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-bold text-gray-900">{post.title}</h3>
                          <span className="text-sm text-gray-500">
                            {(() => {
                              const now = new Date();
                              const postDate = new Date(post.created_at);
                              const diffMs = now.getTime() - postDate.getTime();
                              const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                              
                              if (diffHours < 1) return 'Just now';
                              if (diffHours === 1) return '1 hour ago';
                              if (diffHours < 24) return `${diffHours} hours ago`;
                              
                              const diffDays = Math.floor(diffHours / 24);
                              if (diffDays === 1) return '1 day ago';
                              return `${diffDays} days ago`;
                            })()} 
                          </span>
                        </div>
                        <p className="text-gray-700 mb-3">{post.content}</p>
                        <div className="flex items-center gap-6 text-sm">
                          <button 
                            className="flex items-center gap-2 text-gray-500 hover:text-wellness transition-colors cursor-pointer"
                            onClick={() => {
                              // Handle like functionality
                              console.log('Liked post:', post.id);
                            }}
                          >
                            <ThumbsUp className="w-4 h-4" />
                            <span>{post.likes_count || 0} likes</span>
                          </button>
                          <button 
                            className="flex items-center gap-2 text-gray-500 hover:text-wellness transition-colors cursor-pointer"
                            onClick={() => {
                              // Handle reply functionality
                              console.log('Reply to post:', post.id);
                            }}
                          >
                            <MessageCircle className="w-4 h-4" />
                            <span>{post.replies_count || 0} replies</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200">
                  <h3 className="font-bold text-gray-900 mb-2">Welcome to the Community</h3>
                  <p className="text-gray-600">No community posts yet. Be the first to start a discussion!</p>
                </div>
              )}
            </div>
          )}
        </section>

        {/* Community Features */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">Community Features</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 text-center">
              <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <MessageCircle className="w-8 h-8 text-wellness" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Discussion Forums</h3>
              <p className="text-gray-600 mb-6">
                Join topic-specific discussions about products, strategies, and experiences
              </p>
              <Link to="/community?tab=discussions" className="inline-block bg-wellness text-white px-6 py-3 rounded-xl hover:bg-wellness-hover font-semibold transition-all duration-200">
                Join Discussions
              </Link>
            </div>

            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 text-center">
              <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Award className="w-8 h-8 text-wellness" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Achievement System</h3>
              <p className="text-gray-600 mb-6">
                Track your progress and earn badges for milestones in your cessation journey
              </p>
              <Link to="/progress" className="inline-block bg-wellness text-white px-6 py-3 rounded-xl hover:bg-wellness-hover font-semibold transition-all duration-200">
                View Achievements
              </Link>
            </div>

            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 text-center">
              <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Calendar className="w-8 h-8 text-wellness" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Support Groups</h3>
              <p className="text-gray-600 mb-6">
                Join scheduled group sessions with peers and certified cessation counselors
              </p>
              <Link to="/community?tab=groups" className="inline-block bg-wellness text-white px-6 py-3 rounded-xl hover:bg-wellness-hover font-semibold transition-all duration-200">
                Find Groups
              </Link>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="bg-gradient-to-br from-wellness-50 to-wellness-100 rounded-3xl p-12 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Join Our Community?
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Connect with thousands of people who understand your journey and are here to support your success
          </p>
          <Link to="/community?action=signup" className="inline-block bg-wellness text-white px-12 py-4 rounded-2xl hover:bg-wellness-hover font-semibold transition-all duration-200 text-lg">
            Join Community
          </Link>
        </section>
      </main>
      </div>
    </div>
  );
};

export default CommunityPage;
