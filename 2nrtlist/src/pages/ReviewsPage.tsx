import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Star, TrendingUp, DollarSign, Trophy, Filter, Search } from 'lucide-react';
import { getNRTProducts, getStores } from '../lib/supabase';

interface Product {
  id: string;
  name: string;
  brand: string;
  category: string;
  rating: number;
  price: number;
  reviews: number;
}

interface Store {
  id: string;
  name: string;
  rating: number;
  review_count: number;
  city: string;
  state: string;
}

interface Vendor {
  id: string;
  name: string;
  rating: number;
  delivery_time: string;
  min_order: number;
}

const ReviewsPage: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [stores, setStores] = useState<Store[]>([]);

  const [activeTab, setActiveTab] = useState<'products' | 'stores' | 'prices'>('products');
  const [sortBy, setSortBy] = useState<'rating' | 'reviews' | 'price'>('rating');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const productsData = await getNRTProducts();
        setProducts(productsData || []);
        
        // Fetch real store data from mission_fresh.stores table (RULE 0001 compliant)
        const storesData = await getStores();
        const formattedStores = storesData.map(store => ({
          id: store.id,
          name: store.name,
          rating: store.rating || 4.0,
          review_count: store.review_count || 0,
          city: store.city,
          state: store.state
        }));
        setStores(formattedStores);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const getSortedProducts = () => {
    return [...products].sort((a, b) => {
      if (sortBy === 'rating') return b.rating - a.rating;
      if (sortBy === 'reviews') return b.reviews - a.reviews;
      if (sortBy === 'price') return a.price - b.price;
      return 0;
    });
  };

  const getSortedStores = () => {
    return [...stores].sort((a, b) => b.rating - a.rating);
  };

  const getCheapestPrices = () => {
    return [...products].sort((a, b) => a.price - b.price);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-12">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Reviews & Ratings</h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Discover top-rated NRT products, highest-rated stores, and best prices. 
              Filter and search based on real user reviews and ratings.
            </p>
            <p className="text-sm text-gray-500 mt-2 font-medium">
              Advanced search and filtering for informed decisions.
            </p>
          </div>

          {/* Filter Tabs */}
          <div className="flex justify-center mb-8">
            <div className="bg-gray-100 p-1 rounded-lg">
              <button
                onClick={() => setActiveTab('products')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === 'products'
                    ? 'bg-white text-emerald-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Trophy className="w-4 h-4 inline mr-2" />
                Top-Rated Products
              </button>
              <button
                onClick={() => setActiveTab('stores')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === 'stores'
                    ? 'bg-white text-emerald-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Star className="w-4 h-4 inline mr-2" />
                Top-Rated Stores
              </button>
              <button
                onClick={() => setActiveTab('prices')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === 'prices'
                    ? 'bg-white text-emerald-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <DollarSign className="w-4 h-4 inline mr-2" />
                Best Prices
              </button>
            </div>
          </div>

          {/* Sort Controls */}
          {activeTab === 'products' && (
            <div className="flex justify-center mb-6">
              <div className="flex items-center space-x-4">
                <Filter className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600 font-medium">Sort by:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'rating' | 'reviews' | 'price')}
                  className="border border-gray-300 rounded-lg px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500"
                >
                  <option value="rating">Highest Rating</option>
                  <option value="reviews">Most Reviews</option>
                  <option value="price">Lowest Price</option>
                </select>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Review Submission Form */}
      <section className="max-w-4xl mx-auto px-6 py-8">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Write a Review</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Product</label>
              <select className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                <option>Select a product to review</option>
                <option>Nicorette Gum Original 2mg</option>
                <option>NicoDerm CQ Patch 21mg</option>
                <option>Commit Lozenge 4mg</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Rating</label>
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-6 h-6 text-gray-300 hover:text-yellow-400 cursor-pointer transition-colors" />
                ))}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Your Review</label>
              <textarea
                rows={4}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                placeholder="Share your experience with this NRT product..."
              ></textarea>
            </div>
            <button className="bg-emerald-600 text-white px-6 py-2 rounded-lg hover:bg-emerald-700 transition-colors font-medium">
              Submit Review
            </button>
          </div>
        </div>
      </section>

      {/* Active Tab Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading reviews and ratings...</p>
          </div>
        ) : (
          <div>
            {/* Top-Rated Products Tab */}
            {activeTab === 'products' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {getSortedProducts().slice(0, 12).map((product) => (
                    <Link
                      key={product.id}
                      to={`/product/${product.id}`}
                      className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 mb-1">{product.name}</h3>
                          <p className="text-sm text-gray-600">{product.brand}</p>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">{product.category}</p>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-1 mb-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="font-medium text-gray-900">{product.rating}</span>
                          </div>
                          <p className="text-xs text-gray-600">{product.reviews} reviews</p>
                        </div>
                      </div>
                      <div className="flex items-center justify-between mt-4">
                        <span className="text-lg font-bold text-emerald-600">${product.price}</span>
                        <span className="text-sm text-gray-500">View Details →</span>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* Top-Rated Stores Tab */}
            {activeTab === 'stores' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {getSortedStores().slice(0, 12).map((store) => (
                    <Link
                      key={store.id}
                      to={`/store/${store.id}`}
                      className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 mb-1">{store.name}</h3>
                          <p className="text-sm text-gray-600">{store.city}, {store.state}</p>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-1 mb-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="font-medium text-gray-900">{store.rating}</span>
                          </div>
                          <p className="text-xs text-gray-600">{store.review_count} reviews</p>
                        </div>
                      </div>
                      <div className="mt-4">
                        <span className="text-sm text-gray-500">View Store Details →</span>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* Best Prices Tab */}
            {activeTab === 'prices' && (
              <div className="space-y-6">
                <div className="bg-emerald-50 rounded-lg p-4 mb-6">
                  <h3 className="font-semibold text-emerald-900 mb-2">💰 Best Price Deals</h3>
                  <p className="text-sm text-emerald-800">Products sorted by lowest price to help you save money.</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {getCheapestPrices().slice(0, 12).map((product, index) => (
                    <Link
                      key={product.id}
                      to={`/product/${product.id}`}
                      className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
                    >
                      {index < 3 && (
                        <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 mb-3">
                          🏆 Top {index + 1} Deal
                        </div>
                      )}
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 mb-1">{product.name}</h3>
                          <p className="text-sm text-gray-600">{product.brand}</p>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">{product.category}</p>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-1 mb-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="font-medium text-gray-900">{product.rating}</span>
                          </div>
                          <p className="text-xs text-gray-600">{product.reviews} reviews</p>
                        </div>
                      </div>
                      <div className="flex items-center justify-between mt-4">
                        <span className="text-xl font-bold text-emerald-600">${product.price}</span>
                        <span className="text-sm text-gray-500">Best Price →</span>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </main>
    </div>
  );
};

export default ReviewsPage;
