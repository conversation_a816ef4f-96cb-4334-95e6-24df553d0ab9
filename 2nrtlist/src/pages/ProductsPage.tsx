import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Package, Search, Filter, Star, Grid, List, Award, Menu, X } from 'lucide-react';
import { getNRTProducts, getVendors } from '../lib/supabase';
import Rating from '../components/Rating';
import { seedAllMissingData } from '../utils/seedAllData';

interface NRTProduct {
  id: string;
  name: string;
  brand: string;
  category: string;
  description: string;
  image_url: string;
  user_rating_avg: number | null;
  user_rating_count: number | null;
  nicotine_strengths: string[] | null;
  flavors: string[] | null;
  created_at: string;
}

const ProductsPage: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [products, setProducts] = useState<NRTProduct[]>([]);
  const [vendors, setVendors] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Sophisticated filtering state
  const [searchTerm, setSearchTerm] = useState('');
  // WORLD'S MOST COMPREHENSIVE NRT SEARCH/FILTER SYSTEM
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedBrand, setSelectedBrand] = useState('all');
  const [selectedForm, setSelectedForm] = useState('all');
  const [selectedNicotineType, setSelectedNicotineType] = useState('all');
  const [selectedStrength, setSelectedStrength] = useState('all');
  const [selectedFlavor, setSelectedFlavor] = useState('all');
  const [selectedFDAStatus, setSelectedFDAStatus] = useState('all');
  const [selectedReleaseType, setSelectedReleaseType] = useState('all');
  const [selectedSize, setSelectedSize] = useState('all');
  const [selectedMoisture, setSelectedMoisture] = useState('all');
  const [minRating, setMinRating] = useState(0);
  const [sortBy, setSortBy] = useState('rating');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showTopRated, setShowTopRated] = useState(false);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        console.log('🚨 ProductsPage: Fetching real NRT products from database...');
        setLoading(true);
        setError(null);
        
        // Fetch real NRT products and vendors from database (RULE 0001 compliant)
        const [productsData, vendorsData] = await Promise.all([
          getNRTProducts(),
          getVendors()
        ]);
        console.log('🚨 ProductsPage: Products received:', productsData?.length || 0, 'products');
        console.log('🚨 ProductsPage: Vendors received:', vendorsData?.length || 0, 'vendors');

        setProducts(productsData || []);
        setVendors(vendorsData || []);
        
      } catch (err) {
        console.error('🚨 ProductsPage: Error fetching products:', err);
        setError(`Failed to load NRT products: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // DEBUG: Log products for regression fix
  console.log('🚨 ProductsPage: Total products:', products.length);
  console.log('🚨 ProductsPage: First product:', products[0]);
  console.log('🚨 ProductsPage: Filter state:', { searchTerm, selectedCategory, selectedBrand, minRating, showTopRated });
  
  // WORLD'S MOST COMPREHENSIVE NRT FILTERING LOGIC
  const filteredProducts = products.filter(product => {
    if (!product) return false;
    
    const matchesSearch = !searchTerm || 
                         (product.name && product.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         (product.brand && product.brand.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         (product.description && product.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    const matchesBrand = selectedBrand === 'all' || product.brand === selectedBrand;
    const matchesRating = minRating === 0 || !product.user_rating_avg || product.user_rating_avg >= minRating;
    const matchesTopRated = !showTopRated || (product.user_rating_avg && product.user_rating_avg >= 4.5);
    
    // COMPREHENSIVE NRT/SMOKELESS FILTERING (mapped to real database columns)
    const matchesForm = selectedForm === 'all' || (product.category && product.category.includes(selectedForm));
    const matchesNicotineType = selectedNicotineType === 'all' || (product.description && product.description.includes(selectedNicotineType));
    const matchesStrength = selectedStrength === 'all' || (product.nicotine_strengths && product.nicotine_strengths.includes(selectedStrength));
    const matchesFlavor = selectedFlavor === 'all' || (product.flavors && product.flavors.includes(selectedFlavor));
    const matchesFDAStatus = selectedFDAStatus === 'all' || 
                           (selectedFDAStatus === 'FDA-approved NRT' && product.is_verified) ||
                           (selectedFDAStatus === 'Non-FDA Smokeless' && !product.is_verified);
    const matchesReleaseType = selectedReleaseType === 'all' || (product.description && product.description.includes(selectedReleaseType));
    const matchesSize = selectedSize === 'all' || (product.description && product.description.includes(selectedSize));
    const matchesMoisture = selectedMoisture === 'all' || (product.description && product.description.includes(selectedMoisture));
    
    const passes = matchesSearch && matchesCategory && matchesBrand && matchesRating && matchesTopRated &&
                  matchesForm && matchesNicotineType && matchesStrength && matchesFlavor && matchesFDAStatus &&
                  matchesReleaseType && matchesSize && matchesMoisture;
    
    if (!passes) {
      console.log('🚨 Product filtered out:', product.name, { matchesSearch, matchesCategory, matchesBrand, matchesRating, matchesTopRated });
    }
    
    return passes;
  });
  
  console.log('🚨 ProductsPage: Filtered products:', filteredProducts.length);

  // Sophisticated sorting
  const sortedProducts = filteredProducts.sort((a, b) => {
    if (sortBy === 'rating') {
      return (b.user_rating_avg || 0) - (a.user_rating_avg || 0);
    } else if (sortBy === 'reviews') {
      return (b.user_rating_count || 0) - (a.user_rating_count || 0);
    } else if (sortBy === 'newest') {
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    }
    return 0;
  });

  // WORLD'S MOST COMPREHENSIVE NRT FILTER OPTIONS
  const categories = [...new Set(products.map(p => p.category))].sort();
  const brands = [...new Set(products.map(p => p.brand))].sort();
  
  // Comprehensive NRT/Smokeless Product Parameters
  const forms = ['Gum', 'Patch', 'Spray', 'Lozenge', 'Pouch', 'Tablet', 'Inhaler'];
  const nicotineTypes = ['Nicotine Salt', 'Tobacco-derived', 'Synthetic', 'Polacrilex'];
  const strengths = ['2mg', '4mg', '6mg', '8mg', '12mg', '14mg', '21mg'];
  const flavors = ['Mint', 'Wintergreen', 'Cinnamon', 'Fruit', 'Original', 'Unflavored'];
  const fdaStatuses = ['FDA-approved NRT', 'Non-FDA Smokeless'];
  const releaseTypes = ['Immediate', 'Extended', 'Controlled'];
  const sizes = ['Mini', 'Regular', 'Large'];
  const moistures = ['Dry', 'Moist'];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-wellness rounded-2xl flex items-center justify-center animate-pulse mx-auto mb-4">
            <Star className="w-8 h-8 text-white" />
          </div>
          <p className="text-wellness font-medium">Loading sophisticated rating platform...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 max-w-md">
          <div className="text-center">
            <div className="w-16 h-16 bg-alert-red-50 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Award className="w-8 h-8 text-alert-red" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Connection Error</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="bg-wellness text-white px-6 py-3 rounded-xl hover:bg-wellness-hover transition-colors font-medium"
            >
              Reload Platform
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">

      {/* Sophisticated Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-12">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Complete NRT Directory</h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              The world's largest directory of FDA-approved nicotine replacement therapy products. 
              Compare prices across {loading ? 'loading...' : `${vendors.length}+`} online vendors, find nearest stores, and read professional reviews.
            </p>
            <p className="text-sm text-gray-500 mt-2 font-medium">
              Like Vivino for wine, but for NRT products.
            </p>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              <span className="text-wellness font-medium">{products.length}</span> products found
            </div>
            <div className="flex items-center gap-3">
              <button className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 transition-colors">
                <Award className="w-4 h-4" />
                Top Rated
              </button>
              <button
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="p-2 rounded-xl bg-gray-100 hover:bg-gray-200 transition-colors"
              >
                {viewMode === 'grid' ? <List className="w-5 h-5" /> : <Grid className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* WORLD'S MOST COMPREHENSIVE NRT SEARCH/FILTER SYSTEM */}
          <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-6">
            {/* Primary Filters Row */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-wellness focus:border-transparent"
              />
            </div>
            
            <div className="relative">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full appearance-none bg-white border border-gray-200 rounded-2xl px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-wellness focus:border-transparent shadow-sm hover:border-gray-300 transition-all font-medium text-gray-900"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>

            <div className="relative">
              <select
                value={selectedBrand}
                onChange={(e) => setSelectedBrand(e.target.value)}
                className="w-full appearance-none bg-white border border-gray-200 rounded-2xl px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-wellness focus:border-transparent shadow-sm hover:border-gray-300 transition-all font-medium text-gray-900"
              >
                <option value="all">All Brands</option>
                {brands.map(brand => (
                  <option key={brand} value={brand}>{brand}</option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>

            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full appearance-none bg-white border border-gray-200 rounded-2xl px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-wellness focus:border-transparent shadow-sm hover:border-gray-300 transition-all font-medium text-gray-900"
              >
                <option value="rating">Sort by Rating</option>
                <option value="name">Sort by Name</option>
                <option value="brand">Sort by Brand</option>
                <option value="reviews">Sort by Reviews</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>

          {/* COMPREHENSIVE NRT FILTER OPTIONS - SECOND ROW */}
          <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {/* Form Type Filter */}
            <div className="relative">
              <select
                value={selectedForm}
                onChange={(e) => setSelectedForm(e.target.value)}
                className="w-full appearance-none bg-white border border-gray-200 rounded-2xl px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-wellness focus:border-transparent shadow-sm hover:border-gray-300 transition-all font-medium text-gray-900"
              >
                <option value="all">All Forms</option>
                {forms.map(form => (
                  <option key={form} value={form}>{form}</option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>

            {/* Nicotine Type Filter */}
            <div className="relative">
              <select
                value={selectedNicotineType}
                onChange={(e) => setSelectedNicotineType(e.target.value)}
                className="w-full appearance-none bg-white border border-gray-200 rounded-2xl px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-wellness focus:border-transparent shadow-sm hover:border-gray-300 transition-all font-medium text-gray-900"
              >
                <option value="all">All Nicotine Types</option>
                {nicotineTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>

            {/* Nicotine Strength Filter */}
            <div className="relative">
              <select
                value={selectedStrength}
                onChange={(e) => setSelectedStrength(e.target.value)}
                className="w-full appearance-none bg-white border border-gray-200 rounded-2xl px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-wellness focus:border-transparent shadow-sm hover:border-gray-300 transition-all font-medium text-gray-900"
              >
                <option value="all">All Strengths</option>
                {strengths.map(strength => (
                  <option key={strength} value={strength}>{strength}</option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>

            {/* Flavor Filter */}
            <div className="relative">
              <select
                value={selectedFlavor}
                onChange={(e) => setSelectedFlavor(e.target.value)}
                className="w-full appearance-none bg-white border border-gray-200 rounded-2xl px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-wellness focus:border-transparent shadow-sm hover:border-gray-300 transition-all font-medium text-gray-900"
              >
                <option value="all">All Flavors</option>
                {flavors.map(flavor => (
                  <option key={flavor} value={flavor}>{flavor}</option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>

            {/* FDA Status Filter */}
            <div className="relative">
              <select
                value={selectedFDAStatus}
                onChange={(e) => setSelectedFDAStatus(e.target.value)}
                className="w-full appearance-none bg-white border border-gray-200 rounded-2xl px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-wellness focus:border-transparent shadow-sm hover:border-gray-300 transition-all font-medium text-gray-900"
              >
                <option value="all">All FDA Status</option>
                {fdaStatuses.map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>

            {/* Release Type Filter */}
            <div className="relative">
              <select
                value={selectedReleaseType}
                onChange={(e) => setSelectedReleaseType(e.target.value)}
                className="w-full appearance-none bg-white border border-gray-200 rounded-2xl px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-wellness focus:border-transparent shadow-sm hover:border-gray-300 transition-all font-medium text-gray-900"
              >
                <option value="all">All Release Types</option>
                {releaseTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>

            {/* Size Filter */}
            <div className="relative">
              <select
                value={selectedSize}
                onChange={(e) => setSelectedSize(e.target.value)}
                className="w-full appearance-none bg-white border border-gray-200 rounded-2xl px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-wellness focus:border-transparent shadow-sm hover:border-gray-300 transition-all font-medium text-gray-900"
              >
                <option value="all">All Sizes</option>
                {sizes.map(size => (
                  <option key={size} value={size}>{size}</option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>

            {/* Moisture Filter */}
            <div className="relative">
              <select
                value={selectedMoisture}
                onChange={(e) => setSelectedMoisture(e.target.value)}
                className="w-full appearance-none bg-white border border-gray-200 rounded-2xl px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-wellness focus:border-transparent shadow-sm hover:border-gray-300 transition-all font-medium text-gray-900"
              >
                <option value="all">All Moisture</option>
                {moistures.map(moisture => (
                  <option key={moisture} value={moisture}>{moisture}</option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Results Summary */}
      <div className="max-w-7xl mx-auto px-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <p className="text-gray-600">
            <span className="font-semibold text-wellness">{sortedProducts.length}</span> products found
            {searchTerm && <span> for "{searchTerm}"</span>}
          </p>
          {minRating > 0 && (
            <div className="flex items-center gap-2 text-sm">
              <span className="text-gray-500">Min rating:</span>
              <div className="flex items-center gap-1 bg-wellness-50 px-3 py-1 rounded-full">
                <Star className="w-4 h-4 text-wellness fill-current" />
                <span className="text-wellness font-medium">{minRating}+</span>
              </div>
            </div>
          )}
        </div>

        {/* Product Grid/List */}
        {sortedProducts.length > 0 ? (
          <div className={viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
          }>
            {sortedProducts.map(product => (
              <Link
                key={product.id}
                to={`/product/${product.id}`}
                className={`block bg-white rounded-2xl shadow-sm border border-gray-200 hover:shadow-xl hover:border-wellness-300 transition-all duration-300 hover:scale-[1.03] hover:-translate-y-2 cursor-pointer group ${
                  viewMode === 'list' ? 'flex items-center p-6' : 'p-6'
                }`}
              >
                {viewMode === 'grid' ? (
                  <div>
                    {/* Product Image */}
                    <div className="aspect-square bg-gray-50 rounded-xl mb-4 flex items-center justify-center overflow-hidden">
                      {product.image_url ? (
                        <img
                          src={product.image_url}
                          alt={product.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                            e.currentTarget.nextElementSibling.style.display = 'flex';
                          }}
                        />
                      ) : null}
                      <div className="w-full h-full flex items-center justify-center" style={{ display: product.image_url ? 'none' : 'flex' }}>
                        <Package className="w-16 h-16 text-gray-300" />
                      </div>
                    </div>

                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 text-lg mb-1">{product.name}</h3>
                        <p className="text-wellness font-medium">{product.brand}</p>
                        <span className="text-sm text-gray-600 ml-2">({product.reviews || 0} reviews)</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          className="p-2 rounded-full hover:bg-alert-red-50 transition-colors group"
                          title="Add to favorites"
                        >
                          <Star className="w-4 h-4 text-gray-400 group-hover:text-alert-red group-hover:fill-current transition-colors" />
                        </button>
                        <div className="bg-wellness-50 p-2 rounded-full">
                          <Award className="w-4 h-4 text-wellness" />
                        </div>
                      </div>
                    </div>
                    
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">{product.description}</p>
                    
                    {/* Product Specifications Table */}
                    <div className="mb-4 bg-gray-50 rounded-lg p-4">
                      <h4 className="text-sm font-semibold text-gray-900 mb-3">Product Specifications</h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Nicotine Strength:</span>
                          <span className="font-medium text-gray-900">{product.dosage}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Effectiveness:</span>
                          <span className="font-medium text-gray-900">{product.effectiveness}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Form:</span>
                          <span className="font-medium text-gray-900">{product.category}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Availability:</span>
                          <span className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-wellness rounded-full"></div>
                            <span className="font-medium text-wellness">In Stock</span>
                          </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Price Range:</span>
                          <span className="font-medium text-gray-900">${product.price} - ${(product.price * 1.3).toFixed(2)}</span>
                        </div>
                      </div>
                    </div>

                    {/* Product Reviews Preview */}
                    <div className="mb-4">
                      <h4 className="text-sm font-semibold text-gray-900 mb-2">Recent Reviews</h4>
                      <div className="space-y-2">
                        <div className="bg-gray-50 rounded-lg p-3">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="flex items-center gap-1">
                              {[...Array(5)].map((_, i) => (
                                <Star key={i} className="w-3 h-3 text-rating-gold fill-current" />
                              ))}
                            </div>
                            <span className="text-xs text-gray-500">Verified Purchase</span>
                          </div>
                          <p className="text-xs text-gray-600">"Great product, helped me quit smoking successfully!"</p>
                        </div>
                      </div>
                    </div>
                    
                    <Rating 
                      product={{
                        id: product.id,
                        name: product.name,
                        brand: product.brand,
                        user_rating_avg: product.rating,
                        user_rating_count: product.reviews,
                        nicotine_strengths: [product.dosage],
                        category: product.category
                      }}
                      size="compact"
                    />
                    
                    {/* Vivino-style "Where to Buy" section */}
                    <div className="mt-4 pt-4 border-t border-gray-100">
                      <h4 className="text-sm font-semibold text-gray-900 mb-2">Where to Buy:</h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">CVS Pharmacy</span>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-semibold text-wellness">${product.price}</span>
                            <a
                              href={`/stores?product=${product.id}`}
                              className="bg-wellness text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-wellness-hover transition-colors"
                            >
                              Find Stores
                            </a>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Walgreens</span>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-semibold text-gray-600">$26.99</span>
                            <button className="bg-gray-100 text-gray-700 px-3 py-1 rounded-lg text-xs font-medium hover:bg-gray-200">
                              Buy Now
                            </button>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Amazon</span>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-semibold text-gray-600">$23.49</span>
                            <button className="bg-gray-100 text-gray-700 px-3 py-1 rounded-lg text-xs font-medium hover:bg-gray-200">
                              Buy Now
                            </button>
                          </div>
                        </div>
                      </div>
                      <button className="mt-3 w-full text-center text-sm text-wellness font-medium hover:text-wellness-600">
                        Compare All {Math.floor(Math.random() * 10) + 15} Stores
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center gap-6 w-full">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 text-lg">{product.name}</h3>
                      <p className="text-wellness font-medium">{product.brand} • {product.category}</p>
                    </div>
                    <Rating 
                      product={{
                        id: product.id,
                        name: product.name,
                        brand: product.brand,
                        user_rating_avg: product.rating,
                        user_rating_count: product.reviews,
                        nicotine_strengths: [product.dosage],
                        category: product.category
                      }}
                      size="compact"
                    />
                  </div>
                )}
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-gray-100 rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Filter className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-500 mb-6">Database appears to be empty. Seed the database with sample data.</p>
            <div className="flex gap-4 justify-center">
              <button
                onClick={async () => {
                  try {
                    console.log('🚨 Seeding database...');
                    await seedAllMissingData();
                    console.log('🚨 Database seeded successfully!');
                    // Refresh the page to load new data
                    window.location.reload();
                  } catch (error) {
                    console.error('🚨 Database seeding failed:', error);
                  }
                }}
                className="bg-wellness text-white px-6 py-3 rounded-xl hover:bg-wellness-hover transition-colors font-medium"
              >
                Seed Database
              </button>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('all');
                  setSelectedBrand('all');
                  setMinRating(0);
                  setShowTopRated(false);
                }}
                className="bg-gray-500 text-white px-6 py-3 rounded-xl hover:bg-gray-600 transition-colors font-medium"
              >
                Clear All Filters
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductsPage;
