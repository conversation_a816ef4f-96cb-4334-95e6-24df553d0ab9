import React from 'react';
import { Link } from 'react-router-dom';
import Header from './Header';
import { Package } from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Universal Header - Always Present */}
      <Header />
      
      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>
      
      {/* Universal Footer - Always Present */}
      <footer className="footer-dark" style={{ backgroundColor: 'hsl(220, 13%, 15%)', color: 'hsl(220, 13%, 91%)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-wellness rounded-lg flex items-center justify-center">
                  <Package className="w-5 h-5 text-white" strokeWidth={2} />
                </div>
                <span className="text-lg font-bold">NRTList</span>
              </div>
              <p className="text-gray-400">
                The premier platform for nicotine replacement therapy discovery and comparison.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Products</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/products" className="hover:text-white transition-colors">Browse All</Link></li>
                <li><Link to="/products?category=pouches" className="hover:text-white transition-colors">Pouches</Link></li>
                <li><Link to="/products?category=gum" className="hover:text-white transition-colors">Gum</Link></li>
                <li><Link to="/products?category=lozenges" className="hover:text-white transition-colors">Lozenges</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Resources</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/community" className="hover:text-white transition-colors">Help Center</Link></li>
                <li><Link to="/community" className="hover:text-white transition-colors">Community</Link></li>
                <li><Link to="/progress" className="hover:text-white transition-colors">Progress Tracking</Link></li>
                <li><Link to="/reviews" className="hover:text-white transition-colors">Reviews</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/community" className="hover:text-white transition-colors">About</Link></li>
                <li><Link to="/community" className="hover:text-white transition-colors">Privacy</Link></li>
                <li><Link to="/community" className="hover:text-white transition-colors">Terms</Link></li>
                <li><Link to="/community" className="hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t mt-16 pt-8 text-center" style={{ borderColor: 'hsl(220, 13%, 25%)', color: 'hsl(220, 9%, 70%)' }}>
            <p>&copy; 2024 NRTList. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
