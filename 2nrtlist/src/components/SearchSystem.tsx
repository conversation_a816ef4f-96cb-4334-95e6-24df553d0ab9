import React, { useState, useEffect } from 'react';
import { Search, Filter, SlidersHorizontal, MapPin, DollarSign, Star, Clock, Truck } from 'lucide-react';

interface SearchFilters {
  query: string;
  category: string[];
  brand: string[];
  strength: string[];
  flavor: string[];
  priceRange: [number, number];
  rating: number;
  availability: string;
  location: string;
  delivery: boolean;
  verified: boolean;
  deals: boolean;
  // Advanced Vivino/Weedmaps/RateBeer-inspired features
  tasteProfile: string[];
  matchPercentage: number;
  socialRating: number;
  communityReviews: boolean;
  vendorTier: string[];
  proximityRadius: number;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  featured: boolean;
  newProducts: boolean;
  trending: boolean;
  sustainableBrand: boolean;
  subscriptionAvailable: boolean;
  bulkPricing: boolean;
  sameDay: boolean;
  freeShipping: boolean;
  loyaltyProgram: boolean;
  personalizedRecommendations: boolean;
}

interface SearchSystemProps {
  onFiltersChange: (filters: SearchFilters) => void;
  productCount: number;
}

const SearchSystem: React.FC<SearchSystemProps> = ({ 
  onFiltersChange, 
  productCount 
}) => {
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    category: [],
    brand: [],
    strength: [],
    flavor: [],
    priceRange: [0, 100],
    rating: 0,
    availability: 'all',
    location: '',
    delivery: false,
    verified: false,
    deals: false,
    // Advanced Vivino/Weedmaps/RateBeer-inspired features
    tasteProfile: [],
    matchPercentage: 0,
    socialRating: 0,
    communityReviews: false,
    vendorTier: [],
    proximityRadius: 25,
    sortBy: 'relevance',
    sortOrder: 'desc',
    featured: false,
    newProducts: false,
    trending: false,
    sustainableBrand: false,
    subscriptionAvailable: false,
    bulkPricing: false,
    sameDay: false,
    freeShipping: false,
    loyaltyProgram: false,
    personalizedRecommendations: false
  });

  const [showAdvanced, setShowAdvanced] = useState(false);

  // Real NRT categories from database
  const categories = [
    'Nicotine Gum', 'Nicotine Patches', 'Nicotine Lozenges', 
    'Nicotine Pouches', 'Inhalers', 'Nasal Spray'
  ];

  // Real brands from market research
  const brands = [
    'Nicorette', 'NicoDerm CQ', 'Zyn', 'On!', 'Velo', 'Rogue', 
    'Lucy', 'Grinds', 'Nordic Spirit', 'Dryft'
  ];

  const strengths = ['1mg', '2mg', '3mg', '4mg', '6mg', '8mg', '10mg', '14mg', '21mg'];

  const flavors = [
    'Original', 'Mint', 'Cool Mint', 'Fresh Mint', 'Spearmint', 
    'Peppermint', 'Wintergreen', 'Citrus', 'Cinnamon', 'Coffee',
    'Cherry', 'Unflavored'
  ];

  // Advanced Vivino/Weedmaps/RateBeer-inspired constants
  const tasteProfiles = [
    'Smooth & Mild', 'Strong & Bold', 'Refreshing & Cool', 'Sweet & Fruity',
    'Herbal & Natural', 'Rich & Complex', 'Clean & Crisp', 'Warming & Spicy'
  ];

  const vendorTiers = [
    'Premium Partners', 'Verified Vendors', 'Local Retailers', 'Online Only',
    'Specialty Stores', 'Chain Pharmacies', 'Health Stores', 'Direct Brands'
  ];

  const sortOptions = [
    { value: 'relevance', label: 'Best Match' },
    { value: 'price-low', label: 'Price: Low to High' },
    { value: 'price-high', label: 'Price: High to Low' },
    { value: 'rating', label: 'Highest Rated' },
    { value: 'popularity', label: 'Most Popular' },
    { value: 'newest', label: 'Newest First' },
    { value: 'distance', label: 'Nearest First' },
    { value: 'availability', label: 'In Stock First' }
  ];

  useEffect(() => {
    onFiltersChange(filters);
  }, [filters, onFiltersChange]);

  const updateFilter = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const toggleArrayFilter = (key: 'category' | 'brand' | 'strength' | 'flavor', value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: prev[key].includes(value) 
        ? prev[key].filter(item => item !== value)
        : [...prev[key], value]
    }));
  };

  const clearFilters = () => {
    setFilters({
      query: '',
      category: [],
      brand: [],
      strength: [],
      flavor: [],
      priceRange: [0, 100],
      rating: 0,
      availability: 'all',
      location: '',
      delivery: false,
      verified: false,
      deals: false,
      // Advanced Vivino/Weedmaps/RateBeer-inspired features
      tasteProfile: [],
      matchPercentage: 0,
      socialRating: 0,
      communityReviews: false,
      vendorTier: [],
      proximityRadius: 25,
      sortBy: 'relevance',
      sortOrder: 'desc',
      featured: false,
      newProducts: false,
      trending: false,
      sustainableBrand: false,
      subscriptionAvailable: false,
      bulkPricing: false,
      sameDay: false,
      freeShipping: false,
      loyaltyProgram: false,
      personalizedRecommendations: false
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      {/* Main Search Bar */}
      <div className="flex items-center space-x-4 mb-6">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search NRT products, brands, flavors..."
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            value={filters.query}
            onChange={(e) => updateFilter('query', e.target.value)}
          />
        </div>
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className={`flex items-center space-x-2 px-4 py-3 rounded-lg border transition-colors ${
            showAdvanced 
              ? 'bg-green-600 text-white border-green-600' 
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          }`}
        >
          <SlidersHorizontal className="h-5 w-5" />
          <span>Filters</span>
        </button>
      </div>

      {/* Enhanced Results Summary with Advanced Sorting */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-600">
            {productCount} products found
            {filters.matchPercentage > 0 && (
              <span className="ml-2 text-green-600 font-medium">
                • {filters.matchPercentage}%+ match
              </span>
            )}
          </div>
          
          {/* Advanced Sorting Controls */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">Sort by:</span>
            <select
              value={filters.sortBy}
              onChange={(e) => updateFilter('sortBy', e.target.value)}
              className="text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            
            <button
              onClick={() => updateFilter('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}
              className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
              title={`Sort ${filters.sortOrder === 'asc' ? 'descending' : 'ascending'}`}
            >
              {filters.sortOrder === 'asc' ? '↑' : '↓'}
            </button>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Personalized Recommendations Toggle */}
          <label className="flex items-center text-sm">
            <input
              type="checkbox"
              checked={filters.personalizedRecommendations}
              onChange={(e) => updateFilter('personalizedRecommendations', e.target.checked)}
              className="rounded border-gray-300 text-green-600 focus:ring-green-500 mr-2"
            />
            <span className="text-gray-700">Personalized</span>
          </label>
          
          {(filters.category.length > 0 || filters.brand.length > 0 || filters.strength.length > 0 || 
            filters.flavor.length > 0 || filters.rating > 0 || filters.deals || filters.verified ||
            filters.tasteProfile.length > 0 || filters.vendorTier.length > 0 || filters.featured ||
            filters.trending || filters.newProducts) && (
            <button
              onClick={clearFilters}
              className="text-sm text-green-600 hover:text-green-700 font-medium"
            >
              Clear all filters
            </button>
          )}
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6 bg-gray-50 rounded-lg">
          {/* Category Filter */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Category</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {categories.map(category => (
                <label key={category} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.category.includes(category)}
                    onChange={() => toggleArrayFilter('category', category)}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">{category}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Brand Filter */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Brand</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {brands.map(brand => (
                <label key={brand} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.brand.includes(brand)}
                    onChange={() => toggleArrayFilter('brand', brand)}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">{brand}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Nicotine Strength Filter */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Nicotine Strength</h4>
            <div className="grid grid-cols-3 gap-2">
              {strengths.map(strength => (
                <label key={strength} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.strength.includes(strength)}
                    onChange={() => toggleArrayFilter('strength', strength)}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="ml-1 text-xs text-gray-700">{strength}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Flavor Filter */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Flavor</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {flavors.map(flavor => (
                <label key={flavor} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.flavor.includes(flavor)}
                    onChange={() => toggleArrayFilter('flavor', flavor)}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">{flavor}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Price Range */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Price Range</h4>
            <div className="space-y-3">
              <input
                type="range"
                min="0"
                max="100"
                value={filters.priceRange[1]}
                onChange={(e) => updateFilter('priceRange', [0, parseInt(e.target.value)])}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-gray-600">
                <span>$0</span>
                <span>${filters.priceRange[1]}</span>
              </div>
            </div>
          </div>

          {/* Rating Filter */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Minimum Rating</h4>
            <div className="flex items-center space-x-2">
              {[1, 2, 3, 4, 5].map(rating => (
                <button
                  key={rating}
                  onClick={() => updateFilter('rating', rating === filters.rating ? 0 : rating)}
                  className={`flex items-center space-x-1 px-2 py-1 rounded ${
                    filters.rating >= rating ? 'text-yellow-500' : 'text-gray-300'
                  }`}
                >
                  <Star className="h-4 w-4 fill-current" />
                </button>
              ))}
              <span className="text-sm text-gray-600 ml-2">
                {filters.rating > 0 ? `${filters.rating}+ stars` : 'Any rating'}
              </span>
            </div>
          </div>

          {/* Location & Availability */}
          <div className="md:col-span-2 lg:col-span-3">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Location</h4>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="ZIP code or city"
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    value={filters.location}
                    onChange={(e) => updateFilter('location', e.target.value)}
                  />
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Availability</h4>
                <select
                  value={filters.availability}
                  onChange={(e) => updateFilter('availability', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="all">All products</option>
                  <option value="in-stock">In stock only</option>
                  <option value="low-stock">Low stock</option>
                  <option value="pre-order">Pre-order available</option>
                </select>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Special Options</h4>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.delivery}
                      onChange={(e) => updateFilter('delivery', e.target.checked)}
                      className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                    />
                    <Truck className="h-4 w-4 ml-2 mr-1 text-gray-500" />
                    <span className="text-sm text-gray-700">Delivery available</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.verified}
                      onChange={(e) => updateFilter('verified', e.target.checked)}
                      className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                    />
                    <span className="text-sm text-gray-700 ml-2">Verified vendors only</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.deals}
                      onChange={(e) => updateFilter('deals', e.target.checked)}
                      className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                    />
                    <DollarSign className="h-4 w-4 ml-2 mr-1 text-gray-500" />
                    <span className="text-sm text-gray-700">On sale</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Advanced Vivino/Weedmaps/RateBeer-inspired Filter Sections */}
          
          {/* Taste Profile Filter */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Taste Profile</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {tasteProfiles.map(profile => (
                <label key={profile} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.tasteProfile.includes(profile)}
                    onChange={() => toggleArrayFilter('tasteProfile', profile)}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">{profile}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Vendor Tier Filter */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Vendor Type</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {vendorTiers.map(tier => (
                <label key={tier} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.vendorTier.includes(tier)}
                    onChange={() => toggleArrayFilter('vendorTier', tier)}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">{tier}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Match Percentage & Social Rating */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Smart Matching</h4>
            <div className="space-y-4">
              {/* Match Percentage Slider */}
              <div>
                <label className="block text-sm text-gray-600 mb-2">
                  Minimum Match: {filters.matchPercentage}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  step="5"
                  value={filters.matchPercentage}
                  onChange={(e) => updateFilter('matchPercentage', parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>
              
              {/* Social Rating Filter */}
              <div>
                <label className="block text-sm text-gray-600 mb-2">
                  Community Rating: {filters.socialRating > 0 ? `${filters.socialRating}+ stars` : 'Any'}
                </label>
                <div className="flex items-center space-x-1">
                  {[1, 2, 3, 4, 5].map(rating => (
                    <button
                      key={rating}
                      onClick={() => updateFilter('socialRating', filters.socialRating === rating ? 0 : rating)}
                      className={`flex items-center space-x-1 px-2 py-1 rounded ${
                        filters.socialRating >= rating ? 'text-yellow-500' : 'text-gray-300'
                      }`}
                    >
                      <Star className="h-4 w-4 fill-current" />
                    </button>
                  ))}
                </div>
              </div>
              
              {/* Community Reviews Toggle */}
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.communityReviews}
                  onChange={(e) => updateFilter('communityReviews', e.target.checked)}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="ml-2 text-sm text-gray-700">Has community reviews</span>
              </label>
            </div>
          </div>

          {/* Proximity & Location */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Location & Proximity</h4>
            <div className="space-y-4">
              {/* Proximity Radius */}
              <div>
                <label className="block text-sm text-gray-600 mb-2">
                  Search Radius: {filters.proximityRadius} miles
                </label>
                <input
                  type="range"
                  min="5"
                  max="100"
                  step="5"
                  value={filters.proximityRadius}
                  onChange={(e) => updateFilter('proximityRadius', parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>
            </div>
          </div>

          {/* Premium Features */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Premium Features</h4>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.featured}
                  onChange={(e) => updateFilter('featured', e.target.checked)}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="ml-2 text-sm text-gray-700">Featured products</span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.trending}
                  onChange={(e) => updateFilter('trending', e.target.checked)}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="ml-2 text-sm text-gray-700">Trending now</span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.newProducts}
                  onChange={(e) => updateFilter('newProducts', e.target.checked)}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="ml-2 text-sm text-gray-700">New arrivals</span>
              </label>
            </div>
          </div>

          {/* Shopping Preferences */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Shopping Preferences</h4>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.subscriptionAvailable}
                  onChange={(e) => updateFilter('subscriptionAvailable', e.target.checked)}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="ml-2 text-sm text-gray-700">Subscription available</span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.bulkPricing}
                  onChange={(e) => updateFilter('bulkPricing', e.target.checked)}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="ml-2 text-sm text-gray-700">Bulk pricing</span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.sameDay}
                  onChange={(e) => updateFilter('sameDay', e.target.checked)}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="ml-2 text-sm text-gray-700">Same-day delivery</span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.freeShipping}
                  onChange={(e) => updateFilter('freeShipping', e.target.checked)}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="ml-2 text-sm text-gray-700">Free shipping</span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.loyaltyProgram}
                  onChange={(e) => updateFilter('loyaltyProgram', e.target.checked)}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="ml-2 text-sm text-gray-700">Loyalty program</span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.sustainableBrand}
                  onChange={(e) => updateFilter('sustainableBrand', e.target.checked)}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="ml-2 text-sm text-gray-700">Sustainable brands</span>
              </label>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchSystem;
