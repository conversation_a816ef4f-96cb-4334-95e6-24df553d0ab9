import React, { useState, useEffect } from 'react';
import { Star } from 'lucide-react';
import { supabase } from '../lib/supabase';

interface ProductRatingProps {
  productId: string;
}

interface RatingData {
  averageRating: number;
  reviewCount: number;
  loading: boolean;
  error: string | null;
}

const ProductRating: React.FC<ProductRatingProps> = ({ productId }) => {
  const [ratingData, setRatingData] = useState<RatingData>({
    averageRating: 0,
    reviewCount: 0,
    loading: true,
    error: null
  });

  useEffect(() => {
    const fetchRatingData = async () => {
      try {
        setRatingData(prev => ({ ...prev, loading: true, error: null }));
        
        // Get all reviews for this product to calculate real average rating and count
        const { data: reviews, error } = await supabase
          .from('nrt_product_reviews')
          .select('overall_rating')
          .eq('product_id', productId);

        if (error) {
          console.error('Rating query error:', error);
          // Don't throw error, just handle empty state gracefully
          setRatingData({
            averageRating: 0,
            reviewCount: 0,
            loading: false,
            error: null
          });
          return;
        }

        if (!reviews || reviews.length === 0) {
          setRatingData({
            averageRating: 0,
            reviewCount: 0,
            loading: false,
            error: null
          });
          return;
        }

        // Calculate real average rating from database
        const totalRating = reviews.reduce((sum, review) => sum + review.overall_rating, 0);
        const averageRating = totalRating / reviews.length;

        setRatingData({
          averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
          reviewCount: reviews.length,
          loading: false,
          error: null
        });

      } catch (err) {
        console.error('Rating fetch error:', err);
        // Handle gracefully - show "No reviews yet" instead of error
        setRatingData({
          averageRating: 0,
          reviewCount: 0,
          loading: false,
          error: null
        });
      }
    };

    if (productId) {
      fetchRatingData();
    }
  }, [productId]);

  if (ratingData.loading) {
    return (
      <div className="flex items-center gap-1">
        <div className="animate-pulse">
          <div className="h-4 w-16 bg-gray-300 rounded"></div>
        </div>
      </div>
    );
  }

  if (ratingData.error) {
    return (
      <div className="flex items-center gap-1 text-red-500">
        <span className="text-sm">Error loading rating</span>
      </div>
    );
  }

  if (ratingData.reviewCount === 0) {
    return (
      <div className="flex items-center gap-1">
        <Star className="w-4 h-4 text-gray-300" />
        <span className="text-sm text-gray-500">No reviews yet</span>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-1">
      <Star className="w-4 h-4 text-yellow-400 fill-current" />
      <span className="text-sm font-medium text-gray-900">
        {ratingData.averageRating.toFixed(1)}
      </span>
      <span className="text-sm text-gray-500">
        ({ratingData.reviewCount} review{ratingData.reviewCount !== 1 ? 's' : ''})
      </span>
    </div>
  );
};

export default ProductRating;
