import React, { useState, useEffect } from 'react';
import { Tag, Clock, Zap, Gift, Percent, Users, Star, ExternalLink, Copy, Check } from 'lucide-react';
import { supabase } from '../lib/supabase';

interface Deal {
  id: string;
  title: string;
  description: string;
  discountType: 'percentage' | 'fixed' | 'bogo' | 'free-shipping' | 'loyalty-points' | 'referral-bonus';
  discountValue: number;
  code?: string;
  vendorName: string;
  vendorId: string;
  productName?: string;
  originalPrice: number;
  discountedPrice: number;
  validUntil: Date;
  usageLimit?: number;
  usedCount: number;
  minimumPurchase?: number;
  isFlashSale: boolean;
  isExclusive: boolean;
  isVerified: boolean;
  category: string;
  tags: string[];
  imageUrl?: string;
  // Weedmaps/RateBeer-inspired advanced features
  geoTargeting?: {
    enabled: boolean;
    targetStates?: string[];
    targetCities?: string[];
    radius?: number; // miles from user location
  };
  vendorPriority?: 'featured' | 'premium' | 'sponsored' | 'standard';
  dealTier?: 'platinum' | 'gold' | 'silver' | 'bronze';
  communityRating?: number; // 1-5 stars from community
  communityVotes?: number; // number of users who voted
  socialProof?: {
    recentClaims: number; // claims in last 24h
    popularityScore: number; // trending algorithm score
    userRecommendations: number; // user recommendations
  };
  bundleDeals?: string[]; // IDs of other deals that can be bundled
  loyaltyMultiplier?: number; // loyalty points multiplier
  newUserOnly?: boolean; // exclusive to new users
  returningUserBonus?: number; // additional discount for returning users
  timeRestrictions?: {
    daysOfWeek?: number[]; // 0=Sunday, 1=Monday, etc.
    startTime?: string; // "09:00"
    endTime?: string; // "17:00"
  };
  vendorBadges?: ('top-rated' | 'fast-shipping' | 'eco-friendly' | 'local-business')[];
  affiliateCommission?: number; // commission percentage for NRTList
  trackingPixel?: string; // for conversion tracking
}

interface DealsSystemProps {
  category?: string;
  vendorId?: string;
}

const DealsSystem: React.FC<DealsSystemProps> = ({ category, vendorId }) => {
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'flash' | 'exclusive' | 'expiring'>('all');
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  useEffect(() => {
    const loadDeals = async () => {
      try {
        setLoading(true);
        
        // Query real deals from Supabase with filtering
        let query = supabase
          .from('deals')
          .select(`
            id,
            title,
            description,
            discount_type,
            discount_value,
            code,
            vendor_name,
            vendor_id,
            product_name,
            original_price,
            discounted_price,
            valid_until,
            usage_limit,
            used_count,
            minimum_purchase,
            is_flash_sale,
            is_exclusive,
            is_verified,
            category,
            tags,
            image_url,
            active,
            geo_targeting,
            vendor_priority,
            deal_tier,
            community_rating,
            community_votes,
            social_proof,
            bundle_deals,
            loyalty_multiplier,
            new_user_only,
            returning_user_bonus,
            time_restrictions,
            vendor_badges,
            affiliate_commission,
            tracking_pixel
          `)
          .eq('active', true)
          .gte('valid_until', new Date().toISOString())
          .order('is_flash_sale', { ascending: false })
          .order('created_at', { ascending: false });

        // Apply category filter
        if (category && category !== 'all') {
          query = query.or(`category.eq.${category},tags.cs.{"${category}"}`);
        }

        // Apply vendor filter
        if (vendorId) {
          query = query.eq('vendor_id', vendorId);
        }

        const { data: dealsData, error } = await query;

        if (error) {
          console.error('Error loading deals:', error);
          setDeals([]);
          return;
        }

        // Transform database results to match Deal interface with advanced features
        const transformedDeals: Deal[] = (dealsData || []).map(deal => ({
          id: deal.id,
          title: deal.title,
          description: deal.description,
          discountType: deal.discount_type,
          discountValue: deal.discount_value,
          code: deal.code,
          vendorName: deal.vendor_name,
          vendorId: deal.vendor_id,
          productName: deal.product_name,
          originalPrice: deal.original_price,
          discountedPrice: deal.discounted_price,
          validUntil: new Date(deal.valid_until),
          usageLimit: deal.usage_limit,
          usedCount: deal.used_count,
          minimumPurchase: deal.minimum_purchase,
          isFlashSale: deal.is_flash_sale,
          isExclusive: deal.is_exclusive,
          isVerified: deal.is_verified,
          category: deal.category,
          tags: deal.tags || [],
          imageUrl: deal.image_url,
          // Advanced Weedmaps/RateBeer-inspired features
          geoTargeting: deal.geo_targeting ? JSON.parse(deal.geo_targeting) : undefined,
          vendorPriority: deal.vendor_priority,
          dealTier: deal.deal_tier,
          communityRating: deal.community_rating,
          communityVotes: deal.community_votes,
          socialProof: deal.social_proof ? JSON.parse(deal.social_proof) : undefined,
          bundleDeals: deal.bundle_deals ? JSON.parse(deal.bundle_deals) : undefined,
          loyaltyMultiplier: deal.loyalty_multiplier,
          newUserOnly: deal.new_user_only,
          returningUserBonus: deal.returning_user_bonus,
          timeRestrictions: deal.time_restrictions ? JSON.parse(deal.time_restrictions) : undefined,
          vendorBadges: deal.vendor_badges ? JSON.parse(deal.vendor_badges) : undefined,
          affiliateCommission: deal.affiliate_commission,
          trackingPixel: deal.tracking_pixel
        }));

        setDeals(transformedDeals);
      } catch (error) {
        console.error('Error loading deals:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDeals();
  }, [category, vendorId]);

  const filteredDeals = deals.filter(deal => {
    switch (filter) {
      case 'flash':
        return deal.isFlashSale;
      case 'exclusive':
        return deal.isExclusive;
      case 'expiring':
        return new Date(deal.validUntil).getTime() - Date.now() < 24 * 60 * 60 * 1000; // expires within 24h
      default:
        return true;
    }
  });

  const copyDiscountCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(code);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  const formatTimeRemaining = (validUntil: Date) => {
    const now = new Date();
    const timeLeft = validUntil.getTime() - now.getTime();
    
    if (timeLeft <= 0) return 'Expired';
    
    const days = Math.floor(timeLeft / (24 * 60 * 60 * 1000));
    const hours = Math.floor((timeLeft % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
    const minutes = Math.floor((timeLeft % (60 * 60 * 1000)) / (60 * 1000));
    
    if (days > 0) return `${days}d ${hours}h left`;
    if (hours > 0) return `${hours}h ${minutes}m left`;
    return `${minutes}m left`;
  };

  const getDiscountText = (deal: Deal) => {
    switch (deal.discountType) {
      case 'percentage':
        return `${deal.discountValue}% OFF`;
      case 'fixed':
        return `$${deal.discountValue} OFF`;
      case 'bogo':
        return 'BUY 2 GET 1 FREE';
      case 'free-shipping':
        return 'FREE SHIPPING';
      case 'loyalty-points':
        return `${deal.discountValue}x POINTS`;
      case 'referral-bonus':
        return `$${deal.discountValue} REFERRAL BONUS`;
      default:
        return 'SPECIAL OFFER';
    }
  };

  // Advanced Weedmaps/RateBeer-inspired helper functions
  const getDealTierColor = (tier?: string) => {
    switch (tier) {
      case 'platinum': return 'bg-gradient-to-r from-gray-100 to-gray-300 text-gray-800';
      case 'gold': return 'bg-gradient-to-r from-rating-gold-50 to-rating-gold text-rating-gold';
      case 'silver': return 'bg-gradient-to-r from-gray-50 to-gray-200 text-gray-700';
      case 'bronze': return 'bg-gradient-to-r from-rating-gold-50 to-rating-gold-50 text-rating-gold';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const getVendorPriorityBadge = (priority?: string) => {
    switch (priority) {
      case 'featured': return { text: 'FEATURED', class: 'bg-rating-gold text-white' };
      case 'premium': return { text: 'PREMIUM', class: 'bg-wellness text-white' };
      case 'sponsored': return { text: 'SPONSORED', class: 'bg-wellness text-white' };
      default: return null;
    }
  };

  const getCommunityRatingColor = (rating?: number) => {
    if (!rating) return 'text-gray-400';
    if (rating >= 4.5) return 'text-wellness';
    if (rating >= 4.0) return 'text-wellness';
    if (rating >= 3.5) return 'text-rating-gold';
    return 'text-alert-red';
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-40 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-gray-900 flex items-center">
            <Tag className="h-6 w-6 text-green-600 mr-2" />
            Active Deals & Discounts
          </h3>
          <p className="text-gray-600 mt-1">{filteredDeals.length} deals available</p>
        </div>

        {/* Filter Tabs */}
        <div className="flex space-x-2">
          {[
            { key: 'all', label: 'All Deals' },
            { key: 'flash', label: 'Flash Sales', icon: Zap },
            { key: 'exclusive', label: 'Exclusive', icon: Star },
            { key: 'expiring', label: 'Expiring Soon', icon: Clock }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setFilter(key as any)}
              className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                filter === key
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {Icon && <Icon className="h-4 w-4" />}
              <span>{label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Deals Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {filteredDeals.map((deal) => {
          const timeRemaining = formatTimeRemaining(deal.validUntil);
          const isExpiringSoon = new Date(deal.validUntil).getTime() - Date.now() < 24 * 60 * 60 * 1000;
          
          return (
            <div
              key={deal.id}
              className={`border rounded-lg p-6 hover:shadow-lg transition-shadow relative overflow-hidden ${
                deal.isFlashSale ? 'border-alert-red bg-alert-red-50' : 'border-gray-200'
              }`}
            >
              {/* Deal Badges */}
              <div className="absolute top-4 right-4 flex flex-col space-y-2">
                {/* Vendor Priority Badge */}
                {(() => {
                  const priorityBadge = getVendorPriorityBadge(deal.vendorPriority);
                  return priorityBadge ? (
                    <span className={`text-xs px-2 py-1 rounded-full font-bold ${priorityBadge.class}`}>
                      {priorityBadge.text}
                    </span>
                  ) : null;
                })()}
                
                {/* Deal Tier Badge */}
                {deal.dealTier && (
                  <span className={`text-xs px-2 py-1 rounded-full font-bold ${getDealTierColor(deal.dealTier)}`}>
                    {deal.dealTier.toUpperCase()}
                  </span>
                )}
                
                {deal.isFlashSale && (
                  <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold flex items-center">
                    <Zap className="h-3 w-3 mr-1" />
                    FLASH
                  </span>
                )}
                {deal.isExclusive && (
                  <span className="bg-purple-500 text-white text-xs px-2 py-1 rounded-full font-bold flex items-center">
                    <Star className="h-3 w-3 mr-1" />
                    EXCLUSIVE
                  </span>
                )}
                {deal.isVerified && (
                  <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-bold">
                    VERIFIED
                  </span>
                )}
                
                {/* New User / Returning User Badges */}
                {deal.newUserOnly && (
                  <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-bold">
                    NEW USER
                  </span>
                )}
                {deal.returningUserBonus && (
                  <span className="bg-indigo-500 text-white text-xs px-2 py-1 rounded-full font-bold">
                    RETURNING BONUS
                  </span>
                )}
              </div>

              {/* Deal Content */}
              <div className="pr-20">
                <div className="flex items-center space-x-2 mb-3">
                  <div className={`text-2xl font-bold ${
                    deal.isFlashSale ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {getDiscountText(deal)}
                  </div>
                </div>

                <h4 className="text-lg font-semibold text-gray-900 mb-2">{deal.title}</h4>
                <p className="text-gray-600 text-sm mb-4">{deal.description}</p>
                
                {/* Community Rating & Social Proof */}
                {(deal.communityRating || deal.socialProof) && (
                  <div className="flex items-center justify-between mb-3 text-sm">
                    {/* Community Rating */}
                    {deal.communityRating && (
                      <div className="flex items-center space-x-1">
                        <Star className={`h-4 w-4 ${getCommunityRatingColor(deal.communityRating)}`} />
                        <span className={`font-medium ${getCommunityRatingColor(deal.communityRating)}`}>
                          {deal.communityRating.toFixed(1)}
                        </span>
                        {deal.communityVotes && (
                          <span className="text-gray-500">({deal.communityVotes} votes)</span>
                        )}
                      </div>
                    )}
                    
                    {/* Social Proof */}
                    {deal.socialProof && (
                      <div className="text-gray-600 text-xs">
                        {deal.socialProof.recentClaims && (
                          <span>{deal.socialProof.recentClaims} recent claims</span>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {/* Vendor & Product Info with Badges */}
                <div className="flex items-center justify-between mb-4">
                  <div className="text-sm text-gray-600">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{deal.vendorName}</span>
                      {/* Vendor Badges */}
                      {deal.vendorBadges && deal.vendorBadges.length > 0 && (
                        <div className="flex space-x-1">
                          {deal.vendorBadges.slice(0, 2).map((badge, index) => (
                            <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                              {badge}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                    {deal.productName && (
                      <div className="text-gray-500">{deal.productName}</div>
                    )}
                    
                    {/* Bundle Deals Info */}
                    {deal.bundleDeals && deal.bundleDeals.length > 0 && (
                      <div className="text-xs text-blue-600 mt-1">
                        Bundle: {deal.bundleDeals[0].name} (+{deal.bundleDeals.length - 1} more)
                      </div>
                    )}
                  </div>
                  {deal.originalPrice > 0 && (
                    <div className="text-right">
                      <div className="text-sm text-gray-500 line-through">
                        ${deal.originalPrice.toFixed(2)}
                      </div>
                      <div className="text-lg font-bold text-green-600">
                        ${deal.discountedPrice.toFixed(2)}
                      </div>
                    </div>
                  )}
                </div>

                {/* Discount Code */}
                {deal.code && (
                  <div className="mb-4">
                    <div className="flex items-center space-x-2 p-3 bg-gray-100 rounded-lg">
                      <div className="flex-1">
                        <div className="text-xs text-gray-500 mb-1">Discount Code</div>
                        <div className="font-mono font-bold text-gray-900">{deal.code}</div>
                      </div>
                      <button
                        onClick={() => copyDiscountCode(deal.code!)}
                        className="flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        {copiedCode === deal.code ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                        <span className="text-sm">
                          {copiedCode === deal.code ? 'Copied!' : 'Copy'}
                        </span>
                      </button>
                    </div>
                  </div>
                )}

                {/* Enhanced Deal Stats with Advanced Features */}
                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <div className="flex items-center space-x-4">
                    {deal.usageLimit && (
                      <div className="flex items-center space-x-1">
                        <Users className="h-4 w-4" />
                        <span>{deal.usedCount}/{deal.usageLimit} used</span>
                      </div>
                    )}
                    {deal.minimumPurchase && (
                      <div>Min. purchase: ${deal.minimumPurchase}</div>
                    )}
                    {/* Loyalty Multiplier */}
                    {deal.loyaltyMultiplier && deal.loyaltyMultiplier > 1 && (
                      <div className="flex items-center space-x-1 text-purple-600">
                        <span>{deal.loyaltyMultiplier}x points</span>
                      </div>
                    )}
                    {/* Geo Targeting */}
                    {deal.geoTargeting && deal.geoTargeting.states && (
                      <div className="text-xs text-gray-500">
                        Available in {deal.geoTargeting.states.join(', ')}
                      </div>
                    )}
                  </div>
                  <div className={`flex items-center space-x-1 ${
                    isExpiringSoon ? 'text-red-600 font-medium' : ''
                  }`}>
                    <Clock className="h-4 w-4" />
                    <span>{timeRemaining}</span>
                  </div>
                </div>

                {/* Progress Bar for Usage Limit */}
                {deal.usageLimit && (
                  <div className="mb-4">
                    <div className="bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full transition-all"
                        style={{ width: `${(deal.usedCount / deal.usageLimit) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Enhanced Action Button with Advanced Features */}
                <div className="space-y-2">
                  {/* Time Restrictions Warning */}
                  {deal.timeRestrictions && deal.timeRestrictions.daysOfWeek && (
                    <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded">
                      Available: {deal.timeRestrictions.daysOfWeek.join(', ')}
                      {deal.timeRestrictions.startTime && deal.timeRestrictions.endTime && (
                        <span> ({deal.timeRestrictions.startTime}-{deal.timeRestrictions.endTime})</span>
                      )}
                    </div>
                  )}
                  
                  <button className="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors font-medium flex items-center justify-center space-x-2">
                    <span>Claim Deal</span>
                    <ExternalLink className="h-4 w-4" />
                  </button>
                  
                  {/* Affiliate Commission Info (for transparency) */}
                  {deal.affiliateCommission && (
                    <div className="text-xs text-gray-400 text-center">
                      We earn a commission when you make a purchase
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredDeals.length === 0 && (
        <div className="text-center py-12">
          <Gift className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No deals available</h3>
          <p className="text-gray-600">Check back later for new discounts and offers.</p>
        </div>
      )}
    </div>
  );
};

export default DealsSystem;
