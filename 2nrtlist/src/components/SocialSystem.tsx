import React, { useState, useEffect, useMemo } from 'react';
import { 
  CheckCircle, MapPin, Users, Award, Star, Target, Sparkles, Rocket,
  Camera, Heart, MessageCircle, Share2, TrendingUp, Trophy, Crown,
  Globe, Pin, Calendar, Clock, Filter, Search, UserPlus, Settings,
  ThumbsUp, Eye, Hash, Flame, Zap, Shield, Medal, Gift
} from 'lucide-react';
import { supabase } from '../lib/supabase';

// Enhanced CheckIn interface with advanced Vivino/RateBeer social features
interface CheckIn {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  productId: string;
  productName: string;
  productBrand: string;
  rating: number;
  review?: string;
  photos: string[];
  location?: {
    name: string;
    city: string;
    state: string;
    coordinates?: { lat: number; lng: number };
  };
  createdAt: Date;
  likes: number;
  comments: number;
  shares: number;
  views: number;
  badges: string[];
  // Advanced social features (Vivino/RateBeer-inspired)
  hashtags: string[];
  mentions: string[];
  isPublic: boolean;
  mood?: 'excited' | 'hopeful' | 'determined' | 'satisfied' | 'proud';
  quitGoal?: string;
  milestone?: number; // days smoke-free
  socialContext?: {
    friends: string[]; // user IDs of friends who also tried this product
    trending: boolean; // is this product trending
    firstTry: boolean; // is this user's first try of this product
    recommended: boolean; // was this recommended by someone
  };
  engagement: {
    likeUsers: string[];
    commentUsers: string[];
    shareUsers: string[];
  };
  moderation: {
    flagged: boolean;
    flagReason?: string;
    approved: boolean;
  };
}

// Enhanced Achievement interface with advanced social features (Vivino/RateBeer-inspired)
interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: JSX.Element | string; // Allow both for flexibility
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  progress: number;
  maxProgress: number;
  unlocked: boolean;
  // Advanced features
  category: 'social' | 'milestone' | 'exploration' | 'quality' | 'community';
  points: number;
  unlockedAt?: Date;
  shareCount: number;
  prerequisites?: string[]; // achievement IDs that must be unlocked first
  rewards?: {
    badge: string;
    title?: string;
    discount?: number; // percentage discount on products
  };
}

interface SocialCheckInSystemProps {
  productId?: string;
  productName?: string;
  productBrand?: string;
}

const SocialCheckInSystem: React.FC<SocialCheckInSystemProps> = ({
  productId,
  productName,
  productBrand
}) => {
  const [recentCheckIns, setRecentCheckIns] = useState<CheckIn[]>([]);
  const [userAchievements, setUserAchievements] = useState<Achievement[]>([]);
  const [showCheckInModal, setShowCheckInModal] = useState(false);
  const [newCheckIn, setNewCheckIn] = useState({
    rating: 0,
    review: '',
    photos: [] as string[],
    location: ''
  });
  const [loading, setLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState<any>(null);

  // Achievement definitions inspired by Untappd's badge system
  const achievementTemplates = [
    {
      name: 'First Step',
      description: 'Complete your first NRT check-in',
      icon: <Target className="h-4 w-4" />,
      rarity: 'common' as const,
      maxProgress: 1
    },
    {
      name: 'Dedication',
      description: 'Check in 10 different NRT products',
      icon: '💪',
      rarity: 'common' as const,
      maxProgress: 10
    },
    {
      name: 'Explorer',
      description: 'Try NRT products from 5 different brands',
      icon: '🗺️',
      rarity: 'rare' as const,
      maxProgress: 5
    },
    {
      name: 'Smoke-Free Warrior',
      description: 'Check in NRT products for 30 consecutive days',
      icon: '⚔️',
      rarity: 'epic' as const,
      maxProgress: 30
    },
    {
      name: 'Quit Master',
      description: 'Rate 100 NRT products',
      icon: '👑',
      rarity: 'legendary' as const,
      maxProgress: 100
    },
    {
      name: 'Community Leader',
      description: 'Get 50 likes on your check-ins',
      icon: <Sparkles className="h-4 w-4" />,
      rarity: 'epic' as const,
      maxProgress: 50
    },
    {
      name: 'Photographer',
      description: 'Upload photos with 25 check-ins',
      icon: '📸',
      rarity: 'rare' as const,
      maxProgress: 25
    },
    {
      name: 'Trendsetter',
      description: 'Be first to check in a new product',
      icon: <Rocket className="h-4 w-4" />,
      rarity: 'legendary' as const,
      maxProgress: 1
    }
  ];

  useEffect(() => {
    loadRecentCheckIns();
    loadCurrentUser();
    if (currentUser) {
      loadUserAchievements();
    }
  }, [currentUser]);

  const loadCurrentUser = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);
    } catch (error) {
      console.error('Error loading user:', error);
    }
  };

  const loadRecentCheckIns = async () => {
    try {
      setLoading(true);
      
      let query = supabase
        .from('product_checkins')
        .select(`
          *,
          profiles (
            username,
            avatar_url
          ),
          products (
            name,
            brand
          )
        `)
        .order('created_at', { ascending: false })
        .limit(20);

      if (productId) {
        query = query.eq('product_id', productId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error loading check-ins:', error);
        return;
      }

      // Enhanced data mapping with advanced Vivino/RateBeer social features (all real data, zero mockups)
      const formattedCheckIns: CheckIn[] = (data || []).map(checkIn => ({
        id: checkIn.id,
        userId: checkIn.user_id,
        userName: checkIn.profiles?.username || 'Anonymous',
        userAvatar: checkIn.profiles?.avatar_url,
        productId: checkIn.product_id,
        productName: checkIn.products?.name || productName || 'Unknown Product',
        productBrand: checkIn.products?.brand || productBrand || 'Unknown Brand',
        rating: checkIn.rating || 0,
        review: checkIn.review,
        photos: checkIn.photos || [],
        location: checkIn.location ? {
          name: checkIn.location.name || '',
          city: checkIn.location.city || '',
          state: checkIn.location.state || '',
          coordinates: checkIn.location.coordinates
        } : undefined,
        createdAt: new Date(checkIn.created_at),
        likes: checkIn.likes || 0,
        comments: checkIn.comments_count || 0,
        shares: checkIn.shares_count || 0,
        views: checkIn.views_count || 0,
        badges: checkIn.badges || [],
        // Advanced social features (real data from database)
        hashtags: checkIn.hashtags || [],
        mentions: checkIn.mentions || [],
        isPublic: checkIn.is_public !== false,
        mood: checkIn.mood,
        quitGoal: checkIn.quit_goal,
        milestone: checkIn.milestone_days,
        socialContext: {
          friends: checkIn.friend_users || [],
          trending: checkIn.is_trending || false,
          firstTry: checkIn.is_first_try || false,
          recommended: checkIn.is_recommended || false
        },
        engagement: {
          likeUsers: checkIn.like_users || [],
          commentUsers: checkIn.comment_users || [],
          shareUsers: checkIn.share_users || []
        },
        moderation: {
          flagged: checkIn.is_flagged || false,
          flagReason: checkIn.flag_reason,
          approved: checkIn.is_approved !== false
        }
      }));

      setRecentCheckIns(formattedCheckIns);
    } catch (error) {
      console.error('Error loading check-ins:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadUserAchievements = async () => {
    if (!currentUser) return;

    try {
      // Load user's check-in statistics
      const { data: userStats, error } = await supabase
        .from('user_stats')
        .select('*')
        .eq('user_id', currentUser.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error loading user stats:', error);
        return;
      }

      const stats = userStats || {
        total_checkins: 0,
        unique_products: 0,
        unique_brands: 0,
        consecutive_days: 0,
        total_likes: 0,
        photos_uploaded: 0
      };

      // Calculate progress for each achievement
      const achievements: Achievement[] = achievementTemplates.map((template, index) => {
        let progress = 0;
        
        switch (template.name) {
          case 'First Step':
            progress = Math.min(stats.total_checkins, 1);
            break;
          case 'Dedication':
            progress = Math.min(stats.unique_products, 10);
            break;
          case 'Explorer':
            progress = Math.min(stats.unique_brands, 5);
            break;
          case 'Smoke-Free Warrior':
            progress = Math.min(stats.consecutive_days, 30);
            break;
          case 'Quit Master':
            progress = Math.min(stats.total_checkins, 100);
            break;
          case 'Community Leader':
            progress = Math.min(stats.total_likes, 50);
            break;
          case 'Photographer':
            progress = Math.min(stats.photos_uploaded, 25);
            break;
          case 'Trendsetter':
            progress = stats.first_checkins || 0;
            break;
          default:
            progress = 0;
        }

        return {
          id: `achievement_${index}`,
          name: template.name,
          description: template.description,
          icon: template.icon,
          rarity: template.rarity,
          progress,
          maxProgress: template.maxProgress,
          unlocked: progress >= template.maxProgress
        };
      });

      setUserAchievements(achievements);
    } catch (error) {
      console.error('Error loading achievements:', error);
    }
  };

  const submitCheckIn = async () => {
    if (!currentUser || !productId || newCheckIn.rating === 0) return;

    try {
      const checkInData = {
        user_id: currentUser.id,
        product_id: productId,
        product_name: productName,
        product_brand: productBrand,
        rating: newCheckIn.rating,
        review: newCheckIn.review || null,
        photos: newCheckIn.photos,
        location: newCheckIn.location ? JSON.parse(newCheckIn.location) : null,
        created_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('product_checkins')
        .insert(checkInData);

      if (error) {
        console.error('Error submitting check-in:', error);
        return;
      }

      // Reset form
      setNewCheckIn({
        rating: 0,
        review: '',
        photos: [],
        location: ''
      });
      setShowCheckInModal(false);

      // Reload data
      loadRecentCheckIns();
      loadUserAchievements();
    } catch (error) {
      console.error('Error submitting check-in:', error);
    }
  };

  const renderStars = (rating: number, interactive: boolean = false, onRatingSelect?: (rating: number) => void) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map(star => (
          <Star 
            key={star} 
            className={`h-5 w-5 cursor-pointer transition-colors ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300 hover:text-yellow-200'
            }`}
            onClick={() => interactive && onRatingSelect && onRatingSelect(star)}
          />
        ))}
      </div>
    );
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'border-gray-300 bg-gray-50';
      case 'rare': return 'border-blue-300 bg-blue-50';
      case 'epic': return 'border-purple-300 bg-purple-50';
      case 'legendary': return 'border-yellow-300 bg-yellow-50';
      default: return 'border-gray-300 bg-gray-50';
    }
  };

  return (
    <div className="space-y-6">
      {/* Quick Check-In Button */}
      {productId && currentUser && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <CheckCircle className="h-6 w-6 text-green-600" />
              <div>
                <h4 className="font-medium text-green-900">Check in this product</h4>
                <p className="text-sm text-green-700">Share your experience with the community</p>
              </div>
            </div>
            <button
              onClick={() => setShowCheckInModal(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium"
            >
              Check In
            </button>
          </div>
        </div>
      )}

      {/* Recent Check-Ins Feed */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-900 flex items-center">
            <Users className="h-6 w-6 text-green-600 mr-2" />
            Recent Check-Ins
          </h3>
          <div className="text-right">
            <div className="text-2xl font-bold text-green-600">{recentCheckIns.length}</div>
            <div className="text-sm text-gray-600">Recent Activity</div>
          </div>
        </div>

        {loading ? (
          <div className="text-center py-8">
            <div className="text-gray-500">Loading recent activity...</div>
          </div>
        ) : recentCheckIns.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-500 mb-4">No recent check-ins.</div>
            <p className="text-sm text-gray-400">Be the first to check in and start the conversation!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {recentCheckIns.map(checkIn => (
              <div key={checkIn.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start space-x-3">
                  {checkIn.userAvatar ? (
                    <img 
                      src={checkIn.userAvatar} 
                      alt={checkIn.userName}
                      className="h-10 w-10 rounded-full"
                    />
                  ) : (
                    <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 font-medium text-sm">
                        {checkIn.userName.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <span className="font-medium text-gray-900">{checkIn.userName}</span>
                        <span className="text-gray-600 mx-2">checked in</span>
                        <span className="font-medium text-green-600">
                          {checkIn.productBrand} {checkIn.productName}
                        </span>
                      </div>
                      <div className="text-sm text-gray-500">
                        {checkIn.createdAt.toLocaleDateString()}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4 mb-2">
                      {renderStars(checkIn.rating)}
                      {checkIn.location && (
                        <div className="flex items-center text-sm text-gray-600">
                          <MapPin className="h-4 w-4 mr-1" />
                          {checkIn.location.name}, {checkIn.location.city}
                        </div>
                      )}
                    </div>
                    
                    {checkIn.review && (
                      <p className="text-gray-700 mb-3">{checkIn.review}</p>
                    )}
                    
                    {checkIn.photos.length > 0 && (
                      <div className="flex space-x-2 mb-3">
                        {checkIn.photos.slice(0, 3).map((photo, idx) => (
                          <img 
                            key={idx}
                            src={photo}
                            alt={`Check-in photo ${idx + 1}`}
                            className="h-16 w-16 object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
                          />
                        ))}
                        {checkIn.photos.length > 3 && (
                          <div className="h-16 w-16 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600 text-xs">
                            +{checkIn.photos.length - 3}
                          </div>
                        )}
                      </div>
                    )}
                    
                    {checkIn.badges.length > 0 && (
                      <div className="flex items-center space-x-2 mb-3">
                        <Award className="h-4 w-4 text-yellow-500" />
                        <span className="text-sm text-gray-600">
                          Unlocked {checkIn.badges.length} badge{checkIn.badges.length !== 1 ? 's' : ''}
                        </span>
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <button className="text-sm text-gray-600 hover:text-red-500 transition-colors flex items-center">
                        <span className="mr-1">❤️</span>
                        {checkIn.likes} likes
                      </button>
                      <button className="text-sm text-gray-600 hover:text-green-600 transition-colors">
                        Comment
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* User Achievements */}
      {currentUser && userAchievements.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
            <Award className="h-6 w-6 text-yellow-500 mr-2" />
            Your Achievements
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {userAchievements.map(achievement => (
              <div 
                key={achievement.id} 
                className={`p-4 rounded-lg border-2 transition-all ${
                  achievement.unlocked 
                    ? getRarityColor(achievement.rarity) + ' shadow-md' 
                    : 'border-gray-200 bg-gray-50 opacity-60'
                }`}
              >
                <div className="text-center">
                  <div className="text-3xl mb-2">{achievement.icon}</div>
                  <h4 className={`font-medium mb-1 ${
                    achievement.unlocked ? 'text-gray-900' : 'text-gray-600'
                  }`}>
                    {achievement.name}
                  </h4>
                  <p className="text-xs text-gray-600 mb-3">{achievement.description}</p>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(achievement.progress / achievement.maxProgress) * 100}%` }}
                    ></div>
                  </div>
                  
                  <div className="text-xs text-gray-600">
                    {achievement.progress}/{achievement.maxProgress}
                    {achievement.unlocked && (
                      <span className="ml-2 text-green-600 font-medium">✓ Unlocked!</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Check-In Modal */}
      {showCheckInModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Check In Product</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Rate this product:
                </label>
                {renderStars(newCheckIn.rating, true, (rating) => 
                  setNewCheckIn(prev => ({ ...prev, rating }))
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Share your thoughts (optional):
                </label>
                <textarea
                  value={newCheckIn.review}
                  onChange={(e) => setNewCheckIn(prev => ({ ...prev, review: e.target.value }))}
                  placeholder="How was your experience with this product?"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  rows={3}
                />
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowCheckInModal(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={submitCheckIn}
                  disabled={newCheckIn.rating === 0}
                  className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Check In
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SocialCheckInSystem;
