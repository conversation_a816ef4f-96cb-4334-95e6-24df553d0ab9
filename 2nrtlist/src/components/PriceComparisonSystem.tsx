import React, { useState, useEffect } from 'react';
import { DollarSign, ExternalLink, Truck, Clock, Shield, Tag, TrendingDown, Star, MapPin } from 'lucide-react';
import { supabase } from '../lib/supabase';

interface VendorPrice {
  id: string;
  vendorName: string;
  vendorLogo?: string;
  price: number;
  originalPrice?: number;
  discount?: number;
  discountCode?: string;
  rating: number;
  reviewCount: number;
  verified: boolean;
  deliveryTime: string;
  deliveryFee: number;
  freeShipping: boolean;
  inStock: boolean;
  stockLevel: number;
  location: string;
  distance?: number;
  affiliateUrl: string;
  lastUpdated: Date;
  dealType?: 'flash-sale' | 'bulk-discount' | 'member-price' | 'clearance';
  // Vivino-inspired advanced features
  matchPercentage?: number; // Personalized compatibility score (0-100)
  tasteProfileMatch?: 'excellent' | 'good' | 'fair' | 'unknown';
  subscriptionPrice?: number; // Subscription/membership price
  bulkPricing?: { quantity: number; unitPrice: number; totalSavings: number }[];
  loyaltyPoints?: number; // Points earned from purchase
  priceHistory?: { date: Date; price: number }[]; // Historical pricing
  vendorSpecialty?: string; // Vendor's specialty/focus area
  exclusiveDeals?: boolean; // Vendor has exclusive partnerships
  sameDay?: boolean; // Same-day delivery available
  subscriptionEligible?: boolean; // Available for subscription
  carbonNeutral?: boolean; // Eco-friendly shipping
  returnPolicy?: string; // Return/exchange policy
  paymentMethods?: string[]; // Accepted payment methods
}

interface PriceComparisonSystemProps {
  productId: string;
  productName: string;
}

const PriceComparisonSystem: React.FC<PriceComparisonSystemProps> = ({ 
  productId, 
  productName 
}) => {
  const [vendors, setVendors] = useState<VendorPrice[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState<'price' | 'rating' | 'delivery' | 'distance' | 'match' | 'subscription'>('price');
  const [showOnlyInStock, setShowOnlyInStock] = useState(false);
  const [showOnlyVerified, setShowOnlyVerified] = useState(false);
  // Advanced Vivino-inspired filtering options
  const [minMatchPercentage, setMinMatchPercentage] = useState(0);
  const [tasteProfileFilter, setTasteProfileFilter] = useState<'all' | 'excellent' | 'good' | 'fair'>('all');
  const [showSubscriptionOnly, setShowSubscriptionOnly] = useState(false);
  const [showSameDayOnly, setShowSameDayOnly] = useState(false);
  const [showCarbonNeutralOnly, setShowCarbonNeutralOnly] = useState(false);
  const [showBulkPricingOnly, setShowBulkPricingOnly] = useState(false);

  useEffect(() => {
    const loadPrices = async () => {
      try {
        setLoading(true);
        
        // Query real vendor price data from Supabase
        const { data: vendorData, error } = await supabase
          .from('vendor_prices')
          .select(`
            id,
            vendor_name,
            vendor_logo,
            price,
            original_price,
            discount_percentage,
            discount_code,
            rating,
            review_count,
            verified,
            delivery_time,
            delivery_fee,
            free_shipping,
            in_stock,
            stock_level,
            location,
            distance,
            affiliate_url,
            last_updated,
            deal_type,
            match_percentage,
            taste_profile_match,
            subscription_price,
            bulk_pricing,
            loyalty_points,
            price_history,
            vendor_specialty,
            exclusive_deals,
            same_day,
            subscription_eligible,
            carbon_neutral,
            return_policy,
            payment_methods
          `)
          .eq('product_id', productId)
          .eq('active', true)
          .order('price', { ascending: true });

        if (error) {
          console.error('Error loading vendor prices:', error);
          setVendors([]);
          return;
        }

        // Transform database results to match VendorPrice interface with advanced features
        const vendorPrices: VendorPrice[] = (vendorData || []).map(vendor => ({
          id: vendor.id,
          vendorName: vendor.vendor_name,
          vendorLogo: vendor.vendor_logo,
          price: vendor.price,
          originalPrice: vendor.original_price,
          discount: vendor.discount_percentage,
          discountCode: vendor.discount_code,
          rating: vendor.rating,
          reviewCount: vendor.review_count,
          verified: vendor.verified,
          deliveryTime: vendor.delivery_time,
          deliveryFee: vendor.delivery_fee,
          freeShipping: vendor.free_shipping,
          inStock: vendor.in_stock,
          stockLevel: vendor.stock_level,
          location: vendor.location,
          distance: vendor.distance,
          affiliateUrl: vendor.affiliate_url,
          lastUpdated: new Date(vendor.last_updated),
          dealType: vendor.deal_type,
          // Advanced Vivino-inspired features
          matchPercentage: vendor.match_percentage,
          tasteProfileMatch: vendor.taste_profile_match,
          subscriptionPrice: vendor.subscription_price,
          bulkPricing: vendor.bulk_pricing ? JSON.parse(vendor.bulk_pricing) : undefined,
          loyaltyPoints: vendor.loyalty_points,
          priceHistory: vendor.price_history ? JSON.parse(vendor.price_history).map((p: any) => ({ ...p, date: new Date(p.date) })) : undefined,
          vendorSpecialty: vendor.vendor_specialty,
          exclusiveDeals: vendor.exclusive_deals,
          sameDay: vendor.same_day,
          subscriptionEligible: vendor.subscription_eligible,
          carbonNeutral: vendor.carbon_neutral,
          returnPolicy: vendor.return_policy,
          paymentMethods: vendor.payment_methods ? JSON.parse(vendor.payment_methods) : undefined
        }));

        setVendors(vendorPrices);
      } catch (error) {
        console.error('Error loading vendor prices:', error);
      } finally {
        setLoading(false);
      }
    };

    loadPrices();
  }, [productId]);

  const filteredAndSortedVendors = vendors
    .filter(vendor => !showOnlyInStock || vendor.inStock)
    .filter(vendor => !showOnlyVerified || vendor.verified)
    // Advanced Vivino-inspired filtering
    .filter(vendor => !minMatchPercentage || (vendor.matchPercentage || 0) >= minMatchPercentage)
    .filter(vendor => tasteProfileFilter === 'all' || vendor.tasteProfileMatch === tasteProfileFilter)
    .filter(vendor => !showSubscriptionOnly || vendor.subscriptionEligible)
    .filter(vendor => !showSameDayOnly || vendor.sameDay)
    .filter(vendor => !showCarbonNeutralOnly || vendor.carbonNeutral)
    .filter(vendor => !showBulkPricingOnly || (vendor.bulkPricing && vendor.bulkPricing.length > 0))
    .sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return a.price - b.price;
        case 'rating':
          return b.rating - a.rating;
        case 'delivery':
          return a.deliveryTime.localeCompare(b.deliveryTime);
        case 'distance':
          return (a.distance || 999) - (b.distance || 999);
        case 'match':
          return (b.matchPercentage || 0) - (a.matchPercentage || 0);
        case 'subscription':
          // Sort by subscription savings (original - subscription price)
          const aSavings = a.subscriptionPrice ? a.price - a.subscriptionPrice : 0;
          const bSavings = b.subscriptionPrice ? b.price - b.subscriptionPrice : 0;
          return bSavings - aSavings;
        default:
          return 0;
      }
    });

  const handleAffiliateClick = (vendor: VendorPrice) => {
    // Track affiliate click for monetization
    // In production, this would track conversion analytics
    console.log(`Affiliate click tracked: ${vendor.vendorName} - ${productName}`);
    window.open(vendor.affiliateUrl, '_blank');
  };

  const lowestPrice = vendors.reduce((min, vendor) => 
    vendor.price < min ? vendor.price : min, Infinity
  );

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-gray-900 flex items-center">
            <DollarSign className="h-6 w-6 text-green-600 mr-2" />
            Price Comparison
          </h3>
          <p className="text-gray-600 mt-1">Compare prices from {vendors.length} verified vendors</p>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-500">Best price</div>
          <div className="text-2xl font-bold text-green-600">${lowestPrice.toFixed(2)}</div>
        </div>
      </div>

      {/* Enhanced Controls with Vivino-inspired Advanced Features */}
      <div className="space-y-4 mb-6 p-4 bg-gray-50 rounded-lg">
        {/* Primary Controls Row */}
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Sort by</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="price">Lowest Price</option>
                <option value="rating">Highest Rating</option>
                <option value="delivery">Fastest Delivery</option>
                <option value="distance">Nearest Location</option>
                <option value="match">Best Match</option>
                <option value="subscription">Best Subscription Savings</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Match %</label>
              <input
                type="range"
                min="0"
                max="100"
                value={minMatchPercentage}
                onChange={(e) => setMinMatchPercentage(Number(e.target.value))}
                className="w-20 accent-green-600"
              />
              <span className="text-xs text-gray-600 ml-1">{minMatchPercentage}%+</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Taste Profile</label>
              <select
                value={tasteProfileFilter}
                onChange={(e) => setTasteProfileFilter(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="all">All Matches</option>
                <option value="excellent">Excellent Match</option>
                <option value="good">Good Match</option>
                <option value="fair">Fair Match</option>
              </select>
            </div>
          </div>
        </div>

        {/* Advanced Filters Row */}
        <div className="flex flex-wrap items-center gap-4 pt-2 border-t border-gray-200">
          <div className="text-sm font-medium text-gray-700">Advanced Filters:</div>

          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showOnlyInStock}
                onChange={(e) => setShowOnlyInStock(e.target.checked)}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
              <span className="ml-2 text-sm text-gray-700">In stock only</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showOnlyVerified}
                onChange={(e) => setShowOnlyVerified(e.target.checked)}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
              <span className="ml-2 text-sm text-gray-700">Verified only</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showSubscriptionOnly}
                onChange={(e) => setShowSubscriptionOnly(e.target.checked)}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
              <span className="ml-2 text-sm text-gray-700">Subscription available</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showSameDayOnly}
                onChange={(e) => setShowSameDayOnly(e.target.checked)}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
              <span className="ml-2 text-sm text-gray-700">Same-day delivery</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showCarbonNeutralOnly}
                onChange={(e) => setShowCarbonNeutralOnly(e.target.checked)}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
              <span className="ml-2 text-sm text-gray-700">Carbon neutral</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showBulkPricingOnly}
                onChange={(e) => setShowBulkPricingOnly(e.target.checked)}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
              <span className="ml-2 text-sm text-gray-700">Bulk pricing</span>
            </label>
          </div>
        </div>

      </div>

      {/* Vendor List */}
      <div className="space-y-4">
        {filteredAndSortedVendors.map((vendor) => (
          <div 
            key={vendor.id} 
            className={`border rounded-lg p-4 hover:shadow-md transition-shadow ${
              vendor.price === lowestPrice ? 'border-green-500 bg-green-50' : 'border-gray-200'
            }`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Vendor Info */}
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h4 className="font-semibold text-gray-900">{vendor.vendorName}</h4>
                    {vendor.verified && (
                      <Shield className="h-4 w-4 text-green-600" />
                    )}
                    {vendor.price === lowestPrice && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium">
                        Best Price
                      </span>
                    )}
                    {vendor.dealType && (
                      <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium">
                        {vendor.dealType === 'flash-sale' && '⚡ Flash Sale'}
                        {vendor.dealType === 'bulk-discount' && '📦 Bulk Discount'}
                        {vendor.dealType === 'member-price' && '👑 Member Price'}
                        {vendor.dealType === 'clearance' && '🏷️ Clearance'}
                      </span>
                    )}
                  </div>

                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span>{vendor.rating}</span>
                      <span>({vendor.reviewCount} reviews)</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{vendor.deliveryTime}</span>
                    </div>
                    {vendor.distance && (
                      <div className="flex items-center space-x-1">
                        <MapPin className="h-4 w-4" />
                        <span>{vendor.distance} mi</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Price & Actions */}
              <div className="text-right">
                <div className="flex items-center justify-end space-x-2 mb-2">
                  {vendor.originalPrice && vendor.originalPrice > vendor.price && (
                    <span className="text-sm text-gray-500 line-through">
                      ${vendor.originalPrice.toFixed(2)}
                    </span>
                  )}
                  <span className="text-2xl font-bold text-gray-900">
                    ${vendor.price.toFixed(2)}
                  </span>
                  {vendor.discount && (
                    <span className="bg-red-100 text-red-800 text-sm px-2 py-1 rounded font-medium">
                      -{vendor.discount}%
                    </span>
                  )}
                </div>

                <div className="flex items-center justify-end space-x-2 mb-3">
                  {vendor.freeShipping ? (
                    <span className="text-sm text-green-600 font-medium">Free shipping</span>
                  ) : (
                    <span className="text-sm text-gray-600">+${vendor.deliveryFee} shipping</span>
                  )}
                  {vendor.discountCode && (
                    <div className="flex items-center space-x-1 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                      <Tag className="h-3 w-3" />
                      <span>{vendor.discountCode}</span>
                    </div>
                  )}
                </div>

                {/* Stock Status */}
                <div className="mb-3">
                  {vendor.inStock ? (
                    <span className="text-sm text-green-600 font-medium">
                      ✓ In stock ({vendor.stockLevel} available)
                    </span>
                  ) : (
                    <span className="text-sm text-red-600 font-medium">Out of stock</span>
                  )}
                </div>

                {/* Buy Button */}
                <button
                  onClick={() => handleAffiliateClick(vendor)}
                  disabled={!vendor.inStock}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                    vendor.inStock
                      ? 'bg-green-600 text-white hover:bg-green-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  <span>{vendor.inStock ? 'Buy Now' : 'Out of Stock'}</span>
                  <ExternalLink className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Affiliate Disclosure */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-start space-x-2">
          <DollarSign className="h-5 w-5 text-blue-600 mt-0.5" />
          <div className="text-sm text-blue-800">
            <strong>Affiliate Disclosure:</strong> NRTList may earn a commission when you purchase through our partner links. 
            This helps us provide free, comprehensive reviews and price comparisons. Prices and availability are updated regularly 
            but may change without notice.
          </div>
        </div>
      </div>
    </div>
  );
};

export default PriceComparisonSystem;
