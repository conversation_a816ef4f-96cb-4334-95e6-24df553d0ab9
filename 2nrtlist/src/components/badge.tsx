import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from '../lib/utils';

const badgeVariants = cva(
  "inline-flex items-center rounded-xl border px-3 py-1.5 text-xs font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 shadow-sm hover:shadow-md",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary-hover hover:scale-105",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary-hover hover:scale-105",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive-hover hover:scale-105",
        outline: "text-foreground border-border hover:bg-secondary hover:scale-105",
        success: "border-transparent bg-success text-success-foreground hover:bg-success hover:scale-105",
                  warning: "border-transparent bg-warning text-warning-foreground hover:bg-warning hover:scale-105",
                  info: "border-transparent bg-info text-info-foreground hover:bg-info hover:scale-105",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return <div className={cn(badgeVariants({ variant }), className)} {...props} />
}

export { Badge, badgeVariants }
