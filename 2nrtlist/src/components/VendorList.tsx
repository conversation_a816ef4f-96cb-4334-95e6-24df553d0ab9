import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

interface Vendor {
  id: string;
  name: string;
  website: string;
  logo_url?: string;
  description: string;
  rating: number;
  review_count: number;
  shipping_options: string[];
  delivery_time: string;
  price_range: string;
  verified: boolean;
  created_at: string;
}

interface VendorListProps {
  productId?: string;
  category?: string;
}

const VendorList: React.FC<VendorListProps> = ({ productId, category }) => {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'rating' | 'price' | 'delivery'>('rating');
  const { user } = useAuth();

  useEffect(() => {
    fetchVendors();
  }, [productId, category, sortBy]);

  const fetchVendors = async () => {
    try {
      setLoading(true);
      let query = supabase
        .from('nrt_vendors')
        .select('*');

      if (category) {
        query = query.contains('categories', [category]);
      }

      // Sort by different criteria
      switch (sortBy) {
        case 'rating':
          query = query.order('rating', { ascending: false });
          break;
        case 'price':
          query = query.order('price_range', { ascending: true });
          break;
        case 'delivery':
          query = query.order('delivery_time', { ascending: true });
          break;
      }

      const { data, error } = await query;
      
      if (error) throw error;
      
      setVendors(data || []);
    } catch (err) {
      console.error('Vendor query error:', err);
      // Handle gracefully - show empty state instead of error for empty tables
      setError(null);
      setVendors([]);
    } finally {
      setLoading(false);
    }
  };

  const StarRating = ({ rating, size = 'small' }: { rating: number; size?: 'small' | 'large' }) => {
    const sizeClass = size === 'large' ? 'w-6 h-6' : 'w-4 h-4';
    
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <svg
            key={star}
            className={`${sizeClass} ${
              star <= rating ? 'text-yellow-400' : 'text-gray-300'
            }`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
        <span className="text-sm text-gray-600 ml-1">({rating.toFixed(1)})</span>
      </div>
    );
  };

  const DeliveryBadge = ({ time }: { time: string }) => (
    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
      </svg>
      {time}
    </span>
  );

  const VerifiedBadge = () => (
    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      Verified
    </span>
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-600">
        <p>Error loading vendors: {error}</p>
        <button 
          onClick={fetchVendors}
          className="mt-2 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with sorting */}
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-bold text-foreground">
          Trusted Vendors ({vendors.length})
        </h3>
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Sort by:</span>
          <select 
            value={sortBy} 
            onChange={(e) => setSortBy(e.target.value as 'rating' | 'price' | 'delivery')}
            className="px-3 py-1 border border-border rounded-md text-sm bg-background"
          >
            <option value="rating">Rating</option>
            <option value="price">Price</option>
            <option value="delivery">Delivery Time</option>
          </select>
        </div>
      </div>

      {/* Vendor Grid */}
      <div className="grid gap-4">
        {vendors.map((vendor) => (
          <div key={vendor.id} className="bg-card border border-border rounded-lg p-4 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-4 flex-1">
                {/* Logo */}
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                  {vendor.logo_url ? (
                    <img 
                      src={vendor.logo_url} 
                      alt={vendor.name}
                      className="w-10 h-10 rounded-lg object-cover"
                    />
                  ) : (
                    <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h2M7 3v4a1 1 0 001 1h8a1 1 0 001-1V3M9 13h6m-6 4h6" />
                    </svg>
                  )}
                </div>

                {/* Content */}
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-semibold text-foreground">{vendor.name}</h4>
                    {vendor.verified && <VerifiedBadge />}
                  </div>
                  
                  <div className="flex items-center gap-4 mb-2">
                    <StarRating rating={vendor.rating} />
                    <span className="text-sm text-muted-foreground">
                      {vendor.review_count} reviews
                    </span>
                  </div>

                  <p className="text-sm text-muted-foreground mb-3">
                    {vendor.description}
                  </p>

                  <div className="flex items-center gap-4 text-sm">
                    <span className="font-medium text-foreground">
                      {vendor.price_range}
                    </span>
                    <DeliveryBadge time={vendor.delivery_time} />
                  </div>

                  {/* Shipping Options */}
                  <div className="flex flex-wrap gap-1 mt-2">
                    {vendor.shipping_options.map((option, index) => (
                      <span 
                        key={index}
                        className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                      >
                        {option}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex flex-col gap-2">
                <button 
                  onClick={() => window.open(vendor.website, '_blank')}
                  className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 text-sm"
                >
                  Visit Store
                </button>
                <button 
                  className="px-4 py-2 border border-border rounded hover:bg-accent text-sm"
                  onClick={() => {
                    // TODO: Implement compare functionality
                  }}
                >
                  Compare
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {vendors.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <p>No vendors found for this category.</p>
          <p className="text-sm mt-2">Try adjusting your filters or check back later.</p>
        </div>
      )}
    </div>
  );
};

export default VendorList;
