import React, { useState, useEffect } from 'react';
import { 
  MapPin, Search, Filter, Star, Phone, Globe, Clock, CheckCircle, 
  DollarSign, Package, Truck, Shield, Award, TrendingUp, Users,
  Navigation, Store as StoreIcon, Heart, AlertCircle, Zap, X
} from 'lucide-react';
import { getStoresWithInventoryAndPricing } from '../lib/supabase';

interface SophisticatedStore {
  id: string;
  name: string;
  brand?: string;
  chain?: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country?: string;
  latitude?: number;
  longitude?: number;
  phone?: string;
  website?: string;
  store_hours?: any;
  nrt_brands_carried: string[];
  pharmacy_available: boolean;
  prescription_required: boolean;
  drive_through: boolean;
  parking_available?: boolean;
  wheelchair_accessible?: boolean;
  rating: number;
  review_count: number;
  verified: boolean;
  distance?: number | null;
  store_inventory?: StoreInventory[];
  store_prices?: StorePrice[];
  store_reviews?: StoreReview[];
  created_at: string;
  updated_at?: string;
}

interface StoreInventory {
  id: string;
  product_id: string;
  product_name: string;
  brand: string;
  category: string;
  flavors_available: string[];
  nicotine_strengths_available: string[];
  in_stock: boolean;
  stock_level: 'high' | 'medium' | 'low' | 'out_of_stock';
  last_updated: string;
}

interface StorePrice {
  id: string;
  product_id: string;
  product_name: string;
  brand: string;
  category: string;
  flavor?: string;
  nicotine_strength?: string;
  price_regular: number;
  price_sale?: number;
  price_bulk?: number;
  bulk_quantity?: number;
  currency: string;
  price_per_unit: number;
  unit_type: string;
  discount_percentage?: number;
  sale_end_date?: string;
  last_updated: string;
}

interface StoreReview {
  id: string;
  rating: number;
  review_text?: string;
  helpful_count: number;
  verified_purchase: boolean;
  review_categories?: any;
  created_at: string;
}

interface SophisticatedFilters {
  searchQuery: string;
  productId: string;
  category: string;
  brand: string;
  flavor: string;
  nicotineStrength: string;
  maxDistance: number;
  sortBy: 'distance' | 'price' | 'rating' | 'availability';
  priceRange: { min: number; max: number };
  storeFeatures: {
    pharmacy: boolean;
    driveThrough: boolean;
    parking: boolean;
    wheelchair: boolean;
  };
}

interface StoreLocatorProps {
  productId?: string;
  category?: string;
}

const StoreLocator: React.FC<StoreLocatorProps> = ({ productId = 'all', category = 'all' }) => {
  const [stores, setStores] = useState<SophisticatedStore[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [location, setLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [locationPermission, setLocationPermission] = useState<'pending' | 'granted' | 'denied'>('pending');
  const [showFilters, setShowFilters] = useState(false);
  
  // Sophisticated filters state
  const [filters, setFilters] = useState<SophisticatedFilters>({
    searchQuery: '',
    productId: productId || '',
    category: category || '',
    brand: '',
    flavor: '',
    nicotineStrength: '',
    maxDistance: 25,
    sortBy: 'distance',
    priceRange: { min: 0, max: 200 },
    storeFeatures: {
      pharmacy: false,
      driveThrough: false,
      parking: false,
      wheelchair: false
    }
  });

  // Request geolocation permission with timeout fallback
  const requestLocation = () => {
    if (navigator.geolocation) {
      setLocationPermission('pending');
      
      // CRITICAL FIX: Add timeout to force fallback after 5 seconds
      const timeoutId = setTimeout(() => {
        console.log('🚨 GEOLOCATION TIMEOUT: Forcing fallback to load stores without location');
        setLocationPermission('denied');
      }, 5000);
      
      navigator.geolocation.getCurrentPosition(
        (position) => {
          clearTimeout(timeoutId);
          setLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
          setLocationPermission('granted');
        },
        (error) => {
          clearTimeout(timeoutId);
          console.error('Error getting location:', error);
          setLocationPermission('denied');
        },
        { timeout: 5000 } // Additional timeout option for getCurrentPosition
      );
    } else {
      setLocationPermission('denied');
    }
  };

  // Auto-request location on component mount
  useEffect(() => {
    requestLocation();
  }, []);

  // Fetch sophisticated stores - CRITICAL FIX: Also fetch when component mounts and when geolocation is denied
  useEffect(() => {
    fetchSophisticatedStores();
  }, [location, filters, productId, category]);

  // CRITICAL FIX: Fallback fetch when geolocation is denied to ensure stores still load
  useEffect(() => {
    if (locationPermission === 'denied') {
      console.log('🚨 GEOLOCATION DENIED: Fetching stores without location data');
      fetchSophisticatedStores();
    }
  }, [locationPermission]);

  const fetchSophisticatedStores = async () => {
    try {
      console.log('🚨🚨🚨 DEBUG: fetchSophisticatedStores STARTING');
      setLoading(true);
      console.log('🚨 SophisticatedStoreLocator: Fetching stores with filters:', filters);
      
      const sophisticatedFilters = {
        productId: filters.productId || productId,
        category: filters.category || category,
        brand: filters.brand,
        flavor: filters.flavor,
        nicotineStrength: filters.nicotineStrength,
        maxDistance: filters.maxDistance,
        userLat: location?.lat,
        userLng: location?.lng,
        sortBy: filters.sortBy,
        priceRange: filters.priceRange
      };
      
      console.log('🚨🚨🚨 DEBUG: About to call getStoresWithInventoryAndPricing with filters:', sophisticatedFilters);
      const sophisticatedStores = await getStoresWithInventoryAndPricing(sophisticatedFilters);
      console.log('🚨🚨🚨 DEBUG: getStoresWithInventoryAndPricing returned:', sophisticatedStores);
      console.log('🚨🚨🚨 DEBUG: Store count received:', sophisticatedStores?.length || 0);
      
      // Apply additional feature filters
      let filteredStores = sophisticatedStores;
      
      if (filters.storeFeatures.pharmacy) {
        filteredStores = filteredStores.filter(store => store.pharmacy_available);
      }
      
      if (filters.storeFeatures.driveThrough) {
        filteredStores = filteredStores.filter(store => store.drive_through);
      }
      
      if (filters.storeFeatures.parking) {
        filteredStores = filteredStores.filter(store => store.parking_available);
      }
      
      if (filters.storeFeatures.wheelchair) {
        filteredStores = filteredStores.filter(store => store.wheelchair_accessible);
      }
      
      // Apply search query filter
      if (filters.searchQuery.trim()) {
        const query = filters.searchQuery.toLowerCase();
        filteredStores = filteredStores.filter(store => 
          store.name.toLowerCase().includes(query) ||
          store.address.toLowerCase().includes(query) ||
          store.city.toLowerCase().includes(query) ||
          store.nrt_brands_carried.some(brand => brand.toLowerCase().includes(query))
        );
      }
      
      setStores(filteredStores);
      setError(null);
      console.log('🚨 SophisticatedStoreLocator: Found', filteredStores.length, 'stores');
    } catch (err) {
      console.error('🚨 SophisticatedStoreLocator: Error:', err);
      setError('Failed to load stores. Please try again.');
      setStores([]);
    } finally {
      setLoading(false);
    }
  };

  const updateFilter = (key: keyof SophisticatedFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const updateStoreFeature = (feature: keyof SophisticatedFilters['storeFeatures'], value: boolean) => {
    setFilters(prev => ({
      ...prev,
      storeFeatures: { ...prev.storeFeatures, [feature]: value }
    }));
  };

  // Helper functions for store hours
  const getCurrentDayStatus = (hours: any): string => {
    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const todayHours = hours[today];
    if (!todayHours) return 'Hours not available';
    if (todayHours === 'Closed') return 'Closed today';
    return `Today: ${todayHours}`;
  };

  const isStoreOpen = (hours: any): boolean => {
    const now = new Date();
    const today = now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const todayHours = hours[today];

    if (!todayHours || todayHours === 'Closed') return false;

    // Simple check - in production would parse actual hours
    const currentHour = now.getHours();
    return currentHour >= 8 && currentHour < 22; // Assume most stores open 8am-10pm
  };

  // Helper function for inventory status colors
  const getStockLevelColor = (stockLevel: string): string => {
    switch (stockLevel.toLowerCase()) {
      case 'in_stock':
      case 'high':
        return 'bg-wellness-100 text-wellness';
      case 'low_stock':
      case 'low':
        return 'bg-rating-gold-50 text-rating-gold';
      case 'out_of_stock':
      case 'out':
        return 'bg-alert-red-50 text-alert-red';
      case 'limited':
        return 'bg-rating-gold-50 text-rating-gold';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStockLevelBadge = (level: string) => {
    switch (level) {
      case 'high': return 'text-wellness bg-wellness-100';
      case 'medium': return 'text-rating-gold bg-rating-gold-50';
      case 'low': return 'text-rating-gold bg-rating-gold-50';
      case 'out_of_stock': return 'text-alert-red bg-alert-red-50';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getLowestPrice = (store: SophisticatedStore) => {
    if (!store.store_prices || store.store_prices.length === 0) return null;
    return Math.min(...store.store_prices.map(p => p.price_regular));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-12">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Find NRT Stores Near You</h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Locate pharmacies, supermarkets, and stores that sell NRT products in your area.
              Get real-time inventory and pricing information.
            </p>
            <p className="text-sm text-gray-500 mt-2 font-medium">
              When cravings hit, find help nearby.
            </p>
          </div>
        </div>
      </div>

      <main className="py-8">
        <div className="max-w-7xl mx-auto px-4 py-8">
          {/* Sophisticated Search Header */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Main Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search stores, products, or locations..."
                    value={filters.searchQuery}
                    onChange={(e) => updateFilter('searchQuery', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                  />
            </div>
          </div>
          
          {/* Location Permission Button */}
          {locationPermission === 'denied' && (
            <button
              onClick={requestLocation}
              className="flex items-center gap-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Navigation className="w-5 h-5" />
              Enable Location
            </button>
          )}

          {locationPermission === 'pending' && (
            <div className="flex items-center gap-2 px-4 py-3 bg-gray-100 text-gray-600 rounded-lg">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              Getting Location...
            </div>
          )}

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 px-4 py-3 bg-wellness text-white rounded-lg hover:bg-wellness/90 transition-colors"
          >
            <Filter className="w-5 h-5" />
            Advanced Filters
          </button>
          </div>

        {/* Advanced Filters Panel */}
        {showFilters && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Product Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  value={filters.category}
                  onChange={(e) => updateFilter('category', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  <option value="gum">Nicotine Gum</option>
                  <option value="lozenges">Lozenges</option>
                  <option value="patches">Patches</option>
                  <option value="pouches">Pouches</option>
                </select>
              </div>

              {/* Brand */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Brand</label>
                <select
                  value={filters.brand}
                  onChange={(e) => updateFilter('brand', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value="">All Brands</option>
                  <option value="Nicorette">Nicorette</option>
                  <option value="NicoDerm CQ">NicoDerm CQ</option>
                  <option value="Commit">Commit</option>
                  <option value="ZYN">ZYN</option>
                </select>
              </div>

              {/* Distance */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Max Distance</label>
                <select
                  value={filters.maxDistance}
                  onChange={(e) => updateFilter('maxDistance', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value={5}>5 miles</option>
                  <option value={10}>10 miles</option>
                  <option value={25}>25 miles</option>
                  <option value={50}>50 miles</option>
                  <option value={100}>100 miles</option>
                </select>
              </div>

              {/* Sort By with Visual Indicators */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sort By
                  <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    {filters.sortBy === 'distance' ? '📍 Distance' :
                     filters.sortBy === 'price' ? '💰 Price' :
                     filters.sortBy === 'rating' ? '⭐ Rating' : '📦 Availability'}
                  </span>
                </label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => updateFilter('sortBy', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value="distance">📍 Distance (Nearest First)</option>
                  <option value="price">💰 Lowest Price</option>
                  <option value="rating">⭐ Highest Rated</option>
                  <option value="availability">📦 Best Availability</option>
                </select>
              </div>
            </div>

            {/* Enhanced Store Features & Amenities */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-3">Store Features & Amenities</label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {[
                  { key: 'pharmacy', label: 'Pharmacy', icon: Shield, color: 'text-wellness' },
                  { key: 'driveThrough', label: 'Drive-Through', icon: Truck, color: 'text-wellness' },
                  { key: 'parking', label: 'Free Parking', icon: Package, color: 'text-wellness' },
                  { key: 'wheelchair', label: 'Wheelchair Access', icon: Heart, color: 'text-wellness' },
                  { key: 'consultation', label: 'Pharmacist Consultation', icon: Users, color: 'text-wellness' },
                  { key: 'delivery', label: 'Home Delivery', icon: Truck, color: 'text-wellness' },
                  { key: 'rewards', label: 'Rewards Program', icon: Award, color: 'text-rating-gold' },
                  { key: 'insurance', label: 'Insurance Accepted', icon: Shield, color: 'text-alert-red' }
                ].map(({ key, label, icon: Icon, color }) => (
                  <label key={key} className="flex items-center gap-2 cursor-pointer p-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <input
                      type="checkbox"
                      checked={filters.storeFeatures[key as keyof typeof filters.storeFeatures] || false}
                      onChange={(e) => updateStoreFeature(key as keyof typeof filters.storeFeatures, e.target.checked)}
                      className="rounded border-gray-300 text-wellness focus:ring-wellness"
                    />
                    <Icon className={`w-4 h-4 ${color}`} />
                    <span className="text-sm text-gray-700 font-medium">{label}</span>
                  </label>
                ))}
              </div>

              {/* Active Filters Display */}
              <div className="mt-3 flex flex-wrap gap-2">
                {Object.entries(filters.storeFeatures).filter(([_, value]) => value).map(([key, _]) => (
                  <span key={key} className="inline-flex items-center gap-1 bg-wellness/10 text-wellness px-2 py-1 rounded-full text-xs font-medium">
                    {key.charAt(0).toUpperCase() + key.slice(1)}
                    <button
                      onClick={() => updateStoreFeature(key as keyof typeof filters.storeFeatures, false)}
                      className="hover:bg-wellness/20 rounded-full p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

        {/* Results Summary */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-semibold text-gray-900">
            {loading ? 'Searching...' : stores.length + ' stores found'}
          </h2>
          {location ? (
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <Navigation className="w-4 h-4" />
              Near your location
            </div>
          ) : null}
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-wellness mx-auto"></div>
          <p className="mt-4 text-gray-600">Finding the best stores for you...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{error}</p>
          </div>
        </div>
      )}

      {/* Store Results */}
      {!loading && !error && stores.length === 0 && (
        <div className="text-center py-12">
          <StoreIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No stores found</h3>
          <p className="text-gray-600">Try adjusting your filters or search criteria.</p>
        </div>
      )}

      {/* Store Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {stores.map((store) => (
          <div key={store.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-xl hover:scale-105 hover:-translate-y-1 hover:border-wellness-300 transition-all duration-300 cursor-pointer group">
            {/* Store Photo Header */}
            <div className="relative h-32 bg-gradient-to-r from-gray-100 to-gray-200">
              {store.store_photos && store.store_photos.length > 0 ? (
                <img
                  src={store.store_photos[0]}
                  alt={`${store.name} storefront`}
                  className="w-full h-32 object-cover"
                />
              ) : (
                <div className="w-full h-32 bg-gradient-to-r from-gray-50 to-wellness-50 flex items-center justify-center">
                  <StoreIcon className="w-8 h-8 text-gray-400" />
                </div>
              )}
              {/* Photo Count Badge */}
              {store.store_photos && store.store_photos.length > 1 && (
                <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded-full">
                  +{store.store_photos.length - 1} photos
                </div>
              )}
              {/* Store Chain Badge */}
              {store.chain && (
                <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full font-medium">
                  {store.chain}
                </div>
              )}
              {/* Store Open/Closed Status Badge */}
              <div className={`absolute bottom-2 right-2 text-white text-xs px-2 py-1 rounded-full font-medium ${
                store.store_hours && isStoreOpen(store.store_hours)
                  ? 'bg-wellness'
                  : 'bg-alert-red'
              }`}>
                {store.store_hours && isStoreOpen(store.store_hours) ? 'OPEN' : 'CLOSED'}
              </div>
            </div>

            <div className="p-6">
            {/* Store Header with Logo */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-start gap-3 flex-1">
                {/* Store Chain/Brand Logo */}
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  {store.chain ? (
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-xs">
                        {store.chain.substring(0, 2).toUpperCase()}
                      </span>
                    </div>
                  ) : (
                    <StoreIcon className="w-6 h-6 text-gray-400" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-lg font-semibold text-gray-900">{store.name}</h3>
                    {store.verified && (
                      <CheckCircle className="w-5 h-5 text-wellness" />
                    )}
                    {store.chain && (
                      <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">
                        {store.chain}
                      </span>
                    )}
                  </div>
                  <p className="text-gray-600">{store.address}, {store.city}, {store.state}</p>
                  {store.distance && (
                    <p className="text-sm text-wellness font-medium">{store.distance.toFixed(1)} miles away</p>
                  )}
                </div>
              </div>
              
              {/* Rating & Comparison */}
              <div className="text-right">
                <div className="flex items-center gap-1 mb-2">
                  <Star className="w-4 h-4 text-rating-gold fill-current" />
                  <span className="font-medium">{store.rating.toFixed(1)}</span>
                </div>
                <div className="flex items-center gap-1 text-xs text-gray-500 mb-2">
                  <span>{store.review_count} reviews</span>
                  {store.review_count > 50 && (
                    <span className="bg-wellness-100 text-wellness px-1 py-0.5 rounded text-xs">Popular</span>
                  )}
                  {store.review_count > 100 && (
                    <span className="bg-wellness-100 text-wellness px-1 py-0.5 rounded text-xs">Trusted</span>
                  )}
                </div>

                {/* Store Comparison Checkbox */}
                <label className="flex items-center gap-2 cursor-pointer justify-end">
                  <span className="text-xs text-gray-600">Compare</span>
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-wellness focus:ring-wellness"
                    onChange={(e) => {
                      console.log('Store comparison toggled:', store.name, e.target.checked);
                      // In production, would add to comparison state
                    }}
                  />
                </label>
              </div>
            </div>
            </div>

            {/* Store Hours - Enhanced Display */}
            <div className="mb-3">
              <div className="flex items-center gap-2 text-sm">
                <Clock className="w-4 h-4 text-gray-500" />
                <div className="flex-1">
                  {store.store_hours ? (
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-gray-900">
                          {getCurrentDayStatus(store.store_hours)}
                        </span>
                        <span className={`text-xs px-2 py-1 rounded-full ${isStoreOpen(store.store_hours) ? 'bg-wellness-100 text-wellness' : 'bg-alert-red-50 text-alert-red'}`}>
                          {isStoreOpen(store.store_hours) ? 'Open' : 'Closed'}
                        </span>
                      </div>
                      <button
                        className="text-xs text-blue-600 hover:text-blue-800 mt-1"
                        onClick={() => {/* Show full hours modal */}}
                      >
                        View all hours
                      </button>
                    </div>
                  ) : (
                    <span className="text-gray-500">Hours not available</span>
                  )}
                </div>
              </div>
            </div>

            {/* Store Features */}
            <div className="flex flex-wrap gap-2 mb-4">
              {store.pharmacy_available && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                  <Shield className="w-3 h-3" />
                  Pharmacy
                </span>
              )}
              {store.drive_through && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-wellness-100 text-wellness text-xs rounded-full">
                  <Truck className="w-3 h-3" />
                  Drive-Through
                </span>
              )}
              {store.parking_available && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                  <Package className="w-3 h-3" />
                  Parking
                </span>
              )}
              {store.wheelchair_accessible && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">
                  <Heart className="w-3 h-3" />
                  Accessible
                </span>
              )}
            </div>

            {/* Enhanced Product Availability with Status Indicators */}
            {store.store_inventory && store.store_inventory.length > 0 && (
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-900">Available Products</h4>
                  <span className="text-xs text-gray-500">
                    {store.store_inventory.filter(item => item.stock_level === 'in_stock').length} in stock
                  </span>
                </div>
                <div className="space-y-2">
                  {store.store_inventory.slice(0, 3).map((item) => (
                    <div key={item.id} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <p className="text-sm font-medium text-gray-900">{item.product_name}</p>
                          <div className={`w-2 h-2 rounded-full ${
                            item.stock_level === 'in_stock' ? 'bg-wellness' :
                            item.stock_level === 'low_stock' ? 'bg-rating-gold' :
                            item.stock_level === 'out_of_stock' ? 'bg-alert-red' : 'bg-gray-500'
                          }`}></div>
                        </div>
                        <p className="text-xs text-gray-500">
                          {item.flavors_available.join(', ')} • {item.nicotine_strengths_available.join(', ')}
                        </p>
                      </div>
                      <div className="text-right">
                        <span className={`px-2 py-1 text-xs rounded-full font-medium ${getStockLevelColor(item.stock_level)}`}>
                          {item.stock_level.replace('_', ' ').toUpperCase()}
                        </span>
                        {item.last_updated && (
                          <p className="text-xs text-gray-400 mt-1">
                            Updated {new Date(item.last_updated).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                  {store.store_inventory.length > 3 && (
                    <button className="text-xs text-blue-600 hover:text-blue-800 font-medium">
                      View all {store.store_inventory.length} products →
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Pricing */}
            {store.store_prices && store.store_prices.length > 0 && (
              <div className="mb-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-900">Starting from</span>
                  <span className="text-lg font-bold text-wellness">${getLowestPrice(store)?.toFixed(2)}</span>
                </div>
              </div>
            )}

            {/* Store Reviews/Ratings Display */}
            {store.store_reviews && store.store_reviews.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Recent Reviews</h4>
                <div className="space-y-2">
                  {store.store_reviews.slice(0, 2).map((review) => (
                    <div key={review.id} className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-1">
                        <div className="flex items-center gap-1">
                          {[...Array(5)].map((_, i) => (
                            <Star key={i} className={`w-3 h-3 ${i < review.rating ? 'text-rating-gold fill-current' : 'text-gray-300'}`} />
                          ))}
                        </div>
                        {review.verified_purchase && (
                          <CheckCircle className="w-3 h-3 text-wellness" />
                        )}
                      </div>
                      <p className="text-xs text-gray-600 line-clamp-2">{review.review_text}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Contact Info */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <div className="flex items-center gap-2 flex-wrap">
                {store.phone && (
                  <a
                    href={`tel:${store.phone}`}
                    className="flex items-center gap-1 text-sm bg-blue-50 text-blue-700 px-3 py-2 rounded-lg hover:bg-blue-100 transition-colors font-medium group"
                    title={`Call ${store.name} at ${store.phone}`}
                    onClick={() => {
                      // Track call clicks for analytics
                      console.log('Store call clicked:', store.name, store.phone);
                    }}
                  >
                    <Phone className="w-4 h-4 group-hover:animate-pulse" />
                    <span className="hidden sm:inline">Call Store</span>
                    <span className="sm:hidden">{store.phone}</span>
                  </a>
                )}
                {store.website && (
                  <a
                    href={store.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 text-sm bg-gray-50 text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors font-medium"
                  >
                    <Globe className="w-4 h-4" />
                    Website
                  </a>
                )}
                <a
                  href={`https://maps.google.com/?q=${encodeURIComponent(store.address + ', ' + store.city + ', ' + store.state)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1 text-sm bg-emerald-50 text-emerald-700 px-3 py-2 rounded-lg hover:bg-emerald-100 transition-colors font-medium"
                >
                  <MapPin className="w-4 h-4" />
                  Get Directions
                </a>
                <button
                  className="flex items-center gap-1 text-sm bg-purple-50 text-purple-700 px-3 py-2 rounded-lg hover:bg-purple-100 transition-colors font-medium"
                  title="Compare this store"
                >
                  <Heart className="w-4 h-4" />
                  Compare
                </button>
                <button
                  className="flex items-center gap-1 text-sm bg-red-50 text-red-700 px-3 py-2 rounded-lg hover:bg-red-100 transition-colors font-medium"
                  title="Add to favorites"
                  onClick={() => {
                    console.log('Store favorited:', store.name);
                    // In production, would save to user favorites
                  }}
                >
                  <Heart className="w-4 h-4" />
                  Favorite
                </button>
              </div>
            </div>
          </div>
        ))}
        </div>
      </div>
      </main>
    </div>
  );
};

export default StoreLocator;
