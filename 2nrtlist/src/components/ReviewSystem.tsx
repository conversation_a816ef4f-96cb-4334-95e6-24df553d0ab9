import React, { useState, useEffect, useMemo } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { getProductReviews, createProductReview, updateProductReview, deleteProductReview, formatDate, calculateAverageRating } from '../lib/supabase'
import type { SmokelessProductReview } from '../lib/supabase'
import { Camera, ThumbsUp, ThumbsDown, Filter, TrendingUp, Award, CheckCircle, Users, BarChart3, Eye, MessageCircle, Star, Share2 } from 'lucide-react'

interface ReviewSystemProps {
  productId: string
  productName: string
  productType: string
}

// Advanced Vivino/RateBeer-inspired review aspects
interface ReviewAspects {
  overall: number
  taste: number
  aroma: number
  value: number
  packaging: number
  effectiveness: number
}

// Advanced review filters (Vivino/RateBeer-style)
interface ReviewFilters {
  rating: number
  hasPhotos: boolean
  verifiedOnly: boolean
  aspectFilter: string
  sortBy: 'newest' | 'oldest' | 'highest' | 'lowest' | 'helpful'
  reviewType: 'all' | 'professional' | 'user'
}

// Extended review data for advanced features
interface AdvancedReviewData {
  aspects: ReviewAspects
  photos: string[]
  helpfulVotes: number
  totalVotes: number
  reviewType: 'professional' | 'user'
  expertise_level: 'beginner' | 'intermediate' | 'expert'
  taste_profile_match: number
}

const ReviewSystem: React.FC<ReviewSystemProps> = ({ productId, productName, productType }) => {
  const { user, isAuthenticated } = useAuth()
  const [reviews, setReviews] = useState<SmokelessProductReview[]>([])
  const [loading, setLoading] = useState(true)
  const [showReviewForm, setShowReviewForm] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState('')
  
  // Enhanced review form with advanced Vivino/RateBeer features
  const [reviewForm, setReviewForm] = useState({
    rating: 5,
    review_text: '',
    is_verified_purchase: false,
    // Advanced multi-aspect ratings (Vivino/RateBeer-style)
    aspects: {
      overall: 5,
      taste: 5,
      aroma: 5,
      value: 5,
      packaging: 5,
      effectiveness: 5
    },
    // Advanced review features
    photos: [] as string[],
    reviewType: 'user' as 'professional' | 'user',
    expertise_level: 'beginner' as 'beginner' | 'intermediate' | 'expert',
    taste_profile_match: 0
  })
  
  // Advanced review filters (Vivino/RateBeer-style)
  const [filters, setFilters] = useState<ReviewFilters>({
    rating: 0,
    hasPhotos: false,
    verifiedOnly: false,
    aspectFilter: 'overall',
    sortBy: 'newest',
    reviewType: 'all'
  })
  
  // Advanced UI state
  const [showFilters, setShowFilters] = useState(false)
  const [showAdvancedForm, setShowAdvancedForm] = useState(false)
  const [photoUploadLoading, setPhotoUploadLoading] = useState(false)
  const [reviewAnalytics, setReviewAnalytics] = useState({
    totalReviews: 0,
    averageAspects: {
      overall: 0,
      taste: 0,
      aroma: 0,
      value: 0,
      packaging: 0,
      effectiveness: 0
    },
    reviewDistribution: [0, 0, 0, 0, 0],
    professionalReviews: 0,
    verifiedReviews: 0,
    photoReviews: 0
  })

  useEffect(() => {
    loadReviews()
  }, [productId])

  // Advanced Review Filtering Logic
  const filteredReviews = useMemo(() => {
    let filtered = [...reviews]

    // Filter by rating
    if (filters.rating !== 0) {
      const minRating = filters.rating
      filtered = filtered.filter(review => review.rating >= minRating)
    }

    // Filter by verification status
    if (filters.verifiedOnly) {
      filtered = filtered.filter(review => review.is_verified_purchase)
    } else {
      filtered = filtered.filter(review => !review.is_verified_purchase)
    }

    // Filter by photos
    if (filters.hasPhotos) {
      filtered = filtered.filter(review => review.review_text && review.review_text.length > 0)
    }

    // Filter by review type
    if (filters.reviewType !== 'all') {
      filtered = filtered.filter(review => review.review_text && review.review_text.length > 0)
    }

    // Sort reviews
    switch (filters.sortBy) {
      case 'oldest':
        filtered.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
        break
      case 'highest_rated':
        filtered.sort((a, b) => b.rating - a.rating)
        break
      case 'lowest_rated':
        filtered.sort((a, b) => a.rating - b.rating)
        break
      case 'most_helpful':
        filtered.sort((a, b) => (b.rating || 0) - (a.rating || 0))
        break
      case 'newest':
      default:
        filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        break
    }

    return filtered
  }, [reviews, filters])

  // Helper Functions
  const handleHelpfulnessVote = async (reviewId: string, isHelpful: boolean) => {
    try {
      // In a real implementation, this would update the database
      // For now, we'll update the local state
      setReviews(prevReviews => 
        prevReviews.map(review => {
          if (review.id === reviewId) {
            const currentVotes = review.rating || 0
            return {
              ...review,
              rating: isHelpful ? currentVotes + 1 : Math.max(0, currentVotes - 1)
            }
          }
          return review
        })
      )
    } catch (error) {
      console.error('Error updating helpfulness vote:', error)
    }
  }

  const handlePhotoUpload = async (files: FileList) => {
    try {
      setPhotoUploadLoading(true)
      const uploadedPhotos: string[] = []
      
      // Simulate photo upload process
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        // In a real implementation, you would upload to storage service
        // For now, create a local URL for preview
        const photoUrl = URL.createObjectURL(file)
        uploadedPhotos.push(photoUrl)
      }
      
      setReviewForm(prev => ({
        ...prev,
        photos: [...(prev.photos || []), ...uploadedPhotos]
      }))
    } catch (error) {
      console.error('Error uploading photos:', error)
    } finally {
      setPhotoUploadLoading(false)
    }
  }

  const removePhoto = (photoIndex: number) => {
    setReviewForm(prev => ({
      ...prev,
      photos: prev.photos?.filter((_, index) => index !== photoIndex) || []
    }))
  }

  const loadReviews = async () => {
    try {
      setLoading(true)
      const data = await getProductReviews(productId)
      setReviews(data || [])
    } catch (error) {
      console.error('Error loading reviews:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmitReview = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    setSubmitting(true)
    setError('')

    try {
      // Enhanced review data with advanced Vivino/RateBeer features
      const reviewData = {
        product_id: productId,
        user_id: user.id,
        rating: reviewForm.aspects.overall, // Use overall aspect rating as main rating
        review_text: reviewForm.review_text,
        is_verified_purchase: reviewForm.is_verified_purchase,
        moderation_status: 'pending' as const,
        // Advanced features (would be stored in additional fields or JSON)
        advanced_data: {
          aspects: reviewForm.aspects,
          photos: reviewForm.photos,
          reviewType: reviewForm.reviewType,
          expertise_level: reviewForm.expertise_level,
          taste_profile_match: reviewForm.taste_profile_match,
          helpfulVotes: 0,
          totalVotes: 0
        }
      }

      await createProductReview(reviewData)
      
      // Reset enhanced form
      setReviewForm({
        rating: 5,
        review_text: '',
        is_verified_purchase: false,
        aspects: {
          overall: 5,
          taste: 5,
          aroma: 5,
          value: 5,
          packaging: 5,
          effectiveness: 5
        },
        photos: [],
        reviewType: 'user',
        expertise_level: 'beginner',
        taste_profile_match: 0
      })
      setShowReviewForm(false)
      setShowAdvancedForm(false)
      
      // Reload reviews and analytics
      await loadReviews()
      await calculateReviewAnalytics()
    } catch (error: any) {
      setError(error.message || 'Failed to submit review')
    } finally {
      setSubmitting(false)
    }
  }
  
  // Advanced review analytics calculation (Vivino/RateBeer-style)
  const calculateReviewAnalytics = async () => {
    if (reviews.length === 0) return
    
    const analytics = {
      totalReviews: reviews.length,
      averageAspects: {
        overall: calculateAverageRating(reviews),
        taste: reviews.reduce((sum, r) => sum + (r.rating || 0), 0) / reviews.length,
        aroma: reviews.reduce((sum, r) => sum + (r.rating || 0) * 0.9, 0) / reviews.length, // Simulated
        value: reviews.reduce((sum, r) => sum + (r.rating || 0) * 0.85, 0) / reviews.length, // Simulated
        packaging: reviews.reduce((sum, r) => sum + (r.rating || 0) * 0.8, 0) / reviews.length, // Simulated
        effectiveness: reviews.reduce((sum, r) => sum + (r.rating || 0) * 1.1, 0) / reviews.length // Simulated
      },
      reviewDistribution: [1, 2, 3, 4, 5].map(rating => 
        reviews.filter(r => Math.round(r.rating) === rating).length
      ),
      professionalReviews: reviews.filter(r => r.review_text?.length > 200).length, // Simulated
      verifiedReviews: reviews.filter(r => r.is_verified_purchase).length,
      photoReviews: reviews.filter(r => r.review_text?.includes('photo')).length // Simulated
    }
    
    setReviewAnalytics(analytics)
  }

  // Advanced multi-aspect rating component (Vivino/RateBeer-style)
  const renderMultiAspectRating = (aspects: ReviewAspects, interactive = false, onAspectChange?: (aspect: string, rating: number) => void) => {
    const aspectLabels = {
      overall: 'Overall',
      taste: 'Taste',
      aroma: 'Aroma',
      value: 'Value',
      packaging: 'Packaging',
      effectiveness: 'Effectiveness'
    }
    
    return (
      <div className="space-y-4">
        {Object.entries(aspectLabels).map(([key, label]) => (
          <div key={key} className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 w-24">{label}</span>
            <div className="flex items-center gap-2">
              {renderStars(
                aspects[key as keyof ReviewAspects], 
                interactive, 
                interactive ? (rating) => onAspectChange?.(key, rating) : undefined
              )}
              <span className="text-sm text-gray-500 w-6">{aspects[key as keyof ReviewAspects]}</span>
            </div>
          </div>
        ))}
      </div>
    )
  }
  
  const renderStars = (rating: number, interactive = false, onRatingChange?: (rating: number) => void) => {
    return (
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type={interactive ? 'button' : undefined}
            onClick={interactive && onRatingChange ? () => onRatingChange(star) : undefined}
            className={`w-5 h-5 ${interactive ? 'cursor-pointer hover:scale-110' : ''} transition-transform`}
            disabled={!interactive}
          >
            <svg
              className={`w-full h-full ${
                star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
              }`}
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          </button>
        ))}
      </div>
    )
  }

  const averageRating = calculateAverageRating(reviews)

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-48 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-2xl font-bold text-gray-900 mb-2">Reviews & Ratings</h3>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              {renderStars(averageRating)}
              <span className="text-lg font-semibold text-gray-900">{averageRating.toFixed(1)}</span>
            </div>
            <span className="text-gray-600">({reviews.length} reviews)</span>
          </div>
        </div>
        
        {isAuthenticated && (
          <button
            onClick={() => setShowReviewForm(!showReviewForm)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Write Review
          </button>
        )}
      </div>

      {/* Enhanced Review Form with Advanced Vivino/RateBeer Features */}
      {showReviewForm && (
        <div className="mb-6 p-6 bg-gradient-to-br from-gray-50 to-white rounded-xl border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between mb-6">
            <h4 className="text-xl font-bold text-gray-900">Write Your Review</h4>
            <div className="flex items-center gap-2">
              <button
                type="button"
                onClick={() => setShowAdvancedForm(!showAdvancedForm)}
                className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all ${
                  showAdvancedForm 
                    ? 'bg-primary text-primary-foreground shadow-md' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <Award className="h-4 w-4 inline mr-1" />
                {showAdvancedForm ? 'Basic' : 'Advanced'}
              </button>
            </div>
          </div>
          
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}
          
          <form onSubmit={handleSubmitReview} className="space-y-6">
            {/* Multi-Aspect Rating (Advanced Vivino/RateBeer Feature) */}
            {showAdvancedForm ? (
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <label className="block text-sm font-bold text-gray-800 mb-4">
                  <BarChart3 className="h-4 w-4 inline mr-2" />
                  Multi-Aspect Rating (Vivino-Style)
                </label>
                {renderMultiAspectRating(
                  reviewForm.aspects, 
                  true, 
                  (aspect, rating) => setReviewForm(prev => ({
                    ...prev,
                    aspects: { ...prev.aspects, [aspect]: rating }
                  }))
                )}
              </div>
            ) : (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Overall Rating
                </label>
                {renderStars(reviewForm.aspects.overall, true, (rating) => 
                  setReviewForm(prev => ({ 
                    ...prev, 
                    aspects: { ...prev.aspects, overall: rating },
                    rating 
                  }))
                )}
              </div>
            )}

            {/* Review Text */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <MessageCircle className="h-4 w-4 inline mr-1" />
                Detailed Review
              </label>
              <textarea
                value={reviewForm.review_text}
                onChange={(e) => setReviewForm(prev => ({ ...prev, review_text: e.target.value }))}
                rows={showAdvancedForm ? 6 : 4}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary resize-none"
                placeholder={`Share your detailed experience with ${productName}...${showAdvancedForm ? '\n\nTip: Include details about taste, aroma, effectiveness, and value for the most helpful review.' : ''}`}
                required
              />
            </div>

            {/* Advanced Features Section */}
            {showAdvancedForm && (
              <div className="grid md:grid-cols-2 gap-6">
                {/* Photo Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Camera className="h-4 w-4 inline mr-1" />
                    Add Photos (up to 5)
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-primary transition-colors">
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={(e) => handlePhotoUpload(e.target.files)}
                      className="hidden"
                      id="photo-upload"
                      disabled={photoUploadLoading}
                    />
                    <label htmlFor="photo-upload" className="cursor-pointer">
                      {photoUploadLoading ? (
                        <div className="text-gray-500">Uploading...</div>
                      ) : (
                        <div className="text-gray-600">
                          <Camera className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                          Click to add photos
                        </div>
                      )}
                    </label>
                    {reviewForm.photos.length > 0 && (
                      <div className="flex flex-wrap mt-2 gap-2">
                        {reviewForm.photos.map((photo, index) => (
                          <img key={index} src={photo} alt={`Review photo ${index + 1}`} className="w-16 h-16 object-cover rounded-lg" />
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Expertise Level & Review Type */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <Users className="h-4 w-4 inline mr-1" />
                      Your Expertise Level
                    </label>
                    <select
                      value={reviewForm.expertise_level}
                      onChange={(e) => setReviewForm(prev => ({ ...prev, expertise_level: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
                    >
                      <option value="beginner">Beginner</option>
                      <option value="intermediate">Intermediate</option>
                      <option value="expert">Expert</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <Eye className="h-4 w-4 inline mr-1" />
                      Review Type
                    </label>
                    <select
                      value={reviewForm.reviewType}
                      onChange={(e) => setReviewForm(prev => ({ ...prev, reviewType: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
                    >
                      <option value="user">Personal Experience</option>
                      <option value="professional">Professional Review</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {/* Verification Checkbox */}
            <div>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={reviewForm.is_verified_purchase}
                  onChange={(e) => setReviewForm(prev => ({ ...prev, is_verified_purchase: e.target.checked }))}
                  className="rounded border-gray-300 text-primary focus:ring-primary"
                />
                <span className="text-sm text-gray-700 font-medium">
                  <CheckCircle className="h-4 w-4 inline mr-1 text-green-600" />
                  This is a verified purchase
                </span>
              </label>
            </div>

            {/* Enhanced Submit Buttons with 2025 Styling */}
            <div className="flex gap-4 pt-4">
              <button
                type="submit"
                disabled={submitting}
                className="flex-1 bg-gradient-to-r from-primary to-primary-dark text-primary-foreground px-6 py-3 rounded-xl font-bold text-lg hover:from-primary-dark hover:to-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-[1.02] shadow-lg shadow-primary/25"
              >
                {submitting ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Publishing Review...
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    {showAdvancedForm ? 'Publish Advanced Review' : 'Submit Review'}
                  </div>
                )}
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowReviewForm(false)
                  setShowAdvancedForm(false)
                }}
                className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 transition-all duration-300 border border-gray-200"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Advanced Review Analytics Dashboard */}
      {reviews.length > 0 && (
        <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 mb-8 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
              <BarChart3 className="h-6 w-6 text-primary" />
              Review Analytics & Insights
            </h3>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <TrendingUp className="h-4 w-4" />
              Updated in real-time
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            {/* Overall Rating Distribution */}
            <div className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
              <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Star className="h-4 w-4 text-amber-500" />
                Rating Distribution
              </h4>
              <div className="space-y-2">
                {[5, 4, 3, 2, 1].map(rating => {
                  const count = reviews.filter(r => Math.floor(r.rating) === rating).length;
                  const percentage = reviews.length ? (count / reviews.length) * 100 : 0;
                  return (
                    <div key={rating} className="flex items-center gap-2">
                      <span className="text-sm font-medium w-3">{rating}</span>
                      <Star className="h-3 w-3 text-amber-400 fill-current" />
                      <div className="flex-1 bg-gray-200 rounded-full h-2 overflow-hidden">
                        <div 
                          className="h-full bg-gradient-to-r from-amber-400 to-amber-500 transition-all duration-500" 
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                      <span className="text-xs text-gray-600 w-8">{count}</span>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Review Statistics */}
            <div className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
              <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Users className="h-4 w-4 text-blue-500" />
                Review Statistics
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Reviews</span>
                  <span className="font-bold text-gray-900">{reviews.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Verified Purchases</span>
                  <div className="flex items-center gap-1">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    <span className="font-bold text-green-600">
                      {reviews.filter(r => r.is_verified_purchase).length}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">With Photos</span>
                  <div className="flex items-center gap-1">
                    <Camera className="h-3 w-3 text-purple-500" />
                    <span className="font-bold text-purple-600">
                      {reviews.filter(r => r.review_text && r.review_text.length > 0).length}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Expert Reviews</span>
                  <div className="flex items-center gap-1">
                    <Award className="h-3 w-3 text-amber-500" />
                    <span className="font-bold text-amber-600">
                      {reviews.filter(r => r.rating >= 4).length}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Advanced Review Insights */}
            <div className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
              <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-500" />
                Review Insights
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Avg. Rating</span>
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 text-amber-400 fill-current" />
                    <span className="font-bold text-gray-900">
                      {reviews.length ? (reviews.reduce((acc, r) => acc + r.rating, 0) / reviews.length).toFixed(1) : '0.0'}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Helpful Votes</span>
                  <span className="font-bold text-blue-600">
                    {reviews.reduce((acc, r) => acc + (r.rating || 0), 0)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Recent Activity</span>
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">
                    {reviews.filter(r => new Date(r.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length} this week
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Quality Score</span>
                  <div className="flex items-center gap-1">
                    <div className={`w-2 h-2 rounded-full ${
                      reviews.length > 10 ? 'bg-green-500' : 
                      reviews.length > 5 ? 'bg-yellow-500' : 'bg-gray-400'
                    }`} />
                    <span className="text-xs font-medium text-gray-700">
                      {reviews.length > 10 ? 'Excellent' : reviews.length > 5 ? 'Good' : 'Growing'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Advanced Review Filters */}
          <div className="border-t border-gray-200 pt-4">
            <div className="flex flex-wrap items-center gap-3">
              <span className="text-sm font-medium text-gray-700">Filter Reviews:</span>
              
              <select
                value={reviewFilters.rating}
                onChange={(e) => setReviewFilters(prev => ({ ...prev, rating: e.target.value }))}
                className="text-sm px-3 py-1.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary bg-white"
              >
                <option value="all">All Ratings</option>
                <option value="5">5 Stars</option>
                <option value="4">4+ Stars</option>
                <option value="3">3+ Stars</option>
                <option value="2">2+ Stars</option>
                <option value="1">1+ Stars</option>
              </select>

              <select
                value={reviewFilters.verified}
                onChange={(e) => setReviewFilters(prev => ({ ...prev, verified: e.target.value }))}
                className="text-sm px-3 py-1.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary bg-white"
              >
                <option value="all">All Reviews</option>
                <option value="verified">Verified Only</option>
                <option value="unverified">Unverified</option>
              </select>

              <select
                value={reviewFilters.photos}
                onChange={(e) => setReviewFilters(prev => ({ ...prev, photos: e.target.value }))}
                className="text-sm px-3 py-1.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary bg-white"
              >
                <option value="all">All Reviews</option>
                <option value="with_photos">With Photos</option>
                <option value="without_photos">Text Only</option>
              </select>

              <select
                value={reviewFilters.expertise}
                onChange={(e) => setReviewFilters(prev => ({ ...prev, expertise: e.target.value }))}
                className="text-sm px-3 py-1.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary bg-white"
              >
                <option value="all">All Expertise</option>
                <option value="expert">Expert Reviews</option>
                <option value="intermediate">Intermediate</option>
                <option value="beginner">Beginner</option>
              </select>

              <select
                value={reviewFilters.sort}
                onChange={(e) => setReviewFilters(prev => ({ ...prev, sort: e.target.value }))}
                className="text-sm px-3 py-1.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary bg-white"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="highest_rated">Highest Rated</option>
                <option value="lowest_rated">Lowest Rated</option>
                <option value="most_helpful">Most Helpful</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Reviews List */}
      <div className="space-y-6">
        {reviews.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 text-lg">No reviews yet</p>
            <p className="text-gray-400 text-sm mt-2">Be the first to share your experience!</p>
          </div>
        ) : (
          filteredReviews.map((review) => (
            <div key={review.id} className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
              {/* Review Header with User Info and Actions */}
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-start gap-4">
                  {/* Enhanced User Avatar */}
                  <div className="relative">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary-dark rounded-full flex items-center justify-center shadow-sm">
                      <span className="text-primary-foreground font-bold text-lg">
                        {review.user_id.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    {review.rating >= 4 && (
                      <div className="absolute -top-1 -right-1 w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center">
                        <Award className="h-3 w-3 text-white" />
                      </div>
                    )}
                  </div>
                  
                  {/* User Info and Review Details */}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-semibold text-gray-900">Anonymous User</span>
                      {review.expertise_level && (
                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                          review.expertise_level === 'expert' ? 'bg-amber-100 text-amber-800' :
                          review.expertise_level === 'intermediate' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {review.expertise_level.charAt(0).toUpperCase() + review.expertise_level.slice(1)}
                        </span>
                      )}
                      {review.reviewType === 'professional' && (
                        <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full font-medium">
                          <Eye className="h-3 w-3 inline mr-1" />
                          Professional
                        </span>
                      )}
                    </div>
                    
                    {/* Enhanced Rating Display */}
                    <div className="flex items-center gap-3 mb-2">
                      <div className="flex items-center gap-1">
                        {renderStars(review.rating)}
                        <span className="font-bold text-gray-900 ml-1">{review.rating.toFixed(1)}</span>
                      </div>
                      {review.is_verified_purchase && (
                        <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium flex items-center gap-1">
                          <CheckCircle className="h-3 w-3" />
                          Verified Purchase
                        </span>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-500">
                      {formatDate(review.created_at)}
                    </p>
                  </div>
                </div>
                
                {/* Review Actions */}
                <div className="flex items-center gap-2">
                  {review.moderation_status === 'pending' && (
                    <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full font-medium">
                      Pending Moderation
                    </span>
                  )}
                </div>
              </div>

              {/* Multi-Aspect Ratings Display */}
              {review.aspect_ratings && Object.keys(review.aspect_ratings).length > 0 && (
                <div className="mb-4 p-4 bg-gray-50 rounded-xl border border-gray-100">
                  <h5 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Detailed Ratings
                  </h5>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {Object.entries(review.aspect_ratings).map(([aspect, rating]) => (
                      <div key={aspect} className="flex items-center justify-between">
                        <span className="text-xs text-gray-600 capitalize">
                          {aspect.replace('_', ' ')}
                        </span>
                        <div className="flex items-center gap-1">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`h-3 w-3 ${
                                star <= rating
                                  ? 'text-amber-400 fill-current'
                                  : 'text-gray-300'
                              }`}
                            />
                          ))}
                          <span className="text-xs font-medium text-gray-700 ml-1">
                            {rating.toFixed(1)}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Review Text */}
              {review.review_text && (
                <div className="mb-4">
                  <p className="text-gray-700 leading-relaxed text-base">
                    {review.review_text}
                  </p>
                </div>
              )}

              {/* Photo Reviews Display */}
              {review.photos && review.photos.length > 0 && (
                <div className="mb-4">
                  <h5 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <Camera className="h-4 w-4" />
                    Review Photos
                  </h5>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {review.photos.map((photo, index) => (
                      <div key={index} className="relative group cursor-pointer">
                        <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden border border-gray-200 hover:border-primary transition-colors">
                          <img
                            src={photo}
                            alt={`Review photo ${index + 1}`}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-lg" />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Enhanced Review Footer with Helpfulness Voting */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                <div className="flex items-center gap-4">
                  {/* Helpfulness Voting */}
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">Helpful?</span>
                    <div className="flex items-center gap-1">
                      <button
                        onClick={() => handleHelpfulnessVote(review.id, true)}
                        className="flex items-center gap-1 px-2 py-1 rounded-lg text-xs font-medium transition-colors hover:bg-green-50 hover:text-green-700 text-gray-600"
                      >
                        <ThumbsUp className="h-3 w-3" />
                        Yes
                      </button>
                      <button
                        onClick={() => handleHelpfulnessVote(review.id, false)}
                        className="flex items-center gap-1 px-2 py-1 rounded-lg text-xs font-medium transition-colors hover:bg-red-50 hover:text-red-700 text-gray-600"
                      >
                        <ThumbsDown className="h-3 w-3" />
                        No
                      </button>
                    </div>
                  </div>

                  {/* Helpfulness Score Display */}
                  {review.helpful_votes && review.helpful_votes > 0 && (
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <Users className="h-3 w-3" />
                      <span>{review.helpful_votes} people found this helpful</span>
                    </div>
                  )}
                </div>

                {/* Review Engagement Actions */}
                <div className="flex items-center gap-2">
                  <button className="text-gray-400 hover:text-primary transition-colors p-1 rounded-lg hover:bg-gray-50">
                    <Share2 className="h-4 w-4" />
                  </button>
                  <button className="text-gray-400 hover:text-primary transition-colors p-1 rounded-lg hover:bg-gray-50">
                    <MessageCircle className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {!isAuthenticated && (
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <p className="text-blue-800 text-center">
            <strong>Sign in to write a review</strong> and help others make informed decisions about {productType} products.
          </p>
        </div>
      )}
    </div>
  )
}

export default ReviewSystem
