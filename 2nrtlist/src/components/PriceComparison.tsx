import React, { useState, useEffect } from 'react';
import { 
  DollarSign, Search, Filter, TrendingUp, Star, Shield, Truck, 
  Clock, Package, MapPin, Globe, Phone, ExternalLink, Award,
  AlertCircle, CheckCircle, Zap, Target, Tag, Heart
} from 'lucide-react';
import { getComprehensivePriceComparison } from '../lib/supabase';

interface PriceComparison {
  id: string;
  type: 'store' | 'vendor';
  source_name: string;
  source_type: string;
  location: string;
  product_name: string;
  brand: string;
  category: string;
  flavor: string;
  nicotine_strength: string;
  package_size: string;
  price_regular: number;
  price_sale?: number;
  price_bulk?: number;
  bulk_quantity?: number;
  price_subscription?: number;
  discount_percentage: number;
  shipping_cost?: number;
  free_shipping_threshold?: number;
  estimated_delivery?: number;
  in_stock: boolean;
  stock_level: string;
  rating: number;
  review_count: number;
  verified: boolean;
  distance?: number;
  phone?: string;
  address?: string;
  website?: string;
  affiliate_link?: string;
  drive_through?: boolean;
  pharmacy_available?: boolean;
  subscription_available?: boolean;
  bulk_available?: boolean;
  last_updated: string;
}

interface SophisticatedFilters {
  searchQuery: string;
  category: string;
  brand: string;
  flavor: string;
  nicotineStrength: string;
  sortBy: 'price' | 'rating' | 'availability' | 'shipping';
  includeStores: boolean;
  includeVendors: boolean;
  inStockOnly: boolean;
  verifiedOnly: boolean;
  maxPrice: number;
}

const PriceComparison: React.FC = () => {
  const [comparisons, setComparisons] = useState<PriceComparison[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  
  // Sophisticated filters state
  const [filters, setFilters] = useState<SophisticatedFilters>({
    searchQuery: '',
    category: 'all',
    brand: 'all',
    flavor: 'all',
    nicotineStrength: 'all',
    sortBy: 'price',
    includeStores: true,
    includeVendors: true,
    inStockOnly: false,
    verifiedOnly: false,
    maxPrice: 100
  });

  // Fetch comprehensive price comparisons
  useEffect(() => {
    fetchPriceComparisons();
  }, [filters]);

  const fetchPriceComparisons = async () => {
    try {
      setLoading(true);
      console.log('🚨 PriceComparison: Fetching price comparisons with filters:', filters);
      
      const sophisticatedFilters = {
        category: filters.category !== 'all' ? filters.category : undefined,
        brand: filters.brand !== 'all' ? filters.brand : undefined,
        flavor: filters.flavor !== 'all' ? filters.flavor : undefined,
        nicotineStrength: filters.nicotineStrength !== 'all' ? filters.nicotineStrength : undefined,
        sortBy: filters.sortBy,
        includeStores: filters.includeStores,
        includeVendors: filters.includeVendors
      };
      
      const priceComparisons = await getComprehensivePriceComparison(sophisticatedFilters);
      
      // Apply additional filters
      let filteredComparisons = priceComparisons;
      
      if (filters.inStockOnly) {
        filteredComparisons = filteredComparisons.filter(item => item.in_stock);
      }
      
      if (filters.verifiedOnly) {
        filteredComparisons = filteredComparisons.filter(item => item.verified);
      }
      
      if (filters.maxPrice < 100) {
        filteredComparisons = filteredComparisons.filter(item => 
          (item.price_sale || item.price_regular) <= filters.maxPrice
        );
      }
      
      // Apply search query filter
      if (filters.searchQuery.trim()) {
        const query = filters.searchQuery.toLowerCase();
        filteredComparisons = filteredComparisons.filter(item => 
          item.source_name.toLowerCase().includes(query) ||
          item.product_name.toLowerCase().includes(query) ||
          item.brand.toLowerCase().includes(query) ||
          item.location.toLowerCase().includes(query)
        );
      }
      
      setComparisons(filteredComparisons);
      setError(null);
      console.log('🚨 SophisticatedPriceComparison: Found', filteredComparisons.length, 'price comparisons');
    } catch (err) {
      console.error('🚨 SophisticatedPriceComparison: Error:', err);
      setError('Failed to load price comparisons. Please try again.');
      setComparisons([]);
    } finally {
      setLoading(false);
    }
  };

  const updateFilter = (key: keyof SophisticatedFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const getBestPrice = (item: PriceComparison) => {
    const prices = [item.price_regular, item.price_sale, item.price_bulk, item.price_subscription].filter(Boolean);
    return Math.min(...prices);
  };

  const getTotalCost = (item: PriceComparison) => {
    const bestPrice = getBestPrice(item);
    const shippingCost = item.shipping_cost || 0;
    // Check if free shipping applies
    if (item.free_shipping_threshold && bestPrice >= item.free_shipping_threshold) {
      return bestPrice;
    }
    return bestPrice + shippingCost;
  };

  const getSavings = (item: PriceComparison) => {
    if (item.price_sale && item.price_sale < item.price_regular) {
      return item.price_regular - item.price_sale;
    }
    return 0;
  };

  const getStockLevelColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-wellness bg-wellness-50';
      case 'medium': return 'text-rating-gold bg-rating-gold-50';
      case 'low': return 'text-alert-red bg-alert-red-50';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTypeIcon = (type: string) => {
    return type === 'store' ? MapPin : Globe;
  };

  const getTypeColor = (type: string) => {
    return type === 'store' ? 'text-wellness bg-wellness-50' : 'text-rating-gold bg-rating-gold-50';
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Sophisticated Search Header */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Main Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search products, stores, or vendors..."
                value={filters.searchQuery}
                onChange={(e) => updateFilter('searchQuery', e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
              />
            </div>
          </div>
          
          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 px-4 py-3 bg-wellness text-white rounded-lg hover:bg-wellness/90 transition-colors"
          >
            <Filter className="w-5 h-5" />
            Price Filters
          </button>
        </div>

        {/* Advanced Filters Panel */}
        {showFilters && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  value={filters.category}
                  onChange={(e) => updateFilter('category', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value="all">All Categories</option>
                  <option value="gum">Nicotine Gum</option>
                  <option value="lozenges">Lozenges</option>
                  <option value="patches">Patches</option>
                  <option value="pouches">Pouches</option>
                </select>
              </div>

              {/* Brand */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Brand</label>
                <select
                  value={filters.brand}
                  onChange={(e) => updateFilter('brand', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value="all">All Brands</option>
                  <option value="Nicorette">Nicorette</option>
                  <option value="NicoDerm CQ">NicoDerm CQ</option>
                  <option value="Commit">Commit</option>
                  <option value="ZYN">ZYN</option>
                </select>
              </div>

              {/* Max Price */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Max Price</label>
                <select
                  value={filters.maxPrice}
                  onChange={(e) => updateFilter('maxPrice', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value={25}>Under $25</option>
                  <option value={50}>Under $50</option>
                  <option value={75}>Under $75</option>
                  <option value={100}>Any Price</option>
                </select>
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => updateFilter('sortBy', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wellness focus:border-transparent"
                >
                  <option value="price">Lowest Price</option>
                  <option value="rating">Highest Rated</option>
                  <option value="availability">Best Availability</option>
                  <option value="shipping">Fastest/Cheapest Shipping</option>
                </select>
              </div>
            </div>

            {/* Source Types & Features */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-3">Include Sources</label>
              <div className="flex flex-wrap gap-3">
                {[
                  { key: 'includeStores', label: 'Physical Stores', icon: MapPin },
                  { key: 'includeVendors', label: 'Online Vendors', icon: Globe },
                  { key: 'inStockOnly', label: 'In Stock Only', icon: Package },
                  { key: 'verifiedOnly', label: 'Verified Only', icon: Shield }
                ].map(({ key, label, icon: Icon }) => (
                  <label key={key} className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters[key as keyof typeof filters] as boolean}
                      onChange={(e) => updateFilter(key as keyof typeof filters, e.target.checked)}
                      className="rounded border-gray-300 text-wellness focus:ring-wellness"
                    />
                    <Icon className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-700">{label}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-semibold text-gray-900">
            {loading ? 'Comparing prices...' : `${comparisons.length} price comparisons found`}
          </h2>
          {comparisons.length > 0 && (
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1 text-sm text-wellness">
                <TrendingUp className="w-4 h-4" />
                Best deal: ${Math.min(...comparisons.map(getBestPrice)).toFixed(2)}
              </div>
              <div className="flex items-center gap-1 text-sm text-blue-600">
                <Truck className="w-4 h-4" />
                Best total: ${Math.min(...comparisons.map(getTotalCost)).toFixed(2)}
              </div>
              <button className="text-sm text-gray-600 hover:text-gray-900 flex items-center gap-1">
                <ExternalLink className="w-4 h-4" />
                Export Results
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-wellness mx-auto"></div>
          <p className="mt-4 text-gray-600">Comparing prices across stores and vendors...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{error}</p>
          </div>
        </div>
      )}

      {/* Price Comparison Results */}
      {!loading && !error && comparisons.length === 0 && (
        <div className="text-center py-12">
          <DollarSign className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No price comparisons found</h3>
          <p className="text-gray-600">Try adjusting your filters or search criteria.</p>
        </div>
      )}

      {/* Price Comparison Cards */}
      <div className="space-y-4">
        {comparisons.map((item) => {
          const TypeIcon = getTypeIcon(item.type);
          const bestPrice = getBestPrice(item);
          const savings = getSavings(item);
          
          return (
            <div key={item.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs rounded-full ${getTypeColor(item.type)}`}>
                      <TypeIcon className="w-3 h-3" />
                      {item.source_type}
                    </span>
                    <h3 className="text-lg font-semibold text-gray-900">{item.source_name}</h3>
                    {item.verified && (
                      <CheckCircle className="w-5 h-5 text-wellness" />
                    )}
                  </div>
                  <p className="text-gray-600">{item.location}</p>
                  <p className="text-sm text-gray-500">{item.product_name} - {item.package_size}</p>
                </div>
                
                {/* Price & Rating */}
                <div className="text-right">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="text-right">
                      {item.price_sale && item.price_sale < item.price_regular ? (
                        <>
                          <span className="text-lg font-bold text-wellness">${item.price_sale.toFixed(2)}</span>
                          <span className="text-sm text-gray-500 line-through ml-2">${item.price_regular.toFixed(2)}</span>
                        </>
                      ) : (
                        <span className="text-lg font-bold text-gray-900">${item.price_regular.toFixed(2)}</span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-sm font-medium">{item.rating.toFixed(1)}</span>
                    <span className="text-xs text-gray-500">({item.review_count})</span>
                  </div>
                </div>
              </div>

              {/* Features & Details */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                {/* Stock Status */}
                <div className="flex items-center gap-2">
                  <Package className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-700">Stock:</span>
                  <span className={`px-2 py-1 text-xs rounded-full ${getStockLevelColor(item.stock_level)}`}>
                    {item.in_stock ? item.stock_level : 'Out of stock'}
                  </span>
                </div>

                {/* Shipping/Distance */}
                <div className="flex items-center gap-2">
                  {item.type === 'store' ? (
                    <>
                      <MapPin className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-700">{item.distance?.toFixed(1)} miles away</span>
                    </>
                  ) : (
                    <>
                      <Truck className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-700">
                        {item.shipping_cost === 0 ? 'Free shipping' : `$${item.shipping_cost?.toFixed(2)} shipping`}
                      </span>
                    </>
                  )}
                </div>

                {/* Special Features */}
                <div className="flex items-center gap-2">
                  {item.subscription_available && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                      <Zap className="w-3 h-3" />
                      Subscription
                    </span>
                  )}
                  {item.bulk_available && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      <Package className="w-3 h-3" />
                      Bulk
                    </span>
                  )}
                </div>
              </div>

              {/* Savings & Discounts */}
              {savings > 0 && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                  <div className="flex items-center gap-2">
                    <Tag className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium text-green-800">
                      Save ${savings.toFixed(2)} ({item.discount_percentage.toFixed(0)}% off)
                    </span>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex items-center gap-4">
                  {item.phone && (
                    <a href={`tel:${item.phone}`} className="flex items-center gap-1 text-sm text-gray-600 hover:text-wellness">
                      <Phone className="w-4 h-4" />
                      Call
                    </a>
                  )}
                  {item.website && (
                    <a href={item.website} target="_blank" rel="noopener noreferrer" className="flex items-center gap-1 text-sm text-gray-600 hover:text-wellness">
                      <Globe className="w-4 h-4" />
                      Website
                    </a>
                  )}
                  {item.type === 'store' && item.address && (
                    <a 
                      href={`https://maps.google.com/?q=${encodeURIComponent(item.address)}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1 text-sm text-gray-600 hover:text-wellness"
                    >
                      <MapPin className="w-4 h-4" />
                      Directions
                    </a>
                  )}
                </div>
                
                <button className="flex items-center gap-1 px-4 py-2 bg-wellness text-white rounded-lg hover:bg-wellness/90 transition-colors">
                  <ExternalLink className="w-4 h-4" />
                  {item.type === 'store' ? 'Visit Store' : 'Shop Now'}
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default PriceComparison;
