import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Search, Menu, X, DollarSign, Users, Star, ArrowRight, Package, Target, Store, BookOpen, Zap, Award, Shield, CheckCircle } from 'lucide-react';
import { getProducts, getTestimonials, getVendors } from './lib/supabase';


const NRTLandingPage: React.FC = () => {
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [products, setProducts] = useState<any[]>([]);
  const [testimonials, setTestimonials] = useState<any[]>([]);
  const [vendors, setVendors] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const searchButtonRef = useRef<HTMLButtonElement>(null);

  // BULLETPROOF SEARCH HANDLER - bypasses React synthetic event issues
  const handleSearch = (e?: React.FormEvent | Event) => {
    if (e) e.preventDefault();
    console.log('🚨🚨🚨 BULLETPROOF SEARCH FIX - FUNCTION CALLED!');
    if (searchQuery.trim()) {
      const searchUrl = `/search?q=${encodeURIComponent(searchQuery.trim())}`;
      console.log('🚨🚨🚨 FORCING NAVIGATION TO:', searchUrl);
      try {
        navigate(searchUrl);
        console.log('🚨 navigate() called successfully');
      } catch (error) {
        console.error('🚨 navigate() failed, using window.location:', error);
        window.location.href = searchUrl;
      }
      setTimeout(() => {
        if (window.location.pathname === '/') {
          console.log('🚨 EMERGENCY BACKUP - Forcing navigation with window.location');
          window.location.href = searchUrl;
        }
      }, 500);
    } else {
      console.log('🚨 Empty search query, not navigating');
    }
  };

  // BULLETPROOF DOM EVENT LISTENER - direct DOM attachment
  useEffect(() => {
    const searchButton = searchButtonRef.current;
    if (searchButton) {
      console.log('🚨 BULLETPROOF: Attaching direct DOM event listener to search button');
      const handleButtonClick = (e: Event) => {
        e.preventDefault();
        console.log('🚨 BULLETPROOF: Direct DOM click event triggered!');
        handleSearch(e);
      };
      
      searchButton.addEventListener('click', handleButtonClick);
      
      return () => {
        console.log('🚨 BULLETPROOF: Cleaning up DOM event listener');
        searchButton.removeEventListener('click', handleButtonClick);
      };
    }
  }, [searchQuery]); // Re-attach when searchQuery changes

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Load products from database (RULE 0001 COMPLIANT)
  useEffect(() => {
    console.log('🚨 App.tsx: useEffect starting - loading products from database...');

    const loadData = async () => {
      try {
        console.log('🚨 App.tsx: Setting loading to true...');
        setLoading(true);

        console.log('🚨 App.tsx: Calling getProducts(), getTestimonials(), and getVendors() functions...');
        const [productData, testimonialsData, vendorsData] = await Promise.all([
          getProducts(),
          getTestimonials(),
          getVendors()
        ]);

        console.log('🚨 App.tsx: getProducts() returned:', productData?.length || 0, 'products');
        console.log('🚨 App.tsx: getTestimonials() returned:', testimonialsData?.length || 0, 'testimonials');
        console.log('🚨 App.tsx: getVendors() returned:', vendorsData?.length || 0, 'vendors');

        setProducts(productData);
        setTestimonials(testimonialsData);
        setVendors(vendorsData);
        setError(null);
        console.log('🚨 App.tsx: Successfully set products and testimonials state');
      } catch (err) {
        console.error('🚨 App.tsx: Error in loadData:', err);
        setError(err instanceof Error ? err.message : 'Failed to load data');
      } finally {
        console.log('🚨 App.tsx: Setting loading to false...');
        setLoading(false);
      }
    };

    console.log('🚨 App.tsx: About to call loadData()...');
    loadData();
  }, []);

  return (
    <div className="min-h-screen bg-white">
      {/* Apple-style Navigation Header */}
      <nav className="sticky top-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo - Apple-style Professional Branding */}
            <Link to="/" className="flex items-center gap-4 hover:opacity-80 transition-opacity duration-200">
              <div className="w-12 h-12 bg-wellness rounded-2xl flex items-center justify-center shadow-sm">
                <Package className="w-7 h-7 text-white" strokeWidth={2.5} />
              </div>
              <span className="text-2xl font-bold text-gray-900 tracking-tight">NRTList</span>
            </Link>

            {/* Desktop Navigation - Correct Business Priority Order */}
            <nav className="hidden md:flex items-center space-x-6">
              <Link to="/store-locator" className="text-gray-700 hover:text-wellness transition-colors font-medium">
                Store Locator
              </Link>
              <Link to="/online-vendors" className="text-gray-700 hover:text-wellness transition-colors font-medium">
                Online Vendors
              </Link>
              <Link to="/price-compare" className="text-gray-700 hover:text-wellness transition-colors font-medium">
                Price Compare
              </Link>
              <Link to="/products" className="text-gray-700 hover:text-wellness transition-colors font-medium">
                NRT Directory
              </Link>
              <Link to="/smokeless-alternatives" className="text-gray-700 hover:text-wellness transition-colors font-medium">
                Smokeless
              </Link>
              <Link to="/reviews" className="text-gray-700 hover:text-wellness transition-colors font-medium">
                Reviews & Ratings
              </Link>
              <Link to="/deals" className="text-gray-700 hover:text-wellness transition-colors font-medium">
                Deals
              </Link>
            </nav>

            {/* Auth Buttons */}
            <div className="hidden md:flex items-center gap-4">
              <Link
                to="/login"
                className="text-gray-700 hover:text-wellness font-medium transition-colors"
              >
                Sign In
              </Link>
              <Link
                to="/signup"
                className="bg-wellness text-white px-6 py-2 rounded-xl hover:bg-wellness-hover font-medium transition-all duration-200"
              >
                Sign Up
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button 
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-t border-gray-200">
            <div className="px-4 py-4 space-y-4">
              <Link to="/store-locator" onClick={() => setIsMenuOpen(false)} className="block text-gray-700 hover:text-wellness font-medium transition-colors">Store Locator</Link>
              <Link to="/online-vendors" onClick={() => setIsMenuOpen(false)} className="block text-gray-700 hover:text-wellness font-medium transition-colors">Online Vendors</Link>
              <Link to="/price-compare" onClick={() => setIsMenuOpen(false)} className="block text-gray-700 hover:text-wellness font-medium transition-colors">Price Compare</Link>
              <Link to="/products" onClick={() => setIsMenuOpen(false)} className="block text-wellness font-medium">NRT Directory</Link>
              <Link to="/smokeless-alternatives" onClick={() => setIsMenuOpen(false)} className="block text-gray-700 hover:text-wellness font-medium transition-colors">Smokeless</Link>
              <Link to="/reviews" onClick={() => setIsMenuOpen(false)} className="block text-gray-700 hover:text-wellness font-medium transition-colors">Reviews & Ratings</Link>
              <Link to="/deals" onClick={() => setIsMenuOpen(false)} className="block text-gray-700 hover:text-wellness font-medium transition-colors">Deals</Link>
              <div className="pt-4 border-t border-gray-200 space-y-3">
                <Link
                  to="/login"
                  onClick={() => setIsMenuOpen(false)}
                  className="block w-full text-left text-gray-700 hover:text-wellness font-medium transition-colors"
                >
                  Sign In
                </Link>
                <Link
                  to="/signup"
                  onClick={() => setIsMenuOpen(false)}
                  className="block w-full bg-wellness text-white px-4 py-2 rounded-xl hover:bg-wellness-hover font-medium transition-all duration-200 text-center"
                >
                  Sign Up
                </Link>
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Accessibility Skip Links */}
      <div className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50">
        <a href="#main-content" className="bg-wellness text-white px-4 py-2 rounded-lg">
          Skip to main content
        </a>
      </div>

      {/* Breadcrumb Navigation */}
      <nav className="bg-gray-50 border-b border-gray-200" aria-label="Breadcrumb">
        <div className="max-w-7xl mx-auto px-6 py-3">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <span className="text-gray-900 font-medium">Home</span>
            </li>
          </ol>
        </div>
      </nav>

      {/* Main Content */}
      <main id="main-content" className="min-h-screen bg-white">
        {/* Sophisticated Hero Section with Apple-style Layout */}
        <div className="relative max-w-7xl mx-auto px-8 pt-24 pb-32 overflow-hidden">
          {/* Refined Background Elements */}
          <div className="absolute inset-0 opacity-3 pointer-events-none">
            <div className="absolute top-16 left-16 w-40 h-40 bg-wellness rounded-full blur-3xl"></div>
            <div className="absolute bottom-24 right-24 w-56 h-56 bg-wellness rounded-full blur-3xl"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-wellness rounded-full blur-3xl opacity-20"></div>
          </div>

          <div className="text-center relative space-y-10">
            {/* Enhanced NRT Platform Badge */}
            <div className="inline-flex items-center gap-3 bg-muted text-wellness px-6 md:px-8 py-3 md:py-4 rounded-full text-sm font-medium border border-border backdrop-blur-sm shadow-sm" role="banner" aria-label="NRT platform features">
              <Zap className="w-4 h-4 animate-pulse" aria-hidden="true" />
              <span className="hidden sm:inline">World's Largest NRT Directory, Store Locator & Price Comparison</span>
              <span className="sm:hidden">NRT Directory & Store Locator</span>
              <Award className="w-4 h-4" aria-hidden="true" />
            </div>

            <header>
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-gray-900 leading-tight tracking-tight">
                <span className="block mb-2">Craving Hit?</span>
                <span className="text-wellness block">
                  Find NRT Help Now
                </span>
              </h1>
            </header>
            
            <div className="bg-alert-red-50/80 border border-alert-red/30 rounded-2xl px-4 md:px-6 py-4 md:py-5 max-w-2xl mx-auto shadow-sm backdrop-blur-sm" role="alert" aria-live="polite">
              <p className="text-alert-red font-medium text-center leading-relaxed text-sm md:text-base">
                Don't let cravings win. Find nicotine replacement therapy near you in seconds.
              </p>
            </div>

            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              Find nearest stores that sell NRT products when cravings hit.
              Compare prices across {loading ? 'loading...' : `${vendors.length}+`} online vendors for the best deals.
              Browse reviews of every NRT product with detailed specifications.
              <br className="hidden md:block" />
              <span className="font-semibold text-foreground mt-4 block">
                The internet's definitive NRT directory - like Vivino for wine, but for NRT.
              </span>
            </p>

            {/* Hero Search Bar */}
            <div className="max-w-2xl mx-auto mb-8">
              <form onSubmit={handleSearch} className="relative" role="search">
                <div className="absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={handleSearchInputChange}
                  placeholder="Find nicotine gum, patches, lozenges near you..."
                  className="block w-full pl-14 pr-28 py-4 text-lg border border-gray-200 rounded-2xl bg-white/95 backdrop-blur-sm focus:ring-2 focus:ring-wellness/20 focus:border-wellness shadow-lg hover:shadow-xl focus:shadow-xl transition-all duration-200 placeholder-gray-400"
                  aria-label="Search for NRT products and stores"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    ref={searchButtonRef}
                    type="submit"
                    className="bg-wellness text-white px-5 py-2.5 rounded-xl hover:bg-wellness-hover focus:ring-2 focus:ring-wellness/20 transition-all duration-200 font-medium shadow-sm hover:shadow-md"
                  >
                    Search
                  </button>
                </div>
              </form>
            </div>

            {/* Apple-style Filter Buttons */}
            <nav className="max-w-3xl mx-auto mb-12" aria-label="Product categories">
              <div className="flex flex-wrap justify-center gap-2 md:gap-3">
                <Link
                  to="/products"
                  className="bg-white/95 backdrop-blur-sm text-gray-700 px-4 md:px-6 py-2.5 md:py-3 rounded-xl border border-gray-200 hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness/20 focus:outline-none text-sm md:text-base"
                  aria-label="Browse all NRT products"
                >
                  All Products
                </Link>
                <Link
                  to="/products?category=gum"
                  className="bg-white/95 backdrop-blur-sm text-gray-700 px-4 md:px-6 py-2.5 md:py-3 rounded-xl border border-gray-200 hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness/20 focus:outline-none text-sm md:text-base"
                  aria-label="Browse nicotine gum products"
                >
                  Gum
                </Link>
                <Link
                  to="/products?category=patch"
                  className="bg-white/95 backdrop-blur-sm text-gray-700 px-4 md:px-6 py-2.5 md:py-3 rounded-xl border border-gray-200 hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness/20 focus:outline-none text-sm md:text-base"
                  aria-label="Browse nicotine patch products"
                >
                  Patch
                </Link>
                <Link
                  to="/products?category=lozenge"
                  className="bg-white/95 backdrop-blur-sm text-gray-700 px-4 md:px-6 py-2.5 md:py-3 rounded-xl border border-gray-200 hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness/20 focus:outline-none text-sm md:text-base"
                  aria-label="Browse nicotine lozenge products"
                >
                  Lozenge
                </Link>
                <Link
                  to="/products?category=spray"
                  className="bg-white/95 backdrop-blur-sm text-gray-700 px-4 md:px-6 py-2.5 md:py-3 rounded-xl border border-gray-200 hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness/20 focus:outline-none text-sm md:text-base"
                  aria-label="Browse nicotine spray products"
                >
                  Spray
                </Link>
              </div>
            </nav>

            {/* Apple-style Priority Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 md:gap-6 justify-center items-center mb-16 md:mb-20" role="group" aria-label="Main action options">
              <Link
                to="/store-locator"
                className="group relative bg-wellness text-white px-8 md:px-12 py-4 md:py-5 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-lg hover:scale-[1.02] flex items-center gap-3 text-base md:text-lg overflow-hidden focus:ring-2 focus:ring-ring focus:outline-none w-full sm:w-auto justify-center sm:justify-start"
                aria-label="Find NRT stores near your location"
              >
                <div className="absolute inset-0 bg-wellness/90 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <Search className="w-5 md:w-6 h-5 md:h-6 relative z-10 group-hover:rotate-6 transition-transform duration-200" strokeWidth={2} aria-hidden="true" />
                <span className="relative z-10">Find NRT Store Near Me</span>
                <div className="absolute inset-0 bg-white/10 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500 skew-x-12"></div>
              </Link>

              <Link
                to="/online-vendors"
                className="group relative bg-white text-gray-900 px-8 md:px-12 py-4 md:py-5 rounded-2xl font-semibold transition-all duration-300 border border-gray-200 hover:border-wellness/30 shadow-lg hover:shadow-xl hover:scale-[1.02] flex items-center gap-3 text-base md:text-lg overflow-hidden focus:ring-2 focus:ring-wellness/30 focus:outline-none w-full sm:w-auto justify-center sm:justify-start"
                aria-label="Compare prices from online NRT vendors"
              >
                <div className="absolute inset-0 bg-muted opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <DollarSign className="w-5 md:w-6 h-5 md:h-6 relative z-10 group-hover:scale-105 transition-transform duration-200" strokeWidth={2} aria-hidden="true" />
                <span className="relative z-10">Compare Online Vendors</span>
                <div className="absolute inset-0 bg-wellness/5 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500 skew-x-12"></div>
              </Link>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-gray-500 mb-16">
              <div className="flex items-center gap-2 bg-white/50 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200/50">
                <Shield className="w-4 h-4 text-wellness" />
                <span>FDA-Approved Products</span>
              </div>
              <div className="flex items-center gap-2 bg-white/50 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200/50">
                <Award className="w-4 h-4 text-wellness" />
                <span>Expert Reviewed</span>
              </div>
              <div className="flex items-center gap-2 bg-white/50 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200/50">
                <Users className="w-4 h-4 text-wellness" />
                <span>{loading ? 'Loading...' : `${testimonials.length || 0}+ Users`}</span>
              </div>
            </div>



            {/* Core Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              <div className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-gray-200/50 text-center hover:shadow-2xl transition-all duration-300">
                <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Search className="w-8 h-8 text-wellness" strokeWidth={2} />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">NRT Store Locator</h3>
                <p className="text-gray-600 leading-relaxed">Find nearest pharmacies, supermarkets & stores that sell NRT when cravings hit. Real-time inventory & pricing.</p>
              </div>

              <div className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-gray-200/50 text-center hover:shadow-2xl transition-all duration-300">
                <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <DollarSign className="w-8 h-8 text-wellness" strokeWidth={2} />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Online Vendor Price Comparison</h3>
                <p className="text-gray-600 leading-relaxed">Compare {loading ? 'loading...' : `${vendors.length}+`} online vendors for the cheapest NRT prices. Check delivery options & discounts.</p>
              </div>
              
              <div className="bg-white/80 backdrop-blur-xl rounded-3xl p-8 shadow-xl border border-gray-200/50 text-center hover:shadow-2xl transition-all duration-300">
                <div className="w-16 h-16 bg-wellness-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Star className="w-8 h-8 text-wellness" strokeWidth={2} />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Complete NRT Directory</h3>
                <p className="text-gray-600 leading-relaxed">Professional reviews of every NRT product. Detailed specs: nicotine strength, flavor, effectiveness.</p>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Apple-style Secondary Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              The Vivino for NRT Products
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover, rate, and find the best NRT deals with our comprehensive directory and price comparison platform
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="grid md:grid-cols-3 gap-8 max-w-4xl">
              <div className="text-center">
                <div className="bg-wellness-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Search className="w-8 h-8 text-wellness" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center gap-2">
                  <Store className="w-5 h-5 text-wellness" />
                  NRT Store Locator
                </h3>
                <p className="text-gray-600">
                  Find nearest pharmacies, supermarkets & stores that sell NRT when cravings hit. Real-time inventory & pricing.
                </p>
              </div>
              <div className="text-center">
                <div className="bg-wellness-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <DollarSign className="w-8 h-8 text-wellness" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center gap-2">
                  <DollarSign className="w-5 h-5 text-wellness" />
                  Online Vendor Price Comparison
                </h3>
                <p className="text-gray-600">
                  Compare {loading ? 'loading...' : `${vendors.length}+`} online vendors for the cheapest NRT prices. Check delivery options & discounts.
                </p>
              </div>
              <div className="text-center">
                <div className="bg-wellness-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Star className="w-8 h-8 text-wellness" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2 flex items-center justify-center gap-2">
                  <BookOpen className="w-5 h-5 text-wellness" />
                  Complete NRT Directory
                </h3>
                <p className="text-gray-600">
                  Professional reviews of every NRT product. Detailed specs: nicotine strength, flavor, effectiveness.
                </p>
              </div>
            </div>

            <div className="bg-muted rounded-3xl p-12 text-center">
              <div className="w-20 h-20 bg-white rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Target className="w-10 h-10 text-wellness" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Start Your Journey</h3>
              <p className="text-gray-600 mb-8">
                Join {loading ? 'loading...' : `${testimonials.length > 0 ? testimonials.length : 'many'} users`} who have found their perfect NRT solution
              </p>
              <Link
                to="/products"
                className="inline-flex items-center gap-2 bg-wellness text-white px-8 py-4 rounded-xl hover:bg-wellness-hover hover:scale-105 hover:shadow-lg font-semibold transition-all duration-200 transform"
                onClick={() => {
                  // Track CTA click for analytics
                  console.log('CTA clicked: Get Started - Homepage Hero');
                  // In production, this would send to analytics service
                  if (typeof (window as any).gtag !== 'undefined') {
                    (window as any).gtag('event', 'click', {
                      event_category: 'CTA',
                      event_label: 'Homepage Hero - Get Started',
                      value: 1
                    });
                  }
                }}
              >
                Get Started
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </section>





      {/* Featured Products Section - Real NRT Data */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Featured NRT Products</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover top-rated nicotine replacement therapy products from trusted brands
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden animate-pulse">
                  <div className="aspect-square bg-gray-200"></div>
                  <div className="p-6">
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded mb-3 w-2/3"></div>
                    <div className="flex items-center gap-2 mb-3">
                      <div className="flex items-center gap-1">
                        <div className="w-4 h-4 bg-gray-200 rounded"></div>
                        <div className="h-3 bg-gray-200 rounded w-8"></div>
                      </div>
                      <div className="h-3 bg-gray-200 rounded w-16"></div>
                    </div>
                    <div className="h-3 bg-gray-200 rounded mb-4"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-600">Error loading products: {error}</p>
            </div>
          ) : products.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {products.slice(0, 8).map((product) => (
                <Link
                  key={product.id}
                  to={`/product/${product.id}`}
                  className="bg-white rounded-2xl shadow-sm hover:shadow-xl hover:scale-105 hover:-translate-y-2 transition-all duration-300 overflow-hidden group border border-gray-100 hover:border-wellness-200"
                  aria-label={`View details for ${product.name} by ${product.brand}`}
                >
                  <div className="aspect-square bg-gray-100 flex items-center justify-center p-6">
                    {product.image_url ? (
                      <img 
                        src={product.image_url} 
                        alt={product.name}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <Package className="w-16 h-16 text-gray-400" />
                    )}
                  </div>
                  <div className="p-6">
                    <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-wellness transition-colors">
                      {product.name}
                    </h3>
                    <p className="text-sm text-gray-600 mb-3">{product.brand}</p>
                    <div className="flex items-center gap-2 mb-3">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm font-medium">
                          {product.user_rating_avg ? product.user_rating_avg.toFixed(1) : 'N/A'}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">
                        ({product.user_rating_count || 0} reviews)
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 line-clamp-2 mb-4">{product.description}</p>

                    {/* Quick View Button */}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <button
                        className="bg-white text-gray-900 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors"
                        onClick={(e) => {
                          e.preventDefault();
                          // Quick view modal functionality
                          console.log('Quick view:', product.name);
                        }}
                      >
                        Quick View
                      </button>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                <div className="flex items-center gap-2 text-red-800 mb-2">
                  <Package className="w-5 h-5" />
                  <span className="font-medium">Failed to load products</span>
                </div>
                <p className="text-red-600 text-sm">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-4 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-600">No products found</p>
            </div>
          )}

          <div className="text-center mt-12">
            <Link
              to="/products"
              className="inline-flex items-center gap-2 bg-wellness text-white px-8 py-4 rounded-xl hover:bg-wellness-hover font-semibold transition-all duration-200"
            >
              View All Products
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Newsletter Signup Section */}
      <section className="bg-wellness py-16">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Stay Updated on NRT Deals</h2>
          <p className="text-white/80 mb-8 text-lg">Get notified about price drops, new products, and exclusive discounts.</p>
          <form className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto" onSubmit={(e) => {
            e.preventDefault();
            const email = e.currentTarget.email.value;
            if (!email || !email.includes('@')) {
              alert('Please enter a valid email address');
              return;
            }
            // Newsletter signup functionality
            console.log('Newsletter signup:', email);
            alert('Thank you for subscribing! You will receive updates about NRT deals and products.');
            e.currentTarget.reset();
          }}>
            <input
              type="email"
              name="email"
              placeholder="Enter your email"
              required
              className="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:outline-none"
              aria-label="Email address for newsletter"
            />
            <button
              type="submit"
              className="bg-white text-wellness px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Subscribe
            </button>
          </form>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">What Our Users Say</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.length > 0 ? (
              testimonials.map((testimonial) => (
                <div key={testimonial.id} className="bg-gray-50 rounded-xl p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-4 h-4 ${i < testimonial.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                      />
                    ))}
                  </div>
                  <p className="text-gray-600 mb-4">"{testimonial.testimonial_text}"</p>
                  <div className="flex items-center justify-between">
                    <p className="font-semibold text-gray-900">{testimonial.user_name}</p>
                    {testimonial.verified_purchase && (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    )}
                  </div>
                  {testimonial.days_smoke_free && (
                    <p className="text-xs text-green-600 mt-2">
                      {testimonial.days_smoke_free} days smoke-free
                    </p>
                  )}
                </div>
              ))
            ) : (
              // Fallback testimonials while loading
              [...Array(3)].map((_, i) => (
                <div key={i} className="bg-gray-50 rounded-xl p-6 animate-pulse">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(5)].map((_, j) => (
                      <div key={j} className="w-4 h-4 bg-gray-300 rounded"></div>
                    ))}
                  </div>
                  <div className="h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-4 bg-gray-300 rounded mb-4 w-3/4"></div>
                  <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                </div>
              ))
            )}
          </div>
        </div>
      </section>

      {/* Apple-style Footer */}
      <footer className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-wellness rounded-lg flex items-center justify-center">
                  <Package className="w-5 h-5 text-white" strokeWidth={2} />
                </div>
                <span className="text-lg font-bold">NRTList</span>
              </div>
              <p className="text-gray-600">
                The premier platform for nicotine replacement therapy discovery and comparison.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-gray-900">Products</h4>
              <ul className="space-y-2 text-gray-600">
                <li><Link to="/products" className="hover:text-wellness transition-colors">Browse All</Link></li>
                <li><Link to="/products?category=pouches" className="hover:text-wellness transition-colors">Pouches</Link></li>
                <li><Link to="/products?category=gum" className="hover:text-wellness transition-colors">Gum</Link></li>
                <li><Link to="/products?category=lozenges" className="hover:text-wellness transition-colors">Lozenges</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-gray-900">Resources</h4>
              <ul className="space-y-2 text-gray-600">
                <li><Link to="/community" className="hover:text-wellness transition-colors">Help Center</Link></li>
                <li><Link to="/community" className="hover:text-wellness transition-colors">Community</Link></li>
                <li><Link to="/community" className="hover:text-wellness transition-colors">Blog</Link></li>
                <li><Link to="/community" className="hover:text-wellness transition-colors">API</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-gray-900">Company</h4>
              <ul className="space-y-2 text-gray-600">
                <li><Link to="/community" className="hover:text-wellness transition-colors">About</Link></li>
                <li><Link to="/community" className="hover:text-wellness transition-colors">Privacy</Link></li>
                <li><Link to="/community" className="hover:text-wellness transition-colors">Terms</Link></li>
                <li><Link to="/community" className="hover:text-wellness transition-colors">Contact</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-200 mt-16 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-600">&copy; 2024 NRTList. All rights reserved.</p>
              <div className="flex items-center gap-4 mt-4 md:mt-0">
                <a
                  href="https://twitter.com/nrtlist"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-600 hover:text-wellness transition-colors"
                  aria-label="Follow us on Twitter"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
                <a
                  href="https://facebook.com/nrtlist"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-600 hover:text-wellness transition-colors"
                  aria-label="Follow us on Facebook"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clipRule="evenodd" />
                  </svg>
                </a>
                <a
                  href="https://instagram.com/nrtlist"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-600 hover:text-wellness transition-colors"
                  aria-label="Follow us on Instagram"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 0C4.477 0 0 4.477 0 10s4.477 10 10 10 10-4.477 10-10S15.523 0 10 0zm4.5 6.5h-9v7h9v-7z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default NRTLandingPage;
