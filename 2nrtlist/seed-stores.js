// Emergency seed script to fix critical Store Locator failure
import { seedSampleStores } from './src/utils/seedStores.js';

console.log('🚨 EMERGENCY: Running store seeding to fix critical Store Locator failure...');

seedSampleStores()
  .then(() => {
    console.log('🚨 SUCCESS: Store seeding completed! Store Locator should now work.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('🚨 CRITICAL ERROR: Store seeding failed:', error);
    process.exit(1);
  });
