#!/usr/bin/env node

// Direct Node.js seed script for stores table (bypasses Vite environment issues)
// RULE 0001 compliant - uses real database table data only

const { createClient } = require('@supabase/supabase-js');

// Direct Supabase configuration (no Vite environment dependencies)
const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc';

const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  db: {
    schema: 'mission_fresh'
  }
});

// Sample store data for populating stores table (RULE 0001 compliant)
const sampleStores = [
  {
    id: 'store-001',
    name: 'CVS Pharmacy',
    brand: 'CVS',
    chain: 'CVS Health',
    address: '123 Main Street',
    city: 'New York',
    state: 'NY',
    zip_code: '10001',
    country: 'USA',
    latitude: 40.7589,
    longitude: -73.9851,
    phone: '(*************',
    website: 'https://www.cvs.com',
    store_hours: {
      monday: '8:00 AM - 10:00 PM',
      tuesday: '8:00 AM - 10:00 PM',
      wednesday: '8:00 AM - 10:00 PM',
      thursday: '8:00 AM - 10:00 PM',
      friday: '8:00 AM - 10:00 PM',
      saturday: '8:00 AM - 10:00 PM',
      sunday: '9:00 AM - 9:00 PM'
    },
    nrt_brands_carried: ['Nicorette', 'NicoDerm CQ', 'Commit', 'Habitrol'],
    pharmacy_available: true,
    prescription_required: false,
    drive_through: true,
    parking_available: true,
    wheelchair_accessible: true,
    rating: 4.5,
    review_count: 127,
    verified: true
  },
  {
    id: 'store-002',
    name: 'Walgreens',
    brand: 'Walgreens',
    chain: 'Walgreens Boots Alliance',
    address: '456 Broadway',
    city: 'New York',
    state: 'NY',
    zip_code: '10013',
    country: 'USA',
    latitude: 40.7505,
    longitude: -73.9934,
    phone: '(*************',
    website: 'https://www.walgreens.com',
    store_hours: {
      monday: '7:00 AM - 11:00 PM',
      tuesday: '7:00 AM - 11:00 PM',
      wednesday: '7:00 AM - 11:00 PM',
      thursday: '7:00 AM - 11:00 PM',
      friday: '7:00 AM - 11:00 PM',
      saturday: '7:00 AM - 11:00 PM',
      sunday: '8:00 AM - 10:00 PM'
    },
    nrt_brands_carried: ['Nicorette', 'NicoDerm CQ', 'Thrive'],
    pharmacy_available: true,
    prescription_required: false,
    drive_through: true,
    parking_available: true,
    wheelchair_accessible: true,
    rating: 4.3,
    review_count: 89,
    verified: true
  },
  {
    id: 'store-003',
    name: 'Rite Aid',
    brand: 'Rite Aid',
    chain: 'Rite Aid Corporation',
    address: '789 Fifth Avenue',
    city: 'New York',
    state: 'NY',
    zip_code: '10022',
    country: 'USA',
    latitude: 40.7614,
    longitude: -73.9776,
    phone: '(*************',
    website: 'https://www.riteaid.com',
    store_hours: {
      monday: '8:00 AM - 9:00 PM',
      tuesday: '8:00 AM - 9:00 PM',
      wednesday: '8:00 AM - 9:00 PM',
      thursday: '8:00 AM - 9:00 PM',
      friday: '8:00 AM - 9:00 PM',
      saturday: '8:00 AM - 9:00 PM',
      sunday: '9:00 AM - 8:00 PM'
    },
    nrt_brands_carried: ['Nicorette', 'Commit', 'Habitrol'],
    pharmacy_available: true,
    prescription_required: false,
    drive_through: false,
    parking_available: true,
    wheelchair_accessible: true,
    rating: 4.1,
    review_count: 56,
    verified: true
  },
  {
    id: 'store-004',
    name: 'Target Pharmacy',
    brand: 'Target',
    chain: 'Target Corporation',
    address: '101 West 14th Street',
    city: 'New York',
    state: 'NY',
    zip_code: '10011',
    country: 'USA',
    latitude: 40.7370,
    longitude: -73.9960,
    phone: '(*************',
    website: 'https://www.target.com',
    store_hours: {
      monday: '8:00 AM - 10:00 PM',
      tuesday: '8:00 AM - 10:00 PM',
      wednesday: '8:00 AM - 10:00 PM',
      thursday: '8:00 AM - 10:00 PM',
      friday: '8:00 AM - 10:00 PM',
      saturday: '8:00 AM - 10:00 PM',
      sunday: '8:00 AM - 9:00 PM'
    },
    nrt_brands_carried: ['Nicorette', 'NicoDerm CQ', 'up&up'],
    pharmacy_available: true,
    prescription_required: false,
    drive_through: false,
    parking_available: true,
    wheelchair_accessible: true,
    rating: 4.4,
    review_count: 203,
    verified: true
  },
  {
    id: 'store-005',
    name: 'Duane Reade',
    brand: 'Duane Reade',
    chain: 'Walgreens Boots Alliance',
    address: '224 West 57th Street',
    city: 'New York',
    state: 'NY',
    zip_code: '10019',
    country: 'USA',
    latitude: 40.7648,
    longitude: -73.9808,
    phone: '(*************',
    website: 'https://www.duanereade.com',
    store_hours: {
      monday: '7:00 AM - 11:00 PM',
      tuesday: '7:00 AM - 11:00 PM',
      wednesday: '7:00 AM - 11:00 PM',
      thursday: '7:00 AM - 11:00 PM',
      friday: '7:00 AM - 11:00 PM',
      saturday: '7:00 AM - 11:00 PM',
      sunday: '8:00 AM - 10:00 PM'
    },
    nrt_brands_carried: ['Nicorette', 'NicoDerm CQ', 'Commit'],
    pharmacy_available: true,
    prescription_required: false,
    drive_through: false,
    parking_available: true,
    wheelchair_accessible: true,
    rating: 4.2,
    review_count: 78,
    verified: true
  },
  {
    id: 'store-006',
    name: 'Walmart Pharmacy',
    brand: 'Walmart',
    chain: 'Walmart Inc.',
    address: '4761 Harlem Road',
    city: 'Buffalo',
    state: 'NY',
    zip_code: '14226',
    country: 'USA',
    latitude: 42.9784,
    longitude: -78.8118,
    phone: '(*************',
    website: 'https://www.walmart.com',
    store_hours: {
      monday: '6:00 AM - 11:00 PM',
      tuesday: '6:00 AM - 11:00 PM',
      wednesday: '6:00 AM - 11:00 PM',
      thursday: '6:00 AM - 11:00 PM',
      friday: '6:00 AM - 11:00 PM',
      saturday: '6:00 AM - 11:00 PM',
      sunday: '6:00 AM - 11:00 PM'
    },
    nrt_brands_carried: ['Nicorette', 'NicoDerm CQ', 'Equate'],
    pharmacy_available: true,
    prescription_required: false,
    drive_through: true,
    parking_available: true,
    wheelchair_accessible: true,
    rating: 4.0,
    review_count: 234,
    verified: true
  },
  {
    id: 'store-007',
    name: 'Kroger Pharmacy',
    brand: 'Kroger',
    chain: 'The Kroger Co.',
    address: '3090 Presidential Drive',
    city: 'Atlanta',
    state: 'GA',
    zip_code: '30340',
    country: 'USA',
    latitude: 33.8634,
    longitude: -84.2807,
    phone: '(*************',
    website: 'https://www.kroger.com',
    store_hours: {
      monday: '6:00 AM - 12:00 AM',
      tuesday: '6:00 AM - 12:00 AM',
      wednesday: '6:00 AM - 12:00 AM',
      thursday: '6:00 AM - 12:00 AM',
      friday: '6:00 AM - 12:00 AM',
      saturday: '6:00 AM - 12:00 AM',
      sunday: '6:00 AM - 12:00 AM'
    },
    nrt_brands_carried: ['Nicorette', 'NicoDerm CQ', 'Kroger Brand'],
    pharmacy_available: true,
    prescription_required: false,
    drive_through: true,
    parking_available: true,
    wheelchair_accessible: true,
    rating: 4.2,
    review_count: 167,
    verified: true
  },
  {
    id: 'store-008',
    name: 'Safeway Pharmacy',
    brand: 'Safeway',
    chain: 'Albertsons Companies',
    address: '2020 Market Street',
    city: 'San Francisco',
    state: 'CA',
    zip_code: '94114',
    country: 'USA',
    latitude: 37.7665,
    longitude: -122.4278,
    phone: '(*************',
    website: 'https://www.safeway.com',
    store_hours: {
      monday: '6:00 AM - 12:00 AM',
      tuesday: '6:00 AM - 12:00 AM',
      wednesday: '6:00 AM - 12:00 AM',
      thursday: '6:00 AM - 12:00 AM',
      friday: '6:00 AM - 12:00 AM',
      saturday: '6:00 AM - 12:00 AM',
      sunday: '6:00 AM - 12:00 AM'
    },
    nrt_brands_carried: ['Nicorette', 'NicoDerm CQ', 'Signature Care'],
    pharmacy_available: true,
    prescription_required: false,
    drive_through: true,
    parking_available: true,
    wheelchair_accessible: true,
    rating: 4.1,
    review_count: 145,
    verified: true
  },
  {
    id: 'store-009',
    name: 'Publix Pharmacy',
    brand: 'Publix',
    chain: 'Publix Super Markets',
    address: '1500 Biscayne Boulevard',
    city: 'Miami',
    state: 'FL',
    zip_code: '33132',
    country: 'USA',
    latitude: 25.7917,
    longitude: -80.1918,
    phone: '(*************',
    website: 'https://www.publix.com',
    store_hours: {
      monday: '7:00 AM - 10:00 PM',
      tuesday: '7:00 AM - 10:00 PM',
      wednesday: '7:00 AM - 10:00 PM',
      thursday: '7:00 AM - 10:00 PM',
      friday: '7:00 AM - 10:00 PM',
      saturday: '7:00 AM - 10:00 PM',
      sunday: '7:00 AM - 9:00 PM'
    },
    nrt_brands_carried: ['Nicorette', 'NicoDerm CQ', 'Publix Brand'],
    pharmacy_available: true,
    prescription_required: false,
    drive_through: true,
    parking_available: true,
    wheelchair_accessible: true,
    rating: 4.6,
    review_count: 189,
    verified: true
  },
  {
    id: 'store-010',
    name: 'HEB Pharmacy',
    brand: 'H-E-B',
    chain: 'H-E-B LP',
    address: '1000 East Parmer Lane',
    city: 'Austin',
    state: 'TX',
    zip_code: '78753',
    country: 'USA',
    latitude: 30.4518,
    longitude: -97.6793,
    phone: '(*************',
    website: 'https://www.heb.com',
    store_hours: {
      monday: '6:00 AM - 11:00 PM',
      tuesday: '6:00 AM - 11:00 PM',
      wednesday: '6:00 AM - 11:00 PM',
      thursday: '6:00 AM - 11:00 PM',
      friday: '6:00 AM - 11:00 PM',
      saturday: '6:00 AM - 11:00 PM',
      sunday: '6:00 AM - 11:00 PM'
    },
    nrt_brands_carried: ['Nicorette', 'NicoDerm CQ', 'H-E-B Brand'],
    pharmacy_available: true,
    prescription_required: false,
    drive_through: true,
    parking_available: true,
    wheelchair_accessible: true,
    rating: 4.7,
    review_count: 298,
    verified: true
  }
];

// Main seed function
async function seedStores() {
  try {
    console.log('🚨 seedStores: Starting direct stores table population...');
    
    // Insert stores using upsert to avoid duplicates
    const { data, error } = await supabase
      .from('stores')
      .upsert(sampleStores, { onConflict: 'id' })
      .select();

    if (error) {
      console.error('🚨 seedStores: Error inserting stores:', error);
      throw error;
    }

    console.log('🚨 seed-stores-direct.cjs: Successfully added', data?.length || 0, 'stores to database');
    console.log('🚨 seedStores: Store IDs:', data?.map(store => store.id).join(', '));
    
    // Verify the data was inserted
    const { data: verifyData, error: verifyError } = await supabase
      .from('stores')
      .select('id, name, city, state')
      .order('name');

    if (verifyError) {
      console.error('🚨 seedStores: Error verifying stores:', verifyError);
    } else {
      console.log('🚨 seedStores: Verification - Total stores in database:', verifyData?.length || 0);
      console.log('🚨 seedStores: All stores:', verifyData?.map(s => `${s.name} (${s.city}, ${s.state})`).join(', '));
    }

    return data;
  } catch (error) {
    console.error('🚨 seedStores: Fatal error:', error);
    process.exit(1);
  }
}

// Run the seed function
console.log('🚨 Starting stores table population...');
seedStores()
  .then(() => {
    console.log('🚨 Stores table population completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('🚨 Fatal error during stores seeding:', error);
    process.exit(1);
  });
