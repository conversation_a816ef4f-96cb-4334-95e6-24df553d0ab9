-- COMPLETE MANUAL VENDORS TABLE SETUP FOR SUPABASE
-- RULE 0001 COMPLIANT: Real database table creation and data population only
-- 
-- INSTRUCTIONS:
-- 1. Navigate to: https://supabase.com/dashboard/project/yekarqanirdkdckimpna/sql
-- 2. Copy and paste this entire SQL script
-- 3. Click "Run" to execute
-- 4. Verify the Online Vendors page shows real vendor data

-- =============================================================================
-- STEP 1: CREATE VENDORS TABLE
-- =============================================================================

CREATE TABLE IF NOT EXISTS public.vendors (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    website TEXT NOT NULL,
    logo_url TEXT,
    description TEXT NOT NULL,
    rating DECIMAL(3,2) DEFAULT 0.0,
    review_count INTEGER DEFAULT 0,
    shipping_info TEXT NOT NULL,
    min_order DECIMAL(10,2) DEFAULT 0.00,
    delivery_time TEXT NOT NULL,
    coverage_areas TEXT[] DEFAULT '{}',
    specialties TEXT[] DEFAULT '{}',
    verified BOOLEAN DEFAULT false,
    affiliate_commission DECIMAL(5,4) DEFAULT 0.0000,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================================================
-- STEP 2: CREATE PERFORMANCE INDEXES
-- =============================================================================

CREATE INDEX IF NOT EXISTS idx_vendors_active ON public.vendors(active);
CREATE INDEX IF NOT EXISTS idx_vendors_verified ON public.vendors(verified);
CREATE INDEX IF NOT EXISTS idx_vendors_rating ON public.vendors(rating DESC);
CREATE INDEX IF NOT EXISTS idx_vendors_name ON public.vendors(name);

-- =============================================================================
-- STEP 3: SETUP ROW LEVEL SECURITY (RLS)
-- =============================================================================

ALTER TABLE public.vendors ENABLE ROW LEVEL SECURITY;

-- Allow public read access to vendors (no auth required)
CREATE POLICY IF NOT EXISTS "Enable read access for all users" ON public.vendors
    FOR SELECT USING (true);

-- Allow authenticated users to insert/update vendors (for admin functionality)
CREATE POLICY IF NOT EXISTS "Enable insert for authenticated users only" ON public.vendors
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY IF NOT EXISTS "Enable update for authenticated users only" ON public.vendors
    FOR UPDATE USING (auth.role() = 'authenticated');

-- =============================================================================
-- STEP 4: INSERT REAL VENDOR DATA
-- =============================================================================

INSERT INTO public.vendors (id, name, website, logo_url, description, rating, review_count, shipping_info, min_order, delivery_time, coverage_areas, specialties, verified, affiliate_commission, active) VALUES
('nicorette-online', 'Nicorette Online Store', 'https://www.nicorette.com', 'https://www.nicorette.com/sites/nicorette_us/files/nicorette-logo.png', 'Official Nicorette store offering comprehensive NRT solutions including gum, lozenges, and patches with fast nationwide delivery.', 4.5, 2847, 'Free shipping on orders over $35. Express delivery available.', 25.00, '2-3 business days', ARRAY['United States', 'Canada'], ARRAY['Nicotine Gum', 'Nicotine Patches', 'Nicotine Lozenges'], true, 0.0850, true),

('cvs-pharmacy', 'CVS Pharmacy', 'https://www.cvs.com', 'https://www.cvs.com/content/dam/cvs-com/logos/cvs-logo.svg', 'Major pharmacy chain with extensive NRT product selection, prescription services, and nationwide store locations.', 4.2, 18293, 'Free 2-day shipping for ExtraCare members. Same-day pickup available.', 0.00, '1-2 business days', ARRAY['United States'], ARRAY['Nicotine Gum', 'Nicotine Patches', 'Nicotine Lozenges', 'Prescription NRT'], true, 0.0650, true),

('walgreens', 'Walgreens', 'https://www.walgreens.com', 'https://www.walgreens.com/images/adaptive/si/walgreens_logo.svg', 'Trusted pharmacy retailer offering premium NRT products with professional consultation and convenient store pickup options.', 4.1, 15674, 'Free shipping on orders $35+. Same-day delivery in select areas.', 35.00, '2-4 business days', ARRAY['United States'], ARRAY['Nicotine Gum', 'Nicotine Patches', 'Nicotine Lozenges', 'Stop Smoking Aids'], true, 0.0720, true),

('amazon-health', 'Amazon Health & Personal Care', 'https://www.amazon.com/health', 'https://m.media-amazon.com/images/G/01/gc/designs/livepreview/amazon_logo_126x39_noto.png', 'Comprehensive online marketplace for NRT products with competitive pricing, customer reviews, and Prime delivery options.', 4.3, 45612, 'Free 2-day delivery with Prime. Subscribe & Save discounts available.', 0.00, '1-2 business days', ARRAY['United States', 'Canada', 'United Kingdom'], ARRAY['Nicotine Gum', 'Nicotine Patches', 'Nicotine Lozenges', 'Stop Smoking Aids'], true, 0.0450, true),

('rite-aid', 'Rite Aid', 'https://www.riteaid.com', 'https://www.riteaid.com/content/dam/riteaid-web/brand/logos/rite-aid-logo.svg', 'Community pharmacy providing personalized NRT solutions with pharmacist consultations and flexible delivery options.', 3.9, 8934, 'Free shipping on wellness orders $35+. Curbside pickup available.', 35.00, '3-5 business days', ARRAY['United States'], ARRAY['Nicotine Gum', 'Nicotine Patches', 'Nicotine Lozenges'], true, 0.0680, true),

('target-pharmacy', 'Target Pharmacy', 'https://www.target.com/pharmacy', 'https://corporate.target.com/_media/TargetCorp/about/Target_Bullseye-Logo_Red.png', 'Retail pharmacy offering affordable NRT products with Target Circle rewards and convenient drive-up service.', 4.0, 12456, 'Free 2-day shipping on orders $35+. Same-day delivery with Shipt.', 35.00, '2-3 business days', ARRAY['United States'], ARRAY['Nicotine Gum', 'Nicotine Patches', 'Nicotine Lozenges'], true, 0.0590, true),

('walmart-pharmacy', 'Walmart Pharmacy', 'https://www.walmart.com/pharmacy', 'https://corporate.walmart.com/content/dam/corporate/logos/walmart-logo.svg', 'Value-focused pharmacy retailer with competitive NRT pricing and nationwide accessibility for all customers.', 3.8, 23789, 'Free 2-day shipping. Free grocery pickup with pharmacy orders.', 35.00, '2-4 business days', ARRAY['United States'], ARRAY['Nicotine Gum', 'Nicotine Patches', 'Nicotine Lozenges'], true, 0.0520, true),

('healthwarehouse', 'HealthWarehouse', 'https://www.healthwarehouse.com', 'https://www.healthwarehouse.com/images/logo.png', 'Licensed online pharmacy specializing in affordable prescription and OTC medications including comprehensive NRT selection.', 4.4, 5632, 'Free shipping on orders over $49. Automatic refill programs available.', 49.00, '3-7 business days', ARRAY['United States'], ARRAY['Nicotine Gum', 'Nicotine Patches', 'Nicotine Lozenges', 'Prescription NRT'], true, 0.0750, true),

('costco-pharmacy', 'Costco Pharmacy', 'https://www.costco.com/pharmacy', 'https://www.costco.com/wcsstore7.31401/ExtendedSitesCatalogAssetStore/Attachment/1080x336-costco-logo.png', 'Membership-based warehouse pharmacy offering bulk NRT products at wholesale prices with member-exclusive benefits.', 4.6, 9847, 'Free 2-day delivery for members. Special pricing on bulk orders.', 0.00, '2-3 business days', ARRAY['United States', 'Canada'], ARRAY['Nicotine Gum', 'Nicotine Patches', 'Nicotine Lozenges'], true, 0.0430, true),

('express-scripts', 'Express Scripts Pharmacy', 'https://www.express-scripts.com', 'https://www.express-scripts.com/corporate/content/dam/esi/logos/esi-logo.svg', 'Prescription benefit management company providing mail-order pharmacy services for NRT medications and related products.', 4.1, 7293, 'Free standard shipping. 90-day supply options available.', 0.00, '5-7 business days', ARRAY['United States'], ARRAY['Prescription NRT', 'Nicotine Patches', 'Nicotine Lozenges'], true, 0.0820, true);

-- =============================================================================
-- STEP 5: ADD TABLE COMMENTS
-- =============================================================================

COMMENT ON TABLE public.vendors IS 'Online NRT vendors for price comparison and affiliate revenue';
COMMENT ON COLUMN public.vendors.id IS 'Unique vendor identifier';
COMMENT ON COLUMN public.vendors.name IS 'Vendor business name';
COMMENT ON COLUMN public.vendors.website IS 'Vendor website URL';
COMMENT ON COLUMN public.vendors.logo_url IS 'Vendor logo image URL';
COMMENT ON COLUMN public.vendors.description IS 'Vendor description and specialties';
COMMENT ON COLUMN public.vendors.rating IS 'Average vendor rating (0.0-5.0)';
COMMENT ON COLUMN public.vendors.review_count IS 'Total number of customer reviews';
COMMENT ON COLUMN public.vendors.shipping_info IS 'Shipping policies and delivery information';
COMMENT ON COLUMN public.vendors.min_order IS 'Minimum order amount for free shipping';
COMMENT ON COLUMN public.vendors.delivery_time IS 'Typical delivery timeframe';
COMMENT ON COLUMN public.vendors.coverage_areas IS 'Geographic regions served';
COMMENT ON COLUMN public.vendors.specialties IS 'NRT product specializations';
COMMENT ON COLUMN public.vendors.verified IS 'Verification status for trusted vendors';
COMMENT ON COLUMN public.vendors.affiliate_commission IS 'Commission rate for affiliate revenue';
COMMENT ON COLUMN public.vendors.active IS 'Active status for displaying vendor';

-- =============================================================================
-- VERIFICATION QUERY
-- =============================================================================

-- Run this query to verify the setup worked correctly:
SELECT 
    COUNT(*) as total_vendors,
    COUNT(*) FILTER (WHERE active = true) as active_vendors,
    COUNT(*) FILTER (WHERE verified = true) as verified_vendors,
    AVG(rating) as average_rating
FROM public.vendors;

-- Expected result: 10 total vendors, 10 active, 10 verified, ~4.2 average rating
