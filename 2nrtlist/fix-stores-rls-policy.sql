-- =============================================================================
-- RLS POLICY FIX FOR STORES TABLE
-- Fix permission denied error by setting up proper RLS policies
-- =============================================================================

-- Enable RLS on the stores table
ALTER TABLE mission_fresh.stores ENABLE ROW LEVEL SECURITY;

-- Create policy to allow public read access (for Store Locator page)
CREATE POLICY IF NOT EXISTS "Enable read access for all users" 
ON mission_fresh.stores FOR SELECT 
USING (true);

-- Create policy to allow public insert access (for seed scripts and data population)
CREATE POLICY IF NOT EXISTS "Enable insert access for all users" 
ON mission_fresh.stores FOR INSERT 
WITH CHECK (true);

-- Create policy to allow public update access (for data maintenance)
CREATE POLICY IF NOT EXISTS "Enable update access for all users" 
ON mission_fresh.stores FOR UPDATE 
USING (true) 
WITH CHECK (true);

-- Alternatively, if the above doesn't work, disable RLS entirely for public access
-- (This is simpler and allows all operations on the stores table)
-- Uncomment the line below if the policies above don't resolve the permission issue:
-- ALTER TABLE mission_fresh.stores DISABLE ROW LEVEL SECURITY;

-- Verify RLS policies are set correctly
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'stores' AND schemaname = 'mission_fresh';

-- Test query to verify access
SELECT COUNT(*) as stores_count FROM mission_fresh.stores;
