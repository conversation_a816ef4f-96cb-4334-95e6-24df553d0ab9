#!/bin/bash

echo "🚨 EMERGENCY: Restoring Cursor Functionality"
echo "============================================"

# Kill current broken Cursor instance
echo "1. 🔄 Restarting Cursor..."
pkill -f "Cursor" 2>/dev/null || true
sleep 3

# Create minimal essential directories and files
echo "2. 🏗️  Creating essential app structure..."
mkdir -p ~/Library/Application\ Support/Cursor/User
mkdir -p ~/Library/Application\ Support/Cursor/logs
mkdir -p ~/Library/Application\ Support/Cursor/CachedData

# Create minimal settings to restore UI functionality
echo "3. ⚙️  Restoring minimal settings..."
cat > ~/Library/Application\ Support/Cursor/User/settings.json << 'EOF'
{
    "workbench.startupEditor": "welcomePage",
    "window.titleBarStyle": "native",
    "editor.fontSize": 14,
    "workbench.colorTheme": "Default Dark+"
}
EOF

# Create minimal keybindings
cat > ~/Library/Application\ Support/Cursor/User/keybindings.json << 'EOF'
[]
EOF

# Restore essential storage structure but keep auth cleared
echo "4. 📁 Restoring storage structure..."
mkdir -p ~/Library/Application\ Support/Cursor/User/globalStorage
mkdir -p ~/Library/Application\ Support/Cursor/User/workspaceStorage

# Create minimal global storage (but keep auth cleared for login fix)
cat > ~/Library/Application\ Support/Cursor/User/globalStorage/storage.json << 'EOF'
{}
EOF

# Fix permissions
echo "5. 🔐 Fixing permissions..."
chmod -R 755 ~/Library/Application\ Support/Cursor/
chmod 644 ~/Library/Application\ Support/Cursor/User/settings.json
chmod 644 ~/Library/Application\ Support/Cursor/User/keybindings.json

echo "6. 🚀 Starting Cursor..."
# Try the main Cursor app first
if [ -d "/Applications/Cursor.app" ]; then
    open -a "/Applications/Cursor.app"
    echo "   ✅ Opened /Applications/Cursor.app"
elif [ -d "/Users/<USER>/Applications/Cursor 0.47 Plus.app" ]; then
    open -a "/Users/<USER>/Applications/Cursor 0.47 Plus.app"
    echo "   ✅ Opened Cursor 0.47 Plus"
else
    echo "   ⚠️  Please manually open your preferred Cursor app"
fi

sleep 5

echo ""
echo "🎉 CURSOR FUNCTIONALITY RESTORED!"
echo "================================="
echo ""
echo "✅ What was fixed:"
echo "   • Restored essential app directories"
echo "   • Created minimal settings for UI functionality"
echo "   • Fixed file permissions"
echo "   • Restarted Cursor with clean state"
echo "   • Kept authentication cleared for login fix"
echo ""
echo "🔥 The login button should work now!"
echo "   • Try clicking 'Sign In' again"
echo "   • If login still fails, use: ~/cursor_emergency_login.sh"
echo ""
echo "💡 What we learned:"
echo "   • Never delete ALL app data - some is essential for basic functionality"
echo "   • Only clear authentication-specific data for login issues"
echo "   • Always preserve UI settings and core app structure"
