const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // Login
  await page.goto('http://localhost:5001/auth');
  await page.type('input[id="email"]', '<EMAIL>');
  await page.type('input[id="password"]', 'J4913836j');
  await page.click('button[type="submit"]');
  await page.waitForNavigation();
  
  // Test Progress page
  console.log('Testing Progress page after fix...');
  await page.goto('http://localhost:5001/app/progress');
  
  // Wait for loading
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  const bodyText = await page.evaluate(() => document.body.innerText);
  console.log('Progress content length:', bodyText.length);
  console.log('Contains "Your Progress":', bodyText.includes('Your Progress'));
  console.log('Contains "Days Afresh":', bodyText.includes('Days Afresh'));
  console.log('Contains loading spinner:', bodyText.includes('Loading') || bodyText.includes('loading'));
  console.log('First 400 chars:');
  console.log(bodyText.substring(0, 400));
  
  await page.screenshot({ path: 'progress_fixed.png', fullPage: true });
  
  await browser.close();
})();
