# Potential Future Features (pf.md)

This list contains potential features or enhancements that are not immediately critical for core functionality but could be considered for future development to make the app even more comprehensive and user-friendly.

1.  [COMPLETED] **Enhanced Health Timeline Visualization:** Explore more visual ways to represent the health milestones, perhaps using a graphical timeline instead of a simple list. (Sharing feature added, including progress and achievements)
2.  [COMPLETED] **Achievement Notifications:** Implement a system to notify users when they unlock a new achievement to increase engagement. (Basic push notification implemented in `gamificationService.ts`)
3.  [COMPLETED] **More Diverse Achievements:** Add achievements based on tool usage, cravings overcome, step goals, community engagement, etc.
4.  [COMPLETED] **Advanced Data Filtering and Reporting:** Allow users to filter their progress data by specific criteria (e.g., product type, trigger) and generate detailed reports. (Filtering implemented)
5.  [COMPLETED] **Integration with Wearable Devices:** Integrate with smartwatches or fitness trackers for automatic step tracking and potentially other health metrics.
6.  [COMPLETED] **Gamification Enhancements:** Explored more sophisticated gamification elements beyond achievements, such as points, badges, leaderboards (optional, considering user privacy), or virtual rewards. (Enhanced points display and virtual rewards section in Rewards page)
7.  [COMPLETED] **Customizable Tapering Schedules:** Allow users more flexibility in customizing their tapering schedules beyond simple date and quantity steps.
8.  [COMPLETED] **Family/Friend Support System:** Allow users to connect with trusted family or friends for support and accountability (with user consent).
9.  [COMPLETED] **Integration with Local Support Resources:** Provide information and links to local quit support groups, helplines, or clinics based on the user's location (with user permission).
10. [BLOCKED] **Multi-language Support:** Implement support for multiple languages to reach a wider audience. (Blocked due to dependency conflict with capacitor-health)
11. [SKIPPED] **Accessibility Enhancements:** Conduct a thorough accessibility audit and implement features to make the app more usable for individuals with disabilities (e.g., screen reader support, keyboard navigation, high-contrast mode). (Skipped due to unresolved dependency conflict preventing frontend development)
12. [COMPLETED] **Enhanced Community Features:** Add features like direct messaging, group chats, or the ability to share progress updates (with privacy controls). (Sharing progress updates implemented)
13. [SKIPPED] **Expert Q&A or Live Sessions:** Host live Q&A sessions or webinars with quit smoking experts. (Cannot be implemented by code assistant)
14. [COMPLETED] **Interactive Learning Modules:** Develop interactive educational content beyond static articles or guides. (Interactive learning page created)
15. [COMPLETED] **Integration with NRT/Medication Reminders:** Allow users to set reminders for taking NRT or other prescribed quit medications. (Backend scheduling for push notifications implemented)
16. [COMPLETED] **Detailed Financial Savings Tracking:** Provide more detailed breakdowns of money saved based on specific product costs and usage patterns. (Display component added)
17. [COMPLETED] **Environmental Impact Tracking:** Show users the positive environmental impact of quitting (e.g., reduced cigarette butt waste).
18. [COMPLETED] **Customizable Dashboard:** Allow users to customize the widgets and information displayed on their dashboard.
19. [COMPLETED] **Log Multiple Nicotine Products:** Allow users to log multiple types of nicotine products and their quantities within a single daily log entry.
20. [COMPLETED] **Log Multiple Cravings:** Allow users to log multiple craving events with specific intensity and triggers throughout the day.
21. [COMPLETED] **Dedicated Journaling Feature:** Implement a separate journaling feature that allows users to create multiple journal entries per day, independent of the daily log.
22. [COMPLETED] Fatigue Tools Page: Create a dedicated page with tools and tips specifically for managing fatigue during nicotine withdrawal.
23. [COMPLETED] **Database Schema Migration:** Add a JSONB column `nicotine_entries_data` to the `nicotine_logs` table to store an array of individual nicotine use entries per day. After migration, regenerate Supabase TypeScript types (`database.types.ts`).
