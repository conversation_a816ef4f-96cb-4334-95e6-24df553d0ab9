/** @type {import('tailwindcss').Config} */
import typography from '@tailwindcss/typography';
import animate from 'tailwindcss-animate';

export default {
  darkMode: ["class"],
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
	],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      fontFamily: {
        heading: ['"GeneralSans-SemiBold"', 'sans-serif'],
        sans: ['"GeneralSans-Regular"', 'sans-serif'],
      },

      letterSpacing: {
        'premium': '-0.035em',
        'elegant': '-0.025em',
        'refined': '-0.015em',
      },
      lineHeight: {
        'premium': '1.15',
        'elegant': '1.65',
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
          hover: "hsl(var(--primary-hover))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      boxShadow: {
        'sm': '0 1px 2px 0 hsl(var(--shadow-color) / 0.03)',
        'DEFAULT': '0 1px 3px 0 hsl(var(--shadow-color) / 0.05), 0 1px 2px -1px hsl(var(--shadow-color) / 0.03)',
        'md': '0 4px 6px -1px hsl(var(--shadow-color) / 0.05), 0 2px 4px -2px hsl(var(--shadow-color) / 0.03)',
        'lg': '0 10px 15px -3px hsl(var(--shadow-color) / 0.05), 0 4px 6px -4px hsl(var(--shadow-color) / 0.04)',
        'xl': '0 20px 25px -5px hsl(var(--shadow-color) / 0.05), 0 8px 10px -6px hsl(var(--shadow-color) / 0.04)',
        '2xl': '0 25px 50px -12px hsl(var(--shadow-color) / 0.1)',
        'inner': 'inset 0 2px 4px 0 hsl(var(--shadow-color) / 0.02)',
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "fade-in": {
          from: { opacity: "0" },
          to: { opacity: "1" },
        },
        "fade-out": {
          from: { opacity: "1" },
          to: { opacity: "0" },
        },
        "slide-in-from-top": {
          from: { transform: "translateY(-10px)", opacity: "0" },
          to: { transform: "translateY(0)", opacity: "1" },
        },
        "slide-out-to-top": {
          from: { transform: "translateY(0)", opacity: "1" },
          to: { transform: "translateY(-10px)", opacity: "0" },
        },
        "subtle-glow": {
          '0%, 100%': { boxShadow: '0 0 5px hsl(var(--primary) / 0.2)' },
          '50%': { boxShadow: '0 0 20px hsl(var(--primary) / 0.4)' },
        },
        "slide-up-and-fade": {
          '0%': { opacity: 0, transform: 'translateY(2px)' },
          '100%': { opacity: 1, transform: 'translateY(0)' },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "fade-in": "fade-in 0.3s ease-in-out",
        "fade-out": "fade-out 0.3s ease-in-out",
        "slide-in-from-top": "slide-in-from-top 0.3s ease-out",
        "slide-out-to-top": "slide-out-to-top 0.3s ease-out",
        "subtle-glow": "subtle-glow 4s ease-in-out infinite",
        "slide-up-and-fade": "slide-up-and-fade 0.4s cubic-bezier(0.16, 1, 0.3, 1)",
      },
    },
  },
  plugins: [typography, animate],
}
