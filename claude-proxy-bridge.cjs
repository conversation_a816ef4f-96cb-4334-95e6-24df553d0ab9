const http = require('http');
const https = require('https');
const net = require('net');
const url = require('url');

// Configuration
const BRIDGE_PORT = 8888;
const GEPH_PROXY_HOST = '127.0.0.1';
const GEPH_PROXY_PORT = 9909;

console.log(`🌉 Starting Claude Code Proxy Bridge on port ${BRIDGE_PORT}`);
console.log(`🔗 Forwarding to Geph VPN proxy at ${GEPH_PROXY_HOST}:${GEPH_PROXY_PORT}`);

// Create HTTP proxy server
const server = http.createServer();

server.on('request', (req, res) => {
  console.log(`📤 HTTP Request: ${req.method} ${req.url}`);
  
  const target = url.parse(req.url);
  const options = {
    hostname: target.hostname,
    port: target.port || (target.protocol === 'https:' ? 443 : 80),
    path: target.path,
    method: req.method,
    headers: req.headers,
    // Use Geph proxy for outgoing requests
    agent: new http.Agent({
      proxy: `http://${GEPH_PROXY_HOST}:${GEPH_PROXY_PORT}`
    })
  };

  const proxyReq = (target.protocol === 'https:' ? https : http).request(options, (proxyRes) => {
    res.writeHead(proxyRes.statusCode, proxyRes.headers);
    proxyRes.pipe(res);
  });

  proxyReq.on('error', (err) => {
    console.error(`❌ Proxy request error: ${err.message}`);
    res.writeHead(500);
    res.end('Proxy Error');
  });

  req.pipe(proxyReq);
});

// Handle HTTPS CONNECT requests
server.on('connect', (req, clientSocket, head) => {
  console.log(`🔐 HTTPS CONNECT: ${req.url}`);
  
  const [hostname, port] = req.url.split(':');
  
  // Connect to Geph proxy first
  const proxySocket = net.connect(GEPH_PROXY_PORT, GEPH_PROXY_HOST, () => {
    // Send CONNECT request to Geph proxy
    proxySocket.write(`CONNECT ${req.url} HTTP/1.1\r\n\r\n`);
  });

  proxySocket.on('data', (data) => {
    const response = data.toString();
    if (response.includes('200')) {
      // Connection established with target through Geph
      clientSocket.write('HTTP/1.1 200 Connection Established\r\n\r\n');
      // Pipe data between client and Geph proxy
      clientSocket.pipe(proxySocket);
      proxySocket.pipe(clientSocket);
    } else {
      console.error(`❌ Geph proxy CONNECT failed: ${response}`);
      clientSocket.write('HTTP/1.1 500 Connection Failed\r\n\r\n');
      clientSocket.end();
    }
  });

  proxySocket.on('error', (err) => {
    console.error(`❌ Geph proxy connection error: ${err.message}`);
    clientSocket.write('HTTP/1.1 500 Connection Failed\r\n\r\n');
    clientSocket.end();
  });

  clientSocket.on('error', (err) => {
    console.error(`❌ Client socket error: ${err.message}`);
    proxySocket.end();
  });
});

server.listen(BRIDGE_PORT, '127.0.0.1', () => {
  console.log(`✅ Claude Code Proxy Bridge listening on 127.0.0.1:${BRIDGE_PORT}`);
  console.log(`\n🚀 Usage:`);
  console.log(`export HTTPS_PROXY=http://127.0.0.1:${BRIDGE_PORT}`);
  console.log(`export HTTP_PROXY=http://127.0.0.1:${BRIDGE_PORT}`);
  console.log(`claude`);
});

server.on('error', (err) => {
  console.error(`❌ Server error: ${err.message}`);
  process.exit(1);
}); 