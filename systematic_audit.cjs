const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // Capture console errors
  const errors = [];
  page.on('console', msg => {
    if (msg.type() === 'error') {
      errors.push({ page: 'current', text: msg.text() });
    }
  });
  page.on('pageerror', err => {
    errors.push({ page: 'current', text: err.toString() });
  });
  
  await page.setViewport({ width: 1200, height: 800 });
  
  // 1. Check Landing Page
  console.log('\n=== LANDING PAGE ===');
  await page.goto('http://localhost:5001/', { waitUntil: 'networkidle2' });
  await page.screenshot({ path: 'audit_1_landing.png', fullPage: true });
  console.log('✓ Screenshot saved');
  
  // 2. Navigate to Auth
  console.log('\n=== AUTH PAGE ===');
  await page.goto('http://localhost:5001/auth', { waitUntil: 'networkidle2' });
  await page.screenshot({ path: 'audit_2_auth.png', fullPage: true });
  
  // Login
  await page.type('input[id="email"]', '<EMAIL>');
  await page.type('input[id="password"]', 'J4913836j');
  await page.click('button[type="submit"]');
  
  // Wait for navigation
  await page.waitForNavigation({ waitUntil: 'networkidle2' });
  console.log('✓ Logged in successfully');
  
  // 3. Dashboard
  console.log('\n=== DASHBOARD ===');
  const dashboardUrl = page.url();
  console.log('Current URL:', dashboardUrl);
  await page.screenshot({ path: 'audit_3_dashboard.png', fullPage: true });
  
  // Report errors
  if (errors.length > 0) {
    console.log('\n=== ERRORS FOUND ===');
    errors.forEach(err => console.log(`[${err.page}] ${err.text}`));
  } else {
    console.log('\n✓ No console errors found');
  }
  
  await browser.close();
})();