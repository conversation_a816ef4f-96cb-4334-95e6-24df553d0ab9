const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: {width: 1920, height: 1080}
  });
  
  const page = await browser.newPage();
  await page.setViewport({width: 1920, height: 1080});
  
  console.log('🔐 AUTH PAGE COMPREHENSIVE AUDIT');
  
  // Navigate to auth page
  await page.goto('http://localhost:5001/auth');
  await page.waitForSelector('body', {timeout: 10000});
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Check page title
  const title = await page.title();
  console.log('📄 Auth Page Title:', title);
  
  // Check for forms
  const forms = await page.$$('form');
  console.log('📝 Forms found:', forms.length);
  
  // Check for inputs
  const inputs = await page.$$('input');
  console.log('⌨️ Input fields found:', inputs.length);
  
  // Check for buttons
  const buttons = await page.$$('button');
  console.log('🔘 Buttons found:', buttons.length);
  
  // Test each input field
  for (let i = 0; i < inputs.length; i++) {
    const inputType = await inputs[i].evaluate(el => el.type);
    const inputPlaceholder = await inputs[i].evaluate(el => el.placeholder);
    const inputRequired = await inputs[i].evaluate(el => el.required);
    console.log(`Input ${i+1}: type="${inputType}", placeholder="${inputPlaceholder}", required=${inputRequired}`);
  }
  
  // Test each button
  for (let i = 0; i < buttons.length; i++) {
    const buttonText = await buttons[i].evaluate(el => el.textContent?.trim());
    const buttonType = await buttons[i].evaluate(el => el.type);
    console.log(`Button ${i+1}: "${buttonText}", type="${buttonType}"`);
  }
  
  // Check for tabs (Login/Signup)
  const tabs = await page.$$('[role="tab"], .tab, [data-tab]');
  console.log('📑 Tabs found:', tabs.length);
  
  // Check for validation messages
  const errorMessages = await page.$$('.error, [class*="error"], .text-red, .text-destructive');
  console.log('❌ Error elements:', errorMessages.length);
  
  // Take screenshot
  await page.screenshot({path: 'auth_page_audit.png', fullPage: true});
  console.log('📸 Auth page screenshot saved');
  
  await browser.close();
  console.log('✅ Auth page audit completed');
})();
