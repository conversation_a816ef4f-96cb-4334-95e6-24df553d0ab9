const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // Login first
  await page.goto('http://localhost:5001/auth');
  await page.type('input[id="email"]', '<EMAIL>');
  await page.type('input[id="password"]', 'J4913836j');
  await page.click('button[type="submit"]');
  await page.waitForNavigation();
  
  // Check dashboard content
  console.log('\nChecking Dashboard...');
  await page.goto('http://localhost:5001/app/dashboard');
  const dashboardText = await page.evaluate(() => document.body.innerText);
  console.log('Dashboard text length:', dashboardText.length);
  console.log('First 500 chars:', dashboardText.substring(0, 500));
  
  // Check if there are any error messages
  const errorElements = await page.$$('.error, .alert, [class*="error"]');
  if (errorElements.length > 0) {
    console.log('\nFound error elements on page!');
  }
  
  await browser.close();
})();
