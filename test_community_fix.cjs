const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // Login
  await page.goto('http://localhost:5001/auth');
  await page.type('input[id="email"]', '<EMAIL>');
  await page.type('input[id="password"]', 'J4913836j');
  await page.click('button[type="submit"]');
  await page.waitForNavigation();
  
  // Test Community page
  console.log('Testing Community page after fix...');
  await page.goto('http://localhost:5001/app/community');
  
  // Wait for loading
  await new Promise(resolve => setTimeout(resolve, 4000));
  
  const bodyText = await page.evaluate(() => document.body.innerText);
  console.log('Community content length:', bodyText.length);
  console.log('Contains "Loading":', bodyText.includes('Loading'));
  console.log('Contains "Community Hub":', bodyText.includes('Community Hub'));
  console.log('Contains "Create Post":', bodyText.includes('Create Post'));
  console.log('First 400 chars:');
  console.log(bodyText.substring(0, 400));
  
  await page.screenshot({ path: 'community_fixed.png', fullPage: true });
  
  await browser.close();
})();
