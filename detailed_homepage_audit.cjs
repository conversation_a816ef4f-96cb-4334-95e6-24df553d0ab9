const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: {width: 1920, height: 1080}
  });
  
  const page = await browser.newPage();
  await page.setViewport({width: 1920, height: 1080});
  
  await page.goto('http://localhost:5001');
  await page.waitForSelector('body', {timeout: 10000});
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  console.log('🔍 DETAILED HOMEPAGE AUDIT - STEVE JOBS STANDARD');
  
  // Test all buttons for functionality
  const buttons = await page.$$('button');
  console.log('\n🔘 BUTTON TESTING:');
  for (let i = 0; i < buttons.length; i++) {
    try {
      const buttonText = await buttons[i].evaluate(el => el.textContent?.trim());
      console.log(`Button ${i+1}: "${buttonText}"`);
      
      // Check if button is properly styled
      const buttonStyles = await buttons[i].evaluate(el => {
        const styles = window.getComputedStyle(el);
        return {
          backgroundColor: styles.backgroundColor,
          color: styles.color,
          padding: styles.padding,
          borderRadius: styles.borderRadius,
          fontSize: styles.fontSize
        };
      });
      console.log(`  Styles:`, buttonStyles);
    } catch (error) {
      console.log(`  ❌ Error testing button ${i+1}:`, error.message);
    }
  }
  
  // Check all links for consistency
  const links = await page.$$('a');
  console.log(`\n🔗 LINK ANALYSIS (${links.length} links):`);
  
  let emptyLinks = 0;
  let brokenStyles = 0;
  
  for (let i = 0; i < Math.min(links.length, 10); i++) {
    const linkText = await links[i].evaluate(el => el.textContent?.trim());
    const href = await links[i].evaluate(el => el.href);
    
    if (!linkText || linkText === '') {
      emptyLinks++;
    }
    
    console.log(`Link ${i+1}: "${linkText}" -> ${href}`);
  }
  
  console.log(`Empty links found: ${emptyLinks}`);
  
  // Check for visual consistency issues
  console.log('\n🎨 VISUAL CONSISTENCY CHECK:');
  
  // Check heading consistency
  const headings = await page.$$eval('h1, h2, h3, h4, h5, h6', elements => {
    return elements.map(el => ({
      tag: el.tagName,
      text: el.textContent?.substring(0, 50),
      fontSize: window.getComputedStyle(el).fontSize,
      color: window.getComputedStyle(el).color,
      fontWeight: window.getComputedStyle(el).fontWeight
    }));
  });
  
  console.log('Headings found:', headings.length);
  headings.forEach((h, i) => {
    console.log(`  ${h.tag}: ${h.fontSize}, ${h.fontWeight}, "${h.text}"`);
  });
  
  // Check spacing consistency
  const sections = await page.$$('section');
  console.log(`\nSections found: ${sections.length}`);
  
  // Check for any error messages or broken content
  const errorElements = await page.$$('[class*="error"], [class*="warning"], .text-red, .text-destructive');
  console.log(`Error elements found: ${errorElements.length}`);
  
  // Check for proper semantic structure
  const main = await page.$('main');
  const nav = await page.$('nav');
  const header = await page.$('header');
  const footer = await page.$('footer');
  
  console.log('\n🏗️ SEMANTIC STRUCTURE:');
  console.log(`Main: ${main !== null}`);
  console.log(`Nav: ${nav !== null}`);
  console.log(`Header: ${header !== null}`);
  console.log(`Footer: ${footer !== null}`);
  
  await browser.close();
  console.log('\n✅ Detailed audit completed');
})();
