import path from "path"
import react from "@vitejs/plugin-react"
import { defineConfig } from "vite"

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react()
  ],

  build: {
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'index.html'),
        nrt: path.resolve(__dirname, 'nrt-standalone.html')
      }
    }
  },

  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },

  server: {
    port: 5001,
    host: true, // Listen on all addresses
    hmr: {
      port: 5001
    },
    // Proxy to enable REAL Supabase auth when browser network is blocked
    proxy: {
      '/api/auth': {
        target: 'https://yekarqanirdkdckimpna.supabase.co',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/auth/, '/auth/v1'),
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // Add Supabase API key to proxied requests
            proxyReq.setHeader('apikey', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc');
          });
        }
      }
    }
  }
});
