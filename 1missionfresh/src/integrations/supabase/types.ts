export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  mission_fresh: {
    Tables: {
      achievement_definitions: {
        Row: {
          created_at: string
          criteria: Json
          description: string
          id: string
          name: string
          points_reward: number
        }
        Insert: {
          created_at?: string
          criteria: Json
          description: string
          id?: string
          name: string
          points_reward?: number
        }
        Update: {
          created_at?: string
          criteria?: Json
          description?: string
          id?: string
          name?: string
          points_reward?: number
        }
        Relationships: []
      }
      activity_points_log: {
        Row: {
          activity_name: string | null
          activity_ref_id: string | null
          activity_type: string
          created_at: string
          id: string
          points_earned: number
          user_id: string
        }
        Insert: {
          activity_name?: string | null
          activity_ref_id?: string | null
          activity_type: string
          created_at?: string
          id?: string
          points_earned: number
          user_id: string
        }
        Update: {
          activity_name?: string | null
          activity_ref_id?: string | null
          activity_type?: string
          created_at?: string
          id?: string
          points_earned?: number
          user_id?: string
        }
        Relationships: []
      }
      claimed_rewards: {
        Row: {
          claimed_at: string
          id: string
          points_deducted: number | null
          reward_description: string | null
          reward_id: string | null
          reward_name: string | null
          tangible_reward_id: string | null
          user_id: string
        }
        Insert: {
          claimed_at?: string
          id?: string
          points_deducted?: number | null
          reward_description?: string | null
          reward_id?: string | null
          reward_name?: string | null
          tangible_reward_id?: string | null
          user_id: string
        }
        Update: {
          claimed_at?: string
          id?: string
          points_deducted?: number | null
          reward_description?: string | null
          reward_id?: string | null
          reward_name?: string | null
          tangible_reward_id?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "claimed_rewards_reward_id_fkey"
            columns: ["reward_id"]
            isOneToOne: false
            referencedRelation: "rewards"
            referencedColumns: ["id"]
          },
        ]
      }
      community_posts: {
        Row: {
          content: string
          created_at: string
          id: string
          is_deleted: boolean
          parent_post_id: string | null
          title: string
          topic_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          is_deleted?: boolean
          parent_post_id?: string | null
          title: string
          topic_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          is_deleted?: boolean
          parent_post_id?: string | null
          title?: string
          topic_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_posts_parent_post_id_fkey"
            columns: ["parent_post_id"]
            isOneToOne: false
            referencedRelation: "community_posts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_posts_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "community_topics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_posts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      community_stats: {
        Row: {
          id: number
          last_updated_at: string | null
          total_cigarettes_not_smoked: number | null
          total_money_saved: number | null
          total_users: number | null
        }
        Insert: {
          id?: never
          last_updated_at?: string | null
          total_cigarettes_not_smoked?: number | null
          total_money_saved?: number | null
          total_users?: number | null
        }
        Update: {
          id?: never
          last_updated_at?: string | null
          total_cigarettes_not_smoked?: number | null
          total_money_saved?: number | null
          total_users?: number | null
        }
        Relationships: []
      }
      community_topics: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
        }
        Relationships: []
      }
      conversation_messages: {
        Row: {
          content: string
          conversation_id: string
          created_at: string
          id: string
          sender_id: string
        }
        Insert: {
          content: string
          conversation_id: string
          created_at?: string
          id?: string
          sender_id: string
        }
        Update: {
          content?: string
          conversation_id?: string
          created_at?: string
          id?: string
          sender_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "conversation_messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      conversation_participants: {
        Row: {
          conversation_id: string
          user_id: string
        }
        Insert: {
          conversation_id: string
          user_id: string
        }
        Update: {
          conversation_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "conversation_participants_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      conversations: {
        Row: {
          created_at: string
          id: string
          is_group_chat: boolean | null
        }
        Insert: {
          created_at?: string
          id?: string
          is_group_chat?: boolean | null
        }
        Update: {
          created_at?: string
          id?: string
          is_group_chat?: boolean | null
        }
        Relationships: []
      }
      coping_strategies: {
        Row: {
          created_at: string
          description: string | null
          id: string
          is_global: boolean | null
          name: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          is_global?: boolean | null
          name: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          is_global?: boolean | null
          name?: string
          user_id?: string | null
        }
        Relationships: []
      }
      craving_logs: {
        Row: {
          coping_mechanism: string | null
          coping_strategy_id: string | null
          created_at: string
          id: string
          intensity: number
          notes: string | null
          timestamp: string
          trigger: string | null
          trigger_id: string | null
          user_id: string
        }
        Insert: {
          coping_mechanism?: string | null
          coping_strategy_id?: string | null
          created_at?: string
          id?: string
          intensity: number
          notes?: string | null
          timestamp?: string
          trigger?: string | null
          trigger_id?: string | null
          user_id: string
        }
        Update: {
          coping_mechanism?: string | null
          coping_strategy_id?: string | null
          created_at?: string
          id?: string
          intensity?: number
          notes?: string | null
          timestamp?: string
          trigger?: string | null
          trigger_id?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "craving_logs_coping_strategy_id_fkey"
            columns: ["coping_strategy_id"]
            isOneToOne: false
            referencedRelation: "coping_strategies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "craving_logs_trigger_id_fkey"
            columns: ["trigger_id"]
            isOneToOne: false
            referencedRelation: "triggers"
            referencedColumns: ["id"]
          },
        ]
      }
      custom_products: {
        Row: {
          created_at: string
          id: string
          name: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          user_id?: string
        }
        Relationships: []
      }
      goal_milestones: {
        Row: {
          achieved_date: string | null
          created_at: string
          description: string | null
          goal_id: string
          id: string
          name: string
          notes: string | null
          status: string
          target_date: string
          updated_at: string
          user_id: string
        }
        Insert: {
          achieved_date?: string | null
          created_at?: string
          description?: string | null
          goal_id: string
          id?: string
          name: string
          notes?: string | null
          status?: string
          target_date: string
          updated_at?: string
          user_id: string
        }
        Update: {
          achieved_date?: string | null
          created_at?: string
          description?: string | null
          goal_id?: string
          id?: string
          name?: string
          notes?: string | null
          status?: string
          target_date?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "goal_milestones_goal_id_fkey"
            columns: ["goal_id"]
            isOneToOne: false
            referencedRelation: "user_goals"
            referencedColumns: ["id"]
          },
        ]
      }
      health_metrics: {
        Row: {
          avg_blood_pressure_diastolic: number | null
          avg_blood_pressure_systolic: number | null
          avg_heart_rate: number | null
          created_at: string
          date: string
          energy_level: number | null
          focus_level: number | null
          id: string
          max_heart_rate: number | null
          min_heart_rate: number | null
          mood: number | null
          notes: string | null
          sleep_hours: number | null
          stress_level: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          avg_blood_pressure_diastolic?: number | null
          avg_blood_pressure_systolic?: number | null
          avg_heart_rate?: number | null
          created_at?: string
          date: string
          energy_level?: number | null
          focus_level?: number | null
          id?: string
          max_heart_rate?: number | null
          min_heart_rate?: number | null
          mood?: number | null
          notes?: string | null
          sleep_hours?: number | null
          stress_level?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          avg_blood_pressure_diastolic?: number | null
          avg_blood_pressure_systolic?: number | null
          avg_heart_rate?: number | null
          created_at?: string
          date?: string
          energy_level?: number | null
          focus_level?: number | null
          id?: string
          max_heart_rate?: number | null
          min_heart_rate?: number | null
          mood?: number | null
          notes?: string | null
          sleep_hours?: number | null
          stress_level?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      journal_entries: {
        Row: {
          content: string
          created_at: string
          id: string
          mood_rating: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          mood_rating?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          mood_rating?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      learning_modules: {
        Row: {
          category: string
          content: Json | null
          created_at: string
          description: string | null
          id: string
          is_published: boolean
          order_index: number | null
          slug: string
          title: string
          updated_at: string
        }
        Insert: {
          category: string
          content?: Json | null
          created_at?: string
          description?: string | null
          id?: string
          is_published?: boolean
          order_index?: number | null
          slug: string
          title: string
          updated_at?: string
        }
        Update: {
          category?: string
          content?: Json | null
          created_at?: string
          description?: string | null
          id?: string
          is_published?: boolean
          order_index?: number | null
          slug?: string
          title?: string
          updated_at?: string
        }
        Relationships: []
      }
      medication_reminders: {
        Row: {
          created_at: string
          dosage: string | null
          end_date: string | null
          frequency: string
          id: string
          local_notification_ids: string[] | null
          medication_name: string
          reminder_time: string
          start_date: string
          user_id: string
        }
        Insert: {
          created_at?: string
          dosage?: string | null
          end_date?: string | null
          frequency: string
          id?: string
          local_notification_ids?: string[] | null
          medication_name: string
          reminder_time: string
          start_date: string
          user_id: string
        }
        Update: {
          created_at?: string
          dosage?: string | null
          end_date?: string | null
          frequency?: string
          id?: string
          local_notification_ids?: string[] | null
          medication_name?: string
          reminder_time?: string
          start_date?: string
          user_id?: string
        }
        Relationships: []
      }
      nicotine_logs: {
        Row: {
          coping_strategy_id: string | null
          cost: number | null
          created_at: string
          date: string
          id: string
          nicotine_strength: number | null
          notes: string | null
          product_type: string | null
          quantity: number | null
          trigger_id: string | null
          updated_at: string
          used_nicotine: boolean | null
          user_id: string
        }
        Insert: {
          coping_strategy_id?: string | null
          cost?: number | null
          created_at?: string
          date: string
          id?: string
          nicotine_strength?: number | null
          notes?: string | null
          product_type?: string | null
          quantity?: number | null
          trigger_id?: string | null
          updated_at?: string
          used_nicotine?: boolean | null
          user_id: string
        }
        Update: {
          coping_strategy_id?: string | null
          cost?: number | null
          created_at?: string
          date?: string
          id?: string
          nicotine_strength?: number | null
          notes?: string | null
          product_type?: string | null
          quantity?: number | null
          trigger_id?: string | null
          updated_at?: string
          used_nicotine?: boolean | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "nicotine_logs_coping_strategy_id_fkey"
            columns: ["coping_strategy_id"]
            isOneToOne: false
            referencedRelation: "coping_strategies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "nicotine_logs_trigger_id_fkey"
            columns: ["trigger_id"]
            isOneToOne: false
            referencedRelation: "triggers"
            referencedColumns: ["id"]
          },
        ]
      }
      nicotine_product_costs: {
        Row: {
          cost_per_unit: number
          created_at: string
          id: string
          product_type: string
          unit_name: string
          user_id: string
        }
        Insert: {
          cost_per_unit: number
          created_at?: string
          id?: string
          product_type: string
          unit_name: string
          user_id: string
        }
        Update: {
          cost_per_unit?: number
          created_at?: string
          id?: string
          product_type?: string
          unit_name?: string
          user_id?: string
        }
        Relationships: []
      }
      notification_preferences: {
        Row: {
          user_id: string
        }
        Insert: {
          user_id: string
        }
        Update: {
          user_id?: string
        }
        Relationships: []
      }
      notifications: {
        Row: {
          created_at: string
          id: string
          is_read: boolean
          link: string | null
          message: string
          title: string
          type: string | null
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          is_read?: boolean
          link?: string | null
          message: string
          title: string
          type?: string | null
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          is_read?: boolean
          link?: string | null
          message?: string
          title?: string
          type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      post_likes: {
        Row: {
          created_at: string
          post_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          post_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          post_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "post_likes_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "community_posts"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          bio: string | null
          country: string | null
          created_at: string | null
          date_of_birth: string | null
          first_name: string | null
          gender: string | null
          id: string
          last_name: string | null
          shareable_code: string
          timezone: string | null
          updated_at: string | null
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          bio?: string | null
          country?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          first_name?: string | null
          gender?: string | null
          id: string
          last_name?: string | null
          shareable_code?: string
          timezone?: string | null
          updated_at?: string | null
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          bio?: string | null
          country?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          first_name?: string | null
          gender?: string | null
          id?: string
          last_name?: string | null
          shareable_code?: string
          timezone?: string | null
          updated_at?: string | null
          username?: string | null
        }
        Relationships: []
      }
      push_tokens: {
        Row: {
          created_at: string | null
          id: string
          platform: string | null
          token: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          platform?: string | null
          token: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          platform?: string | null
          token?: string
          user_id?: string | null
        }
        Relationships: []
      }
      quotes: {
        Row: {
          author: string
          category: string | null
          created_at: string
          id: string
          text: string
        }
        Insert: {
          author: string
          category?: string | null
          created_at?: string
          id?: string
          text: string
        }
        Update: {
          author?: string
          category?: string | null
          created_at?: string
          id?: string
          text?: string
        }
        Relationships: []
      }
      relapses: {
        Row: {
          created_at: string
          id: number
          notes: string | null
          relapse_at: string
          situation: string | null
          trigger: string | null
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: number
          notes?: string | null
          relapse_at?: string
          situation?: string | null
          trigger?: string | null
          user_id: string
        }
        Update: {
          created_at?: string
          id?: number
          notes?: string | null
          relapse_at?: string
          situation?: string | null
          trigger?: string | null
          user_id?: string
        }
        Relationships: []
      }
      rewards: {
        Row: {
          active: boolean
          created_at: string
          description: string
          id: string
          name: string
          points_required: number
          reward_data: Json | null
          reward_type: string | null
        }
        Insert: {
          active?: boolean
          created_at?: string
          description: string
          id?: string
          name: string
          points_required?: number
          reward_data?: Json | null
          reward_type?: string | null
        }
        Update: {
          active?: boolean
          created_at?: string
          description?: string
          id?: string
          name?: string
          points_required?: number
          reward_data?: Json | null
          reward_type?: string | null
        }
        Relationships: []
      }
      smokeless_product_reviews: {
        Row: {
          created_at: string
          id: string
          moderation_status: Database["mission_fresh"]["Enums"]["moderation_status_enum"]
          moderator_notes: string | null
          product_id: string
          rating: number
          review_text: string | null
          review_title: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          moderation_status?: Database["mission_fresh"]["Enums"]["moderation_status_enum"]
          moderator_notes?: string | null
          product_id: string
          rating: number
          review_text?: string | null
          review_title?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          moderation_status?: Database["mission_fresh"]["Enums"]["moderation_status_enum"]
          moderator_notes?: string | null
          product_id?: string
          rating?: number
          review_text?: string | null
          review_title?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_product_for_review"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "smokeless_products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_user_for_review"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      smokeless_product_vendors: {
        Row: {
          created_at: string
          is_available: boolean | null
          last_checked_at: string | null
          price: number | null
          product_id: string
          product_url_on_vendor_site: string | null
          vendor_id: string
        }
        Insert: {
          created_at?: string
          is_available?: boolean | null
          last_checked_at?: string | null
          price?: number | null
          product_id: string
          product_url_on_vendor_site?: string | null
          vendor_id: string
        }
        Update: {
          created_at?: string
          is_available?: boolean | null
          last_checked_at?: string | null
          price?: number | null
          product_id?: string
          product_url_on_vendor_site?: string | null
          vendor_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_product_on_join"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "smokeless_products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_vendor_on_join"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "smokeless_vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      smokeless_products: {
        Row: {
          brand: string | null
          category: string
          country_of_origin: string | null
          created_at: string
          description: string | null
          expert_notes_chemicals: string | null
          expert_notes_gum_health: string | null
          flavors: string[] | null
          id: string
          image_url: string | null
          ingredients: string | null
          is_verified: boolean | null
          manufacturer: string | null
          name: string
          nicotine_strengths: Json | null
          tags: string[] | null
          updated_at: string
          user_rating_avg: number | null
          user_rating_count: number | null
        }
        Insert: {
          brand?: string | null
          category: string
          country_of_origin?: string | null
          created_at?: string
          description?: string | null
          expert_notes_chemicals?: string | null
          expert_notes_gum_health?: string | null
          flavors?: string[] | null
          id?: string
          image_url?: string | null
          ingredients?: string | null
          is_verified?: boolean | null
          manufacturer?: string | null
          name: string
          nicotine_strengths?: Json | null
          tags?: string[] | null
          updated_at?: string
          user_rating_avg?: number | null
          user_rating_count?: number | null
        }
        Update: {
          brand?: string | null
          category?: string
          country_of_origin?: string | null
          created_at?: string
          description?: string | null
          expert_notes_chemicals?: string | null
          expert_notes_gum_health?: string | null
          flavors?: string[] | null
          id?: string
          image_url?: string | null
          ingredients?: string | null
          is_verified?: boolean | null
          manufacturer?: string | null
          name?: string
          nicotine_strengths?: Json | null
          tags?: string[] | null
          updated_at?: string
          user_rating_avg?: number | null
          user_rating_count?: number | null
        }
        Relationships: []
      }
      smokeless_vendors: {
        Row: {
          countries_shipped_to: Json | null
          created_at: string
          customer_service_contact: string | null
          description: string | null
          id: string
          is_partner: boolean | null
          logo_url: string | null
          name: string
          shipping_info: string | null
          updated_at: string
          user_rating_avg: number | null
          user_rating_count: number | null
          website_url: string | null
        }
        Insert: {
          countries_shipped_to?: Json | null
          created_at?: string
          customer_service_contact?: string | null
          description?: string | null
          id?: string
          is_partner?: boolean | null
          logo_url?: string | null
          name: string
          shipping_info?: string | null
          updated_at?: string
          user_rating_avg?: number | null
          user_rating_count?: number | null
          website_url?: string | null
        }
        Update: {
          countries_shipped_to?: Json | null
          created_at?: string
          customer_service_contact?: string | null
          description?: string | null
          id?: string
          is_partner?: boolean | null
          logo_url?: string | null
          name?: string
          shipping_info?: string | null
          updated_at?: string
          user_rating_avg?: number | null
          user_rating_count?: number | null
          website_url?: string | null
        }
        Relationships: []
      }
      step_rewards: {
        Row: {
          created_at: string
          date: string
          id: string
          points_awarded: number | null
          steps: number
          user_id: string
        }
        Insert: {
          created_at?: string
          date: string
          id?: string
          points_awarded?: number | null
          steps: number
          user_id: string
        }
        Update: {
          created_at?: string
          date?: string
          id?: string
          points_awarded?: number | null
          steps?: number
          user_id?: string
        }
        Relationships: []
      }
      success_stories: {
        Row: {
          created_at: string
          id: string
          is_anonymous: boolean
          is_approved: boolean
          story_content: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          is_anonymous?: boolean
          is_approved?: boolean
          story_content: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          is_anonymous?: boolean
          is_approved?: boolean
          story_content?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "success_stories_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      success_story_comments: {
        Row: {
          content: string
          created_at: string
          id: string
          is_deleted: boolean
          parent_comment_id: string | null
          story_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          is_deleted?: boolean
          parent_comment_id?: string | null
          story_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          is_deleted?: boolean
          parent_comment_id?: string | null
          story_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "success_story_comments_parent_comment_id_fkey"
            columns: ["parent_comment_id"]
            isOneToOne: false
            referencedRelation: "success_story_comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "success_story_comments_story_id_fkey"
            columns: ["story_id"]
            isOneToOne: false
            referencedRelation: "success_stories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "success_story_comments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      success_story_likes: {
        Row: {
          created_at: string
          story_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          story_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          story_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "success_story_likes_story_id_fkey"
            columns: ["story_id"]
            isOneToOne: false
            referencedRelation: "success_stories"
            referencedColumns: ["id"]
          },
        ]
      }
      support_connections: {
        Row: {
          created_at: string | null
          id: string
          user1_id: string
          user2_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          user1_id: string
          user2_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          user1_id?: string
          user2_id?: string
        }
        Relationships: []
      }
      support_requests: {
        Row: {
          created_at: string | null
          id: string
          receiver_id: string
          sender_id: string
          status:
            | Database["mission_fresh"]["Enums"]["support_request_status"]
            | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          receiver_id: string
          sender_id: string
          status?:
            | Database["mission_fresh"]["Enums"]["support_request_status"]
            | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          receiver_id?: string
          sender_id?: string
          status?:
            | Database["mission_fresh"]["Enums"]["support_request_status"]
            | null
          updated_at?: string | null
        }
        Relationships: []
      }
      testimonials: {
        Row: {
          avatar_url: string | null
          content: string
          created_at: string
          id: string
          is_approved: boolean
          rating: number | null
          user_id: string | null
          user_name: string
        }
        Insert: {
          avatar_url?: string | null
          content: string
          created_at?: string
          id?: string
          is_approved?: boolean
          rating?: number | null
          user_id?: string | null
          user_name: string
        }
        Update: {
          avatar_url?: string | null
          content?: string
          created_at?: string
          id?: string
          is_approved?: boolean
          rating?: number | null
          user_id?: string | null
          user_name?: string
        }
        Relationships: []
      }
      triggers: {
        Row: {
          created_at: string
          description: string | null
          id: string
          is_global: boolean | null
          name: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          is_global?: boolean | null
          name: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          is_global?: boolean | null
          name?: string
          user_id?: string | null
        }
        Relationships: []
      }
      user_achievements: {
        Row: {
          achievement_id: string
          created_at: string
          id: string
          unlocked_at: string
          user_id: string
        }
        Insert: {
          achievement_id: string
          created_at?: string
          id?: string
          unlocked_at?: string
          user_id: string
        }
        Update: {
          achievement_id?: string
          created_at?: string
          id?: string
          unlocked_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_achievements_achievement_id_fkey"
            columns: ["achievement_id"]
            isOneToOne: false
            referencedRelation: "achievement_definitions"
            referencedColumns: ["id"]
          },
        ]
      }
      user_badges: {
        Row: {
          badge_id: string
          earned_at: string
          id: string
          user_id: string
        }
        Insert: {
          badge_id: string
          earned_at?: string
          id?: string
          user_id: string
        }
        Update: {
          badge_id?: string
          earned_at?: string
          id?: string
          user_id?: string
        }
        Relationships: []
      }
      user_gamification_stats: {
        Row: {
          created_at: string
          current_level: number
          current_points: number
          current_streak: number
          last_login_date: string | null
          longest_streak: number
          tool_usage_count: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          current_level?: number
          current_points?: number
          current_streak?: number
          last_login_date?: string | null
          longest_streak?: number
          tool_usage_count?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          current_level?: number
          current_points?: number
          current_streak?: number
          last_login_date?: string | null
          longest_streak?: number
          tool_usage_count?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      user_goals: {
        Row: {
          cost_per_unit: number | null
          created_at: string
          daily_step_goal: number | null
          goal_type: string
          id: string
          method: string
          method_details: Json | null
          motivation: string | null
          product_type: string
          quit_date: string
          typical_daily_usage: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          cost_per_unit?: number | null
          created_at?: string
          daily_step_goal?: number | null
          goal_type: string
          id?: string
          method: string
          method_details?: Json | null
          motivation?: string | null
          product_type: string
          quit_date: string
          typical_daily_usage?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          cost_per_unit?: number | null
          created_at?: string
          daily_step_goal?: number | null
          goal_type?: string
          id?: string
          method?: string
          method_details?: Json | null
          motivation?: string | null
          product_type?: string
          quit_date?: string
          typical_daily_usage?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      user_learning_progress: {
        Row: {
          created_at: string
          id: string
          module_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          module_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          module_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_learning_progress_module_id_fkey"
            columns: ["module_id"]
            isOneToOne: false
            referencedRelation: "learning_modules"
            referencedColumns: ["id"]
          },
        ]
      }
      user_logins: {
        Row: {
          id: number
          login_at: string
          user_id: string
        }
        Insert: {
          id?: number
          login_at?: string
          user_id: string
        }
        Update: {
          id?: number
          login_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_logins_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_preferences: {
        Row: {
          cost_per_product: Json | null
          created_at: string
          dashboard_widgets: string[] | null
          id: string
          notification_cravings: boolean | null
          notification_logs: boolean | null
          notification_milestones: boolean | null
          notifications: Json | null
          theme: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          cost_per_product?: Json | null
          created_at?: string
          dashboard_widgets?: string[] | null
          id?: string
          notification_cravings?: boolean | null
          notification_logs?: boolean | null
          notification_milestones?: boolean | null
          notifications?: Json | null
          theme?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          cost_per_product?: Json | null
          created_at?: string
          dashboard_widgets?: string[] | null
          id?: string
          notification_cravings?: boolean | null
          notification_logs?: boolean | null
          notification_milestones?: boolean | null
          notifications?: Json | null
          theme?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      user_trigger_strategy_map: {
        Row: {
          created_at: string
          id: string
          strategy_id: string
          trigger_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          strategy_id: string
          trigger_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          strategy_id?: string
          trigger_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_trigger_strategy_map_strategy_id_fkey"
            columns: ["strategy_id"]
            isOneToOne: false
            referencedRelation: "coping_strategies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_trigger_strategy_map_trigger_id_fkey"
            columns: ["trigger_id"]
            isOneToOne: false
            referencedRelation: "triggers"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      check_and_award_streak_achievements: {
        Args: { p_user_id: string; p_current_streak: number }
        Returns: Json
      }
      get_random_quote: {
        Args: Record<PropertyKey, never>
        Returns: {
          author: string
          category: string | null
          created_at: string
          id: string
          text: string
        }[]
      }
      get_user_streak: {
        Args: { p_user_id: string }
        Returns: {
          current_streak: number
          longest_streak: number
        }[]
      }
      increment_user_points: {
        Args: { user_id_param: string; points_to_add: number }
        Returns: {
          created_at: string
          current_level: number
          current_points: number
          current_streak: number
          last_login_date: string | null
          longest_streak: number
          tool_usage_count: number | null
          updated_at: string
          user_id: string
        }[]
      }
      update_user_streak: {
        Args: { p_user_id: string }
        Returns: number
      }
    }
    Enums: {
      moderation_status_enum: "pending" | "approved" | "rejected"
      support_request_status: "pending" | "accepted" | "rejected"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  mission_fresh: {
    Enums: {
      moderation_status_enum: ["pending", "approved", "rejected"],
      support_request_status: ["pending", "accepted", "rejected"],
    },
  },
} as const
