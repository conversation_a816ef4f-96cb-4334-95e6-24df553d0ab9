import { <PERSON> } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MailCheck } from "lucide-react";

const PleaseVerifyEmailPage = () => {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background p-4">
      <div className="w-full max-w-md space-y-6 text-center">
        <div className="mx-auto flex h-14 w-14 items-center justify-center bg-secondary" style={{borderRadius: '50%'}}>
          <MailCheck className="h-7 w-7 text-primary" />
        </div>
        <div className="space-y-2">
          <h1 className="font-heading text-3xl font-semibold tracking-tight">Check Your Email</h1>
          <p className="text-muted-foreground">
            We've sent a verification link to your email address. Please click the
            link to activate your account.
          </p>
        </div>
        <div className="pt-4">
          <Button asChild className="w-full" size="lg">
            <Link to="/auth?tab=login">Back to Sign In</Link>
          </Button>
          <p className="mt-6 text-sm text-muted-foreground">
            Didn't receive the email? Check your spam folder.
          </p>
        </div>
      </div>
    </div>
  );
};

export default PleaseVerifyEmailPage;
