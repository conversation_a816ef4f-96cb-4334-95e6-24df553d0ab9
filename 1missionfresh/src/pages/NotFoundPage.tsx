import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { Home, LayoutDashboard, Crown, Compass, ArrowLeft } from 'lucide-react';
import { motion } from 'framer-motion';
import SEOHead from '@/components/common/SEOHead';

const NotFoundPage = () => {
  const { user } = useAuth();

  const homePath = user ? '/app/dashboard' : '/';
  const homeText = user ? 'Go to Dashboard' : 'Return to Homepage';

  return (
    <>
      <SEOHead
        title="Page Not Found - Mission Fresh"
        description="The page you're looking for doesn't exist. Return to Mission Fresh to continue your wellness journey."
      />
      <div className="relative flex min-h-screen flex-col items-center justify-center overflow-hidden bg-background px-8 py-24 text-center">
        {/* Sophisticated Decorative Background - 100% sharp */}
        <div aria-hidden="true" className="absolute inset-0 z-0">
          {/* Large 404 Background */}
          <div className="absolute inset-0 flex items-center justify-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1.5, ease: [0.22, 1, 0.36, 1] }}
            >
              <p className="font-heading text-[20rem] md:text-[30rem] font-black leading-none text-secondary select-none">
                404
              </p>
            </motion.div>
          </div>
        </div>

        {/* Content */}
        <motion.div
          initial={{ opacity: 0, y: 24 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: 'easeOut', delay: 0.3 }}
          className="relative z-10 max-w-2xl mx-auto"
        >
          <div className="inline-flex items-center gap-3 px-6 py-3 bg-secondary border-2 border-primary text-primary font-semibold mb-8 shadow-sm" style={{borderRadius: '50px'}}>
            <Compass className="w-4 h-4" />
            <div className="text-center space-y-4">Lost in Space</div>
          </div>
          
          <h1 className="text-6xl md:text-7xl font-bold tracking-tight text-foreground mb-8">
            A Moment of{" "}
            <span className="text-primary">Stillness</span>
          </h1>
          
          <p className="mx-auto text-xl text-muted-foreground leading-relaxed mb-12">
            It seems you've wandered off the path. Let's guide you back to a place
            of clarity, purpose, and wellness with the elegance you deserve.
          </p>
          
          <div className="space-y-6 sm:flex-row items-center justify-center gap-6">
            <Button asChild size="xl" className="group shadow-lg hover:shadow-xl transition-all duration-300">
              <Link to={homePath}>
                {homeText}
                {user ? (
                  <LayoutDashboard className="ml-2 w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                ) : (
                  <Home className="ml-2 w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                )}
              </Link>
            </Button>
            
            <Button variant="outline" size="xl" className="group border-2 border-primary hover:border-primary hover:bg-secondary" onClick={() => window.history.back()}>
              <ArrowLeft className="mr-2 w-5 h-5 transition-transform duration-300 group-hover:-translate-x-1" />
              Go Back
            </Button>
          </div>
          
          {/* Elegant help section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="mt-16"
          >
            <div className="bg-background p-8 border-2 border-primary shadow-sm" style={{borderRadius: '32px'}}>
              <div className="flex items-center justify-center gap-2 mb-4">
                <Compass className="w-5 h-5 text-primary" />
                <span className="text-sm text-muted-foreground font-semibold">
                  Need assistance?
                </span>
              </div>
              <p className="text-sm text-muted-foreground text-center">
                If you believe this is an error, please contact our support team for immediate assistance.
              </p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </>
  );
};

export default NotFoundPage;
