import React from 'react';
import { Shield, FileText, Crown, Scale, Mail } from 'lucide-react';
import SEOHead from '@/components/common/SEOHead';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const TermsOfServicePage: React.FC = () => {
  const sections = [
    {
      icon: FileText,
      title: "Acceptance of Terms",
      content: "By accessing and using Mission Fresh, you accept and agree to be bound by the terms and provision of this agreement. These terms are designed to protect both you and our service while ensuring a premium experience.",
    },
    {
      icon: Crown,
      title: "Use License",
      content: "Permission is granted to temporarily use Mission Fresh for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title. You may access our premium features as part of your subscription.",
    },
    {
      icon: Shield,
      title: "User Responsibilities",
      content: "You are responsible for maintaining the confidentiality of your account and password and for restricting access to your account. We provide tools to help you maintain security and privacy.",
    },
    {
      icon: Scale,
      title: "Service Availability",
      content: "We strive to keep Mission Fresh available at all times with premium reliability, but we cannot guarantee uninterrupted service. We may modify or discontinue the service with appropriate notice to our valued users.",
    }
  ];

  return (
    <>
      <SEOHead
        title="Terms of Service - Mission Fresh"
        description="Read Mission Fresh's Terms of Service to understand your rights and responsibilities when using our premium wellness platform."
      />
      <div className="bg-background text-foreground">
        <section className="py-16 sm:py-20 text-center">
          <div className="container mx-auto max-w-6xl px-6">
            <div className="space-y-12">
              <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-foreground leading-tight">
                <span className="text-primary font-black block mb-2">Terms of</span> Service
              </h1>
              <p className="max-w-4xl mx-auto text-lg sm:text-xl md:text-2xl text-foreground leading-relaxed font-medium">
                Your rights and responsibilities when using our services.
              </p>
            </div>
          </div>
        </section>
        <section className="py-20 bg-background">
          <div className="container mx-auto max-w-5xl px-6">

            <div className="space-y-8">
              {sections.map((section) => (
                <Card key={section.title} className="hover:shadow-xl transition-all duration-300 hover-scale-sm border-border bg-card shadow-lg">
                  <CardHeader className="flex flex-row items-center gap-6 pb-4">
                    <div className="flex w-12 h-12 items-center justify-center bg-background border-2 border-primary" style={{borderRadius: '24px'}}>
                      <section.icon className="icon-xl text-primary" strokeWidth={2} />
                    </div>
                    <CardTitle className="text-xl font-bold text-foreground leading-tight">
                      {section.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="px-6 pb-8">
                    <p className="text-base text-muted-foreground leading-relaxed pl-16">
                      {section.content}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>

          </div>
        </section>
        <section className="py-20">
          <div className="container mx-auto max-w-4xl px-6 text-center">
            <Card className="bg-background border-2 border-primary shadow-xl">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto flex w-12 h-12 items-center justify-center bg-background border-2 border-primary mb-6" style={{borderRadius: '24px'}}>
                  <Mail className="icon-xl text-primary" strokeWidth={2} />
                </div>
                <CardTitle className="text-2xl sm:text-3xl font-bold text-foreground leading-tight">
                  Questions About Our Terms?
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center px-8 pb-8">
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed font-medium">
                  If you have any questions, please contact us at{' '}
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-primary hover:text-primary-hover font-semibold transition-colors duration-300 underline decoration-primary/30 hover:decoration-primary"
                  >
                    <EMAIL>
                  </a>
                  . We're here to help.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    </>
  );
};

export default TermsOfServicePage;
