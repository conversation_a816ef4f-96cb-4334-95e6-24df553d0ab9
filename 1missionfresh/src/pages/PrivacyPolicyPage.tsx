import { Shield, Eye, Lock, UserCheck, Database, Mail } from 'lucide-react';
import <PERSON><PERSON><PERSON> from '@/components/common/SEOHead';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

const PrivacyPolicyPage: React.FC = () => {
  const sections = [
    {
      icon: Database,
      title: "Information We Collect",
      content: "We collect information you provide directly to us, such as when you create an account, update your profile, or contact us for support. This includes your wellness tracking data, which remains private and secure.",
    },
    {
      icon: Eye,
      title: "How We Use Your Information", 
      content: "We use the information we collect to provide, maintain, and improve our premium services, communicate with you, and ensure the security of our platform. Your data helps us personalize your wellness experience.",
    },
    {
      icon: UserCheck,
      title: "Information Sharing",
      content: "We do not sell, trade, or otherwise transfer your personal information to third parties without your explicit consent, except as described in this policy. Your privacy is our priority.",
    },
    {
      icon: Lock,
      title: "Data Security",
      content: "We implement enterprise-grade security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. Your wellness data is encrypted and secure.",
    }
  ];

  return (
    <>
      <SEOHead
        title="Privacy Policy - Mission Fresh"
        description="Learn how Mission Fresh protects your privacy and handles your personal information with the highest standards of security and care."
      />
      <div className="bg-background text-foreground">
        <section className="py-16 sm:py-20 text-center">
          <div className="container mx-auto max-w-6xl px-6">
            <div className="space-y-12">
              <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-foreground leading-tight">
                <span className="text-primary font-black block mb-2">Privacy</span> Policy
              </h1>
              <p className="max-w-4xl mx-auto text-lg sm:text-xl md:text-2xl text-foreground leading-relaxed font-medium">
                Your privacy is paramount to us. This policy explains how we collect, use, and protect your information with the highest standards of care and security.
              </p>
            </div>
          </div>
        </section>
        <section className="py-20 bg-background">
          <div className="container mx-auto max-w-5xl px-6">
            
            <div className="space-y-8">
              {sections.map((section) => (
                <Card key={section.title} className="hover:shadow-xl transition-all duration-300 hover-scale-sm border-border bg-card shadow-lg">
                  <CardHeader className="flex flex-row items-center gap-6 pb-4">
                    <div className="flex w-12 h-12 items-center justify-center bg-primary/10" style={{borderRadius: '24px'}}>
                      <section.icon className="icon-xl text-primary" strokeWidth={2} />
                    </div>
                    <CardTitle className="text-xl font-bold text-foreground leading-tight">
                      {section.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="px-6 pb-8">
                    <p className="text-base text-muted-foreground leading-relaxed pl-16">
                      {section.content}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>

          </div>
        </section>
        <section className="py-20">
          <div className="container mx-auto max-w-4xl px-6 text-center">
            <Card className="bg-primary/5 border-primary/20 shadow-xl">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto flex w-12 h-12 items-center justify-center bg-primary/10 mb-6" style={{borderRadius: '24px'}}>
                  <Mail className="icon-xl text-primary" strokeWidth={2} />
                </div>
                <CardTitle className="text-2xl sm:text-3xl font-bold text-foreground leading-tight">
                  Questions About Your Privacy?
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center px-8 pb-8">
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed font-medium">
                  If you have any questions about this Privacy Policy or how we handle your data, please contact us at{' '}
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-primary hover:text-primary-hover font-semibold transition-colors duration-300 underline decoration-primary/30 hover:decoration-primary"
                  >
                    <EMAIL>
                  </a>
                  . We're committed to transparency and protecting your privacy.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    </>
  );
};

export default PrivacyPolicyPage;
