import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Link } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Heart, Users, Target, Shield, ArrowRight, Award, CheckCircle } from 'lucide-react';
import SE<PERSON>ead from '@/components/common/SEOHead';

const values = [
  {
    icon: Heart,
    title: 'Compassionate Care',
    description: 'We understand that quitting smoking is deeply personal. Every feature is designed with empathy and genuine care for your well-being.',
  },
  {
    icon: Shield,
    title: 'Privacy First',
    description: 'Your health data is sacred. We use industry-leading encryption and never share your personal information without explicit consent.',
  },
  {
    icon: Target,
    title: 'Evidence-Based',
    description: 'Every tool and technique is grounded in scientific research and proven methodologies from leading addiction medicine experts.',
  },
  {
    icon: Users,
    title: 'Community Driven',
    description: 'Real people sharing real experiences. Our community features are designed to foster genuine support and connection.',
  },
];

const AboutUsPage = () => {
  return (
    <>
      <SEOHead
        title="About Us - Mission Fresh"
        description="Learn about Mission Fresh's mission to support individuals on their journey to reduce or quit nicotine with compassionate, premium tools."
      />
      <div className="bg-background text-foreground">
        <section className="py-20 sm:py-24 text-center">
          <div className="container mx-auto max-w-6xl px-8">
            <div className="space-y-12">
              <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-foreground leading-tight">
                We're Here to <span className="text-primary font-black block mt-4">Transform Lives</span>
              </h1>
              <div className="max-w-4xl mx-auto text-center space-y-6">
                <p className="text-lg sm:text-xl md:text-2xl text-foreground leading-relaxed font-medium">
                  Founded by former smokers who understand the journey intimately, we've dedicated ourselves to creating the most comprehensive and compassionate quit-smoking platform ever built.
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="py-24 bg-background">
          <div className="container mx-auto max-w-7xl px-8">
            <div className="text-center mb-20">
              <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-foreground mb-8 leading-tight">
                What Drives Us
              </h2>
              <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed font-medium">
                These core values guide every decision we make and every feature we build.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {values.map((value, index) => (
                <Card 
                  key={value.title} 
                  className="text-center group hover:shadow-xl transition-all duration-300 hover-scale-sm border-border bg-card shadow-lg"
                >
                  <CardHeader className="pb-6">
                    <div className="mx-auto flex w-16 h-16 items-center justify-center bg-primary-light group-hover:bg-primary-hover transition-all duration-300" style={{borderRadius: '24px'}}>
                      <value.icon className="icon-xl text-primary" strokeWidth={2} />
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6 px-8 pb-8">
                    <h3 className="text-xl font-bold text-foreground leading-tight">
                      {value.title}
                    </h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        <section className="py-24">
          <div className="container mx-auto max-w-4xl px-8 text-center">
            <div className="space-y-12">
              <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-foreground leading-tight">
                Ready to Begin Your Transformation?
              </h2>
              <p className="text-lg sm:text-xl text-muted-foreground mb-12 max-w-2xl mx-auto leading-relaxed font-medium">
                Join thousands who have found clarity and reclaimed their vitality with Mission Fresh.
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center gap-6 pt-8">
                <Button 
                  asChild 
                  className="min-h-12 text-lg px-8 py-4 bg-primary hover:bg-primary-hover text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 hover-scale group" style={{borderRadius: '24px'}}
                >
                  <Link to="/auth?tab=signup" className="flex items-center gap-3 min-w-[18rem]">
                    Start Your Journey Today
                    <ArrowRight className="icon-base group-hover:translate-x-1 transition-transform duration-300" strokeWidth={2} />
                  </Link>
                </Button>
                <Button 
                  asChild 
                  variant="outline" 
                  className="min-h-12 text-lg px-8 py-4 border-2 border-border hover:border-primary hover:bg-primary-light transition-all duration-300 hover-scale" style={{borderRadius: '24px'}}
                >
                  <Link to="/features" className="min-w-[18rem]">
                    Explore Features
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default AboutUsPage;
