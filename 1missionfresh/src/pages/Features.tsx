import React, { memo, useCallback, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  <PERSON>rkles,
  Flame,
  CheckCircle2,
  ClipboardList,
  BarChart3,
  Trophy,
  HeartPulse,
  Users,
  ArrowRight,
  Shield,
  Lightbulb, // Replaced Brain with Lightbulb
  Target,
} from "lucide-react";
import SE<PERSON>ead from "@/components/common/SEOHead";

interface Feature {
  icon: React.ElementType;
  title: string;
  description: string;
  category: string;
}

// IMPLEMENTATION NOTE: Features are currently static for <PERSON>. Future enhancement: move to database for dynamic content management
const featureList: Feature[] = [
  {
    icon: Target,
    title: "Flexible Goal Setting",
    description: "Define your unique path with personalized milestones, whether quitting completely or reducing gradually.",
    category: "Planning"
  },
  {
    icon: ClipboardList,
    title: "Universal Product Tracking",
    description: "Comprehensive tracking for all nicotine products: cigarettes, vapes, pouches, gums, and more.",
    category: "Tracking"
  },
  {
    icon: Bar<PERSON>hart3,
    title: "Insightful Analytics",
    description: "Beautiful visualizations and dynamic charts that reveal patterns and celebrate your progress.",
    category: "Analytics"
  },
  {
    icon: HeartPulse,
    title: "Holistic Wellness",
    description: "Monitor sleep quality, energy levels, mood patterns, and focus metrics for complete well-being.",
    category: "Wellness"
  },
  {
    icon: Trophy,
    title: "Achievement System",
    description: "Earn meaningful badges and rewards that recognize your commitment and celebrate milestones.",
    category: "Motivation"
  },
  {
    icon: Users,
    title: "Supportive Community",
    description: "Connect with like-minded individuals through our thoughtfully moderated community spaces.",
    category: "Community"
  },
  {
    icon: Flame,
    title: "Craving Management",
    description: "Immediate access to science-backed techniques for overcoming intense cravings in real-time.",
    category: "Tools"
  },
  {
    icon: Sparkles,
    title: "Energy & Mood Support",
    description: "Targeted exercises and mindfulness techniques to combat fatigue and regulate emotional balance.",
    category: "Tools"
  },
  {
    icon: Zap,
    title: "Focus Enhancement",
    description: "Proven strategies and exercises to sharpen mental clarity and improve concentration.",
    category: "Tools"
  },
  {
    icon: Shield,
    title: "Privacy-First Design",
    description: "Your data stays yours. Built with privacy by design and transparent data practices.",
    category: "Security"
  },
  {
    icon: Lightbulb, // Replaced Brain with Lightbulb
    title: "Intelligent Insights",
    description: "Personalized recommendations and insights powered by your unique patterns and preferences.",
    category: "Intelligence"
  },
  {
    icon: CheckCircle2,
    title: "Progress Validation",
    description: "Celebrate every victory with detailed progress tracking and milestone validation.",
    category: "Motivation"
  },
];

const FeaturesPage = memo(() => {
  // TODO: Add internationalization support for multilingual features
  // TODO: Add loading states for dynamic content when moved to database
  // TODO: Add error boundaries for robust error handling
  
  // Memoize feature list to prevent unnecessary re-renders
  const memoizedFeatureList = useMemo(() => featureList, []);
  
  // Memoize event handlers to prevent unnecessary re-renders
  const handleSignupClick = useCallback(() => {
    // TODO: Add analytics tracking for Features CTA conversion
    // analytics.track('features_cta_clicked', { source: 'features_page', action: 'signup' });
  }, []);
  
  const handleLearnMoreClick = useCallback(() => {
    // TODO: Add analytics tracking for Features Learn More CTA
    // analytics.track('features_learn_more_clicked', { source: 'features_page', action: 'learn_more' });
  }, []);
  
  // Memoize keyboard event handlers to prevent unnecessary re-renders
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLAnchorElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      e.currentTarget.click();
    }
  }, []);
  return (
    <>
      <SEOHead
        title="Features - Mission Fresh"
        description="Explore the powerful, science-backed features of Mission Fresh designed to support your journey away from nicotine."
      />
      <div className="bg-background text-foreground" role="main">
        {/* Skip link for keyboard navigation accessibility */}
        <a 
          href="#main-content" 
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2" style={{borderRadius: '8px'}}
        >
          Skip to main content
        </a>
        <section className="text-center py-section" id="main-content">
          <div className="container mx-auto max-w-6xl px-container">
            <div className="space-y-6">
              <h1 className="text-heading-xl font-bold text-foreground leading-tight tracking-tight">
                A Smarter Way to <span className="text-primary block mt-1">Wellness</span>
              </h1>
              <div className="max-w-3xl mx-auto text-center">
                <p className="text-base text-muted-foreground leading-refined">
                  Mission Fresh is more than a tracker. It's a comprehensive suite of features designed to support every aspect of your journey.
                </p>
                <div className="pt-6">
                  <Button asChild className="bg-primary hover:bg-primary-hover text-primary-foreground transition-all duration-200 group font-semibold">
                    <Link to="/auth?tab=signup" className="flex items-center gap-2">
                      Get Started For Free
                      <ArrowRight className="icon-xs group-hover:translate-x-1 transition-transform duration-200" strokeWidth={2} aria-hidden="true" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section className="bg-background py-section" aria-labelledby="features-heading">
          <div className="container mx-auto max-w-6xl px-container">
            <h2 className="sr-only" id="features-heading">Mission Fresh Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" role="list">
              {memoizedFeatureList.map((feature, index) => (
                <Card 
                  key={`feature-${index}-${feature.title.replace(/\s+/g, '-').toLowerCase()}`} 
                  className="bg-card border border-border hover:border-primary hover:shadow-md transition-all duration-200 h-full"
                  role="listitem"
                >
                  <CardContent className="flex flex-col items-center text-center h-full p-6 space-y-4">
                    <div className="flex items-center justify-center bg-primary transition-all duration-200 icon-2xl" style={{borderRadius: '24px'}}>
                      <feature.icon className="icon-sm text-primary-foreground" strokeWidth={2} aria-hidden="true" />
                    </div>
                    <div className="flex-1 flex flex-col space-y-3">
                      <h3 className="text-heading-sm font-semibold text-foreground leading-tight">{feature.title}</h3>
                      <p className="text-body text-muted-foreground leading-relaxed">{feature.description}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        <section className="py-section" aria-labelledby="cta-heading">
          <div className="container mx-auto max-w-6xl px-container text-center">
            <div className="space-y-6">
              <h2 className="text-heading-lg font-bold text-foreground leading-tight tracking-tight" id="cta-heading">
                Ready to Experience Mission Fresh?
              </h2>
              <p className="text-body text-muted-foreground leading-refined mx-auto max-w-3xl">
                Join thousands who have discovered a better way to wellness. Start your journey today.
              </p>
              <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
                <Button asChild className="bg-primary hover:bg-primary-hover text-primary-foreground transition-all duration-200 group font-semibold" type="button">
                  <Link to="/auth?tab=signup" className="flex items-center gap-2" rel="noopener noreferrer" onClick={handleSignupClick} onKeyDown={handleKeyDown}>
                    Start Your Journey
                    <ArrowRight className="icon-xs group-hover:translate-x-1 transition-transform duration-200" strokeWidth={2} aria-hidden="true" />
                  </Link>
                </Button>
                <Button asChild variant="outline" className="border border-primary hover:border-primary hover:bg-primary hover:text-primary-foreground transition-all duration-200 font-semibold" type="button">
                  <Link to="/how-it-works" rel="noopener noreferrer" onClick={handleLearnMoreClick} onKeyDown={handleKeyDown}>
                    Learn More
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
});

// Set displayName for debugging
FeaturesPage.displayName = 'FeaturesPage';

export default FeaturesPage;
