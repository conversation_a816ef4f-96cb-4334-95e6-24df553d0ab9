import React from 'react';
import { HelpCircle, Book, MessageCircle, Mail, Search, ChevronRight } from 'lucide-react';
import SEOHead from '@/components/common/SEOHead';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const HelpCenterPage: React.FC = () => {
  const helpCategories = [
    {
      icon: Book,
      title: "Getting Started",
      description: "Learn the basics of using Mission Fresh",
      articles: [
        "Creating your account",
        "Setting up your goals",
        "Understanding the dashboard",
        "Your first log entry"
      ]
    },
    {
      icon: HelpCircle,
      title: "Tracking & Progress",
      description: "How to track your journey effectively",
      articles: [
        "Logging nicotine use",
        "Understanding progress charts",
        "Setting milestones",
        "Viewing your history"
      ]
    },
    {
      icon: MessageCircle,
      title: "Community & Support",
      description: "Connect with others and get help",
      articles: [
        "Joining community discussions",
        "Creating posts",
        "Finding support groups",
        "Community guidelines"
      ]
    }
  ];

  return (
    <>
      <SEOHead
        title="Help Center - Mission Fresh"
        description="Find answers to your questions and get help with using Mission Fresh to support your wellness journey."
      />
      <div className="bg-background text-foreground">
        <section className="py-16 sm:py-20 text-center">
          <div className="container mx-auto max-w-6xl px-6">
            <div className="space-y-12">
              <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-foreground leading-tight">
                <span className="text-primary font-black block mb-2">Help</span> Center
              </h1>
              <p className="max-w-4xl mx-auto text-lg sm:text-xl md:text-2xl text-foreground leading-relaxed font-medium">
                Find answers to your questions and get the support you need on your wellness journey.
              </p>
            </div>
          </div>
        </section>

        <section className="py-16">
          <div className="container mx-auto max-w-6xl px-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {helpCategories.map((category) => (
                <Card key={category.title} className="hover:shadow-xl transition-all duration-300 hover-scale-sm border-border bg-card shadow-lg">
                  <CardHeader className="text-center pb-4">
                    <div className="mx-auto flex w-12 h-12 items-center justify-center bg-primary/10 mb-4" style={{ borderRadius: '24px' }}>
                      <category.icon className="w-6 h-6 text-primary" strokeWidth={2} />
                    </div>
                    <CardTitle className="text-xl font-bold text-foreground leading-tight">
                      {category.title}
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      {category.description}
                    </p>
                  </CardHeader>
                  <CardContent className="px-6 pb-8">
                    <ul className="space-y-3">
                      {category.articles.map((article) => (
                        <li key={article} className="flex items-center gap-3 text-sm text-foreground hover:text-primary transition-colors cursor-pointer">
                          <ChevronRight className="w-4 h-4 text-muted-foreground" />
                          {article}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        <section className="py-20">
          <div className="container mx-auto max-w-4xl px-6 text-center">
            <Card className="bg-primary/5 border-primary/20 shadow-xl">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto flex w-12 h-12 items-center justify-center bg-primary/10 mb-6" style={{ borderRadius: '24px' }}>
                  <Mail className="w-6 h-6 text-primary" strokeWidth={2} />
                </div>
                <CardTitle className="text-2xl sm:text-3xl font-bold text-foreground leading-tight">
                  Still Need Help?
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center px-8 pb-8">
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed font-medium mb-6">
                  Can't find what you're looking for? Our support team is here to help you succeed.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild variant="default" size="lg">
                    <Link to="/contact">Contact Support</Link>
                  </Button>
                  <Button asChild variant="outline" size="lg">
                    <Link to="/app/community">Join Community</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    </>
  );
};

export default HelpCenterPage;
