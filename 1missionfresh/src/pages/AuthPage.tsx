import AuthForm from "@/components/auth/AuthForm"
import SEOHead from "@/components/common/SEOHead"
import Header from "@/components/layout/Header"
import { useSearchParams } from "react-router-dom"

export default function AuthPage() {
  const [searchParams] = useSearchParams();
  const isSignUp = searchParams.get('tab') === 'signup' || searchParams.get('action') === 'signup';
  return (
    <>
      <SEOHead
        title={`${isSignUp ? 'Join' : 'Sign In'} - Mission Fresh`}
        description="Sign in to Mission Fresh to continue your wellness journey with our premium tools and supportive community."
      />
      {/* CONSISTENT HEADER - Fixed critical issue: same header on every page */}
      <Header />
      
      {/* Auth content with proper spacing below consistent header */}
      <div 
        className="bg-background relative overflow-hidden" 
        style={{
          minHeight: 'calc(100vh - 80px)',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          paddingTop: '2rem',
          paddingBottom: '2rem'
        }}
      >
        {/* Auth form container with proper spacing */}
        <div className="w-full relative" style={{ maxWidth: '480px', padding: '0 1.5rem' }}>
          <div className="text-center" style={{ marginBottom: '2rem' }}>
            {/* Enhanced typography hierarchy */}
            <div>
              <h1 
                className="font-bold text-foreground tracking-tight" 
                style={{
                  fontSize: '2.25rem',
                  lineHeight: '2.5rem',
                  marginBottom: '0.75rem'
                }}
              >
                {isSignUp ? 'Create Your Account' : 'Welcome Back'}
              </h1>
              <p 
                className="text-muted-foreground font-medium" 
                style={{
                  fontSize: '1rem',
                  lineHeight: '1.625',
                  maxWidth: '400px',
                  margin: '0 auto'
                }}
              >
                {isSignUp ? 'Start your journey to a nicotine-free life with our supportive community.' : 'Continue your journey to freedom with personalized tools and insights.'}
              </p>
            </div>
          </div>
          
          {/* Auth form container */}
          <div className="relative">
            <AuthForm />
          </div>
        </div>
      </div>
    </>
  )
}
