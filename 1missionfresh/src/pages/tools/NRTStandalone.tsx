import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, ArrowRight, Heart, Shield, Clock, Zap, Target, Droplets, ExternalLink } from 'lucide-react';
import { cn } from "@/lib/utils";

const NRTStandalone = () => {
  const [activeTab, setActiveTab] = useState("overview");

  const handleValueChange = (newValue: string) => {
    setActiveTab(newValue);
  };

  return (
    <div className="bg-background min-h-screen">
      <div className="container py-12 px-6 sm:px-8 mx-auto max-w-7xl">
        <div className="text-center mb-8">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground leading-tight tracking-tight mb-4">
            Nicotine Replacement Therapy <span className="text-primary font-bold">Guide</span>
          </h1>
          <p className="text-base text-muted-foreground max-w-2xl mx-auto leading-[1.6]">
            Understanding your NRT options to find the right solution for your wellness journey
          </p>
        </div>

        <div className="w-full">
          <div className="flex flex-wrap justify-center gap-3 mb-8 max-w-4xl mx-auto">
            <button 
              onClick={() => handleValueChange("overview")}
              className={cn(
                "py-2 px-4 text-sm font-medium transition-all duration-200",
                activeTab === "overview" ? "bg-primary text-primary-foreground" : "bg-background text-muted-foreground hover:text-primary hover:bg-accent border border-border"
              )}
            >
              Overview
            </button>
            <button 
              onClick={() => handleValueChange("patches")}
              className={cn(
                "py-2 px-4 text-sm font-medium transition-all duration-200",
                activeTab === "patches" ? "bg-primary text-primary-foreground" : "bg-background text-muted-foreground hover:text-primary hover:bg-accent border border-border"
              )}
            >
              Patches
            </button>
            <button 
              onClick={() => handleValueChange("gum")}
              className={cn(
                "py-2 px-4 text-sm font-medium transition-all duration-200",
                activeTab === "gum" ? "bg-primary text-primary-foreground" : "bg-background text-muted-foreground hover:text-primary hover:bg-accent border border-border"
              )}
            >
              Gum
            </button>
            <button 
              onClick={() => handleValueChange("lozenges")}
              className={cn(
                "py-2 px-4 text-sm font-medium transition-all duration-200",
                activeTab === "lozenges" ? "bg-primary text-primary-foreground" : "bg-background text-muted-foreground hover:text-primary hover:bg-accent border border-border"
              )}
            >
              Lozenges
            </button>
            <button 
              onClick={() => handleValueChange("inhalers")}
              className={cn(
                "py-2 px-4 text-sm font-medium transition-all duration-200",
                activeTab === "inhalers" ? "bg-primary text-primary-foreground" : "bg-background text-muted-foreground hover:text-primary hover:bg-accent border border-border"
              )}
            >
              Inhalers
            </button>
            <button 
              onClick={() => handleValueChange("sprays")}
              className={cn(
                "py-2 px-4 text-sm font-medium transition-all duration-200",
                activeTab === "sprays" ? "bg-primary text-primary-foreground" : "bg-background text-muted-foreground hover:text-primary hover:bg-accent border border-border"
              )}
            >
              Sprays
            </button>
          </div>

          {activeTab === "overview" && (
            <div className="mt-8">
              <Card className="bg-card border border-border/40 shadow-sm hover:shadow-md transition-all duration-200" style={{borderRadius: '24px'}}>
                <CardHeader className="pb-8 pt-10 px-10">
                  <CardTitle className="text-3xl sm:text-4xl font-heading tracking-tight flex items-center gap-4">
                    <Target className="w-8 h-8 text-primary shrink-0 drop-shadow-sm" />
                    NRT Options Overview
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-10 px-10 pb-10">
                  <p className="text-lg sm:text-xl text-muted-foreground leading-[1.8] font-medium">
                    Nicotine Replacement Therapy (NRT) helps manage withdrawal symptoms and cravings by providing controlled amounts of nicotine without the harmful chemicals found in cigarettes.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-card border border-border/40 p-6 shadow-sm" style={{borderRadius: '24px'}}>
                      <h3 className="text-xl sm:text-2xl font-bold mb-4 text-foreground flex items-center gap-2">
                        <CheckCircle className="w-6 h-6 text-primary" />
                        Benefits of NRT
                      </h3>
                      <ul className="space-y-3 text-muted-foreground">
                        <li className="flex items-start gap-2">
                          <CheckCircle className="w-5 h-5 text-primary mt-1 shrink-0" />
                          <span>Reduces withdrawal symptoms</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="w-5 h-5 text-primary mt-1 shrink-0" />
                          <span>Doubles your chances of quitting successfully</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="w-5 h-5 text-primary mt-1 shrink-0" />
                          <span>Available without prescription</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="w-5 h-5 text-primary mt-1 shrink-0" />
                          <span>Multiple delivery methods available</span>
                        </li>
                      </ul>
                    </div>
                    <div className="bg-card border border-border/40 p-6 shadow-sm" style={{borderRadius: '24px'}}>
                      <h3 className="text-xl sm:text-2xl font-bold mb-4 text-foreground flex items-center gap-2">
                        <Shield className="w-6 h-6 text-primary" />
                        How NRT Works
                      </h3>
                      <p className="text-muted-foreground leading-[1.8]">
                        NRT provides a controlled dose of nicotine to help manage cravings and withdrawal symptoms. 
                        This allows you to break the behavioral habit of smoking while gradually reducing your nicotine dependence.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {(() => {
            const nrtItems = [
              {
                id: "patches",
                title: "Nicotine Patches",
                icon: <Shield className="w-8 h-8 text-primary shrink-0 drop-shadow-sm" />,
                description: "Transdermal patches provide steady, long-lasting nicotine release throughout the day for consistent craving management.",
                details: {
                  title: "How They Work",
                  content: "Applied to clean, dry skin, patches deliver nicotine through your skin over 16-24 hours. Available in different strengths to match your smoking habits and gradually reduce nicotine dependence."
                }
              },
              {
                id: "gum",
                title: "Nicotine Gum",
                icon: <Zap className="w-8 h-8 text-primary shrink-0 drop-shadow-sm" />,
                description: "Chewing gum that releases nicotine when chewed, providing flexible dosing for immediate craving relief.",
                details: {
                  title: "Usage Tips",
                  content: "Chew slowly until you taste nicotine, then park between cheek and gum. Avoid eating or drinking 15 minutes before and during use for optimal absorption."
                }
              },
              {
                id: "lozenges",
                title: "Nicotine Lozenges",
                icon: <Clock className="w-8 h-8 text-primary shrink-0 drop-shadow-sm" />,
                description: "Lozenges dissolve slowly in your mouth, providing controlled nicotine release for craving management.",
                details: {
                  title: "Benefits",
                  content: "Discreet, easy to use, and provide steady nicotine absorption through the mouth's lining. Perfect for situations where gum isn't appropriate."
                }
              },
              {
                id: "inhalers",
                title: "Nicotine Inhalers",
                icon: <Heart className="w-8 h-8 text-primary shrink-0 drop-shadow-sm" />,
                description: "Inhalers mimic the hand-to-mouth action of smoking while delivering controlled nicotine doses.",
                details: {
                  title: "Ideal For",
                  content: "Those who miss the physical habit of smoking and want to address both nicotine dependence and behavioral patterns. Provides familiar hand-to-mouth action."
                }
              },
              {
                id: "sprays",
                title: "Nicotine Sprays",
                icon: <Droplets className="w-8 h-8 text-primary shrink-0 drop-shadow-sm" />,
                description: "Nasal and mouth sprays provide the fastest nicotine delivery for immediate craving relief.",
                details: {
                  title: "Fast Action",
                  content: "Rapidly absorbed through nasal or oral membranes, providing quick relief for intense cravings. Most effective for heavy smokers needing immediate relief."
                }
              }
            ];

            const currentItem = nrtItems.find(item => item.id === activeTab);
            
            return currentItem ? (
              <div key={currentItem.id} className="mt-12">
                <Card className="bg-card border border-border/40 shadow-sm hover:shadow-md transition-all duration-200" style={{borderRadius: '24px'}}>
                  <CardHeader className="pb-8 pt-10 px-10">
                    <CardTitle className="text-3xl sm:text-4xl font-heading tracking-tight flex items-center gap-4">
                      {currentItem.icon}
                      {currentItem.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-10 px-10 pb-10">
                    <p className="text-lg sm:text-xl text-muted-foreground leading-[1.8] font-medium">
                      {currentItem.description}
                    </p>
                    <div className="bg-card border border-border/40 p-6 shadow-sm" style={{borderRadius: '24px'}}>
                      <h3 className="text-xl sm:text-2xl font-bold mb-6 text-foreground">{currentItem.details.title}</h3>
                      <p className="text-muted-foreground leading-[1.8] text-lg">
                        {currentItem.details.content}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : null
          })()}
        </div>

        <Card className="mt-20 bg-card border border-border shadow-sm hover:shadow-md transition-all duration-300" style={{borderRadius: '24px'}}>
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-foreground mb-6">
              Ready for Comprehensive Support?
            </h2>
            <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
              Mission Fresh offers comprehensive tools to track your progress, manage cravings, and boost your well-being during your wellness journey.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                onClick={() => window.open('https://missionfresh.com/tools/smokeless-directory', '_blank')}
                variant="outline" 
                size="lg" 
                className="w-full sm:w-auto"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Explore Alternatives
              </Button>
              <Button 
                onClick={() => window.open('https://missionfresh.com/auth?tab=signup', '_blank')}
                size="lg" 
                className="w-full sm:w-auto"
              >
                Get Started
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>

        <footer className="mt-16 text-center border-t border-border/40 pt-8">
          <p className="text-muted-foreground text-sm">
            This guide is for informational purposes only. Please consult with a healthcare professional before starting any NRT program.
          </p>
          <p className="text-muted-foreground text-sm mt-2">
            Part of <a href="https://missionfresh.com" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">Mission Fresh</a> - Your wellness journey companion
          </p>
        </footer>
      </div>
    </div>
  );
};

export default NRTStandalone;
