import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import * as TabsPrimitive from "@radix-ui/react-tabs";
import { Button } from '@/components/ui/button';
import { CheckCircle, ArrowRight, Heart, Shield, Clock, Zap, Target, Droplets } from 'lucide-react';
import { Link } from 'react-router-dom';
import { cn } from "@/lib/utils";

const NRTGuide = () => {
  const [activeTab, setActiveTab] = useState("overview");

  const handleValueChange = (newValue: string) => {
  
    setActiveTab(newValue);
  };

  return (
    <div className="bg-background min-h-screen">
      <div className="container py-12 px-6 sm:px-8 mx-auto max-w-7xl">
        <div className="text-center mb-8">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground leading-tight tracking-tight mb-4">
            Nicotine Replacement Therapy <span className="text-primary font-bold">Guide</span>
          </h1>
          <p className="text-base text-muted-foreground max-w-2xl mx-auto leading-[1.6]">
            Understanding your NRT options to find the right solution for your wellness journey
          </p>
        </div>

        <div className="w-full">
          <div className="flex flex-wrap justify-center gap-3 mb-8 max-w-4xl mx-auto">
            <button 
              onClick={() => handleValueChange("overview")}
              className={cn(
                "py-2 px-4 text-sm font-medium transition-all duration-200",
                activeTab === "overview" ? "bg-primary text-primary-foreground" : "bg-background text-muted-foreground hover:text-primary hover:bg-accent border border-border"
              )}
            >
              Overview
            </button>
            <button 
              onClick={() => handleValueChange("patches")}
              className={cn(
                "py-2 px-4 text-sm font-medium transition-all duration-200",
                activeTab === "patches" ? "bg-primary text-primary-foreground" : "bg-background text-muted-foreground hover:text-primary hover:bg-accent border border-border"
              )}
            >
              Patches
            </button>
            <button 
              onClick={() => handleValueChange("gum")}
              className={cn(
                "py-2 px-4 text-sm font-medium transition-all duration-200",
                activeTab === "gum" ? "bg-primary text-primary-foreground" : "bg-background text-muted-foreground hover:text-primary hover:bg-accent border border-border"
              )}
            >
              Gum
            </button>
            <button 
              onClick={() => handleValueChange("lozenges")}
              className={cn(
                "py-2 px-4 text-sm font-medium transition-all duration-200",
                activeTab === "lozenges" ? "bg-primary text-primary-foreground" : "bg-background text-muted-foreground hover:text-primary hover:bg-accent border border-border"
              )}
            >
              Lozenges
            </button>
            <button 
              onClick={() => handleValueChange("inhalers")}
              className={cn(
                "py-2 px-4 text-sm font-medium transition-all duration-200",
                activeTab === "inhalers" ? "bg-primary text-primary-foreground" : "bg-background text-muted-foreground hover:text-primary hover:bg-accent border border-border"
              )}
            >
              Inhalers
            </button>
            <button 
              onClick={() => handleValueChange("sprays")}
              className={cn(
                "py-2 px-4 text-sm font-medium transition-all duration-200",
                activeTab === "sprays" ? "bg-primary text-primary-foreground" : "bg-background text-muted-foreground hover:text-primary hover:bg-accent border border-border"
              )}
            >
              Sprays
            </button>
          </div>

          {activeTab === "overview" && (
            <div className="mt-8">
              <Card className="bg-background border-2 border-border hover:border-primary/20 mb-12 shadow-lg hover:shadow-xl transition-all duration-300" style={{borderRadius: '24px'}}>
                <CardHeader className="p-8">
                  <CardTitle className="text-2xl font-bold text-foreground">What is NRT?</CardTitle>
                </CardHeader>
                <CardContent className="p-8 pt-0 space-y-6">
                  <p className="text-base text-foreground/90 leading-[1.6] font-medium">
                    Nicotine Replacement Therapy (NRT) is a medically-approved way to take nicotine by means other
                    than tobacco. It can help reduce unpleasant withdrawal effects such as mood swings, cravings and anxiety
                    that may occur when you stop smoking or using nicotine products.
                  </p>
                  <p className="text-base text-foreground/90 leading-[1.6] font-medium">
                    NRT is available as skin patches, chewing gum, inhalers, tablets, oral strips, lozenges, nasal and
                    mouth spray. These products provide low, controlled doses of nicotine without the tar, carbon monoxide
                    and other poisonous chemicals present in tobacco smoke.
                  </p>
                </CardContent>
              </Card>

              <div className="grid md:grid-cols-2 gap-8 mb-12 items-start">
                <Card className="bg-card border-2 border-border hover:border-primary/20 shadow-lg hover:shadow-xl transition-all duration-300 h-full flex flex-col" style={{borderRadius: '24px'}}>
                  <CardHeader className="p-8">
                    <CardTitle className="text-xl font-bold flex items-center gap-3">
                      <Heart className="w-6 h-6 text-primary shrink-0" />
                      Benefits of NRT
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="flex-1 p-8 pt-0">
                    <ul className="space-y-4">
                      {[
                        'Relieves physical withdrawal symptoms',
                        'Reduces cravings',
                        'Can double your chances of quitting successfully',
                        'Safe and regulated with consistent dosing',
                        'Allows you to focus on behavioral aspects of quitting',
                        'Available without prescription (in most countries)'
                      ].map(item => (
                        <li key={item} className="flex items-start gap-3">
                          <CheckCircle className="w-5 h-5 text-primary mt-1 shrink-0" />
                          <span className="text-foreground/90 leading-[1.6] font-medium text-base">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                                </Card>

                <Card className="bg-card border-2 border-border hover:border-primary/20 shadow-lg hover:shadow-xl transition-all duration-300 h-full flex flex-col" style={{borderRadius: '24px'}}>
                  <CardHeader className="p-8">
                    <CardTitle className="text-xl font-bold flex items-center gap-3">
                      <Shield className="w-6 h-6 text-primary shrink-0" />
                      Considerations
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="flex-1 p-8 pt-0">
                    <ul className="space-y-4">
                      {[
                        'Different products work better for different people',
                        'May need to try multiple forms to find what works',
                        'Proper usage instructions should be followed',
                        'Some side effects are possible (typically mild)',
                        'Pregnant women should consult healthcare providers',
                        'Combining NRT forms may be more effective'
                      ].map(item => (
                        <li key={item} className="flex items-start gap-3">
                          <CheckCircle className="w-5 h-5 text-primary mt-1 shrink-0" />
                          <span className="text-foreground/90 leading-[1.6] font-medium text-base">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {(() => {
            const nrtItems = [
              {
                id: "patches",
                title: "Nicotine Patches",
                icon: <Clock className="w-8 h-8 text-primary shrink-0 drop-shadow-sm" />,
                description: "Patches provide steady, controlled nicotine release throughout the day, making them ideal for consistent support.",
                details: {
                  title: "How They Work",
                  content: "Applied to clean, dry skin, patches deliver nicotine through the skin over 16-24 hours, providing steady relief from withdrawal symptoms."
                }
              },
            {
              id: "gum",
              title: "Nicotine Gum",
              icon: <Zap className="w-8 h-8 text-primary shrink-0 drop-shadow-sm" />,
              description: "Nicotine gum provides fast-acting relief when cravings strike, giving you control over your dosing.",
              details: {
                title: "How to Use",
                content: "Chew slowly until you taste nicotine, then park between cheek and gum. Repeat the chew-and-park process for 30 minutes."
              }
            },
            {
              id: "lozenges",
              title: "Nicotine Lozenges",
              icon: <Target className="w-8 h-8 text-primary shrink-0 drop-shadow-sm" />,
              description: "Lozenges dissolve slowly in your mouth, providing controlled nicotine release for craving management.",
              details: {
                title: "Benefits",
                content: "Discreet, easy to use, and provide steady nicotine absorption through the mouth's lining."
              }
            },
            {
              id: "inhalers",
              title: "Nicotine Inhalers",
              icon: <Heart className="w-8 h-8 text-primary shrink-0 drop-shadow-sm" />,
              description: "Inhalers mimic the hand-to-mouth action of smoking while delivering controlled nicotine doses.",
              details: {
                title: "Ideal For",
                content: "Those who miss the physical habit of smoking and want to address both nicotine dependence and behavioral patterns."
              }
            },
            {
              id: "sprays",
              title: "Nicotine Sprays",
              icon: <Droplets className="w-8 h-8 text-primary shrink-0 drop-shadow-sm" />,
              description: "Nasal and mouth sprays provide the fastest nicotine delivery for immediate craving relief.",
              details: {
                title: "Fast Action",
                content: "Rapidly absorbed through nasal or oral membranes, providing quick relief for intense cravings."
              }
            }
          ];

          const currentItem = nrtItems.find(item => item.id === activeTab);
          
          return currentItem ? (
            <div key={currentItem.id} className="mt-12">
              <Card className="bg-card border border-border/40 shadow-sm hover:shadow-md transition-all duration-200" style={{borderRadius: '24px'}}>
                <CardHeader className="pb-8 pt-10 px-10">
                  <CardTitle className="text-3xl sm:text-4xl font-heading tracking-tight flex items-center gap-4">
                    {currentItem.icon}
                    {currentItem.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-10 px-10 pb-10">
                  <p className="text-lg sm:text-xl text-muted-foreground leading-[1.8] font-medium">
                    {currentItem.description}
                  </p>
                  <div className="bg-card border border-border/40 p-6 shadow-sm" style={{borderRadius: '24px'}}>
                    <h3 className="text-xl sm:text-2xl font-bold mb-6 text-foreground">{currentItem.details.title}</h3>
                    <p className="text-muted-foreground leading-[1.8] text-lg">
                      {currentItem.details.content}
                    </p>
                  </div>
                </CardContent>
                </Card>
              </div>
            ) : null
          })()}
        </div>

        <Card className="mt-20 bg-card border border-border shadow-sm hover:shadow-md transition-all duration-300" style={{borderRadius: '24px'}}>
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-foreground mb-6">
              Ready for Personalized Support?
            </h2>
            <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
              Mission Fresh offers comprehensive tools to track your progress, manage cravings, and boost your well-being during your wellness journey.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild variant="outline" size="lg" className="w-full sm:w-auto">
                <Link to="/tools/smokeless-directory">Explore Alternatives</Link>
              </Button>
              <Button asChild size="lg" className="w-full sm:w-auto">
                <Link to="/auth?tab=signup" className="flex items-center justify-center gap-2">
                  Get Started
                  <ArrowRight className="w-5 h-5" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default NRTGuide;
