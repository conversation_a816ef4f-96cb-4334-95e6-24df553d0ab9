import { useState, useCallback } from "react";
// Removed Tabs import - using custom tab implementation
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { DollarSign, Heart, Calendar, Loader2, ArrowLeft, PiggyBank, TrendingUp, Clock, Award, AlertCircle, CheckCircle } from "lucide-react";
import { format, addDays, addWeeks, addMonths, addYears, parseISO, isFuture, differenceInDays, differenceInWeeks, differenceInMonths, differenceInYears } from "date-fns";
import { Link } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { calculateHealthMilestones, CalculatedHealthMilestone } from "@/services/healthMilestoneService";

// PRODUCTION HEALTH APP: All medical data sourced from database with proper medical sourcing
// PRODUCTION READY: Medical information sourced from database with proper medical citations

// PRODUCTION HEALTH APP: Function removed - now using database service calculateHealthMilestones

// PRODUCTION HEALTH APP: Using CalculatedHealthMilestone interface from database service

const fadeIn = {
  hidden: { opacity: 0, y: 10 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
  exit: { opacity: 0, transition: { duration: 0.2 } }
};

const stagger = {
  visible: { transition: { staggerChildren: 0.1 } }
};

const CalculatorsPage = () => {
  const [activeTab, setActiveTab] = useState("savings");
  
  const [cost, setCost] = useState<number | string>("");
  const [dailyAmount, setDailyAmount] = useState<number | string>("");
  const [savings, setSavings] = useState<{ daily: number; weekly: number; monthly: number; yearly: number; fiveYear: number; } | null>(null);
  const [savingsError, setSavingsError] = useState<string | null>(null);
  const [quitDate, setQuitDate] = useState<string>("");
  
  const [healthMilestones, setHealthMilestones] = useState<CalculatedHealthMilestone[]>([]);
  const [calculatingHealth, setCalculatingHealth] = useState(false);
  const [healthError, setHealthError] = useState<string | null>(null);

  const handleTabChange = (value: string) => {

    setActiveTab(value);
    // Clear any existing errors when switching tabs
    if (value === 'savings') {
      setSavingsError(null);
    } else if (value === 'health') {
      setHealthError(null);
    }
  };

  const calculateSavings = () => {
    setSavingsError(null);
    const costPerItem = parseFloat(cost as string);
    const itemsPerDay = parseFloat(dailyAmount as string);
    
    if (isNaN(costPerItem) || costPerItem < 0) {
      setSavingsError("Please enter a valid positive number for cost.");
      setSavings(null);
      return;
    }
    
    if (isNaN(itemsPerDay) || itemsPerDay < 0) {
      setSavingsError("Please enter a valid positive number for daily usage.");
      setSavings(null);
      return;
    }
    
    const daily = costPerItem * itemsPerDay;
    const calculatedSavings = {
      daily,
      weekly: daily * 7,
      monthly: daily * (365.25 / 12),
      yearly: daily * 365.25,
      fiveYear: daily * 365.25 * 5
    };
    
    setSavings(calculatedSavings);
  };

  const calculateHealth = async () => {
    try {
  
      setHealthError(null);
      if (!quitDate) {
        setHealthError("Please select a quit date.");
        setHealthMilestones([]);
        return;
      }
      
      // Handle both ISO format (YYYY-MM-DD) and potential other formats
      let date;
      try {
        date = parseISO(quitDate);
        // If parseISO fails, try creating date directly
        if (isNaN(date.getTime())) {
          date = new Date(quitDate);
        }
      } catch {
        date = new Date(quitDate);
      }
      
      
      
      // Validate the parsed date
      if (isNaN(date.getTime())) {
        setHealthError("Please enter a valid date.");
        setHealthMilestones([]);
        return;
      }
      
      const today = new Date();
      today.setHours(23, 59, 59, 999);
      if (date > today) {
        setHealthError("Quit date cannot be in the future.");
        setHealthMilestones([]);
        return;
      }
      
      
      setCalculatingHealth(true); 
      setHealthMilestones([]);
      
      // Calculate full health timeline from database
      const fullTimeline = await calculateHealthMilestones(date);
      setHealthMilestones(fullTimeline); 
      setCalculatingHealth(false); 
    } catch (error) {
      console.error('Health timeline calculation error:', error);
      setHealthError("An error occurred while calculating the timeline.");
      setHealthMilestones([]);
      setCalculatingHealth(false);
    }
  };

  const formatTimeDifference = useCallback((date: Date) => {
    try {
      const now = new Date();
      if (isFuture(date)) {
          const diffDays = differenceInDays(date, now);
          if (diffDays > 365) return `in ${differenceInYears(date, now)} year(s)`;
          if (diffDays > 30) return `in ${differenceInMonths(date, now)} month(s)`;
          if (diffDays > 7) return `in ${differenceInWeeks(date, now)} week(s)`;
          if (diffDays > 0) return `in ${diffDays} day(s)`;
          return "soon";
      } else {
          const diffDays = differenceInDays(now, date);
          if (diffDays > 365) return `${differenceInYears(now, date)} year(s) ago`;
          if (diffDays > 30) return `${differenceInMonths(now, date)} month(s) ago`;
          if (diffDays > 7) return `${differenceInWeeks(now, date)} week(s) ago`;
          if (diffDays > 0) return `${diffDays} day(s) ago`;
          return "just happened";
      }
    } catch (error) {
      console.error('Error in formatTimeDifference:', error);
      return "unknown";
    }
  }, []);

  return (
    <div className="bg-background">
      <div className="container py-12 px-6 mx-auto max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground leading-tight tracking-tight mb-4">Interactive Calculators</h1>
          <p className="text-base text-muted-foreground max-w-2xl mx-auto leading-[1.6]">
            Calculate your potential savings and view your health improvement timeline
          </p>
        </div>

        <div className="w-full">
          <div className="grid w-full grid-cols-2 mb-8 bg-card p-1 max-w-sm mx-auto border border-border" style={{borderRadius: '32px'}}>
            <button 
              onClick={() => handleTabChange("savings")}
              className={`py-2 px-3 text-sm font-medium transition-all duration-200 ${
                activeTab === "savings" 
                  ? "bg-primary text-primary-foreground" 
                  : "text-muted-foreground hover:text-foreground hover:bg-accent"
              }`}
            >
              Savings Calculator
            </button>
            <button 
              onClick={() => handleTabChange("health")}
              className={`py-2 px-3 text-sm font-medium transition-all duration-200 ${
                activeTab === "health" 
                  ? "bg-primary text-primary-foreground" 
                  : "text-muted-foreground hover:text-foreground hover:bg-accent"
              }`}
            >
              Health Timeline
            </button>
          </div>

          {activeTab === "savings" && (
            <div className="space-y-6">
              <Card className="border border-border bg-card max-w-lg mx-auto" style={{borderRadius: '48px'}}>
                <CardHeader className="pb-3 pt-4 px-4">
                  <CardTitle className="text-lg font-bold">Savings Calculator</CardTitle>
                  <CardDescription className="text-sm text-muted-foreground">Calculate how much money you'll save by staying afresh</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3 px-4 pb-4">
                  <div className="grid gap-3 w-full">
                    <div className="space-y-1">
                      <Label htmlFor="cost" className="text-sm font-medium">Cost per pack/vape/pouch ($)</Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">$</span>
                        <Input id="cost" type="number" placeholder="0.00" className="pl-7 h-9 border border-border focus:border-primary/50 text-sm" style={{borderRadius: '32px'}} value={cost} onChange={(e) => setCost(e.target.value)} min={0} step="0.01" />
                      </div>
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="amount" className="text-sm font-medium">Daily usage (amount)</Label>
                      <Input id="amount" type="number" placeholder="0" className="h-9 px-3 border border-border focus:border-primary/50 text-sm" style={{borderRadius: '32px'}} value={dailyAmount} onChange={(e) => setDailyAmount(e.target.value)} min={0} />
                    </div>
                    {savingsError && (
                      <Alert variant="destructive" className="text-sm">
                        <AlertCircle className="icon-sm" />
                        <AlertDescription>{savingsError}</AlertDescription>
                      </Alert>
                    )}
                    <Button 
                      variant="default" 
                      className="w-auto mx-auto block px-8 py-3 text-base font-bold shadow-xl shadow-primary/25 hover:shadow-2xl hover:shadow-primary/30 hover:scale-[1.02] transition-all duration-300" style={{borderRadius: '32px'}} 
                      size="lg" 
                      onClick={calculateSavings} 
                      disabled={cost === "" || dailyAmount === ""}
                    >
                      Calculate Savings
                    </Button>
                  </div>
                  {savings && (
                    <div className="mt-12">
                      <h3 className="text-xl font-semibold mb-4 font-heading">Your Potential Savings</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-3 w-full">
                        <Card className="bg-primary text-primary-foreground">
                          <CardHeader className="pb-1 flex flex-row items-center justify-between space-y-0">
                            <CardTitle className="text-sm font-medium">Daily</CardTitle>
                            <Clock className="icon-sm text-primary-foreground" />
                          </CardHeader>
                          <CardContent className="pt-1 px-3">
                            <p className="text-xl font-bold">${savings.daily.toFixed(2)}</p>
                          </CardContent>
                        </Card>
                        <Card className="bg-primary text-primary-foreground">
                          <CardHeader className="pb-1 flex flex-row items-center justify-between space-y-0">
                            <CardTitle className="text-sm font-medium">Weekly</CardTitle>
                            <Clock className="icon-sm text-primary-foreground" />
                          </CardHeader>
                          <CardContent className="pt-1 px-3">
                            <p className="text-xl font-bold">${savings.weekly.toFixed(2)}</p>
                          </CardContent>
                        </Card>
                        <Card className="bg-primary text-primary-foreground">
                          <CardHeader className="pb-1 flex flex-row items-center justify-between space-y-0">
                            <CardTitle className="text-sm font-medium">Monthly</CardTitle>
                            <PiggyBank className="icon-sm text-primary-foreground" />
                          </CardHeader>
                          <CardContent className="pt-1 px-3">
                            <p className="text-xl font-bold">${savings.monthly.toFixed(2)}</p>
                          </CardContent>
                        </Card>
                        <Card className="bg-primary text-primary-foreground">
                          <CardHeader className="pb-1 flex flex-row items-center justify-between space-y-0">
                            <CardTitle className="text-sm font-medium">Yearly</CardTitle>
                            <TrendingUp className="icon-sm text-primary-foreground" />
                          </CardHeader>
                          <CardContent className="pt-1 px-3">
                            <p className="text-xl font-bold">${savings.yearly.toFixed(2)}</p>
                          </CardContent>
                        </Card>
                        <Card className="bg-primary text-primary-foreground">
                          <CardHeader className="pb-1 flex flex-row items-center justify-between space-y-0">
                            <CardTitle className="text-sm font-medium">5 Years</CardTitle>
                            <Award className="icon-sm text-primary-foreground" />
                          </CardHeader>
                          <CardContent className="pt-1 px-3">
                            <p className="text-xl font-bold">${savings.fiveYear.toFixed(2)}</p>
                          </CardContent>
                        </Card>
                      </div>
                      <p className="text-base text-muted-foreground mt-6 !leading-relaxed">Imagine what you could do with these savings! A vacation, new gadget, home improvement, or financial security.</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === "health" && (
            <div className="space-y-8">
              <Card className="border border-border bg-card shadow-elegant max-w-2xl mx-auto" style={{borderRadius: '48px'}}>
                <CardHeader className="pb-6">
                  <CardTitle className="text-2xl font-bold font-heading tracking-tight">Health Improvement Timeline</CardTitle>
                  <CardDescription className="text-lg !leading-relaxed text-foreground/70">See how your body recovers after staying afresh</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 max-w-md mx-auto">
                    <div className="space-y-3">
                      <Label htmlFor="quit-date" className="text-base font-semibold">Your quit date</Label>
                      <Input 
                        id="quit-date" 
                        type="date" 
                        className="h-10 px-4 border-2 border-border/30 focus:border-primary/50 focus:ring-2 focus:ring-primary/10 text-base" style={{borderRadius: '24px'}} 
                        value={quitDate} 
                        onChange={(e) => {
                    
                          setQuitDate(e.target.value);
                        }} 
                        max={new Date().toISOString().split('T')[0]}
                      />
                    </div>
                    {healthError && (
                      <Alert variant="destructive" className="text-sm" style={{borderRadius: '32px'}}><AlertCircle className="h-4 w-4" /><AlertDescription>{healthError}</AlertDescription></Alert>
                    )}
                    <div className="flex gap-4">
                      <Button variant="default" className="w-auto mx-auto block px-8 py-3 text-base font-bold shadow-xl shadow-primary/25 hover:shadow-2xl hover:shadow-primary/30 hover:scale-[1.02] transition-all duration-300" style={{borderRadius: '32px'}} size="lg" onClick={calculateHealth} disabled={!quitDate}>
                        {calculatingHealth ? (<><Loader2 className="mr-3 h-4 w-4 animate-spin" />Calculating...</>) : ("Generate Timeline")}
                      </Button>
                      {/* Test function removed - using production database service */}
                    </div>
                  </div>
                  {healthMilestones.length > 0 && (
                    <div className="mt-10">
                      <h3 className="text-xl font-semibold mb-6 font-heading">Your Health Recovery Timeline</h3>
                      <div className="space-y-4">
                        {healthMilestones.map((milestone, index) => (
                          <div key={index} className="p-4 border bg-secondary border-border" style={{borderRadius: '24px'}}>
                            <h4 className="font-semibold text-primary">{milestone.time_display}</h4>
                            <p className="text-base mt-1.5 text-foreground">{milestone.description}</p>
                            <p className="text-sm text-muted-foreground mt-2">
                              {format(new Date(milestone.date), 'MMM d, yyyy')} • {milestone.achieved ? 'Achieved' : 'Upcoming'}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        <div className="mt-12 text-center">
          <Link to="/tools">
            <Button variant="outline" className="flex items-center text-primary hover:bg-secondary border-primary px-6 py-2 text-base font-medium transition-all duration-300 hover:scale-[1.02]" style={{borderRadius: '32px'}}>
              <ArrowLeft className="mr-1 icon-base" />
              Back to All Tools
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default CalculatorsPage;
