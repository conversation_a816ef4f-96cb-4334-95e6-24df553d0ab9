import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Star, ShoppingCart, Heart, Share2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { getProductById, type ProductWithVendors } from '@/services/productService';
import { cartService } from '@/services/cartService';
import { useToast } from '@/components/ui/use-toast';

const ProductDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [product, setProduct] = useState<ProductWithVendors | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddingToCart, setIsAddingToCart] = useState(false);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        if (id) {
          const foundProduct = await getProductById(id);
          if (foundProduct) {
            setProduct(foundProduct);
          } else {
            setError('Product not found');
          }
        } else {
          setError('Invalid product ID');
        }
      } catch (err) {
        setError('Failed to load product details');
        console.error('Error fetching product:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center space-x-4 mb-8">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/tools/smokeless-directory')}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Directory</span>
            </Button>
          </div>
          <div className="flex items-center justify-center py-20">
            <div className="animate-spin h-12 w-12 border-b-2 border-primary" style={{borderRadius: '50%'}}></div>
            <p className="ml-4 text-lg">Loading product details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center space-x-4 mb-8">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/tools/smokeless-directory')}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Directory</span>
            </Button>
          </div>
          <div className="text-center py-20">
            <h2 className="text-2xl font-bold text-destructive mb-4">
              {error || 'Product not found'}
            </h2>
            <p className="text-muted-foreground mb-6">
              The product you're looking for doesn't exist or couldn't be loaded.
            </p>
            <Button onClick={() => navigate('/tools/smokeless-directory')}>
              Return to Directory
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const handleAddToCart = async () => {
    if (!product) return;
    
    try {
      setIsAddingToCart(true);
      
      // Get the first available vendor and price
      const availableVendor = product.smokeless_product_vendors?.find(v => v.is_available);
      if (!availableVendor) {
        toast({
          title: "Product unavailable",
          description: "This product is currently out of stock from all vendors.",
          variant: "destructive"
        });
        return;
      }

      await cartService.addToCart({
        productId: product.id,
        quantity: 1,
        price: availableVendor.price || 0,
        vendorId: availableVendor.smokeless_vendors?.id
      });

      toast({
        title: "Added to cart",
        description: `${product.name} has been added to your cart.`,
      });
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add item to cart",
        variant: "destructive"
      });
    } finally {
      setIsAddingToCart(false);
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-6 w-6 ${
                                i < Math.floor(rating) ? 'fill-primary text-primary drop-shadow-sm' : 'text-muted-foreground'
        }`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center space-x-4 mb-8">
          <Button
            variant="ghost"
            size="lg"
            onClick={() => navigate('/tools/smokeless-directory')}
            className="flex items-center space-x-3 hover:bg-accent px-6 py-3 shadow-lg hover:shadow-xl transition-all duration-300 border border-border/20" style={{borderRadius: '32px'}}
          >
            <ArrowLeft className="h-5 w-5" />
            <span className="font-semibold">Back to Directory</span>
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-4">
                            <div className="aspect-square bg-accent shadow-elegant flex items-center justify-center border border-border" style={{borderRadius: '48px'}}>
              {product.image_url ? (
                <img
                  src={product.image_url}
                  alt={product.name}
                  className="max-w-full max-h-full object-contain shadow-xl" style={{borderRadius: '48px'}}
                  onError={(e) => {
                    // Remove broken image instead of showing placeholder
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                />
              ) : null}
              
              {(!product.image_url || product.image_url === '') && (
                <div className="text-muted-foreground text-center">
                  <div className="text-6xl mb-2">📦</div>
                  <p>No image available</p>
                </div>
              )}
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <Badge variant="secondary" className="mb-4 px-4 py-2 text-sm font-semibold bg-primary/10 text-primary border-primary/20" style={{borderRadius: '32px'}}>
                {product.category}
              </Badge>
              <h1 className="text-4xl lg:text-5xl font-black text-foreground mb-3 leading-tight">
                {product.name}
              </h1>
              <p className="text-xl font-semibold text-muted-foreground mb-6">
                by <span className="text-primary font-bold">{product.brand}</span>
              </p>
              
              <div className="flex items-center space-x-3 mb-6">
                <div className="flex items-center space-x-1">
                  {renderStars(product.user_rating_avg || 0)}
                </div>
                <span className="text-lg font-semibold text-foreground">
                  {(product.user_rating_avg || 0).toFixed(1)}
                </span>
                <span className="text-base text-muted-foreground">
                  ({product.user_rating_count || 0} reviews)
                </span>
              </div>

              {product.smokeless_product_vendors && product.smokeless_product_vendors.length > 0 && (
                <div className="flex items-center space-x-6 mb-8">
                  {product.smokeless_product_vendors.map((vendorLink, index) => 
                    vendorLink.smokeless_vendors && (
                      <div key={index} className="flex items-center space-x-3">
                        <span className="text-3xl font-black text-primary drop-shadow-sm">
                          ${vendorLink.price || 'N/A'}
                        </span>
                        <Badge 
                          variant={vendorLink.is_available ? "default" : "destructive"}
                          className="px-3 py-1 text-xs font-bold shadow-lg" style={{borderRadius: '24px'}}
                        >
                          {vendorLink.is_available ? "In Stock" : "Out of Stock"}
                        </Badge>
                        <span className="text-base font-medium text-muted-foreground">
                          at <span className="text-foreground font-semibold">{vendorLink.smokeless_vendors.name}</span>
                        </span>
                      </div>
                    )
                  )}
                </div>
              )}
            </div>

            <div className="flex space-x-4">
              <Button 
                className="flex-1 flex items-center space-x-3 h-14 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300" style={{borderRadius: '32px'}}
                disabled={!product.smokeless_product_vendors?.some((v: any) => v.is_available) || isAddingToCart}
                onClick={handleAddToCart}
              >
                <ShoppingCart className="h-5 w-5" />
                <span>{isAddingToCart ? 'Adding...' : 'Add to Cart'}</span>
              </Button>
              <Button variant="outline" size="icon" className="h-14 w-14 shadow-lg hover:shadow-xl transition-all duration-300 border-2" style={{borderRadius: '32px'}}>
                <Heart className="h-6 w-6" />
              </Button>
              <Button variant="outline" size="icon" className="h-14 w-14 shadow-lg hover:shadow-xl transition-all duration-300 border-2" style={{borderRadius: '32px'}}>
                <Share2 className="h-6 w-6" />
              </Button>
            </div>

            <Separator />

            <div>
              <h3 className="text-2xl font-bold mb-4 text-foreground">Description</h3>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {product.description || 'No description available.'}
              </p>
            </div>

            {product.flavors && product.flavors.length > 0 && (
              <div>
                <h3 className="text-2xl font-bold mb-4 text-foreground">Available Flavors</h3>
                <div className="flex flex-wrap gap-3">
                  {product.flavors.map((flavor, index) => (
                    <Badge key={index} className="px-4 py-2 text-sm font-semibold bg-accent text-accent-foreground border-accent hover:bg-accent/80 transition-colors" style={{borderRadius: '32px'}}>
                      {flavor}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {product.nicotine_strengths && (
              <div>
                <h3 className="text-2xl font-bold mb-4 text-foreground">Nicotine Strengths</h3>
                <div className="flex flex-wrap gap-3">
                  {Array.isArray(product.nicotine_strengths) ? 
                    product.nicotine_strengths.map((strength: any, index: number) => (
                      <Badge key={index} className="px-4 py-2 text-sm font-semibold bg-primary/10 text-primary border-primary/20 hover:bg-primary/20 transition-colors" style={{borderRadius: '32px'}}>
                        {typeof strength === 'object' && strength.value && strength.unit 
                          ? `${strength.value}${strength.unit}` 
                          : String(strength)}
                      </Badge>
                    )) : (
                      <Badge className="px-4 py-2 text-sm font-semibold bg-primary/10 text-primary border-primary/20 hover:bg-primary/20 transition-colors" style={{borderRadius: '32px'}}>{String(product.nicotine_strengths)}</Badge>
                    )
                  }
                </div>
              </div>
            )}
          </div>
        </div>

                  <Card className="mt-12 shadow-elegant border border-border bg-background" style={{borderRadius: '48px'}}>
          <CardHeader className="pb-6">
            <CardTitle className="text-3xl font-bold text-foreground">Product Information</CardTitle>
            <CardDescription className="text-lg text-muted-foreground">
              Additional details about {product.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {product.manufacturer && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">Manufacturer:</span>
                  <span className="text-muted-foreground">{product.manufacturer}</span>
                </div>
              )}
              {product.country_of_origin && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-medium">Country of Origin:</span>
                  <span className="text-muted-foreground">{product.country_of_origin}</span>
                </div>
              )}
              {product.is_verified && (
                <div className="flex justify-between py-3 border-b">
                  <span className="font-semibold text-base">Verification Status:</span>
                  <Badge className="px-4 py-2 text-sm font-bold bg-primary/10 text-primary border-primary/20 shadow-lg" style={{borderRadius: '32px'}}>
                    ✓ Verified Product
                  </Badge>
                </div>
              )}
              {product.ingredients && product.ingredients.length > 0 && (
                <div className="col-span-full">
                  <h4 className="font-medium mb-2">Ingredients:</h4>
                  <p className="text-muted-foreground text-sm">
                    {Array.isArray(product.ingredients) ? product.ingredients.join(', ') : String(product.ingredients)}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {product.smokeless_product_vendors && product.smokeless_product_vendors.length > 0 && (
          <Card className="mt-12 shadow-elegant border border-border bg-background" style={{borderRadius: '48px'}}>
            <CardHeader className="pb-6">
              <CardTitle className="text-3xl font-bold text-foreground">Where to Buy</CardTitle>
              <CardDescription className="text-lg text-muted-foreground">
                Trusted vendors offering this product
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {product.smokeless_product_vendors.map((vendorLink, index) => 
                  vendorLink.smokeless_vendors && (
                    <div key={index} className="flex items-center justify-between p-6 border-2 shadow-xl hover:shadow-2xl transition-all duration-300 bg-accent/20" style={{borderRadius: '32px'}}>
                      <div className="flex items-center space-x-6">
                        <div>
                          <h4 className="font-bold text-xl text-foreground">{vendorLink.smokeless_vendors.name}</h4>
                          <p className="text-base text-muted-foreground mt-1">
                            {vendorLink.smokeless_vendors.description}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-6">
                        <div className="text-right">
                          <p className="font-black text-2xl text-primary">${vendorLink.price || 'N/A'}</p>
                          <Badge 
                            variant={vendorLink.is_available ? "default" : "destructive"}
                            className="mt-2 px-3 py-1 text-xs font-bold shadow-lg" style={{borderRadius: '24px'}}
                          >
                            {vendorLink.is_available ? "Available" : "Out of Stock"}
                          </Badge>
                        </div>
                        {vendorLink.product_url_on_vendor_site && (
                          <Button
                            onClick={() => vendorLink.product_url_on_vendor_site && window.open(vendorLink.product_url_on_vendor_site, '_blank')}
                            disabled={!vendorLink.is_available}
                            className="h-12 px-6 text-base font-semibold shadow-xl hover:shadow-2xl transition-all duration-300" style={{borderRadius: '32px'}}
                          >
                            Buy Now
                          </Button>
                        )}
                      </div>
                    </div>
                  )
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ProductDetails;
