import React, { useState, useMemo, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Star, Search, MapPin, Shield, Award, Loader2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import { getProductsWithVendors, getVendors, type Vendor, type ProductWithVendors } from '@/services/productService';

const SmokelessDirectory: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('products');
  const [products, setProducts] = useState<ProductWithVendors[]>([]);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [productsData, vendorsData] = await Promise.all([
          getProductsWithVendors(),
          getVendors()
        ]);
        setProducts(productsData || []);
        setVendors(vendorsData || []);
      } catch (err) {
        console.error('Error fetching directory data:', err);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const filteredProducts = useMemo(() => {
    if (!searchTerm) return products;
    return products.filter(product => 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (product.brand && product.brand.toLowerCase().includes(searchTerm.toLowerCase())) ||
      product.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [products, searchTerm]);

  const filteredVendors = useMemo(() => {
    if (!searchTerm) return vendors;
    return vendors.filter(vendor => 
      vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (vendor.description && vendor.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [vendors, searchTerm]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="icon-xl animate-spin text-primary mx-auto mb-4" />
          <p className="text-muted-foreground">Loading directory...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-6 sm:px-8 max-w-7xl py-12">
        <div className="text-center mb-8">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground leading-tight tracking-tight mb-4">
            Smokeless Products <span className="text-primary font-bold">Directory</span>
          </h1>
          <p className="text-base text-muted-foreground max-w-2xl mx-auto leading-[1.6]">
            Discover quality smokeless tobacco alternatives and trusted vendors to support your wellness journey.
          </p>
        </div>

        <div className="mb-8">
          <div className="relative max-w-xl mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search products or vendors..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-10 text-sm bg-background border border-border hover:border-primary/50 transition-all duration-200" style={{borderRadius: '32px'}}
            />
          </div>
        </div>

        <div className="w-full">
          <div className="flex flex-wrap justify-center gap-3 mb-8 max-w-2xl mx-auto">
            <button
              onClick={() => setActiveTab('products')}
              className={`inline-flex items-center justify-center gap-2 py-2 px-4 text-sm font-medium transition-all duration-200 ${
                activeTab === 'products'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-background text-muted-foreground hover:text-foreground hover:bg-accent border border-border'
              }`}
              style={{borderRadius: '32px'}}
            >
              <Award className="w-4 h-4" />
              Products ({filteredProducts.length})
            </button>
            <button
              onClick={() => setActiveTab('vendors')}
              className={`inline-flex items-center justify-center gap-2 py-2 px-4 text-sm font-medium transition-all duration-200 ${
                activeTab === 'vendors'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-background text-muted-foreground hover:text-foreground hover:bg-accent border border-border'
              }`}
              style={{borderRadius: '32px'}}
            >
              <MapPin className="w-4 h-4" />
              Vendors ({filteredVendors.length})
            </button>
          </div>

          {/* PRODUCTS TAB CONTENT */}
          {activeTab === 'products' && (
            <div className="space-y-6">
            {filteredProducts.length > 0 ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {filteredProducts.map((product) => (
                  <Card key={product.id} className="h-full hover:shadow-xl transition-all duration-300 border-2 border-border hover:border-primary-muted bg-card" style={{borderRadius: '24px'}}>
                    <CardHeader className="pb-3 pt-4 px-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1 flex-1">
                          <CardTitle className="text-lg font-bold text-primary leading-tight">{product.name}</CardTitle>
                          <CardDescription className="text-sm font-medium text-muted-foreground">{product.brand}</CardDescription>
                        </div>
                        <Badge variant="secondary" className="ml-2 shrink-0 px-2 py-1 text-xs font-medium bg-primary text-primary-foreground" style={{borderRadius: '16px'}}>
                          {product.category}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-3 px-4 pb-4">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`w-4 h-4 ${
                                i < Math.floor(product.user_rating_avg || 0)
                                  ? 'fill-primary text-primary'
                                  : 'text-muted-foreground/30'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-sm font-bold text-foreground">{(product.user_rating_avg || 0).toFixed(1)}</span>
                        <span className="text-xs text-muted-foreground">({product.user_rating_count || 0} reviews)</span>
                      </div>
                      
                      <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">{product.description}</p>
                      
                      <div className="flex items-center justify-between pt-3">
                        <div className="text-sm">
                          {product.smokeless_product_vendors && product.smokeless_product_vendors.length > 0 ? (
                            <div className="flex flex-col gap-1">
                              {product.smokeless_product_vendors
                                .filter((pv: any) => pv.is_available && pv.price)
                                .slice(0, 1)
                                .map((pv: any, index: number) => (
                                  <div key={index} className="flex items-center gap-1">
                                    <span className="font-bold text-primary text-lg">${pv.price}</span>
                                    <span className="text-xs text-muted-foreground">
                                      at <span className="text-foreground font-medium">{pv.smokeless_vendors?.name}</span>
                                    </span>
                                  </div>
                                ))}
                              {product.smokeless_product_vendors.filter((pv: any) => pv.is_available && pv.price).length > 1 && (
                                <span className="text-xs text-muted-foreground">
                                  +{product.smokeless_product_vendors.filter((pv: any) => pv.is_available && pv.price).length - 1} more
                                </span>
                              )}
                            </div>
                          ) : (
                            <span className="text-muted-foreground text-xs">Available</span>
                          )}
                        </div>
                        <Link to={`/tools/smokeless-directory/product/${product.id}`}>
                          <Button size="sm" className="bg-primary hover:bg-primary-hover text-primary-foreground font-medium px-3 py-2 text-xs transition-all duration-200" style={{borderRadius: '32px'}}>
                            View Details
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Award className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-muted-foreground mb-2">
                  {searchTerm ? 'No products found' : 'No products available'}
                </h3>
                <p className="text-muted-foreground">
                  {searchTerm ? 'Try adjusting your search terms.' : 'Products will be added soon.'}
                </p>
              </div>
            )}
            </div>
          )}

          {/* VENDORS TAB CONTENT */}
          {activeTab === 'vendors' && (
            <div className="space-y-6">
            {filteredVendors.length > 0 ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {filteredVendors.map((vendor) => (
                  <Card key={vendor.id} className="h-full hover:shadow-lg transition-all duration-300 border-border/50 bg-card">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1 flex-1">
                          <CardTitle className="text-lg font-semibold text-primary">{vendor.name}</CardTitle>
                          {vendor.website_url && (
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <MapPin className="icon-xs" />
                              Online Store
                            </div>
                          )}
                        </div>
                        <Badge className="ml-2 shrink-0 bg-card border-2 border-primary text-primary">
                          <Shield className="icon-xs mr-1" />
                          Verified
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`icon-base ${
                                i < Math.floor(vendor.user_rating_avg || 4.5)
                                  ? 'fill-primary text-primary drop-shadow-sm'
                                  : 'text-muted-foreground'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-xl font-black text-foreground">{(vendor.user_rating_avg || 4.5).toFixed(1)}</span>
                        <span className="text-sm text-muted-foreground">({vendor.user_rating_count || 0} reviews)</span>
                      </div>
                      
                      <p className="text-sm text-muted-foreground line-clamp-2">{vendor.description}</p>
                      
                      <div className="flex gap-2 pt-2">
                        <Button size="sm" variant="outline" className="flex-1">
                          Contact
                        </Button>
                        {vendor.website_url && (
                          <Button 
                            size="sm" 
                            className="flex-1 bg-primary hover:bg-primary"
                            onClick={() => vendor.website_url && window.open(vendor.website_url, '_blank')}
                          >
                            Visit Store
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-muted-foreground mb-2">
                  {searchTerm ? 'No vendors found' : 'No vendors available'}
                </h3>
                <p className="text-muted-foreground">
                  {searchTerm ? 'Try adjusting your search terms.' : 'Vendors will be added soon.'}
                </p>
              </div>
            )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SmokelessDirectory;
