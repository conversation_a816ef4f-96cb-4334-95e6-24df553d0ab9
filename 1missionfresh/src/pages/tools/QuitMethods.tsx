import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON> } from 'react-helmet-async';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckCircle, Clock, TrendingUp, Users, AlertCircle } from 'lucide-react';
import { Link } from 'react-router-dom';

interface QuitMethod {
  id: string;
  title: string;
  description: string;
  best_for: string[];
  considerations: string[];
  success_rate: string;
  timeframe: string;
  difficulty: 'Easy' | 'Moderate' | 'Hard' | 'Very Hard';
  cta_text?: string;
  cta_link?: string;
  sort_order: number;
}

const QuitMethods: React.FC = () => {
  const [quitMethods, setQuitMethods] = useState<QuitMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchQuitMethods();
  }, []);

  const fetchQuitMethods = async () => {
    try {
      console.log('🔍 Attempting to fetch quit methods...');
      const { data, error } = await supabase
        .from('quit_methods')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      console.log('📊 Supabase response:', { data, error });
      
      if (error) {
        console.error('❌ Supabase error:', error);
        throw error;
      }
      
      setQuitMethods(data || []);
      console.log('✅ Successfully loaded quit methods:', data?.length || 0);
    } catch (err) {
      console.error('💥 Error fetching quit methods:', err);
      setError(`Failed to load quit methods: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-secondary text-primary border border-primary';
      case 'Moderate': return 'bg-primary text-primary-foreground border border-primary';
      case 'Hard': return 'bg-primary text-primary-foreground border border-primary';
      case 'Very Hard': return 'bg-primary text-primary-foreground border border-primary';
      default: return 'bg-primary text-primary-foreground border border-primary';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin h-12 w-12 border-b-2 border-primary mx-auto" style={{borderRadius: '50%'}}></div>
          <p className="mt-4 text-muted-foreground">Loading quit methods...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <p className="text-destructive">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Quit Methods - Mission Fresh</title>
        <meta name="description" content="Explore different evidence-based methods to quit tobacco and nicotine use. Find the approach that works best for you." />
      </Helmet>
      
      <div className="container mx-auto px-6 py-12 max-w-7xl">
        <div className="text-center mb-8">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground leading-tight tracking-tight mb-4">
            Quit Methods
          </h1>
          <p className="text-base text-muted-foreground max-w-2xl mx-auto leading-[1.6]">
            Discover evidence-based approaches to quitting tobacco and nicotine use. 
            Each method has different strengths - find the one that aligns with your goals and lifestyle.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 items-start">
          {quitMethods.map((method) => (
            <Card key={method.id} className="h-full flex flex-col hover:shadow-md transition-all duration-200 border border-border hover:border-primary/50 bg-background" style={{borderRadius: '32px'}}>
              <CardHeader className="pb-3 pt-4 px-4">
                <div className="flex items-center justify-between mb-2">
                  <CardTitle className="text-lg font-bold text-foreground">{method.title}</CardTitle>
                  <Badge className={`px-2 py-1 text-xs font-medium ${getDifficultyColor(method.difficulty)}`} style={{borderRadius: '16px'}}>
                    {method.difficulty}
                  </Badge>
                </div>
                <CardDescription className="text-sm leading-relaxed line-clamp-3 text-muted-foreground">
                  {method.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col justify-between space-y-6 px-8 pb-8">
                <div className="space-y-6">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <TrendingUp className="icon-md text-primary flex-shrink-0" />
                      <span className="font-bold text-lg text-foreground">Success Rate</span>
                    </div>
                    <p className="text-lg text-foreground/80 leading-refined ml-6 font-medium">{method.success_rate}</p>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Clock className="icon-md text-primary flex-shrink-0" />
                      <span className="font-bold text-lg text-foreground">Timeframe</span>
                    </div>
                    <p className="text-lg text-foreground/80 leading-refined ml-6 font-medium">{method.timeframe}</p>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="icon-md text-primary flex-shrink-0" />
                      <span className="font-bold text-lg text-foreground">Best For</span>
                    </div>
                    <ul className="text-lg text-foreground/80 space-y-2 ml-6">
                      {method.best_for.slice(0, 3).map((item, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <CheckCircle className="icon-sm text-primary mt-1 flex-shrink-0" />
                          <span className="line-clamp-2 leading-refined font-medium">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <AlertCircle className="icon-md text-primary flex-shrink-0" />
                      <span className="font-bold text-lg text-foreground">Considerations</span>
                    </div>
                    <ul className="text-lg text-foreground/80 space-y-2 ml-6">
                      {method.considerations.slice(0, 3).map((item, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <div className="h-2 w-2 bg-primary mt-2 flex-shrink-0" style={{borderRadius: '50%'}}></div>
                          <span className="line-clamp-2 leading-refined font-medium">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {method.cta_text && method.cta_link && (
                  <div className="mt-8 pt-6 border-t border-border">
                    <Button asChild className="w-full h-16 bg-primary hover:bg-primary-hover text-primary-foreground font-bold text-lg shadow-lg hover:shadow-xl hover-scale-sm transition-all duration-300" style={{borderRadius: '24px'}}>
                      <Link to={method.cta_link}>
                        {method.cta_text}
                      </Link>
                    </Button>
                    </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center">
          <div className="bg-background p-8 max-w-2xl mx-auto border border-border" style={{borderRadius: '24px'}}>
            <h3 className="text-lg font-semibold mb-2">Need Personalized Support?</h3>
            <p className="text-muted-foreground mb-6">
              Combining multiple approaches often increases success rates. Consider consulting with healthcare providers 
              or using our comprehensive tracking tools to find what works best for you.
            </p>
            <div className="flex flex-wrap gap-3 justify-center">
              <Button asChild variant="outline">
                <Link to="/app/progress">Track Your Progress</Link>
              </Button>
              <Button asChild variant="outline">
                <Link to="/tools/nrt-guide">NRT Guide</Link>
              </Button>
              <Button asChild variant="outline">
                <Link to="/tools/smokeless-directory">Smokeless Products</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default QuitMethods;