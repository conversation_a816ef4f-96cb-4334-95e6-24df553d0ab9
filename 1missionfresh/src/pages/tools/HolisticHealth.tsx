import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Header, <PERSON>T<PERSON>le, CardDescription, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Heart, Brain, Moon, Zap, Flower, Clock, Star, Sparkles, Leaf, Sun, ListChecks, ArrowLeft } from 'lucide-react';
import { motion } from "framer-motion";
import { useHaptics, HapticImpact } from '@/hooks/useHaptics';
import { useAuth } from '@/contexts/AuthContext';
import { earnPoints } from '@/services/rewardsService';
import { toast } from 'sonner';
import { Link } from 'react-router-dom';

interface Practice {
  id: string;
  title: string;
  description: string;
  duration: string;
  category: 'sleep' | 'energy' | 'mood' | 'focus';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  icon: React.ReactNode;
  steps: string[];
  benefits: string[];
}

const practices: Practice[] = [
  {
    id: 'box-breathing',
    title: 'Box Breathing',
    description: 'A powerful breathing technique for instant calm and focus',
    duration: '5-10 min',
    category: 'focus',
    difficulty: 'beginner',
    icon: <Heart className="h-5 w-5" />,
    steps: [
      'Sit comfortably with your back straight',
      'Exhale completely through your mouth',
      'Inhale through your nose for 4 counts',
      'Hold your breath for 4 counts',
      'Exhale through your mouth for 4 counts',
      'Hold empty for 4 counts',
      'Repeat for 5-10 cycles'
    ],
    benefits: ['Reduces stress and anxiety', 'Improves focus and concentration', 'Lowers blood pressure', 'Enhances emotional regulation']
  },
  {
    id: 'progressive-muscle-relaxation',
    title: 'Progressive Muscle Relaxation',
    description: 'Systematic tension and release of muscle groups for deep relaxation',
    duration: '15-20 min',
    category: 'sleep',
    difficulty: 'beginner',
    icon: <Moon className="h-5 w-5" />,
    steps: [
      'Lie down in a comfortable position',
      'Start with your toes - tense for 5 seconds, then release',
      'Move to your calves - tense and release',
      'Continue with thighs, abdomen, hands, arms, shoulders',
      'Tense your face muscles, then release',
      'Take a moment to feel the complete relaxation',
      'Breathe deeply and enjoy the peaceful state'
    ],
    benefits: ['Improves sleep quality', 'Reduces muscle tension', 'Decreases anxiety', 'Promotes physical relaxation']
  },
  {
    id: 'mindful-walking',
    title: 'Mindful Walking',
    description: 'Walking meditation to connect with the present moment',
    duration: '10-30 min',
    category: 'mood',
    difficulty: 'beginner',
    icon: <Leaf className="h-5 w-5" />,
    steps: [
      'Choose a quiet path or space to walk',
      'Begin walking at a slow, natural pace',
      'Focus on the sensation of your feet touching the ground',
      'Notice the movement of your legs and the rhythm of your steps',
      'When your mind wanders, gently return focus to walking',
      'Observe your surroundings without judgment',
      'End with a moment of gratitude'
    ],
    benefits: ['Improves mood and mental clarity', 'Reduces stress', 'Increases mindfulness', 'Boosts creativity']
  },
  {
    id: 'gratitude-meditation',
    title: 'Gratitude Meditation',
    description: 'Cultivate appreciation and positive emotions through focused gratitude',
    duration: '5-15 min',
    category: 'mood',
    difficulty: 'beginner',
    icon: <Star className="h-5 w-5" />,
    steps: [
      'Sit comfortably and close your eyes',
      'Take three deep breaths to center yourself',
      'Bring to mind something you are grateful for',
      'Feel the emotion of gratitude in your body',
      'Think of three more things you appreciate',
      'Hold each one in your awareness for 30 seconds',
      'End by sending gratitude to yourself'
    ],
    benefits: ['Enhances positive emotions', 'Improves relationships', 'Increases life satisfaction', 'Reduces depression']
  },
  {
    id: 'energy-visualization',
    title: 'Energy Visualization',
    description: 'Visualize and direct healing energy throughout your body',
    duration: '10-20 min',
    category: 'energy',
    difficulty: 'intermediate',
    icon: <Zap className="h-5 w-5" />,
    steps: [
      'Sit or lie down in a comfortable position',
      'Close your eyes and take several deep breaths',
      'Imagine a warm, healing light at the top of your head',
      'Visualize this light slowly moving down through your body',
      'See it healing and energizing every cell it touches',
      'Focus on areas that need extra healing',
      'End by surrounding yourself in this protective light'
    ],
    benefits: ['Increases energy levels', 'Promotes healing', 'Enhances self-awareness', 'Reduces fatigue']
  },
  {
    id: 'sleep-body-scan',
    title: 'Sleep Body Scan',
    description: 'Systematic relaxation technique to prepare for restful sleep',
    duration: '15-25 min',
    category: 'sleep',
    difficulty: 'beginner',
    icon: <Moon className="h-5 w-5" />,
    steps: [
      'Lie down comfortably in your bed',
      'Start by focusing on your breath',
      'Bring attention to the top of your head',
      'Slowly scan down through your face, neck, and shoulders',
      'Notice any tension and consciously release it',
      'Continue scanning through your arms, chest, and abdomen',
      'Move through your hips, legs, and feet',
      'End by feeling your whole body relaxed and heavy'
    ],
    benefits: ['Improves sleep onset', 'Reduces physical tension', 'Calms the nervous system', 'Enhances sleep quality']
  }
];

const categories = [
  { id: 'all', label: 'All Practices', icon: <Sparkles className="h-5 w-5" /> },
  { id: 'sleep', label: 'Sleep', icon: <Moon className="h-5 w-5" /> },
  { id: 'energy', label: 'Energy', icon: <Zap className="h-5 w-5" /> },
  { id: 'mood', label: 'Mood', icon: <Heart className="h-5 w-5" /> },
  { id: 'focus', label: 'Focus', icon: <Brain className="h-5 w-5" /> }
];

const HolisticHealth = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedPractice, setSelectedPractice] = useState<Practice | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [forceRerender, setForceRerender] = useState(0);
  const { impact } = useHaptics();
  const { user } = useAuth();

  // Force component re-render when modal state changes
  useEffect(() => {
    console.log('Modal state changed:', isModalOpen, 'Practice:', selectedPractice?.title);
    setForceRerender(prev => prev + 1);
  }, [isModalOpen, selectedPractice]);

  const filteredPractices = selectedCategory === 'all' 
    ? practices 
    : practices.filter(practice => practice.category === selectedCategory);

  const openPracticeModal = (practice: Practice) => {
    console.log('=== OPENING MODAL DEBUG ===');
    console.log('Practice:', practice.title);
    console.log('Current modal state before:', isModalOpen);
    console.log('Current selected practice before:', selectedPractice?.title);
    
    try {
      // First set the practice, then the modal state
      setSelectedPractice(practice);
      
      // Use a small timeout to ensure state updates properly
      setTimeout(() => {
        setIsModalOpen(true);
        console.log('Modal state set to true after timeout');
        
        // Check DOM after state change
        setTimeout(() => {
          const modalElements = document.querySelectorAll('[role="dialog"], [data-radix-dialog-content], [data-radix-dialog-overlay]');
          console.log('Modal elements in DOM after state change:', modalElements.length);
          if (modalElements.length === 0) {
            console.error('CRITICAL: Modal state is true but no DOM elements found!');
            // Force re-render
            setForceRerender(prev => prev + 1);
          }
        }, 100);
      }, 10);
      
      // Make haptic feedback optional to prevent errors in public context
      if (impact) {
        impact(HapticImpact.LIGHT);
      }
    } catch (error) {
      console.error('Error opening modal:', error);
      // Fallback: still try to open the modal
      setSelectedPractice(practice);
      setIsModalOpen(true);
    }
  };

  const handlePracticeComplete = async () => {
    if (user?.id && selectedPractice) {
      try {
        await earnPoints(user.id, 15, 'exercise', selectedPractice.title);
        toast.success(`Completed ${selectedPractice.title}! +15 points earned`);
        if (impact) {
          impact(HapticImpact.MEDIUM);
        }
      } catch (error) {
        console.error('Failed to award points:', error);
        toast.error('Practice completed, but failed to award points');
      }
    } else {
      toast.success(`Completed ${selectedPractice?.title}!`);
      if (impact) {
        impact(HapticImpact.MEDIUM);
      }
    }
    setIsModalOpen(false);
  };

  const getDifficultyColor = (difficulty: string) => {
    return 'bg-muted text-muted-foreground border-0 font-medium text-xs';
  };

  const getCategoryColor = (category: string) => {
    return 'bg-primary/10 text-primary border-0 font-medium text-xs';
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center gap-4 mb-4">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground leading-tight tracking-tight">
              Holistic Health Practices
            </h1>
          </div>
          <p className="text-base text-muted-foreground max-w-2xl mx-auto leading-[1.6]">
            Discover evidence-based practices for mind-body wellness. From breathing techniques to meditation, 
            find the perfect practice for your current needs.
          </p>
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
            <TabsList className="grid w-full grid-cols-5 mb-6 max-w-2xl mx-auto">
              {categories.map((category) => (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className="flex items-center gap-2"
                >
                  {category.icon}
                  <span className="hidden sm:inline">{category.label}</span>
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </motion.div>

        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {filteredPractices.map((practice, index) => (
            <motion.div
              key={practice.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
            >
              <Card className="hover:shadow-lg transition-all duration-300 cursor-pointer border border-border bg-card hover:border-primary/30 flex flex-col h-full"
                    onClick={() => openPracticeModal(practice)}>
                <CardHeader className="pb-4 flex-shrink-0">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-primary w-12 h-12 flex items-center justify-center" style={{borderRadius: '32px'}}>
                        {React.cloneElement(practice.icon as React.ReactElement, { className: "h-5 w-5 text-primary-foreground" })}
                      </div>
                      <div>
                        <CardTitle className="text-xl font-bold">{practice.title}</CardTitle>
                        <div className="flex items-center gap-2 mt-2">
                          <Clock className="h-5 w-5 text-muted-foreground" />
                          <span className="text-base font-medium text-muted-foreground">{practice.duration}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <CardDescription className="mt-3 text-sm leading-relaxed line-clamp-2 min-h-[2.5rem]">
                    {practice.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0 flex-grow flex flex-col justify-end pb-6">
                  <div className="flex items-center justify-between mt-4">
                    <div className="flex gap-2">
                      <Badge className={getCategoryColor(practice.category)}>
                        {practice.category}
                      </Badge>
                      <Badge className={getDifficultyColor(practice.difficulty)}>
                        {practice.difficulty}
                      </Badge>
                    </div>
                    <Button variant="default" size="sm" className="bg-primary text-primary-foreground hover:bg-primary/90 font-medium px-4 py-2 h-9 min-w-[80px] transition-all duration-300">
                      Start →
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Back to All Tools Navigation */}
      <div className="max-w-6xl mx-auto mt-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="text-center"
        >
          <Button asChild variant="outline" size="lg" className="px-8 py-6 text-lg font-medium border-2 border-border hover:border-primary/50 shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300" style={{borderRadius: '32px'}}>
            <Link to="/tools" className="flex items-center gap-4">
              <ArrowLeft className="w-5 h-5" strokeWidth="2" />
              <span>Back to All Tools</span>
            </Link>
          </Button>
        </motion.div>
      </div>

      <Dialog key={selectedPractice?.id || 'modal'} open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary text-primary-foreground" style={{borderRadius: '32px'}}>
                {selectedPractice?.icon}
              </div>
              <div>
                <DialogTitle className="text-xl">{selectedPractice?.title}</DialogTitle>
                <DialogDescription className="mt-1">
                  {selectedPractice?.description}
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          {selectedPractice && (
            <div className="space-y-6 mt-6">
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <ListChecks className="h-5 w-5" />
                  Practice Steps
                </h3>
                <div className="space-y-3">
                  {selectedPractice.steps.map((step, index) => (
                    <div key={index} className="flex gap-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium" style={{borderRadius: '50%'}}>
                        {index + 1}
                      </div>
                      <p className="text-gray-700 dark:text-gray-300">{step}</p>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <Star className="h-5 w-5" />
                  Benefits
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {selectedPractice.benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center gap-2">
                                                <div className="w-2 h-2 bg-primary" style={{borderRadius: '50%'}}></div>
                      <span className="text-sm text-gray-600 dark:text-gray-400">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex gap-3 pt-4 border-t">
                <Button 
                  onClick={handlePracticeComplete}
                  className="flex-1 bg-primary hover:bg-primary/90"
                >
                  Complete Practice (+15 points)
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setIsModalOpen(false)}
                  className="flex-1"
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default HolisticHealth;