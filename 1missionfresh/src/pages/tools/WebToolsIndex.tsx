import React, { memo, useCallback, useMemo } from 'react';
import { Helmet } from 'react-helmet-async';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from 'react-router-dom';
import { Compass, Calculator, SunMoon, Tablet, ArrowRight, Store } from 'lucide-react';
import { motion } from 'framer-motion';
import { ToolCard } from '@/components/tools/ToolCard';

const fadeInUp = {
  hidden: { y: 16, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.3, ease: "easeOut" as const },
  },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
    },
  },
};

const toolList = [
  {
    title: "NRT Guide",
    description: "Comprehensive guidance on nicotine replacement therapy options and evidence-based best practices.",
    icon: <Tablet className="shrink-0" />,
    link: "/tools/nrt-guide",
    category: "therapy"
  },
  {
    title: "Smokeless Directory",
    description: "Curated directory of verified smokeless alternatives and comprehensive support resources.",
    icon: <Store className="shrink-0" />,
    link: "/tools/smokeless-directory",
    category: "directory"
  },
  {
    title: "Quit Methods",
    description: "Evidence-based approaches and proven strategies for successful nicotine cessation.",
    icon: <Compass className="shrink-0" />,
    link: "/tools/quit-methods",
    category: "methods"
  },
  {
    title: "Calculators",
    description: "Track your progress with detailed health improvement and cost-saving metrics.",
    icon: <Calculator className="shrink-0" />,
    link: "/tools/calculators",
    category: "tools"
  },
  {
    title: "Holistic Health",
    description: "Integrative wellness approaches and mindfulness techniques to support your journey.",
    icon: <SunMoon className="shrink-0" />,
    link: "/tools/holistic-health",
    category: "wellness"
  },
  {
    title: "Product Details",
    description: "Comprehensive database of nicotine products with detailed specifications and alternatives.",
    icon: <Store className="shrink-0" />,
    link: "/tools/product-details",
    category: "directory"
  },
];

const WebToolsIndex = memo(() => {
  // TODO: Add internationalization support for multilingual tools
  // TODO: Add loading states for dynamic content when moved to database
  // TODO: Add error boundaries for robust error handling
  
  // Memoize tool list to prevent unnecessary re-renders
  const memoizedToolList = useMemo(() => toolList, []);
  
  // Memoize event handlers to prevent unnecessary re-renders
  const handleSignupClick = useCallback(() => {
    // TODO: Add analytics tracking for signup CTA button
    console.log('Signup CTA clicked from WebToolsIndex');
  }, []);
  
  const handleLearnMoreClick = useCallback(() => {
    // TODO: Add analytics tracking for learn more CTA button
    console.log('Learn More CTA clicked from WebToolsIndex');
  }, []);
  
  // Memoize keyboard event handlers to prevent unnecessary re-renders
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLAnchorElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      e.currentTarget.click();
    }
  }, []);
  
  return (
    <>
      <Helmet>
        <title>Web Tools - Mission Fresh</title>
        <meta name="description" content="Explore a collection of free tools from Mission Fresh to help you on your journey to a nicotine-free life." />
      </Helmet>
      <div className="bg-background min-h-screen">
        {/* Skip Link for Keyboard Navigation */}
        <a href="#main-content" className="sr-only focus:not-sr-only focus:absolute focus:top-1 focus:left-1 bg-primary text-primary-foreground px-3 py-1 z-40 transition-all duration-theme" style={{borderRadius: '4px'}}>
          Skip to main content
        </a>
        {/* Page Header */}
        <section className="text-center py-section">
          <motion.div
            initial={{ opacity: 0, y: -16 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, ease: "easeOut" }}
            className="container max-w-6xl mx-auto"
          >
            <h1 className="text-heading-xl font-bold text-foreground leading-tight tracking-normal">
              A Library of Tools for <span className="text-primary font-bold">Everyone</span>
            </h1>
            <p className="mx-auto text-foreground/70 leading-refined font-normal">
              Explore our collection of wellness resources and calculators, freely available to support
              your journey.
            </p>
          </motion.div>
        </section>

        {/* Tools Grid */}
        <section className="bg-background py-section" role="main" id="main-content" aria-labelledby="tools-heading">
          <div className="container max-w-6xl mx-auto px-container">
            <h2 id="tools-heading" className="sr-only">Available Tools</h2>
            <motion.div 
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-space-md"
              variants={staggerContainer}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.2 }}
              role="list"
            >
              {memoizedToolList.map((tool, index) => (
                <motion.div variants={fadeInUp} key={`${index}-${tool.title.toLowerCase().replace(/\s+/g, '-')}`} className="h-full" role="listitem">
                  <ToolCard
                    title={tool.title}
                    description={tool.description}
                    icon={tool.icon}
                    link={tool.link}
                  />
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-background border-t border-border py-section" aria-labelledby="cta-heading">
          <div className="container max-w-6xl mx-auto px-container">
            <motion.div
              className="bg-card text-center p-8 border-theme border-border hover:border-primary/60 transition-all duration-theme shadow-theme hover:shadow-theme-lg"
              initial={{ opacity: 0, y: 16 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.4 }}
            >
              <h2 id="cta-heading" className="text-heading-lg font-bold text-foreground leading-tight tracking-normal">
                Ready for Personalized Support?
              </h2>
              <p className="text-foreground/70 mx-auto leading-refined font-normal">
                Get access to personalized tracking, advanced tools, and join a community of people on the same journey.
              </p>
              <div className="flex flex-col md:flex-row items-center justify-center gap-4">
                <Button asChild size="lg" className="shadow-theme hover:shadow-theme-lg transition-all duration-theme" type="button">
                  <Link to="/auth?tab=signup" className="flex items-center gap-space-sm" rel="noopener noreferrer" onClick={handleSignupClick} onKeyDown={handleKeyDown}>
                    <span>Get Started</span>
                    <ArrowRight className="icon-sm" strokeWidth="1.5" aria-hidden="true" />
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="border-theme border-border hover:border-primary/60 shadow-theme hover:shadow-theme-lg transition-all duration-theme" type="button">
                  <Link to="/features" className="flex items-center gap-space-sm" rel="noopener noreferrer" onClick={handleLearnMoreClick} onKeyDown={handleKeyDown}>
                    <span>Learn More</span>
                    <ArrowRight className="icon-sm" strokeWidth="1.5" aria-hidden="true" />
                  </Link>
                </Button>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
});

WebToolsIndex.displayName = 'WebToolsIndex';

export default WebToolsIndex;
