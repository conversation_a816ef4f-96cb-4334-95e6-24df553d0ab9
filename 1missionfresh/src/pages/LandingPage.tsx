import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardFooter } from '@/components/ui/card';
import { InfoCard } from '@/components/ui/InfoCard';
import { Skeleton } from '@/components/ui/skeleton';
import { Link } from 'react-router-dom';
import { 
  ArrowRight, 
  Bot, 
  CheckCircle, 
  Heart, 
  Send, 
  Target, 
  Activity, 
  Eye, 
  Sparkles, 
  Award,
  TrendingUp,
  Shield,
  Search,
  FileText,
  Calculator,
  Grid3x3,
  MessageSquareQuote,
  UserCircle2,
  Leaf,
  Zap,
  Headphones
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useAIChat } from '@/hooks/useAIChat';
import { supabase } from '@/lib/supabase';
import SEOHead from '@/components/common/SEOHead';

// Data for How It Works section
const steps = [
  {
    icon: Target,
    title: "Sign Up & Define Your Journey",
    description: "Create your personalized account and tell us about your unique habits. Choose your wellness goal and the method that resonates best with your lifestyle.",
    stepNumber: "1"
  },
  {
    icon: Activity,
    title: "Track Your Daily Progress",
    description: "Log your nicotine use, cravings, and wellness metrics like mood, energy, and focus to build a comprehensive picture of your health journey.",
    stepNumber: "2"
  },
  {
    icon: Eye,
    title: "Visualize Your Growth",
    description: "Watch your transformation unfold through intuitive charts showcasing your reduction progress, savings, and overall wellbeing improvements.",
    stepNumber: "3"
  },
  {
    icon: Sparkles,
    title: "Access Wellness Tools",
    description: "Use our curated collection of science-backed tools for managing cravings, boosting energy, improving focus, and regulating mood naturally.",
    stepNumber: "4"
  },
  {
    icon: Award,
    title: "Celebrate Your Success",
    description: "Stay motivated with our thoughtful achievement system that recognizes your milestones and celebrates your commitment to wellness.",
    stepNumber: "5"
  }
];

// Data for Features section
const features = [
  {
    icon: Target,
    title: 'Intelligent Insights Engine',
    description: 'AI-powered analysis provides personalized guidance for your unique journey, adapting to your progress and needs.',
  },
  {
    icon: TrendingUp,
    title: 'Holistic Wellness Tools',
    description: 'Comprehensive suite of tools designed to support your mind, body, and spirit throughout your wellness journey.',
  },
  {
    icon: Shield,
    title: 'Unyielding Privacy',
    description: 'Enterprise-grade encryption and security measures ensure your personal data remains private and protected.',
  },
];

// Data for Web Tools section
const tools = [
  {
    icon: Heart,
    title: 'NRT Guide',
    description: 'Expert guidance and personalized recommendations for nicotine replacement therapy, tailored to your needs.',
    link: '/tools/nrt-guide',
  },
  {
    icon: Search,
    title: 'Smokeless Directory',
    description: 'Comprehensive, curated directory of smokeless alternatives with expert reviews and recommendations.',
    link: '/tools/smokeless-directory',
  },
  {
    icon: FileText,
    title: 'Quitting Methods',
    description: 'Evidence-based strategies and step-by-step guides for successful smoking cessation and long-term wellness.',
    link: '/tools/quitting-methods',
  },
  {
    icon: Calculator,
    title: 'Wellness Calculator',
    description: 'Track your progress, calculate health improvements, and visualize your journey to better wellness.',
    link: '/tools/calculators',
  },
  {
    icon: Activity,
    title: 'Holistic Wellness',
    description: 'Comprehensive guides and tools for improving sleep quality, energy levels, and mental focus.',
    link: '/tools/holistic-wellness',
  },
  {
    icon: Grid3x3,
    title: 'Explore All Tools',
    description: 'Access our complete suite of wellness tools, resources, and expert guidance for your journey.',
    link: '/tools',
  },
];

// Conversation starters for Hero AI chat
const conversationStarters = [
  "How do I handle nicotine cravings?",
  "What's the timeline for quitting smoking?",
  "Which quit method is best for my lifestyle?"
];

// Testimonials interface
interface Testimonial {
  id: string;
  content: string;
  user_name: string;
  avatar_url: string | null;
  rating: number | null;
  created_at: string;
}

const LandingPage: React.FC = () => {
  // AI Chat hook for Hero section
  const {
    messages,
    inputValue,
    isTyping,
    handleSendMessage,
    handleStartChat,
    setInputValue
  } = useAIChat();

  // Testimonials state
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [testimonialsLoading, setTestimonialsLoading] = useState(true);
  const [testimonialsError, setTestimonialsError] = useState<string | null>(null);

  // Fetch testimonials on component mount
  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        setTestimonialsLoading(true);
        const { data, error } = await supabase
          .schema('mission_fresh')
          .from('testimonials')
          .select('*')
          .eq('is_approved', true)
          .order('created_at', { ascending: false })
          .limit(6);

        if (error) throw error;
        setTestimonials(data || []);
      } catch (err) {
        console.error('Error fetching testimonials:', err);
        setTestimonialsError('Failed to load testimonials');
      } finally {
        setTestimonialsLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  return (
    <>
      {/* Added SEOHead component */}
      <SEOHead
        title="Mission Fresh - Quit Smoking & Nicotine"
        description="Start your journey to a nicotine-free life with Mission Fresh. Track progress, manage cravings, and connect with a supportive community."
        keywords="quit smoking, nicotine cessation, stop vaping, craving management, addiction recovery, health app"
      />

      {/* HERO SECTION */}
      <section className="relative py-16 bg-background">
          <div className="container max-w-7xl mx-auto px-6 flex flex-col-reverse lg:flex-row items-start gap-12">
          {/* Left Column - Hero Content */}
          <div className="w-full lg:w-2/3">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className=""
            >
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight tracking-tight mb-6">
                Take the first step
                <span className="block font-extrabold text-primary">to a smoke-free future</span>
              </h1>

              <p className="text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto lg:mx-0 mb-8">
                Get instant, personalized support for your quit smoking journey.
                Our Fresh Assistant provides proven strategies and motivates you
                every step of the way.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start mt-8">
                <Button
                  asChild
                  variant="primary"
                  size="lg"
                  className="group font-semibold min-w-[200px] h-14 text-lg shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <Link to="/auth?action=signup" className="flex items-center justify-center gap-2">
                    <Sparkles className="w-4 h-4" strokeWidth={2} aria-hidden="true" />
                    <span>Start Your Journey</span>
                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" strokeWidth={2} aria-hidden="true" />
                  </Link>
                </Button>

                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="group font-semibold min-w-[200px] h-14 text-lg shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <Link to="/how-it-works" className="flex items-center justify-center gap-2">
                    <Eye className="w-4 h-4" strokeWidth={2} aria-hidden="true" />
                    <span>Learn How It Works</span>
                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" strokeWidth={2} aria-hidden="true" />
                  </Link>
                </Button>
              </div>
            </motion.div>
          </div>

          {/* Right Column - AI Chat */}
          <div className="w-full lg:w-1/3 mt-8 lg:mt-0">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1, duration: 0.6 }}
            >
              <Card className="w-full h-auto bg-background border border-border/50 shadow-lg hover:shadow-xl hover:border-primary/30 transition-all duration-200">
                <CardHeader className="p-6 border-b border-border/50 bg-background">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-primary flex items-center justify-center border border-primary" style={{ borderRadius: '50%' }}>
                      <Bot className="w-6 h-6 text-primary-foreground" strokeWidth={2} />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-foreground tracking-tight">Fresh Assistant</h3>
                      <p className="text-muted-foreground text-sm">
                        Your 24/7 wellness coach
                      </p>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="p-6 space-y-4">
                  <p className="text-lg text-muted-foreground">Start a conversation:</p>
                  <div className="flex flex-col gap-4 w-full">
                    {conversationStarters.slice(0, 3).map((starter) => (
                      <Button
                        key={starter}
                        variant="outline"
                        className="w-full h-14 text-base font-medium text-left justify-start px-6 py-4 transition-all duration-200 hover:bg-primary/5"
                        style={{ borderRadius: '24px' }}
                        onClick={async () => {
                          setInputValue(starter);
                          await handleSendMessage(starter);
                        }}
                      >
                        {starter}
                      </Button>
                    ))}
                  </div>

                  <div className="mt-6 pt-6 border-t border-border">
                    <div className="flex items-center gap-4">
                      <input
                        type="text"
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onKeyDown={async (e) => {
                          if (e.key === 'Enter' && inputValue.trim()) {
                            await handleSendMessage(inputValue);
                          }
                        }}
                        placeholder="Type your question..."
                        className="flex-1 h-14 px-6 text-lg border border-border bg-input text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                        style={{ borderRadius: '24px' }}
                      />
                      <Button
                        variant="primary"
                        size="icon"
                        onClick={async () => {
                          if (inputValue.trim()) {
                            await handleSendMessage(inputValue);
                          }
                        }}
                        disabled={!inputValue.trim()}
                        className="w-14 h-14 flex-shrink-0 transition-all duration-200"
                        style={{ borderRadius: '50%' }}
                      >
                        <Send className="w-5 h-5" strokeWidth={2} />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* HOW IT WORKS SECTION */}
      <section className="bg-card border-y border-border py-20">
        <div className="container max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground tracking-tight mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground mx-auto leading-relaxed max-w-3xl">
              Our proven process helps you quit smoking naturally, with personalized support every step of the way.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mx-auto">
            {steps.map((step, index) => (
              <motion.div
                key={step.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="flex"
              >
                <Card className="border border-border/50 bg-card group hover:border-primary/30 hover:shadow-lg transition-all duration-200 flex flex-col w-full h-full">
                  <CardContent className="p-8 flex flex-col items-center text-center flex-1 space-y-6">
                    <div className="flex items-center justify-center w-16 h-16 bg-primary transition-all duration-200" style={{borderRadius: '24px'}}>
                      <step.icon className="w-8 h-8 text-primary-foreground" strokeWidth={2} />
                    </div>
                    <div className="flex-1 space-y-4">
                      <h3 className="text-xl font-semibold text-foreground leading-tight">
                        {step.title}
                      </h3>
                      <p className="text-lg text-muted-foreground leading-relaxed">
                        {step.description}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>



      {/* TOOLS SECTION */}
      <section 
        className="relative bg-background"
        style={{
          paddingTop: '80px',
          paddingBottom: '80px',
          marginTop: '64px'
        }}
      >
        <div className="container max-w-7xl mx-auto px-container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-12"
          >
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground tracking-tight leading-tight">
                Wellness Tools & Resources
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                Access our comprehensive suite of tools designed to support your journey to wellness.
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {tools.map((tool, index) => (
                <InfoCard
                  key={index}
                  index={index}
                  icon={tool.icon}
                  title={tool.title}
                  description={tool.description}
                  link={tool.link}
                />
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
};

export default LandingPage;
