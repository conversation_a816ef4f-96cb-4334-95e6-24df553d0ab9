import React, { useState } from 'react';
import { ChevronDown, ChevronUp, HelpCircle } from 'lucide-react';
import SEOHead from '@/components/common/SEOHead';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const FAQPage: React.FC = () => {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const faqData = [
    {
      category: "Getting Started",
      questions: [
        {
          question: "How do I create an account?",
          answer: "Creating an account is simple! Click the 'Get Started' button on our homepage, fill in your details, and verify your email address. You'll be ready to start your wellness journey in minutes."
        },
        {
          question: "Is Mission Fresh free to use?",
          answer: "Yes! Mission Fresh offers a comprehensive free tier with all essential tracking and wellness tools. We also offer premium features for users who want advanced analytics and personalized coaching."
        },
        {
          question: "What devices can I use Mission Fresh on?",
          answer: "Mission Fresh works on any device with a web browser - desktop, tablet, or mobile. We're also developing native mobile apps for iOS and Android."
        }
      ]
    },
    {
      category: "Tracking & Progress",
      questions: [
        {
          question: "How do I log my nicotine use?",
          answer: "Navigate to the 'Log Entry' section in your dashboard. You can quickly log different types of nicotine products, track cravings, and record your mood and energy levels."
        },
        {
          question: "Can I edit or delete previous entries?",
          answer: "Yes! You can view your log history and edit or delete any previous entries. This helps you maintain accurate tracking of your wellness journey."
        },
        {
          question: "How are my progress charts calculated?",
          answer: "Your progress charts use your daily log entries to show trends in nicotine use, mood, energy, and other wellness metrics. The data is updated in real-time as you make new entries."
        }
      ]
    },
    {
      category: "Privacy & Security",
      questions: [
        {
          question: "Is my health data secure?",
          answer: "Absolutely. We use enterprise-grade encryption and security measures to protect your data. Your personal health information is never shared without your explicit consent."
        },
        {
          question: "Can I delete my account and data?",
          answer: "Yes, you have complete control over your data. You can delete your account and all associated data at any time from your account settings."
        },
        {
          question: "Do you share my data with third parties?",
          answer: "No, we do not sell or share your personal data with third parties. Your privacy is our top priority, and we only use your data to improve your experience on our platform."
        }
      ]
    }
  ];

  let questionIndex = 0;

  return (
    <>
      <SEOHead
        title="FAQ - Mission Fresh"
        description="Find answers to frequently asked questions about Mission Fresh and how to make the most of your wellness journey."
      />
      <div className="bg-background text-foreground">
        <section className="py-16 sm:py-20 text-center">
          <div className="container mx-auto max-w-6xl px-6">
            <div className="space-y-12">
              <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-foreground leading-tight">
                <span className="text-primary font-black block mb-2">Frequently</span> Asked Questions
              </h1>
              <p className="max-w-4xl mx-auto text-lg sm:text-xl md:text-2xl text-foreground leading-relaxed font-medium">
                Find answers to common questions about Mission Fresh and your wellness journey.
              </p>
            </div>
          </div>
        </section>

        <section className="py-16">
          <div className="container mx-auto max-w-4xl px-6">
            <div className="space-y-12">
              {faqData.map((category) => (
                <div key={category.category}>
                  <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                    <HelpCircle className="w-6 h-6 text-primary" />
                    {category.category}
                  </h2>
                  <div className="space-y-4">
                    {category.questions.map((item) => {
                      const currentIndex = questionIndex++;
                      const isOpen = openItems.includes(currentIndex);
                      
                      return (
                        <Card key={currentIndex} className="shadow-lg border-border">
                          <CardHeader 
                            className="cursor-pointer hover:bg-muted/50 transition-colors"
                            onClick={() => toggleItem(currentIndex)}
                          >
                            <CardTitle className="flex items-center justify-between text-lg font-semibold text-foreground">
                              {item.question}
                              {isOpen ? (
                                <ChevronUp className="w-5 h-5 text-muted-foreground" />
                              ) : (
                                <ChevronDown className="w-5 h-5 text-muted-foreground" />
                              )}
                            </CardTitle>
                          </CardHeader>
                          {isOpen && (
                            <CardContent className="px-6 pb-6">
                              <p className="text-muted-foreground leading-relaxed">
                                {item.answer}
                              </p>
                            </CardContent>
                          )}
                        </Card>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="py-20">
          <div className="container mx-auto max-w-4xl px-6 text-center">
            <Card className="bg-primary/5 border-primary/20 shadow-xl">
              <CardHeader className="text-center pb-4">
                <CardTitle className="text-2xl sm:text-3xl font-bold text-foreground leading-tight">
                  Still Have Questions?
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center px-8 pb-8">
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed font-medium">
                  Can't find the answer you're looking for? Our support team is here to help you succeed on your wellness journey.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    </>
  );
};

export default FAQPage;
