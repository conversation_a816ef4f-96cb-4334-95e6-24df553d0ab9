import { memo } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import {
  Target,
  Activity,
  Eye,
  Sparkles,
  Award,
  ArrowRight,
} from "lucide-react";
import SEOHead from "@/components/common/SEOHead";
import { Card, CardContent } from "@/components/ui/card";

// IMPLEMENTATION NOTE: Process steps are currently static for <PERSON>. Future enhancement: move to database for dynamic content management
const stepsData = [
  {
    id: 'signup-journey',
    icon: Target,
    title: "Sign Up & Define Your Journey",
    description: "Create your personalized account and tell us about your unique habits. Choose your wellness goal and the method that resonates best with your lifestyle.",
  },
  {
    id: 'track-progress',
    icon: Activity,
    title: "Track Your Daily Progress",
    description: "Log your nicotine use, cravings, and wellness metrics like mood, energy, and focus to build a comprehensive picture of your health journey.",
  },
  {
    id: 'visualize-growth',
    icon: Eye,
    title: "Visualize Your Growth",
    description: "Watch your transformation unfold through intuitive charts showcasing your reduction progress, savings, and overall wellbeing improvements.",
  },
  {
    id: 'access-tools',
    icon: Sparkles,
    title: "Access Wellness Tools",
    description: "Use our curated collection of science-backed tools for managing cravings, boosting energy, improving focus, and regulating mood naturally.",
  },
  {
    id: 'celebrate-success',
    icon: Award,
    title: "Celebrate Your Success",
    description: "Stay motivated with our thoughtful achievement system that recognizes your milestones and celebrates your commitment to wellness.",
  },
];

const HowItWorksPage = memo(() => {
  // TODO: Add loading states for dynamic content when moved to database
  // TODO: Add error boundaries for robust error handling
  
  return (
    <>
      <SEOHead
        title="How It Works - Mission Fresh"
        description="Discover the science-backed approach Mission Fresh uses to help you quit or reduce nicotine use for good."
      />
      <div className="bg-background text-foreground" role="main">
        {/* Skip link for keyboard navigation accessibility */}
        <a 
          href="#main-content" 
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2" style={{borderRadius: '8px'}}
        >
          Skip to main content
        </a>
        <section className="text-center py-section" id="main-content">
          <div className="container mx-auto max-w-6xl px-container">
            <div className="space-y-6">
              <h1 className="text-heading-xl font-bold text-foreground leading-tight tracking-tight">
                A Clear Path to <span className="text-primary block">Wellness</span>
                {/* TODO: Internationalize text content for multilingual support */}
              </h1>
              <div className="mx-auto text-center max-w-4xl">
                <p className="text-body text-muted-foreground leading-refined">
                  We combine science-backed methods with a supportive platform to guide you every step of the way.
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="bg-background py-section" aria-labelledby="process-steps-heading">
          <div className="container mx-auto max-w-6xl px-container">
            <div className="space-y-6">
              <h2 className="sr-only" id="process-steps-heading">Process Steps</h2>
              <ol className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mx-auto list-none">
                {stepsData.map((step, index) => (
                  <li key={step.id}>
                    <Card className="border border-border/50 bg-card group hover:border-primary/30 hover:shadow-md transition-all duration-200 flex flex-col w-full h-full">
                      <CardContent className="p-6 flex flex-col items-center text-center flex-1 space-y-4">
                        <div className="flex items-center justify-center icon-2xl bg-primary transition-all duration-200" style={{borderRadius: '24px'}}>
                          <step.icon className="icon-sm text-primary-foreground" strokeWidth={2} aria-hidden="true" />
                        </div>
                        <div className="flex-1 space-y-3">
                          <h2 className="text-heading-sm font-semibold text-foreground leading-tight" id={`step-title-${index}`}>
                            {step.title}
                          </h2>
                          <p className="text-body text-muted-foreground leading-relaxed" id={`step-desc-${index}`} aria-describedby={`step-title-${index}`}>
                            {step.description}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </li>
                ))}
              </ol>
            </div>
          </div>
        </section>

        <section className="py-section" aria-labelledby="cta-heading">
          <div className="container mx-auto max-w-6xl px-container text-center">
            <div className="bg-card border border-border hover:border-primary hover:shadow-md transition-all duration-200 p-8 space-y-6" style={{borderRadius: '24px'}}>
              <h2 className="text-heading-lg font-bold text-foreground leading-tight tracking-tight" id="cta-heading">
                Ready to Begin Your Transformation?
              </h2>
              <p className="mx-auto leading-refined text-muted-foreground max-w-4xl">
                Join thousands who have found clarity and reclaimed their vitality with Mission Fresh.
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <Button asChild className="w-full sm:w-auto group font-semibold bg-primary hover:bg-primary-hover text-primary-foreground transition-all duration-200" type="button">
                  <Link 
                    to="/auth?tab=signup" 
                    className="flex items-center justify-center gap-2"
                    rel="noopener noreferrer"
                    onClick={() => {
                      // TODO: Add analytics tracking for CTA button clicks
                      // analytics.track('how_it_works_cta_clicked', { button: 'start_journey' });
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        e.currentTarget.click();
                      }
                    }}
                  >
                    <Sparkles className="icon-xs text-current" strokeWidth={2} aria-hidden="true" />
                    Start Your Journey Today
                    <ArrowRight className="icon-xs group-hover:translate-x-1 transition-transform duration-200" strokeWidth={2} aria-hidden="true" />
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full sm:w-auto group font-semibold border border-primary hover:border-primary text-primary hover:bg-primary hover:text-primary-foreground transition-all duration-200" type="button">
                  <Link 
                    to="/tools" 
                    className="flex items-center justify-center gap-2"
                    rel="noopener noreferrer"
                    onClick={() => {
                      // TODO: Add analytics tracking for CTA button clicks
                      // analytics.track('how_it_works_cta_clicked', { button: 'explore_tools' });
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        e.currentTarget.click();
                      }
                    }}
                  >
                    <Target className="icon-xs text-current" strokeWidth={2} aria-hidden="true" />
                    Explore Tools
                    <ArrowRight className="icon-xs group-hover:translate-x-1 transition-transform duration-200" strokeWidth={2} aria-hidden="true" />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
});

HowItWorksPage.displayName = 'HowItWorksPage';

export default HowItWorksPage;
