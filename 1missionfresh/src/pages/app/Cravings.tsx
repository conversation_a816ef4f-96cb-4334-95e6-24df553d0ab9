import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Flame, CheckCircle, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useQuery } from '@tanstack/react-query';
import { Database } from '@/lib/database.types';
import { supabase } from '@/lib/supabase';
import { CravingsTab, CravingEntry } from '@/components/log/CravingsTab';
import { saveCraving } from '@/services/logService';
import { toast } from 'sonner';
import SEOHead from '../../components/common/SEOHead';

const Cravings: React.FC = () => {
  const { user } = useAuth();
  const [cravingEntries, setCravingEntries] = useState<CravingEntry[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: userTriggers } = useQuery<Database['public']['Tables']['user_triggers']['Row'][]>({
    queryKey: ['user_triggers', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const { data, error } = await supabase.from('user_triggers').select('*').eq('user_id', user.id);
      if (error) throw new Error(error.message);
      return data;
    },
    enabled: !!user?.id,
  });

  const { data: userStrategies } = useQuery<Database['public']['Tables']['coping_strategies']['Row'][]>({
    queryKey: ['coping_strategies', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const { data, error } = await supabase.from('coping_strategies').select('*').eq('user_id', user.id);
      if (error) throw new Error(error.message);
      return data;
    },
    enabled: !!user?.id,
  });

  const handleSubmit = async () => {
    if (!user || cravingEntries.length === 0) {
      toast.error("Please add at least one craving entry to save.");
      return;
    }

    setIsSubmitting(true);

    try {
      for (const entry of cravingEntries) {
        const cravingData = {
          intensity: entry.intensity,
          trigger: entry.trigger === 'other' ? entry.otherTrigger : entry.trigger,
          timestamp: new Date().toISOString(),
          trigger_id: entry.trigger_id,
          coping_strategy_id: entry.coping_strategy_id,
        };
        
        await saveCraving(cravingData);
      }

      toast.success("All cravings logged successfully!");
      setCravingEntries([]); // Clear entries after successful save
    } catch (error) {
      console.error('Error saving cravings:', error);
      toast.error("Failed to save craving entries. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <SEOHead title="Cravings" description="Log and track your cravings to understand patterns and celebrate progress." />
      <div className="px-6 py-8 space-y-8 max-w-7xl mx-auto">
        
        {/* FIXED FLAWS #86-89: 2025 PERFECTION - CRAVINGS PAGE HEADER */}
        <header className="text-center space-y-4 mb-8">
          <div className="flex items-center justify-center gap-3 mb-3">
            <div className="flex items-center justify-center w-10 h-10 bg-primary/10 border border-primary/20" style={{borderRadius: '32px'}}>
              <Flame className="w-5 h-5 text-primary" strokeWidth={2} />
            </div>
            <h1 className="text-2xl font-bold text-foreground leading-tight tracking-tight">Craving Log</h1>
          </div>
          <p className="text-sm text-muted-foreground font-normal leading-[1.5] max-w-2xl mx-auto">
            Track your cravings with precision. Understand patterns, identify triggers, and celebrate every moment of progress on your wellness journey.
          </p>
        </header>

        <Card className="border border-border/50 bg-background hover:border-primary/30 hover:shadow-md transition-all duration-200" style={{borderRadius: '24px'}}>
          <CardHeader className="p-6 pb-4">
            <CardTitle className="text-lg font-semibold text-foreground leading-tight">Log Your Cravings</CardTitle>
          </CardHeader>
          <CardContent className="p-6 pt-4 space-y-6">
            <CravingsTab 
              cravingEntries={cravingEntries}
              setCravingEntries={setCravingEntries}
              userTriggers={userTriggers || []}
              userStrategies={userStrategies || []}
              validationErrors={{}}
            />
            
            {cravingEntries.length > 0 && (
              <div className="space-y-4">
                <Button 
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="w-full bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 h-12 font-semibold text-base transition-all duration-200 hover:shadow-md" style={{borderRadius: '24px'}}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-3 w-5 h-5 animate-spin" strokeWidth={2} />
                      Saving Cravings...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-3 w-5 h-5" strokeWidth={2} />
                      Save {cravingEntries.length} Craving{cravingEntries.length > 1 ? 's' : ''}
                    </>
                  )}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

      </div>
    </>
  );
};

export default Cravings;