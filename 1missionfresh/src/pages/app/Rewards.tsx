import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { getRewardsPageData, claimSpecificReward, RewardType as ServiceRewardType, RewardHistoryItem, UserBadge } from "@/services/rewardsService";
import { Loader2, Gift, Clock, Trophy, Award } from "lucide-react";
import { formatDistanceToNow } from 'date-fns';
import { LoadingSpinner } from '@/components/common/LoadingSpinner';
import ErrorDisplay from '@/components/common/ErrorDisplay';
import EarnedBadgesDisplay from '@/components/app/gamification/EarnedBadgesDisplay';
import { badges as allBadgesDefinitions } from '@/lib/badgesData';

// --- Sub-components ---

const UserStats: React.FC<{ userPoints: number }> = ({ userPoints }) => (
  <Card className="border border-gray-100 bg-white shadow-sm h-fit" style={{ borderRadius: '16px' }}>
    <CardHeader className="p-6 pb-4">
      <div className="flex items-center gap-4 mb-2">
        <div className="flex items-center justify-center h-16 w-16 bg-primary" style={{ borderRadius: '16px' }}>
          <Trophy className="h-8 w-8 text-primary-foreground" strokeWidth={2} />
        </div>
        <CardTitle className="text-xl font-bold text-gray-900">Your Points</CardTitle>
      </div>
    </CardHeader>
    <CardContent className="p-6 pt-0">
      <div className="text-center py-6">
        <div className="text-5xl font-bold text-primary mb-3">
          {Math.abs(userPoints)}
        </div>
        <div className="text-base font-medium text-gray-600">
          {userPoints < 0 ? 'Points Needed' : 'Total Points'}
        </div>
        {userPoints < 0 && (
          <div className="mt-4 px-4 py-2 bg-primary/10 border border-primary/20" style={{ borderRadius: '12px' }}>
            <p className="text-sm text-primary font-medium">
              Complete activities to earn your first points!
            </p>
          </div>
        )}
      </div>
      <div className="space-y-3 mt-4 text-sm text-gray-600">
        <h3 className="font-semibold text-gray-900 text-base">How to earn points:</h3>
        <p className="leading-relaxed">Earn points for completing daily logs, achieving goals, and maintaining streaks.</p>
      </div>
    </CardContent>
  </Card>
);

const AvailableRewards: React.FC<{ rewards: ServiceRewardType[], userPoints: number, onClaim: (reward: ServiceRewardType) => void, isClaiming: boolean }> = ({ rewards, userPoints, onClaim, isClaiming }) => (
  <Card className="border border-gray-100 bg-white shadow-sm" style={{ borderRadius: '16px' }}>
    <CardHeader className="p-6">
      <CardTitle className="flex items-center gap-3 text-xl font-bold text-gray-900">
        <Trophy className="h-7 w-7 text-primary" strokeWidth={2} />
        Available Rewards
      </CardTitle>
      <CardDescription className="text-base text-gray-600">Claim rewards with your earned points.</CardDescription>
    </CardHeader>
    <CardContent className="p-6 pt-0">
      {rewards.length === 0 ? (
        <p className="py-8 text-center text-gray-500 text-base">No rewards available at this time.</p>
      ) : (
        <div className="space-y-4">
          {rewards.map((reward) => (
            <div key={reward.id} className="flex items-center justify-between p-6 border border-gray-100 bg-white" style={{ borderRadius: '16px' }}>
              <div className="flex-1">
                <h3 className="font-bold text-gray-900 text-lg leading-tight mb-2">{reward.name}</h3>
                <p className="text-base text-gray-600 leading-relaxed mb-3">{reward.description}</p>
                <div className="flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-primary" strokeWidth={2} />
                  <p className="text-base font-semibold text-primary">{reward.points_required} points</p>
                </div>
              </div>
              <div className="ml-8">
                <Button
                  onClick={() => onClaim(reward)}
                  disabled={isClaiming || userPoints < reward.points_required}
                  className={`px-6 py-3 font-semibold text-base transition-all duration-200 ${
                    userPoints < reward.points_required
                      ? "opacity-60 cursor-not-allowed border-2 border-gray-200 text-gray-400 bg-white"
                      : "bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm hover:shadow-md"
                  }`}
                  style={{ borderRadius: '12px' }}
                >
                  {isClaiming && <Loader2 className="mr-2 h-5 w-5 animate-spin" />}
                  {userPoints < reward.points_required ? "Insufficient Points" : "Claim Reward"}
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
    </CardContent>
  </Card>
);

const UserBadges: React.FC<{ earnedBadges: UserBadge[] }> = ({ earnedBadges }) => (
  <Card className="border border-gray-100 bg-white shadow-sm" style={{ borderRadius: '16px' }}>
    <CardHeader className="p-6">
      <CardTitle className="flex items-center gap-3 text-xl font-bold text-gray-900">
        <Award className="h-7 w-7 text-primary" strokeWidth={2} />
        Your Badges
      </CardTitle>
      <CardDescription className="text-base text-gray-600">Badges you've earned on your journey.</CardDescription>
    </CardHeader>
    <CardContent className="p-6 pt-0">
      <EarnedBadgesDisplay earnedBadges={earnedBadges} allBadgesDefinitions={allBadgesDefinitions} />
    </CardContent>
  </Card>
);

const RewardHistory: React.FC<{ history: RewardHistoryItem[] }> = ({ history }) => (
  <Card className="border border-gray-100 bg-white shadow-sm" style={{ borderRadius: '16px' }}>
    <CardHeader className="p-6">
      <CardTitle className="flex items-center gap-3 text-xl font-bold text-gray-900">
        <Clock className="h-7 w-7 text-primary" strokeWidth={2} />
        Recent Activity
      </CardTitle>
    </CardHeader>
    <CardContent className="p-6 pt-0">
      {history.length === 0 ? (
        <p className="py-8 text-center text-gray-500 text-base">No recent activity.</p>
      ) : (
        <div className="space-y-4">
          {history.slice(0, 5).map((item) => (
            <div key={item.id} className="flex items-center justify-between py-3">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-primary/10" style={{ borderRadius: '12px' }}>
                  <Gift className="h-6 w-6 text-primary" strokeWidth={2} />
                </div>
                <div>
                  <p className="text-base font-semibold text-gray-900">{item.name || `+${item.points} points`}</p>
                  <p className="text-sm text-gray-500">{formatDistanceToNow(new Date(item.date), { addSuffix: true })}</p>
                </div>
              </div>
              <div className={`text-base font-bold ${item.points > 0 ? 'text-primary' : 'text-destructive'}`}>
                {item.points > 0 ? `+${item.points}` : item.points}
              </div>
            </div>
          ))}
        </div>
      )}
    </CardContent>
  </Card>
);

// --- Main Rewards Page Component ---

const RewardsPage = () => {
  const queryClient = useQueryClient();

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ['rewardsPageData'],
    queryFn: getRewardsPageData,
  });

  const { userPoints = 0, availableRewards = [], rewardHistory = [], earnedBadges = [] } = data || {};

  const { mutate: claimReward, isPending: isClaiming } = useMutation({
    mutationFn: async (reward: ServiceRewardType) => {
      console.log('🎯 CLAIM MUTATION STARTED:', reward);
      try {
        const result = await claimSpecificReward(reward);
        console.log('✅ CLAIM MUTATION SUCCESS:', result);
        return result;
      } catch (error) {
        console.error('❌ CLAIM MUTATION ERROR:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log('🎉 MUTATION onSuccess called:', data);
      toast.success("Reward claimed successfully!");
      queryClient.invalidateQueries({ queryKey: ['rewardsPageData'] });
      queryClient.invalidateQueries({ queryKey: ['userProfile'] }); // Also update points in header
    },
    onError: (err: any) => {
      console.error('💥 MUTATION onError called:', err);
      toast.error(err.message || "Failed to claim reward");
    }
  });

  if (isLoading) return <LoadingSpinner text="Loading rewards..." />;
  if (isError) return <ErrorDisplay message={`Failed to load rewards: ${error.message}`} />;

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* FIXED FLAW #201-210: Header section with consistent styling */}
      <header className="text-center space-y-2 mb-8">
        <div className="flex items-center justify-center gap-2 mb-2">
          <div className="flex items-center justify-center h-10 w-10 bg-primary/10 shadow-sm" style={{ borderRadius: '16px' }}>
            <Trophy className="h-5 w-5 text-primary" strokeWidth={2.5} />
          </div>
          <h1 className="text-2xl font-bold text-foreground leading-tight">Rewards & Achievements</h1>
        </div>
        <p className="text-sm text-muted-foreground leading-relaxed max-w-2xl mx-auto">
          Your dedication drives your success. Celebrate milestones and claim your well-earned rewards.
        </p>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div className="space-y-6">
          <AvailableRewards rewards={availableRewards} userPoints={userPoints} onClaim={claimReward} isClaiming={isClaiming} />
          <UserBadges earnedBadges={earnedBadges} />
        </div>
        <div className="space-y-6">
          <UserStats userPoints={userPoints} />
          <RewardHistory history={rewardHistory} />
        </div>
      </div>
    </div>
  );
};

export default RewardsPage;
