import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { getUserProfile } from '@/services/profileService';
import { Loader2, Award, Share2 } from 'lucide-react';
import { toast } from 'sonner';
import { getDashboardStats, DashboardStats } from '@/services/dashboardService';
import { gamificationService, UserAchievementDisplay } from '@/services/gamificationService';
import * as htmlToImage from 'html-to-image';
import ShareableProgressImage from '@/components/app/progress/ShareableProgressImage';
import AchievementsList from '@/components/app/progress/AchievementsList';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useNavigate } from 'react-router-dom';

const ProfilePage: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [profileData, setProfileData] = useState<{ fullName: string; avatarUrl: string } | null>(null);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [achievements, setAchievements] = useState<UserAchievementDisplay[]>([]);
  const [loading, setLoading] = useState(true);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const shareImageRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchAllData = async () => {
      if (!user) return;

      setLoading(true);
      try {
        const [profile, stats, fetchedUserAchievements] = await Promise.all([
          getUserProfile(),
          getDashboardStats(user.id),
          gamificationService.getUserAchievements(user.id),
        ]);

        if (profile) {
          setProfileData({
            fullName: profile.full_name || '',
            avatarUrl: profile.avatar_url || '',
          });
        }
        setDashboardStats(stats);
        setAchievements(Array.isArray(fetchedUserAchievements) ? fetchedUserAchievements : []);
      } catch (error) {
        console.error('Error fetching profile page data:', error);
        toast.error('Failed to load profile information');
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, [user]);

  const handleShareProgress = async () => {
    if (!shareImageRef.current) {
      toast.error('Could not generate share image.');
      return;
    }

    setIsGeneratingImage(true);
    try {
      const dataUrl = await htmlToImage.toPng(shareImageRef.current);
      const blob = await (await fetch(dataUrl)).blob();

      if (navigator.share && navigator.canShare({ files: [new File([blob], 'progress.png', { type: 'image/png' })] })) {
        await navigator.share({
          files: [new File([blob], 'progress.png', { type: 'image/png' })],
          title: 'My Fresh Start Progress',
          text: `Check out my progress on Fresh Start!`,
        });
      } else {
        const link = document.createElement('a');
        link.download = 'fresh-start-progress.png';
        link.href = dataUrl;
        link.click();
        toast.info('Image downloaded. You can share it from your downloads.');
      }
    } catch (error) {
      console.error('Error generating or sharing image:', error);
      toast.error('Failed to generate or share progress image.');
    } finally {
      setIsGeneratingImage(false);
    }
  };

  if (loading) {
    return <div className="flex items-center justify-center h-full"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>;
  }

  return (
      <div className="p-4 md:p-6 space-y-6">
        <header className="text-center md:text-left">
          <h1 className="text-2xl font-semibold">Your Profile</h1>
          <p className="text-muted-foreground mt-1">View your progress and achievements.</p>
        </header>

        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-1 space-y-6">
            <Card>
              <CardHeader className="flex flex-row items-center gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={profileData?.avatarUrl} alt={profileData?.fullName} />
                  <AvatarFallback>{profileData?.fullName?.[0] || 'U'}</AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="text-xl">{profileData?.fullName || user?.email}</CardTitle>
                  <CardDescription>Member</CardDescription>
                </div>
              </CardHeader>
              <CardContent>
                <Button className="w-full" onClick={() => navigate('/app/settings')}>
                  Edit Profile
                </Button>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Share Your Progress</CardTitle>
                <CardDescription>Share a snapshot of your journey.</CardDescription>
              </CardHeader>
              <CardContent>
                <Button onClick={handleShareProgress} disabled={isGeneratingImage} className="w-full">
                  {isGeneratingImage ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Share2 className="mr-2 h-4 w-4" />}
                   Share Progress
                </Button>
              </CardContent>
            </Card>
          </div>
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-primary" />
                  Achievements
                </CardTitle>
                <CardDescription>All the milestones you've reached.</CardDescription>
              </CardHeader>
              <CardContent>
                <AchievementsList achievements={achievements} isLoading={loading} />
              </CardContent>
            </Card>
          </div>
        </div>
        <div ref={shareImageRef} className="fixed top-[-9999px] left-[-9999px]">
          {isGeneratingImage && dashboardStats && profileData && (
            <ShareableProgressImage
              daysQuit={dashboardStats.daysAfresh}
              moneySaved={dashboardStats.moneySaved}
              cigarettesNotSmoked={dashboardStats.cigarettesNotSmoked}
              userName={profileData.fullName || user?.email || 'User'}
              achievementsCount={achievements.length}
            />
          )}
        </div>
      </div>
  );
};

export default ProfilePage;
