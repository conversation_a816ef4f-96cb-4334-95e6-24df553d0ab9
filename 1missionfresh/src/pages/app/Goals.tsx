import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { UserGoal, UserGoalInsert, getUserGoal, saveUserGoal, updateUserGoal, deleteUserGoal } from "@/services/goalService";
import { useAuth } from "@/contexts/AuthContext";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Trash2, Loader2, Calendar as CalendarIcon, Target, DollarSign, MessageCircle } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { LoadingSpinner } from '@/components/common/LoadingSpinner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { useOfflineSupport } from "@/hooks/useOfflineSupport";
import { ProductSelector } from "@/components/goals/ProductSelector";

// --- Sub-components for Display and Form ---

const GoalDetailItem = ({ label, value }: { label: string, value: React.ReactNode }) => (
  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between detail-item-padding detail-item-border last:border-b-0 detail-item-gap">
    <p className="detail-item-label-text font-medium text-muted-foreground leading-[1.5] min-w-0 flex-shrink-0">{label}</p>
    <div className="detail-item-value-text font-semibold text-foreground text-right min-w-0 flex-1">
      {value || <span className="text-muted-foreground italic font-normal">Not set</span>}
    </div>
  </div>
);

interface GoalDisplayProps {
  goal: Partial<UserGoal>;
  onEdit: () => void;
  onDelete: () => void;
}

const GoalDisplay: React.FC<GoalDisplayProps> = ({ goal, onEdit, onDelete }) => (
  <div className="w-full display-max-width mx-auto">
    <div className="grid grid-cols-1 lg:grid-cols-2 display-grid-gap display-margin-bottom">
      {/* FIXED FLAWS #76-85: 2025 PERFECTION - HOMEPAGE-LEVEL CARD STYLING */}
      <Card className="card-unified h-full flex flex-col">
        <CardHeader className="display-card-header-padding flex-shrink-0">
          <div className="flex items-center display-header-gap display-header-margin-bottom">
            <div className="flex items-center justify-center icon-container-md card-rounded bg-primary/10 border border-primary/20">
              <Target className="icon-md text-primary" strokeWidth={2} />
            </div>
            <CardTitle className="display-card-title">Current Goal</CardTitle>
          </div>
          <CardDescription className="display-card-description">
            Your commitment to wellness and recovery
          </CardDescription>
        </CardHeader>
        <CardContent className="display-card-content-padding display-card-content-spacing flex-grow flex flex-col">
          <div className="display-content-spacing flex-grow">
            <GoalDetailItem 
              label="Goal Type" 
              value={goal.goal_type === 'quit_nicotine' ? 'Quit Nicotine Entirely' : goal.goal_type === 'reduce_usage' ? 'Reduce Usage' : goal.goal_type} 
            />
            <GoalDetailItem 
              label="Method" 
              value={goal.method === 'cold_turkey' ? 'Cold Turkey' : goal.method === 'gradual_reduction' ? 'Gradual Reduction' : goal.method} 
            />
            <GoalDetailItem 
              label="Start/Quit Date" 
              value={goal.quit_date ? format(new Date(goal.quit_date), 'PPP') : null} 
            />
          </div>
          <div className="display-button-margin-top mt-auto">
            <Button onClick={onEdit} className="btn-primary w-full">
              <Target className="display-button-icon-margin icon-md" strokeWidth={2} />
              Edit Goal
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card className="card-unified">
        <CardHeader className="display-card-header-padding flex-shrink-0">
          <div className="flex items-center display-header-gap display-header-margin-bottom">
            <div className="flex items-center justify-center" style={{ borderRadius: '16px', backgroundColor: 'hsl(var(--primary) / 0.1)', border: '1px solid hsl(var(--primary) / 0.2)' }}>
              <DollarSign className="icon-md text-primary" strokeWidth={2} />
            </div>
            <CardTitle className="display-card-title">Financial Details</CardTitle>
          </div>
          <CardDescription className="display-card-description">
            Track your savings and financial progress
          </CardDescription>
        </CardHeader>
        <CardContent className="display-card-content-padding display-financial-card-spacing flex-grow">
          <div className="financial-detail-spacing">
            <GoalDetailItem 
              label="Product Type" 
              value={goal.product_type} 
            />
            <GoalDetailItem 
              label="Daily Usage" 
              value={goal.typical_daily_usage ? `${goal.typical_daily_usage} units/day` : null} 
            />
            <GoalDetailItem 
              label="Cost Per Unit" 
              value={goal.cost_per_unit ? `$${parseFloat(goal.cost_per_unit.toString()).toFixed(2)}` : null} 
            />
          </div>
        </CardContent>
      </Card>
    </div>

    {/* FIXED FLAWS #76-85: 2025 PERFECTION - YOUR MOTIVATION SECTION */}
    <Card className="card-unified mt-6">
      <CardHeader className="display-card-header-padding">
        <div className="flex items-center display-header-gap display-header-margin-bottom">
          <div className="flex items-center justify-center" style={{ borderRadius: '16px', backgroundColor: 'hsl(var(--primary) / 0.1)', border: '1px solid hsl(var(--primary) / 0.2)' }}>
            <MessageCircle className="icon-md text-primary" strokeWidth={2} />
          </div>
          <CardTitle className="display-card-title">Your Motivation</CardTitle>
        </div>
        <CardDescription className="display-card-description">
          Your personal 'why' that drives your journey
        </CardDescription>
      </CardHeader>
      <CardContent className="display-card-content-padding">
        <div className="bg-primary/5 card-rounded motivation-content-padding border border-primary/10">
          <blockquote className="text-foreground italic leading-[1.6] text-left font-medium">
            "{goal.motivation}"
          </blockquote>
        </div>
      </CardContent>
    </Card>
    
    {/* FIXED FLAWS #76-85: 2025 PERFECTION - DELETE GOAL BUTTON */}
    <div className="flex justify-center">
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button variant="outline" className="border-2 border-destructive/20 text-destructive hover:bg-destructive/10 font-semibold px-6 py-3" style={{ borderRadius: '24px' }}>
            <Trash2 className="delete-button-icon-margin icon-md" strokeWidth={2} />
            Delete Goal
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete your goal. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={onDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  </div>
);

interface GoalFormProps {
  goal: Partial<UserGoal>;
  onFieldChange: (field: keyof UserGoal, value: any) => void;
  onSubmit: (e: React.FormEvent) => void;
  onCancel: () => void;
  isSaving: boolean;
  existingGoalId: string | null;
}

const GoalForm: React.FC<GoalFormProps> = ({ goal, onFieldChange, onSubmit, onCancel, isSaving, existingGoalId }) => (
  <form onSubmit={onSubmit} className="w-full max-w-3xl mx-auto space-y-8">
    <div className="p-6 bg-card border border-border shadow-sm" style={{ borderRadius: '16px' }}>
      <CardHeader className="pb-4">
        <CardTitle className="text-2xl font-black tracking-tight text-foreground">Goal Type</CardTitle>
        <CardDescription className="text-lg font-semibold text-muted-foreground font-medium">What are you aiming for?</CardDescription>
      </CardHeader>
      <CardContent className="pt-2">
        <RadioGroup
          value={goal.goal_type || ''}
          onValueChange={(value) => onFieldChange('goal_type', value)}
          className="flex flex-col space-y-4"
        >
          <Label className="flex items-center space-x-6 space-y-0 cursor-pointer p-6 hover:bg-secondary border border-transparent hover:border-primary transition-all duration-300" style={{ borderRadius: '16px' }}>
            <RadioGroupItem value="quit_nicotine" className="text-primary" />
            <span className="text-lg font-medium">Quit Nicotine Entirely</span>
          </Label>
          <Label className="flex items-center space-x-6 space-y-0 cursor-pointer p-6 hover:bg-secondary border border-transparent hover:border-primary transition-all duration-300" style={{ borderRadius: '16px' }}>
            <RadioGroupItem value="reduce_usage" className="text-primary" />
            <span className="text-lg font-medium">Reduce Usage</span>
          </Label>
        </RadioGroup>
      </CardContent>
    </div>

    <Card className="p-6 bg-card border border-border shadow-sm" style={{ borderRadius: '16px' }}>
      <CardHeader className="pb-4">
        <CardTitle className="text-2xl font-bold tracking-tight text-foreground">Method</CardTitle>
        <CardDescription className="text-lg text-muted-foreground font-medium">How do you plan to achieve this?</CardDescription>
      </CardHeader>
      <CardContent className="pt-2">
        <RadioGroup
          value={goal.method || ''}
          onValueChange={(value) => onFieldChange('method', value)}
          className="flex flex-col space-y-4"
        >
          <Label className="flex items-center space-x-6 space-y-0 cursor-pointer p-6 hover:bg-secondary border border-transparent hover:border-primary transition-all duration-300" style={{ borderRadius: '16px' }}>
            <RadioGroupItem value="cold_turkey" className="text-primary" />
            <span className="text-lg font-medium">Cold Turkey</span>
          </Label>
          <Label className="flex items-center space-x-6 space-y-0 cursor-pointer p-6 hover:bg-secondary border border-transparent hover:border-primary transition-all duration-300" style={{ borderRadius: '16px' }}>
            <RadioGroupItem value="gradual_reduction" className="text-primary" />
            <span className="text-lg font-medium">Gradual Reduction</span>
          </Label>
        </RadioGroup>
      </CardContent>
    </Card>

    <Card className="p-6 bg-card border border-border shadow-sm" style={{ borderRadius: '16px' }}>
      <CardHeader className="pb-4">
        <CardTitle className="text-2xl font-bold tracking-tight text-foreground">Start / Quit Date</CardTitle>
        <CardDescription className="text-lg text-muted-foreground font-medium">When will your journey begin?</CardDescription>
      </CardHeader>
      <CardContent className="pt-2">
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="lg" className={cn("w-full justify-start text-left font-medium text-lg shadow-sm hover:shadow-md transition-all duration-300", !goal.quit_date && "text-muted-foreground")}>
              <CalendarIcon className="mr-4 h-6 w-6" />
              {goal.quit_date ? format(new Date(goal.quit_date), "PPP") : <span>Pick a date</span>}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={goal.quit_date ? new Date(goal.quit_date) : undefined}
              onSelect={(date) => onFieldChange('quit_date', date)}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </CardContent>
    </Card>

    <Card className="p-6 bg-card border border-border shadow-sm" style={{ borderRadius: '16px' }}>
      <CardHeader className="pb-4">
        <CardTitle className="text-2xl font-black tracking-tight text-foreground">Financial Tracking (Optional)</CardTitle>
        <CardDescription className="text-lg font-semibold text-muted-foreground font-medium">Help us calculate your savings progress.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 pt-2">
        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="product_type">Product Type</Label>
            <ProductSelector
              product={goal.product_type || ''}
              setProduct={(product) => onFieldChange('product_type', product)}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="space-y-2">
              <Label htmlFor="typical_daily_usage">Daily Usage</Label>
              <Input id="typical_daily_usage" type="number" step="0.1" value={goal.typical_daily_usage || ''} onChange={e => onFieldChange('typical_daily_usage', parseFloat(e.target.value) || null)} placeholder="e.g., 20" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="cost_per_unit">Cost Per Unit ($)</Label>
              <Input id="cost_per_unit" type="number" step="0.01" value={goal.cost_per_unit || ''} onChange={e => onFieldChange('cost_per_unit', parseFloat(e.target.value) || null)} placeholder="e.g., 0.50" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <Card className="border-2 border-primary/20 shadow-2xl hover:shadow-xl transition-all duration-500" style={{ borderRadius: '24px' }}>
      <CardHeader className="pb-4">
        <CardTitle className="text-2xl font-black tracking-tight text-foreground">Your Motivation</CardTitle>
        <CardDescription className="text-lg font-semibold text-muted-foreground font-medium">What is your 'why'? This will be your anchor.</CardDescription>
      </CardHeader>
      <CardContent className="pt-2">
        <Textarea id="motivation" value={goal.motivation || ''} onChange={e => onFieldChange('motivation', e.target.value)} placeholder="For my health, for my family..." className="min-h-[100px]" />
      </CardContent>
    </Card>

    <div className="flex items-center justify-center gap-6 pt-8">
      <Button 
        type="submit" 
        disabled={isSaving}
        className="px-8 py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300 border-2 border-primary"
      >
        {isSaving && <Loader2 className="mr-3 h-6 w-6 animate-spin" />}
        {existingGoalId ? 'Save Changes' : 'Set My Goal'}
      </Button>
      {existingGoalId && (
        <Button 
          variant="outline" 
          onClick={onCancel}
          className="px-8 py-4 text-lg font-bold border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300"
        >
          Cancel
        </Button>
      )}
    </div>
  </form>
);

// --- Main Goals Page Component ---

const GoalsPage: React.FC = () => {
  const { user } = useAuth();
  const { isOnline } = useOfflineSupport();
  
  const [goal, setGoal] = useState<Partial<UserGoal>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [existingGoalId, setExistingGoalId] = useState<string | null>(null);

  // Fix infinite loop by using useEffect with direct dependencies instead of useCallback
  useEffect(() => {
    if (!user) {
      setIsLoading(false);
      return;
    }
    
    const fetchGoal = async () => {
      setIsLoading(true);
      try {
        if (isOnline) {
          const serverGoal = await getUserGoal();
          if (serverGoal) {
            setGoal(serverGoal);
            setExistingGoalId(serverGoal.id);
            setIsEditing(false);
          } else {
            setGoal({});
            setExistingGoalId(null);
            setIsEditing(true);
          }
        } else {
          toast.info("You are offline. Goal management is limited.");
          setIsEditing(true);
        }
      } catch (error) {
        console.error("Goal fetch error:", error);
        toast.error("Could not load your goal.");
        setGoal({});
        setExistingGoalId(null);
        setIsEditing(true);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchGoal();
  }, [user, isOnline]); // Direct dependencies prevent infinite loop

  const handleFieldChange = (field: keyof UserGoal, value: any) => {
    setGoal((prev: Partial<UserGoal>) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return toast.error("You must be logged in.");
    
    // More lenient validation - only require the essential fields
    if (!goal.goal_type || !goal.method) {
      return toast.error("Please fill out Goal Type and Method.");
    }
    
    // Prepare goal data with proper date handling (no infinite loop)
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day for accurate comparison
    
    let finalQuitDate = goal.quit_date;
    if (!finalQuitDate) {
      console.log("No quit date set, using today");
      finalQuitDate = today.toISOString();
    } else {
      const quitDate = new Date(finalQuitDate);
      quitDate.setHours(0, 0, 0, 0);
      if (quitDate > today) {
        console.log("Future quit date detected, using today instead");
        finalQuitDate = today.toISOString();
      }
    }

    setIsSaving(true);
    const goalToSave: Partial<UserGoal> = { 
      ...goal,
      quit_date: finalQuitDate,
      user_id: user.id,
      status: existingGoalId ? goal.status || 'in_progress' : 'in_progress'
    };

    try {
      console.log("Saving goal data:", goalToSave);
      const savedGoal = existingGoalId
        ? await updateUserGoal(existingGoalId, goalToSave)
        : await saveUserGoal(goalToSave as UserGoalInsert);

      if (!savedGoal) throw new Error("Failed to save goal.");

      console.log("Goal saved successfully:", savedGoal);
      toast.success(`Goal ${existingGoalId ? 'updated' : 'set'} successfully!`);
      setGoal(savedGoal);
      setExistingGoalId(savedGoal.id);
      setIsEditing(false);
    } catch (error: any) {
      console.error("Goal save error:", error);
      toast.error(error.message || "Failed to save goal.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!existingGoalId) return;
    try {
      await deleteUserGoal(existingGoalId);
      toast.success("Goal deleted.");
      setGoal({});
      setExistingGoalId(null);
      setIsEditing(true);
    } catch (error) {
      toast.error("Failed to delete goal.");
    }
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="page-container page-spacing page-max-width mx-auto">
      <header className="text-center header-spacing section-margin-bottom">
        <div className="flex items-center justify-center header-icon-gap icon-margin-bottom">
          <div className="flex items-center justify-center icon-container-md card-rounded bg-primary/10 border border-primary/20">
            <Target className="icon-md text-primary" strokeWidth={2} />
          </div>
          <h1 className="text-heading-xl font-bold text-foreground leading-premium tracking-premium">Goals</h1>
        </div>
        <p className="page-description page-description-max-width mx-auto">
          {existingGoalId ? "Your personalized roadmap to wellness and freedom." : "Define your path to a healthier, smoke-free future."}
        </p>
      </header>

      {!isOnline && (
        <div className="notification-padding bg-warning notification-border-left border-warning text-warning-foreground notification-rounded">
          <p className="font-bold">Offline Mode</p>
          <p>You are currently offline. Goal management is limited.</p>
        </div>
      )}

      {isEditing || !existingGoalId ? (
        <GoalForm
          goal={goal}
          onFieldChange={handleFieldChange}
          onSubmit={handleSubmit}
          onCancel={() => setIsEditing(false)}
          isSaving={isSaving}
          existingGoalId={existingGoalId}
        />
      ) : (
        <GoalDisplay
          goal={goal}
          onEdit={() => setIsEditing(true)}
          onDelete={handleDelete}
        />
      )}
    </div>
  );
};

export default GoalsPage;
