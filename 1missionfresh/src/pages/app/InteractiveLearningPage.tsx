import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { fetchLearningModuleBySlug, LearningModule } from '@/services/learningService';
import SEOHead from '@/components/common/SEOHead';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Loader2, AlertTriangle, BookOpen, ArrowLeft } from 'lucide-react';

interface ContentItemBase {
  id: string;
  type: string;
}
interface ParagraphItem extends ContentItemBase { type: 'paragraph'; text: string; }
interface HeadingItem extends ContentItemBase { type: 'heading'; level: 1 | 2 | 3 | 4 | 5 | 6; text: string; }
interface ImageItem extends ContentItemBase { type: 'image'; url: string; caption?: string; alt?: string; }
interface VideoItem extends ContentItemBase { type: 'video'; embed_url: string; title?: string; }
interface ListItem extends ContentItemBase { type: 'list'; style: 'ordered' | 'unordered'; items: string[]; }
interface QuoteItem extends ContentItemBase { type: 'quote'; text: string; attribution?: string; }
type LearningContentItem = ParagraphItem | HeadingItem | ImageItem | VideoItem | ListItem | QuoteItem;

const ModuleContentRenderer: React.FC<{ item: LearningContentItem }> = ({ item }) => {
  switch (item.type) {
    case 'paragraph':
      return <p className="mb-4 text-lg leading-relaxed text-foreground">{item.text}</p>;
    case 'heading': {
      const Tag = `h${item.level}` as keyof JSX.IntrinsicElements;
      const textSize = ['text-4xl', 'text-3xl', 'text-2xl', 'text-xl', 'text-lg', 'text-base'][item.level - 1] || 'text-xl';
      return <Tag className={`font-bold font-heading tracking-tight mt-8 mb-4 ${textSize}`}>{item.text}</Tag>;
    }
    case 'image':
      return (
        <figure className="my-8">
          <img src={item.url} alt={item.alt || item.caption || 'Learning module image'} className="max-w-full h-auto shadow-lg mx-auto" style={{borderRadius: '32px'}} />
          {item.caption && <figcaption className="text-center text-sm text-muted-foreground mt-2">{item.caption}</figcaption>}
        </figure>
      );
    case 'video':
      return (
        <div className="my-8 aspect-video">
          <iframe
            src={item.embed_url}
            title={item.title || 'Learning module video'}
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            className="w-full h-full shadow-lg" style={{borderRadius: '32px'}}
          ></iframe>
        </div>
      );
    case 'list': {
      const ListTag = item.style === 'ordered' ? 'ol' : 'ul';
      return (
        <ListTag className={`my-6 pl-6 text-lg ${item.style === 'ordered' ? 'list-decimal' : 'list-disc'}`}>
          {item.items.map((text, index) => (
            <li key={index} className="mb-2">{text}</li>
          ))}
        </ListTag>
      );
    }
    case 'quote':
      return (
        <blockquote className="my-8 p-6 border-l-4 border-primary bg-secondary text-foreground" style={{borderTopRightRadius: '16px', borderBottomRightRadius: '16px'}}>
          <p className="text-xl italic leading-relaxed">"{item.text}"</p>
          {item.attribution && <footer className="mt-4 text-base font-medium">- {item.attribution}</footer>}
        </blockquote>
      );
    default:
      return null;
  }
};

const InteractiveLearningPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();

  const { data: module, isLoading, error, isError } = useQuery<LearningModule | null, Error>({
    queryKey: ['learningModule', slug],
    queryFn: () => fetchLearningModuleBySlug(slug || ''),
    enabled: !!slug,
  });

  if (isLoading) {
    return (
      <>
        <SEOHead title="Loading Module..." description="Loading learning content..." />
        <div className="flex flex-col justify-center items-center min-h-[calc(100vh-200px)]">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="mt-4 text-muted-foreground">Loading learning module...</p>
        </div>
      </>
    );
  }

  if (isError || !module) {
    return (
      <>
        <SEOHead title="Module Not Found" description="Could not find the requested learning module." />
        <div className="flex flex-col justify-center items-center min-h-[calc(100vh-200px)] text-center px-4">
          <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
          <h1 className="text-2xl font-semibold mb-2">Oops! Module not found.</h1>
          <p className="text-muted-foreground mb-6">
            {error?.message || "We couldn't find the learning module you're looking for. It might have been moved or doesn't exist."}
          </p>
          <Button asChild variant="outline">
            <Link to="/app/learn">
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Learning Modules
            </Link>
          </Button>
        </div>
      </>
    );
  }

  const contentArray = Array.isArray(module.content) ? module.content as unknown as LearningContentItem[] : [];

  return (
    <>
      <SEOHead title={module.title} description={module.description || 'Learn more about your quit journey.'} />
      <div className="container mx-auto py-10 px-4 max-w-4xl">
        <Button asChild variant="ghost" className="mb-8 text-muted-foreground hover:text-foreground">
          <Link to="/app/learn">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to All Modules
          </Link>
        </Button>

        <Card className="overflow-hidden">
          <CardHeader className="p-8 border-b bg-muted">
            {module.category && <Badge variant="outline" className="mb-3">{module.category}</Badge>}
            <CardTitle className="text-4xl font-bold tracking-tight font-heading">{module.title}</CardTitle>
            {module.description && <CardDescription className="text-lg mt-2 text-muted-foreground">{module.description}</CardDescription>}
            {module.estimated_duration_minutes && (
              <p className="text-sm text-muted-foreground mt-4">
                Estimated duration: {module.estimated_duration_minutes} minutes
              </p>
            )}
          </CardHeader>
          <CardContent className="p-8">
            {contentArray.length > 0 ? (
              contentArray.map((item) => (
                <ModuleContentRenderer key={item.id || JSON.stringify(item)} item={item} />
              ))
            ) : (
              <div className="text-center text-muted-foreground py-16">
                <BookOpen className="mx-auto h-12 w-12 mb-4" />
                <p className="text-lg">This learning module doesn't have any content yet.</p>
                <p>Please check back later!</p>
              </div>
            )}
          </CardContent>
          <CardFooter className="p-8 border-t bg-muted flex justify-end">
            <Button 
              onClick={() => {
                // Mark module as complete in user progress
                if (module.id) {
                  // Implementation would update user's completed modules
                  console.log('Marking module as complete:', module.id);
                }
              }}
            >
              Mark as Complete
            </Button>
          </CardFooter>
        </Card>
      </div>
    </>
  );
};

export default InteractiveLearningPage;
