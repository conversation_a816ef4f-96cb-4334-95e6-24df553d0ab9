import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { fetchLearningModuleBySlug, markModuleAsComplete, LearningModule, getUserLearningProgress } from '@/services/learningService';
import { LoadingSpinner } from '@/components/common/LoadingSpinner';
import ErrorDisplay from '@/components/common/ErrorDisplay';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { CheckCircle, BookOpen, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

import SEOHead from '@/components/common/SEOHead';
import { earnPoints } from '@/services/rewardsService'; // For gamification

// Define a basic structure for module content sections if it's an array of objects
interface ModuleSection {
  type: 'paragraph' | 'heading' | 'list' | 'image'; // Example types
  content?: string; // For paragraph, heading
  items?: string[]; // For list
  src?: string; // For image
  alt?: string; // For image
}

const ModulePage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [module, setModule] = useState<LearningModule | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const [isCompleting, setIsCompleting] = useState(false);

  useEffect(() => {
    const loadModule = async () => {
      if (!slug) {
        setError("Module slug not found.");
        setIsLoading(false);
        return;
      }
      setIsLoading(true);
      try {
        const fetchedModule = await fetchLearningModuleBySlug(slug) as LearningModule;
        if (fetchedModule) {
          setModule(fetchedModule);
          if (user?.id && fetchedModule.id) {
            const completedModules = await getUserLearningProgress(user.id);
            setIsCompleted(completedModules.includes(fetchedModule.id));
          }
        } else {
          setError("Learning module not found or not published.");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load module.");
      } finally {
        setIsLoading(false);
      }
    };
    loadModule();
  }, [slug, user?.id]);

  const handleMarkComplete = async () => {
    if (!user || !module || !module.id) {
      toast.error("Cannot mark module as complete. User or module data missing.");
      return;
    }
    setIsCompleting(true);
    try {
      const success = await markModuleAsComplete(user.id, module.id);
      if (success) {
        setIsCompleted(true);
        toast.success(`"${module.title}" marked as complete!`);
        await earnPoints(user.id, 25, 'learning_module_completion', module.title || 'a module');
      } else {
        toast.error("Failed to mark module as complete.");
      }
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "An error occurred.");
    } finally {
      setIsCompleting(false);
    }
  };

  const renderModuleContent = (content: any) => {
    if (!content) return <p>No content available for this module.</p>;

    if (typeof content === 'string') {
      return <p>{content}</p>;
    }

    if (Array.isArray(content)) {
      return content.map((section: any, index: number) => {
        if (typeof section === 'string') {
          return <p key={index} className="mb-4">{section}</p>;
        }
        if (typeof section === 'object' && section !== null && section.type) {
          const typedSection = section as ModuleSection;
          switch (typedSection.type) {
            case 'heading':
              return <h2 key={index} className="text-2xl font-semibold mt-6 mb-3">{typedSection.content}</h2>;
            case 'paragraph':
              return <p key={index} className="mb-4 leading-relaxed">{typedSection.content}</p>;
            case 'list':
              return (
                <ul key={index} className="list-disc pl-6 mb-4 space-y-1">
                  {typedSection.items?.map((item, itemIndex) => <li key={itemIndex}>{item}</li>)}
                </ul>
              );
            case 'image':
              return (
                <div key={index} className="my-6 text-center">
                  <img src={typedSection.src} alt={typedSection.alt || 'Module image'} className="max-w-full h-auto shadow-lg border border-border/20 mx-auto" style={{ borderRadius: '24px' }} />
                  {typedSection.alt && <p className="text-sm text-muted-foreground mt-2">{typedSection.alt}</p>}
                </div>
              );
            default:
              return <p key={index} className="mb-4">Unsupported content type.</p>;
          }
        }
        return <p key={index} className="mb-4">{JSON.stringify(section)}</p>;
      });
    }
    
    if (typeof content === 'object' && content !== null) {
        return <pre className="whitespace-pre-wrap bg-muted p-4 text-sm" style={{ borderRadius: '12px' }}>{JSON.stringify(content, null, 2)}</pre>;
    }

    return <p>Unsupported module content format.</p>;
  };


  if (isLoading) return <LoadingSpinner text="Loading module..." />;
  if (error) return <ErrorDisplay message={error} />;
  if (!module) return <ErrorDisplay message="Module not found." />;

  return (
    <>
      <SEOHead title={module.title} description={module.description || ''} />
      <div className="container mx-auto max-w-4xl py-12 px-4 md:px-6">
        <article className="mt-8">
          <header className="mb-8">
            <div className="flex items-center space-x-4 mb-4">
              <div className="p-3 bg-secondary text-primary" style={{ borderRadius: '16px' }}>
                <BookOpen className="h-8 w-8" />
              </div>
              <div>
                <p className="text-sm font-medium text-primary">{module.category}</p>
                <h1 className="text-4xl font-bold font-heading tracking-tight">{module.title}</h1>
              </div>
            </div>
            {module.description && (
              <p className="text-xl text-muted-foreground">
                {module.description}
              </p>
            )}
          </header>
          
          <div className="prose prose-lg max-w-none dark:prose-invert prose-p:leading-relaxed prose-headings:font-heading prose-headings:tracking-tight">
            {renderModuleContent(module.content)}
          </div>
        </article>

        <div className="mt-12 border-t pt-8 text-center">
          <Button
            onClick={handleMarkComplete}
            disabled={isCompleted || isCompleting}
            size="lg"
            className="min-w-[240px] text-lg py-3 px-8 transition-all duration-300 ease-in-out transform hover:scale-105"
            style={{ borderRadius: '50%' }}
          >
            {isCompleting ? (
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
            ) : isCompleted ? (
              <CheckCircle className="mr-2 h-5 w-5" />
            ) : null}
            {isCompleting ? "Saving..." : isCompleted ? "Completed" : "Mark as Complete"}
          </Button>
          {isCompleted && <p className="text-success mt-3 text-base">You've completed this module!</p>}
        </div>
      </div>
    </>
  );
};

export default ModulePage;
