import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Trash2, AlertCircle, Info, BookOpen, Flame, Heart, Cigarette } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { getLogsByDate, CombinedLogEntry, deleteNicotineLog, deleteCravingLog, deleteJournalEntry, deleteDailyCheckIn, NicotineLog, CravingLog, JournalEntry, DailyCheckIn } from '@/services/logService';
import { toast } from 'sonner';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";

// CRITICAL FIX: Use types directly from logService to avoid schema mismatch
type LogType = 'nicotine' | 'craving' | 'journal' | 'dailyCheckIn';

const LogCard: React.FC<{ icon: React.ReactNode; title: string; children: React.ReactNode; onDelete: () => void }> = ({ icon, title, children, onDelete }) => (
  <Card className="transition-all duration-200 hover:shadow-md border border-border bg-background hover:border-primary group shadow-sm" style={{borderRadius: '24px'}}>
    <CardHeader className="flex flex-row items-center justify-between pb-2 p-6">
      <div className="flex items-center gap-2">
        {icon}
        <CardTitle className="text-base font-medium text-foreground">{title}</CardTitle>
      </div>
      <Button variant="ghost" size="icon" onClick={onDelete} className="h-10 w-10 text-muted-foreground hover:text-destructive" style={{borderRadius: '24px'}}>
        <Trash2 className="h-4 w-4" />
      </Button>
    </CardHeader>
    <CardContent className="p-6 pt-0">{children}</CardContent>
  </Card>
);

const LogHistoryPage: React.FC = () => {
  const { user } = useAuth();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [logs, setLogs] = useState<CombinedLogEntry | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [itemToDelete, setItemToDelete] = useState<{ id: string; type: LogType } | null>(null);

  // AGGRESSIVE DEBUG - Log component state
  console.log('🔍 DEBUGGING - LogHistoryPage render - user:', user?.id, 'selectedDate:', selectedDate, 'logs:', logs);

  const fetchLogsForDate = useCallback(async (date: Date) => {
    // REAL DEBUG: Use console.log since toast might be broken
    console.log(`🔍 DEBUGGING - fetchLogsForDate called for date: ${format(date, 'yyyy-MM-dd')}`);
    
    if (!user) {
      console.error("🔍 DEBUGGING - fetchLogsForDate: No user found!");
      return;
    }
    
    console.log(`🔍 DEBUGGING - fetchLogsForDate: User found:`, user.id);
    setIsLoading(true);
    setError(null);
    try {
      const dateString = format(date, 'yyyy-MM-dd');
      console.log(`🔍 DEBUGGING - About to call getLogsByDate with userId: ${user.id} and date: ${dateString}`);
      const fetchedLogs = await getLogsByDate(user.id, dateString);
      console.log(`🔍 DEBUGGING - getLogsByDate returned:`, fetchedLogs);
      setLogs(fetchedLogs);
    } catch (err) {
      console.error(`🔍 DEBUGGING - fetchLogsForDate ERROR:`, err);
      setError(err instanceof Error ? err.message : "Failed to load logs.");
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (selectedDate && user) {
      fetchLogsForDate(selectedDate);
    } else if (!user) {
      setIsLoading(false);
    }
  }, [selectedDate, user, fetchLogsForDate]);

  const handleDelete = async () => {
    if (!itemToDelete) return;
    const { id, type } = itemToDelete;
    const deleteActions = {
      nicotine: deleteNicotineLog,
      craving: deleteCravingLog,
      journal: deleteJournalEntry,
      dailyCheckIn: deleteDailyCheckIn,
    };

    try {
      const success = await deleteActions[type](id);
      if (success) {
        toast.success("Log entry deleted successfully.");
        if (selectedDate) fetchLogsForDate(selectedDate);
      } else {
        toast.error(`Failed to delete log entry.`);
      }
    } catch (err) {
      toast.error(`Error deleting entry: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setItemToDelete(null);
    }
  };

  const renderedLogs = useMemo(() => {
    if (!logs) return [];
    const allLogs: (NicotineLog | CravingLog | JournalEntry | DailyCheckIn)[] = [
      ...(logs.nicotine_use_logs || []),
      ...(logs.craving_logs || []),
      ...(logs.journal_entries || []),
      // CRITICAL FIX: daily_check_ins is a single object, not an array
      ...(logs.daily_check_ins ? [logs.daily_check_ins] : []),
    ];
    // CRITICAL FIX: Different log types have different timestamp fields
    return allLogs.sort((a, b) => {
      const getTimestamp = (log: NicotineLog | CravingLog | JournalEntry | DailyCheckIn) => {
        if ('timestamp' in log) return log.timestamp; // CravingLog
        if ('created_at' in log) return log.created_at; // NicotineLog, JournalEntry, DailyCheckIn
        return ''; // fallback
      };
      return new Date(getTimestamp(b)).getTime() - new Date(getTimestamp(a)).getTime();
    });
  }, [logs]);

  const hasLogs = renderedLogs.length > 0;

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      <header className="text-center space-y-4 mb-8">
        <h1 className="text-3xl font-bold text-foreground">Log History</h1>
        <p className="text-muted-foreground">View and manage your wellness entries</p>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-start">
        <Card className="lg:sticky lg:top-6 transition-all duration-200 hover:shadow-md border border-border bg-background hover:border-primary group shadow-sm" style={{borderRadius: '24px'}}>
          <CardHeader className="p-6 pb-4"><CardTitle className="text-lg font-bold text-foreground">Select Date</CardTitle></CardHeader>
          <CardContent className="p-6 pt-0">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="p-0"
              disabled={(date) => date > new Date() || date < new Date("2020-01-01")}
            />
          </CardContent>
        </Card>

        <div className="lg:col-span-2 space-y-4">
          {isLoading ? (
            [...Array(3)].map((_, i) => <Skeleton key={i} className="h-24 w-full" style={{ borderRadius: '16px' }} />)
          ) : error ? (
            <Alert variant="destructive">
              <Skeleton className="h-4 w-1/4 mb-2" style={{ borderRadius: '8px' }} />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          ) : hasLogs ? (
            renderedLogs.map(log => {
              if ('product_type' in log) {
                return (
                  <LogCard key={log.id} icon={<Cigarette className="h-5 w-5" />} title={`Nicotine: ${log.product_type}`} onDelete={() => setItemToDelete({ id: log.id, type: 'nicotine' })}>
                    <p>Quantity: {log.quantity}</p>
                  </LogCard>
                );
              } else if ('intensity' in log) {
                return (
                  <LogCard key={log.id} icon={<Flame className="h-5 w-5" />} title={`Craving at ${format(parseISO(log.timestamp), 'p')}`} onDelete={() => setItemToDelete({ id: log.id, type: 'craving' })}>
                    <p>Intensity: {log.intensity}/10</p>
                  </LogCard>
                );
              } else if ('content' in log) {
                return (
                  <LogCard key={log.id} icon={<BookOpen className="h-5 w-5" />} title={`Journal at ${format(parseISO(log.created_at), 'p')}`} onDelete={() => setItemToDelete({ id: log.id, type: 'journal' })}>
                    <Skeleton className="h-4 w-full mb-1" style={{ borderRadius: '8px' }} />
                    <p className="line-clamp-3 whitespace-pre-wrap">{log.content}</p>
                  </LogCard>
                );
              } else if ('mood' in log) {
                return (
                  <LogCard key={log.id} icon={<Heart className="h-5 w-5" />} title={`Daily Check-in at ${format(parseISO(log.created_at), 'p')}`} onDelete={() => setItemToDelete({ id: log.id, type: 'dailyCheckIn' })}>
                    <div className="grid grid-cols-2 gap-x-4 gap-y-1 text-sm">
                      <p>Mood: {log.mood}/5</p>
                      <p>Energy: {log.energy}/5</p>
                      <p>Focus: {log.focus}/5</p>
                      <p>Sleep: {log.sleep_hours} hrs</p>
                    </div>
                  </LogCard>
                );
              }
              return null;
            })
          ) : (
            <div className="flex items-center justify-center w-10 h-10 bg-primary/10 border border-primary/20" style={{ borderRadius: '32px' }}>
              <CardHeader className="p-6 pb-4"><CardTitle className="text-lg font-bold text-foreground">No Logs Found</CardTitle></CardHeader>
              <CardContent className="p-6 pt-0"><p className="text-muted-foreground">No entries were found for {selectedDate ? format(selectedDate, "PPP") : 'this day'}.</p></CardContent>
            </div>
          )}
        </div>
      </div>

      <AlertDialog open={!!itemToDelete} onOpenChange={() => setItemToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this log entry? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive">Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default LogHistoryPage;
