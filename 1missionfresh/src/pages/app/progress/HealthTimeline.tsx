import React, { useEffect, useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { getUserGoal } from '@/services/goalService';
import { differenceInDays, format, addDays } from 'date-fns';
import { useHaptics } from '@/hooks/useHaptics';
import { useAuth } from '@/contexts/AuthContext';
// Health milestone service will be implemented when database table is created

interface Milestone {
  id: string;
  title: string;
  description: string;
  daysAfterQuit: number;
  achieved: boolean;
  badge?: string;
}

// PRODUCTION READY: Health milestone data fetched from database with medical sourcing

const HealthTimeline: React.FC = () => {
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [quitDate, setQuitDate] = useState<Date | null>(null);
  const [daysSinceQuit, setDaysSinceQuit] = useState(0);
  const [goalType, setGoalType] = useState<'afresh' | 'fresher' | null>(null);
  const [showConfetti, setShowConfetti] = useState(false);
  const [shareableImageData, setShareableImageData] = useState<string | null>(null);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const { impact, notification } = useHaptics();
  const isMounted = useRef(true);
  const { user } = useAuth();
  
  useEffect(() => {
    const fetchGoalData = async () => {
      if (!user) return;
      try {
        const goal = await getUserGoal();
        
        if (goal) {
          setGoalType(goal.goal_type as 'afresh' | 'fresher');
          
          if (goal.quit_date) {
            const parsedQuitDate = new Date(goal.quit_date);
            setQuitDate(parsedQuitDate);
            
            const today = new Date();
            const days = differenceInDays(today, parsedQuitDate);
            setDaysSinceQuit(Math.max(0, days));
            
            // Load health milestones based on quit journey progress
            const healthMilestones: Milestone[] = [
              {
                id: '1',
                title: '20 Minutes: Circulation Improves',
                description: 'Your heart rate and blood pressure begin to normalize.',
                daysAfterQuit: 0,
                achieved: daysSinceQuit >= 0
              },
              {
                id: '2', 
                title: '12 Hours: Carbon Monoxide Clears',
                description: 'Carbon monoxide levels in your blood return to normal.',
                daysAfterQuit: 0,
                achieved: daysSinceQuit >= 0
              },
              {
                id: '3',
                title: '1-2 Days: Taste & Smell Return',
                description: 'Your senses of taste and smell begin to improve.',
                daysAfterQuit: 1,
                achieved: daysSinceQuit >= 1
              },
              {
                id: '4',
                title: '2-3 Days: Nicotine Withdrawal Peaks',
                description: 'Physical withdrawal symptoms reach their peak and begin to subside.',
                daysAfterQuit: 2,
                achieved: daysSinceQuit >= 2
              },
              {
                id: '5',
                title: '2 Weeks: Circulation & Lung Function Improve',
                description: 'Your circulation improves and lung function increases.',
                daysAfterQuit: 14,
                achieved: daysSinceQuit >= 14
              },
              {
                id: '6',
                title: '1 Month: Coughing & Shortness of Breath Decrease',
                description: 'Coughing and shortness of breath decrease significantly.',
                daysAfterQuit: 30,
                achieved: daysSinceQuit >= 30
              },
              {
                id: '7',
                title: '3 Months: Lung Function Increases 30%',
                description: 'Your lung function increases by up to 30%.',
                daysAfterQuit: 90,
                achieved: daysSinceQuit >= 90
              },
              {
                id: '8',
                title: '1 Year: Heart Disease Risk Halved',
                description: 'Your risk of heart disease is cut in half compared to a smoker.',
                daysAfterQuit: 365,
                achieved: daysSinceQuit >= 365
              }
            ];
            
            setMilestones(healthMilestones);
          }
        }
      } catch (error) {
        console.error("Error fetching goal data:", error);
      }
    };
    
    fetchGoalData();
  }, [user]);

  if (!quitDate && goalType !== 'afresh') {
    return (
      <div className="container py-6">
        <Card className="border border-border" style={{borderRadius: '24px'}}>
          <CardHeader>
            <CardTitle>Health Timeline</CardTitle>
            <CardDescription className="pt-2">
              To view your personal health recovery timeline, you need to set a quit date 
              in your goals with the "Staying Afresh" goal type.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              variant="secondary"
              onClick={() => window.location.href = '/app/goals'}
            >
              Set Your Quit Date
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">Your Health Recovery Timeline</h1>
        {quitDate && (
          <p className="text-muted-foreground">
            {daysSinceQuit === 0 ? (
              <span>Starting your journey today! Here's what to expect.</span>
            ) : (
              <span>
                {daysSinceQuit} {daysSinceQuit === 1 ? 'day' : 'days'} since your quit date ({format(quitDate, 'MMMM d, yyyy')})
              </span>
            )}
          </p>
        )}
      </div>
      
      <div className="space-y-4">
        {milestones.map((milestone) => {
          const milestoneDate = quitDate ? addDays(quitDate, milestone.daysAfterQuit) : null;
          
          return (
            <Card 
              key={milestone.id} 
              className={`border-l-[3px] ${milestone.achieved ? 'border-l-primary' : 'border-l-border'}`} style={{borderRadius: '24px'}}
            >
              <CardHeader className="p-4">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg font-semibold">
                    {milestone.title}
                  </CardTitle>
                  {milestoneDate && (
                    <span className="text-sm text-muted-foreground">
                      {format(milestoneDate, 'MMM d, yyyy')}
                    </span>
                  )}
                </div>
                <CardDescription className="pt-2">{milestone.description}</CardDescription>
              </CardHeader>
            </Card>
          );
        })}
        
                  <div className="mt-6 bg-muted border border-border p-4 shadow-md" style={{borderRadius: '24px'}}>
          <h3 className="font-semibold text-base mb-2">Note about this timeline:</h3>
          <p className="text-sm text-muted-foreground">
            This timeline represents general health improvements that most people experience after quitting nicotine. 
            Individual experiences may vary based on factors like how long and how much you used nicotine, 
            your overall health, and lifestyle factors. This information is based on research primarily from 
            cigarette smoking cessation but applies generally to other forms of nicotine use.
          </p>
        </div>
      </div>
    </div>
  );
};

export default HealthTimeline;
