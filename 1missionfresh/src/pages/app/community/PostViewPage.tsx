import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getPostById,
  getCommentsForPost,
  addComment,
  likePost,
  unlikePost,
  likeComment,
  unlikeComment,
  getPostLikeStatus,
  getCommentLikeStatus,
  CommunityPost,
  CommunityComment,
  CommunityCommentInsert,
} from '@/services/communityService';
import { useAuth } from '@/contexts/AuthContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Loader2, MessageSquare, ThumbsUp, Send, ArrowLeft, Heart } from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';

const CommentItem: React.FC<{ 
  comment: CommunityComment, 
  currentUserId: string | undefined, 
  userHasLiked: boolean,
  onLikeUnlike: (commentId: string, currentlyLiked: boolean) => Promise<void>,
  onReply?: (commentId: string) => void 
}> = ({ comment, currentUserId, userHasLiked, onLikeUnlike, onReply }) => {
  const [isLiking, setIsLiking] = useState(false);

  const handleLikeComment = async () => {
    if (!currentUserId) {
      toast.error("You must be logged in to like comments.");
      return;
    }
    setIsLiking(true);
    try {
      await onLikeUnlike(comment.id, userHasLiked);
    } catch (error) {
      // Error already handled by service or parent mutation
    } finally {
      setIsLiking(false);
    }
  };

  return (
    <div className="flex space-x-4 py-4 border-b border-border last:border-b-0">
      <Avatar className="h-10 w-10">
        <AvatarImage src={comment.profiles?.avatar_url || undefined} alt={comment.profiles?.username || 'User'} />
        <AvatarFallback>{comment.profiles?.username?.charAt(0).toUpperCase() || 'U'}</AvatarFallback>
      </Avatar>
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <p className="font-semibold text-foreground text-base">{comment.profiles?.username || 'Anonymous'}</p>
          <p className="text-xs text-muted-foreground">
            {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })}
          </p>
        </div>
        <p className="mt-1 text-foreground whitespace-pre-wrap text-base leading-relaxed">{comment.content}</p>
        <div className="flex items-center gap-1 mt-2">
          <Button variant="ghost" size="sm" onClick={handleLikeComment} disabled={isLiking || !currentUserId} className="text-muted-foreground hover:text-primary px-2 py-1 h-auto">
                            <Heart size={16} className={`mr-1.5 transition-colors ${userHasLiked ? 'fill-destructive text-destructive' : 'text-muted-foreground group-hover:text-destructive'}`} />
            {comment.like_count || 0}
          </Button>
        </div>
      </div>
    </div>
  );
};

const PostViewPage: React.FC = () => {
  const { postId } = useParams<{ postId: string }>();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const [newComment, setNewComment] = useState('');
  const [userHasLikedPost, setUserHasLikedPost] = useState(false);
  const [commentLikeStatuses, setCommentLikeStatuses] = useState<Record<string, boolean>>({});

  const { data: post, isLoading: isLoadingPost, error: postError } = useQuery<CommunityPost | null, Error>({
    queryKey: ['communityPost', postId],
    queryFn: () => getPostById(postId!),
    enabled: !!postId,
  });

  useEffect(() => {
    if (user && post?.id) {
      const fetchLikeStatus = async () => {
        const liked = await getPostLikeStatus(post.id, user.id);
        setUserHasLikedPost(liked);
      };
      fetchLikeStatus();
    }
  }, [user, post, postId]);

  const { data: comments, isLoading: isLoadingComments, error: commentsError } = useQuery<CommunityComment[], Error>({
    queryKey: ['communityComments', postId],
    queryFn: () => getCommentsForPost(postId!),
    enabled: !!postId,
  });

  useEffect(() => {
    if (user && comments && comments.length > 0) {
      const fetchCommentLikes = async () => {
        const commentIds = comments.map(c => c.id);
        const statuses = await getCommentLikeStatus(commentIds, user.id);
        setCommentLikeStatuses(statuses);
      };
      fetchCommentLikes();
    }
  }, [user, comments]);


  const addCommentMutation = useMutation({
    mutationFn: (commentData: CommunityCommentInsert) => addComment(commentData),
    onSuccess: () => {
      setNewComment('');
      queryClient.invalidateQueries({ queryKey: ['communityComments', postId] });
      queryClient.invalidateQueries({ queryKey: ['communityPost', postId] }); 
    },
    onError: (error: any) => { 
      toast.error(error.message || "Failed to add comment.");
    }
  });

  const togglePostLikeMutation = useMutation({
    mutationFn: async () => {
      if (!user || !post) throw new Error("User or post not available");
      if (userHasLikedPost) {
        await unlikePost(post.id, user.id);
      } else {
        await likePost(post.id, user.id);
      }
      return !userHasLikedPost;
    },
    onSuccess: (newLikeStatus) => {
      setUserHasLikedPost(newLikeStatus);
      queryClient.invalidateQueries({ queryKey: ['communityPost', postId] });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update like status.");
    }
  });

  const toggleCommentLikeMutation = useMutation({
    mutationFn: async ({ commentId, currentlyLiked }: { commentId: string, currentlyLiked: boolean }) => {
      if (!user) throw new Error("User not available");
      if (currentlyLiked) {
        await unlikeComment(commentId, user.id);
      } else {
        await likeComment(commentId, user.id);
      }
      return { commentId, newLikeStatus: !currentlyLiked };
    },
    onSuccess: ({ commentId, newLikeStatus }) => {
      setCommentLikeStatuses(prev => ({ ...prev, [commentId]: newLikeStatus }));
      queryClient.invalidateQueries({ queryKey: ['communityComments', postId] });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update comment like status.");
    }
  });


  const handleAddComment = (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      toast.error("You must be logged in to comment.");
      return;
    }
    if (!newComment.trim() || !postId) {
      toast.error("Comment cannot be empty.");
      return;
    }
    addCommentMutation.mutate({
      post_id: postId,
      user_id: user.id,
      content: newComment.trim(),
      parent_comment_id: null,
    });
  };

  if (isLoadingPost) {
    return <div className="flex justify-center items-center h-screen"><Loader2 className="h-12 w-12 animate-spin text-primary" /></div>;
  }

  if (postError || !post) {
    return <div className="text-center py-10 text-destructive">Error loading post: {(postError as Error)?.message || "Post not found."}</div>;
  }

  return (
      <div className="max-w-4xl mx-auto py-6 px-4 space-y-6">
        <Button variant="ghost" asChild className="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-primary transition-colors">
          <Link to="/app/community">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to community
          </Link>
        </Button>

        {isLoadingPost && <div className="flex justify-center py-20"><Loader2 className="h-12 w-12 animate-spin text-primary" /></div>}
        {postError && <p className="text-center text-destructive py-20">Error loading post: {(postError as Error)?.message}</p>}
        {!isLoadingPost && !post && <p className="text-center text-muted-foreground py-20">Post not found.</p>}

        {post && (
          <Card className="bg-background shadow-md overflow-hidden" style={{borderRadius: '24px'}}>
            <CardHeader className="border-b bg-muted/40 p-4">
              <CardTitle className="text-3xl font-bold tracking-tight">{post.title}</CardTitle>
              <CardDescription className="text-base pt-1">
                Posted by <Link to={`/user/${post.profiles?.username}`} className="font-semibold text-primary hover:text-primary/80 transition-colors duration-200">{post.profiles?.username || 'Anonymous'}</Link>
                <span> &middot; {format(new Date(post.created_at), "PPP p")}</span>
              </CardDescription>
            </CardHeader>
            <CardContent className="prose dark:prose-invert max-w-none text-lg leading-relaxed p-4">
              <p className="whitespace-pre-wrap">{post.content}</p>
            </CardContent>
            <CardFooter className="flex items-center justify-between border-t p-4">
              <Button variant="ghost" onClick={() => togglePostLikeMutation.mutate()} disabled={togglePostLikeMutation.isPending || !user} className="text-muted-foreground hover:text-primary text-base">
                <Heart size={20} className={`mr-2 transition-colors ${userHasLikedPost ? 'fill-destructive text-destructive' : ''}`} />
                {post.like_count || 0} Like{(post.like_count || 0) !== 1 ? 's' : ''}
              </Button>
              <div className="text-muted-foreground flex items-center gap-2 text-base">
                <MessageSquare size={20} />
                {post.comment_count || 0} Comments
              </div>
            </CardFooter>
          </Card>
        )}

        <Card className="bg-background shadow-md" style={{borderRadius: '24px'}}>
          <CardHeader className="p-4">
            <h2 className="text-2xl font-bold">Comment{(comments?.length || 0) !== 1 ? 's' : ''} ({comments?.length || 0})</h2>
          </CardHeader>
          <CardContent className="p-4 pt-2">
          {user && (
            <form onSubmit={handleAddComment} className="flex gap-4 mb-6 items-start">
              <Avatar className="h-11 w-11 mt-1">
                <AvatarImage src={user.user_metadata?.avatar_url} />
                <AvatarFallback>{user.email?.[0].toUpperCase()}</AvatarFallback>
              </Avatar>
              <Textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Write a thoughtful comment..."
                rows={3} 
                className="flex-grow text-base p-2"
              />
              <Button type="submit" disabled={addCommentMutation.isPending || !newComment.trim()} size="lg" className="h-11 self-end">
                {addCommentMutation.isPending ? <Loader2 className="h-5 w-5 animate-spin" /> : <Send className="h-5 w-5" />}
              </Button>
            </form>
          )}
          {!user && <p className="text-muted-foreground mb-4 text-base">Please <Link to="/auth?tab=login" className="text-primary hover:text-primary/80 transition-colors duration-200">log in</Link> to comment.</p>}

          {isLoadingComments && <div className="flex justify-center py-6"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>}
          {commentsError && <p className="text-destructive text-center">Error loading comments: {commentsError?.message || 'Unknown error'}</p>}
          {!isLoadingComments && !commentsError && comments && comments.length > 0 ? (
            <div className="space-y-2">
              {comments.map(comment => (
                <CommentItem 
                  key={comment.id} 
                  comment={comment} 
                  currentUserId={user?.id}
                  userHasLiked={commentLikeStatuses[comment.id] || false}
                  onLikeUnlike={async (commentId, currentlyLiked) => {
                    await toggleCommentLikeMutation.mutateAsync({ commentId, currentlyLiked });
                  }}
                />
              ))}
            </div>
          ) : (
            !isLoadingComments && !commentsError && (!comments || comments.length === 0) && (
              <p className="text-muted-foreground text-center py-6 text-base">No comments yet. Be the first to share your thoughts!</p>
            )
          )}
          </CardContent>
        </Card>
      </div> 
  );
};

export default PostViewPage;
