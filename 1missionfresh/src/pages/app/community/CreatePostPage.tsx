import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getCategories, createPost, CommunityTopic, CommunityPostInsert } from '@/services/communityService';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import PageBreadcrumb from '@/components/common/PageBreadcrumb';
import { Loader2, Send, ArrowLeft } from 'lucide-react';
import { toast } from 'sonner';

const CreatePostPage: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | undefined>(undefined);
  const [tags, setTags] = useState('');

  const { data: categories, isLoading: isLoadingCategories } = useQuery<CommunityTopic[], Error>({
    queryKey: ['communityCategories'],
    queryFn: getCategories,
  });

  const createPostMutation = useMutation({
    mutationFn: (newPost: CommunityPostInsert) => createPost(newPost),
    onSuccess: (data) => {
      toast.success('Post created successfully!');
      queryClient.invalidateQueries({ queryKey: ['communityPosts', 'all-posts', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['communityPosts', 'my-posts', user?.id] });
      if (data?.id) {
        navigate(`/app/community/post/${data.id}`);
      } else {
        navigate('/app/community');
      }
    },
    onError: (error) => {
      toast.error(`Failed to create post: ${error.message}`);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      toast.error("You must be logged in to create a post.");
      return;
    }
    if (!title.trim() || !content.trim() || !selectedCategoryId) {
      toast.error("Please fill in title, content, and select a category.");
      return;
    }

    const postData: CommunityPostInsert = {
      user_id: user.id,
      topic_id: selectedCategoryId,
      title: title.trim(),
      content: content.trim(),
      tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
    };
    createPostMutation.mutate(postData);
  };

  return (
    <div className="max-w-3xl mx-auto py-6 px-4 space-y-6">
      <Button variant="ghost" onClick={() => navigate('/app/community')} className="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-primary">
        <ArrowLeft className="mr-2 h-4 w-4"/>
        Back to Community
      </Button>
      
      <Card className="bg-background border shadow-md" style={{borderRadius: '24px'}}>
        <CardHeader>
          <CardTitle className="text-2xl font-bold">Create a New Post</CardTitle>
          <CardDescription>Share your thoughts and experiences with the community.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title" className="text-base">Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="A clear and engaging title"
                required
                className="text-base"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category" className="text-base">Category</Label>
              <Select
                value={selectedCategoryId}
                onValueChange={setSelectedCategoryId}
                required
              >
                <SelectTrigger id="category" className="text-base">
                  <SelectValue placeholder={isLoadingCategories ? "Loading..." : "Select a category"} />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingCategories ? (
                    <SelectItem value="loading" disabled>Loading...</SelectItem>
                  ) : (
                    categories?.map(category => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="content" className="text-base">Content</Label>
              <Textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="What's on your mind? Share your story, ask a question, or offer support."
                rows={10}
                required
                className="text-base"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="tags" className="text-base">Tags (optional, comma-separated)</Label>
              <Input
                id="tags"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                placeholder="e.g., motivation, day1, craving-help"
                className="text-base"
              />
            </div>

            <Button type="submit" disabled={createPostMutation.isPending} className="w-full" size="lg">
              {createPostMutation.isPending ? (
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              ) : (
                <Send className="mr-2 h-5 w-5" />
              )}
              Create Post
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default CreatePostPage;
