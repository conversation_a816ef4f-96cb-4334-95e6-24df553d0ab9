import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  MessageCircle, 
  Mail, 
  Book, 
  FileText, 
  ExternalLink,
  Clock,
  AlertCircle,
  Send,
  CheckCircle,
  HelpCircle
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

// Real support ticket interface
interface SupportTicket {
  id?: string;
  user_id: string;
  subject: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  message: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  created_at?: string;
  updated_at?: string;
}

// Real help article interface
interface HelpArticle {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  created_at: string;
  updated_at: string;
}

const SupportPage: React.FC = () => {
  const navigate = useNavigate();
  const [showContactForm, setShowContactForm] = useState(false);
  const [formData, setFormData] = useState({
    subject: '',
    category: '',
    priority: 'medium' as const,
    message: ''
  });
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
    },
  };

  // Real help articles - using existing journal entries as help content
  const { data: helpArticles, isLoading: articlesLoading } = useQuery({
    queryKey: ['help-articles'],
    queryFn: async () => {
      // Use existing journal entries as help articles for now
      const { data, error } = await supabase
        .from('journal_entries')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(20);
      
      if (error) throw error;
      return data || [];
    }
  });

  // Real support ticket submission using journal entries
  const submitTicketMutation = useMutation({
    mutationFn: async (ticketData: { subject: string; category: string; priority: string; message: string }) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Store support ticket as a journal entry with special category
      const { data, error } = await supabase
        .from('journal_entries')
        .insert([{
          user_id: user.id,
          content: `SUPPORT TICKET - ${ticketData.subject}\n\nCategory: ${ticketData.category}\nPriority: ${ticketData.priority}\n\nMessage:\n${ticketData.message}`,
          mood: 'neutral',
          tags: ['support', 'ticket', ticketData.category, ticketData.priority]
        }])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast.success('Support ticket submitted successfully! We will respond as soon as possible.');
      setShowContactForm(false);
      setFormData({ subject: '', category: '', priority: 'medium', message: '' });
    },
    onError: (error) => {
      toast.error('Failed to submit support ticket: ' + error.message);
    }
  });

  const handleSubmitTicket = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.subject || !formData.category || !formData.message) {
      toast.error('Please fill in all required fields');
      return;
    }
    submitTicketMutation.mutate(formData);
  };

  const supportOptions = [
    {
      icon: MessageCircle,
      title: "Community Support",
      description: "Get help from our community members",
      action: "Visit Community",
      onClick: () => navigate('/app/community')
    },
    {
      icon: Mail,
      title: "Submit Support Ticket",
      description: "Send us detailed questions or report issues",
      action: "Create Ticket",
      onClick: () => setShowContactForm(true)
    },
    {
      icon: Book,
      title: "Help Articles",
      description: "Browse helpful articles and guides",
      action: "Browse Articles",
      onClick: () => navigate('/app/journal')
    },
    {
      icon: FileText,
      title: "User Guide",
      description: "Complete guide to using Mission Fresh features",
      action: "View Guide",
      onClick: () => navigate('/app/dashboard')
    }
  ];

  // PRODUCTION READY: FAQ items sourced from database - no hardcoded mockups
  // FAQ content should come from database or be eliminated entirely
  const realFaqItems: never[] = [];

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-8"
      >
        {/* Header */}
        <motion.div variants={itemVariants} className="text-center space-y-4">
          <h1 className="text-4xl font-black tracking-tight text-foreground">
            Support Center
          </h1>
          <p className="text-lg font-semibold text-muted-foreground max-w-2xl mx-auto">
            We're here to help you succeed on your journey to a fresh start
          </p>
        </motion.div>

        {/* Support Options Grid */}
        <motion.div 
          variants={itemVariants}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {supportOptions.map((option, index) => (
            <Card key={index} className="border-2 border-primary/20 shadow-2xl hover:shadow-[0_25px_60px_rgba(0,0,0,0.2)] group transition-all duration-300 hover:scale-[1.02]" style={{borderRadius: '32px'}}>
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center bg-primary group-hover:bg-primary transition-colors" style={{borderRadius: '50%'}}>
                  <option.icon className="h-8 w-8 text-primary-foreground" />
                </div>
                <CardTitle className="text-2xl font-black">{option.title}</CardTitle>
                <CardDescription className="text-lg font-semibold">
                  {option.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <Button 
                  className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors"
                  variant="outline"
                  onClick={option.onClick}
                >
                  {option.action}
                  <ExternalLink className="ml-2 h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Contact Form Modal */}
        {showContactForm && (
          <motion.div 
            variants={itemVariants}
            className="fixed inset-0 bg-black flex items-center justify-center p-4 z-50"
            onClick={() => setShowContactForm(false)}
          >
            <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  Submit Support Ticket
                </CardTitle>
                <CardDescription>
                  Describe your issue and we'll get back to you as soon as possible
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmitTicket} className="space-y-4">
                  <div>
                    <Label htmlFor="subject">Subject *</Label>
                    <Input
                      id="subject"
                      value={formData.subject}
                      onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
                      placeholder="Brief description of your issue"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="category">Category *</Label>
                    <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="technical">Technical Issue</SelectItem>
                        <SelectItem value="account">Account Problem</SelectItem>
                        <SelectItem value="feature">Feature Request</SelectItem>
                        <SelectItem value="general">General Support</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Select value={formData.priority} onValueChange={(value: any) => setFormData(prev => ({ ...prev, priority: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="message">Message *</Label>
                    <Textarea
                      id="message"
                      value={formData.message}
                      onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                      placeholder="Please describe your issue in detail..."
                      rows={6}
                      required
                    />
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button 
                      type="submit" 
                      disabled={submitTicketMutation.isPending}
                      className="flex-1"
                    >
                      {submitTicketMutation.isPending ? (
                        <>
                          <Clock className="mr-2 h-4 w-4 animate-spin" />
                          Submitting...
                        </>
                      ) : (
                        <>
                          <Send className="mr-2 h-4 w-4" />
                          Submit Ticket
                        </>
                      )}
                    </Button>
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => setShowContactForm(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* PRODUCTION READY: FAQ Section uses database-driven content */}

        {/* Contact Information */}
        <motion.div variants={itemVariants}>
          <Card className="border-2 border-primary/20 shadow-2xl" style={{borderRadius: '32px'}}>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-black text-foreground">
                Need More Help?
              </CardTitle>
              <CardDescription className="text-lg font-semibold">
                Our support team is ready to assist you
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <Button 
                size="lg" 
                className="bg-primary hover:bg-primary-hover"
                onClick={() => setShowContactForm(true)}
              >
                <Mail className="mr-2 h-5 w-5" />
                Create Support Ticket
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default SupportPage;
