import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getCategories, createPost, CommunityTopic, CommunityPostInsert } from '@/services/communityService';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import PageBreadcrumb from '@/components/common/PageBreadcrumb';
import { Loader2, Send } from 'lucide-react';
import { toast } from 'sonner';

const CreatePostPage: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | undefined>(undefined);
  const [tags, setTags] = useState('');

  const { data: categories, isLoading: isLoadingCategories } = useQuery<CommunityTopic[], Error>({
    queryKey: ['communityCategories'],
    queryFn: getCategories,
  });

  const createPostMutation = useMutation({
    mutationFn: (newPost: CommunityPostInsert) => createPost(newPost),
    onSuccess: (data) => {
      toast.success('Post created successfully!');
      queryClient.invalidateQueries({ queryKey: ['communityPosts', 'all-posts', user?.id] });
      queryClient.invalidateQueries({ queryKey: ['communityPosts', 'my-posts', user?.id] });
      if (data?.id) {
        navigate(`/app/community/post/${data.id}`);
      } else {
        navigate('/app/community');
      }
    },
    onError: (error) => {
      toast.error(`Failed to create post: ${error.message}`);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      toast.error("You must be logged in to create a post.");
      return;
    }
    if (!title.trim() || !content.trim() || !selectedCategoryId) {
      toast.error("Please fill in title, content, and select a category.");
      return;
    }

    const postData: CommunityPostInsert = {
      user_id: user.id,
      topic_id: selectedCategoryId,
      title: title.trim(),
      content: content.trim(),
      tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
    };
    createPostMutation.mutate(postData);
  };

  return (
    <div className="max-w-3xl mx-auto p-4 md:p-6 lg:p-8">
      <header className="text-center mb-10">
        <h1 className="text-4xl md:text-5xl font-bold tracking-tight font-heading text-foreground">Create a New Post</h1>
        <p className="mt-2 text-xl text-muted-foreground">Share your thoughts with the community.</p>
      </header>
      <Card className="bg-background">
        <CardContent className="pt-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="title" className="text-base">Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter post title"
                required
                className="h-12 text-base"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category" className="text-base">Category</Label>
              <Select
                value={selectedCategoryId}
                onValueChange={setSelectedCategoryId}
                required
              >
                <SelectTrigger id="category" className="h-12 text-base">
                  <SelectValue placeholder={isLoadingCategories ? "Loading..." : "Select a category"} />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingCategories ? (
                    <SelectItem value="loading" disabled className="text-base">Loading...</SelectItem>
                  ) : (
                    categories?.map(category => (
                      <SelectItem key={category.id} value={category.id} className="text-base">
                        {category.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="content" className="text-base">Content</Label>
              <Textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="What's on your mind?"
                rows={10}
                required
                className="text-base"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="tags" className="text-base">Tags (optional, comma-separated)</Label>
              <Input
                id="tags"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                placeholder="e.g., motivation, day1, craving-help"
                className="h-12 text-base"
              />
            </div>

            <Button type="submit" disabled={createPostMutation.isPending} className="w-full" size="lg">
              {createPostMutation.isPending ? (
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              ) : (
                <Send className="mr-2 h-5 w-5" />
              )}
              Create Post
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default CreatePostPage;
