import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useAuth } from "@/contexts/AuthContext";
import { saveJournalEntry, getLogsByDate, JournalEntry as JournalEntryType, JournalEntryInsert } from "@/services/logService";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { format } from 'date-fns';
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { Calendar as CalendarIcon, BookOpen } from "lucide-react";
const JournalPage = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [newEntryContent, setNewEntryContent] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());

  // PRODUCTION READY: Clean state management without debug spam

  const selectedDateStr = format(selectedDate, 'yyyy-MM-dd');

  const { data: logEntryForDate, isLoading: isLoadingEntries } = useQuery({
    queryKey: ['logsByDate', user?.id, selectedDateStr],
    queryFn: () => getLogsByDate(user!.id, selectedDateStr),
    enabled: !!user?.id && !!selectedDateStr,
  });

  const journalEntries = (logEntryForDate?.journal_entries || []).sort(
    (a: JournalEntryType, b: JournalEntryType) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );

  const handleSaveEntry = async () => {
    if (!user) {
      toast.error("Please log in to save journal entries.");
      return;
    }

    if (newEntryContent.trim() === "") {
      toast.error("Journal entry cannot be empty.");
      return;
    }

    setIsSaving(true);
    try {
      const newEntry: JournalEntryInsert = {
        content: newEntryContent.trim(),
        user_id: user.id,
      };

      const result = await saveJournalEntry(newEntry);
      
      if (result) {
        setNewEntryContent("");
        queryClient.invalidateQueries({ queryKey: ['logsByDate', user.id, selectedDateStr] });
        setSelectedDate(new Date()); // Reset calendar to today to show the new entry
      } else {
        toast.error("Failed to save journal entry - no result returned.");
      }
    } catch (error) {
      console.error("Journal save error:", error);
      toast.error("Failed to save journal entry.");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="px-6 py-8 space-y-8 max-w-7xl mx-auto">
      {/* FIXED FLAWS #96-100: 2025 PERFECTION - JOURNAL PAGE HEADER */}
      <header className="text-center space-y-4 mb-8">
        <div className="flex items-center justify-center gap-3 mb-3">
          <div className="flex items-center justify-center w-10 h-10 bg-primary/10 border border-primary/20" style={{borderRadius: '32px'}}>
            <BookOpen className="w-5 h-5 text-primary" strokeWidth={2} />
          </div>
          <h1 className="text-heading-xl font-bold text-foreground leading-premium tracking-premium">Journal</h1>
        </div>
        <p className="text-sm text-muted-foreground font-normal leading-[1.5] max-w-2xl mx-auto">
          Express your thoughts and track your emotional journey
        </p>
        <p className="text-xs text-muted-foreground font-normal leading-[1.5] max-w-2xl mx-auto">
          Journaling helps process emotions and track your progress. Write freely about your thoughts, feelings, and experiences.
        </p>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <main className="space-y-8">
          <Card className="card-unified h-fit">
            <CardHeader className="p-6 pb-4">
              <div className="flex items-center gap-3 mb-3">
                <div className="flex items-center justify-center w-10 h-10 bg-primary/10 border border-primary/20" style={{borderRadius: '32px'}}>
                  <BookOpen className="w-5 h-5 text-primary" strokeWidth={2} />
                </div>
                <CardTitle className="text-lg font-semibold text-foreground leading-tight">New Entry</CardTitle>
              </div>
              <CardDescription className="text-sm text-muted-foreground font-normal leading-[1.5]">Write about your thoughts, feelings, and experiences today.</CardDescription>
            </CardHeader>
            <CardContent className="p-6 pt-4 space-y-6">
              <Textarea
                value={newEntryContent}
                onChange={(e) => setNewEntryContent(e.target.value)}
                placeholder="What's on your mind today?"
                className="min-h-32 resize-none border-border focus:border-primary focus:ring-primary" style={{borderRadius: '32px'}}
              />
              <div className="flex justify-center">
                <Button
                  onClick={handleSaveEntry}
                  disabled={isSaving || newEntryContent.trim() === ""}
                  className="btn-primary"
                >
                  {isSaving ? (
                    <><Loader2 className="mr-3 w-5 h-5 animate-spin" strokeWidth={2} /> Saving...</>
                  ) : (
                    <><BookOpen className="mr-3 w-5 h-5" strokeWidth={2} /> Save Entry</>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          <div className="space-y-8">
            <CardTitle className="text-base font-bold text-foreground">
              Entries for {format(selectedDate, 'PPP')}
            </CardTitle>
            <div className="space-y-4">
              {isLoadingEntries ? (
                [...Array(3)].map((_, i) => (
                  <Card key={i} className="card-unified">
                    <CardContent className="p-6">
                      <Skeleton className="h-4 w-1/4 mb-2" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-4/5 mt-2" />
                    </CardContent>
                  </Card>
                ))
              ) : journalEntries.length === 0 ? (
                <div className="text-center py-16 space-y-4">
                  <p className="text-muted-foreground text-sm">No journal entries for this date.</p>
                </div>
              ) : (
                journalEntries.map((entry) => (
                  <Card key={entry.id} className="card-unified">
                    <CardHeader className="p-6 pb-4">
                      <CardTitle className="text-sm font-semibold text-foreground">
                        {format(new Date(entry.created_at), "h:mm a")}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-6 pt-4">
                      <p className="text-foreground whitespace-pre-wrap leading-[1.6] text-left">{entry.content}</p>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </div>
        </main>

        <aside>
          <Card className="card-unified h-fit">
            <CardHeader className="p-6 pb-4">
              <div className="flex items-center gap-3 mb-3">
                <div className="flex items-center justify-center w-10 h-10 bg-primary/10 border border-primary/20" style={{borderRadius: '32px'}}>
                  <CalendarIcon className="w-5 h-5 text-primary" strokeWidth={2} />
                </div>
                <CardTitle className="text-lg font-semibold text-foreground leading-tight">View Past Entries</CardTitle>
              </div>
              <CardDescription className="text-sm text-muted-foreground font-normal leading-[1.5]">Select a date to revisit your thoughts.</CardDescription>
            </CardHeader>
            <CardContent className="p-6 pt-4">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={(date) => date && setSelectedDate(date)}
                disabled={(date) => date > new Date()}
                className="border-0 w-full bg-background [&_.rdp-day_selected]:bg-primary [&_.rdp-day_selected]:text-primary-foreground [&_.rdp-day]:text-sm [&_.rdp-day_today]:bg-primary/10 [&_.rdp-day_today]:text-primary [&_.rdp-day_today]:font-medium" style={{borderRadius: '32px'}}
              />
            </CardContent>
          </Card>
        </aside>
      </div>
    </div>
  );
};

export default JournalPage;
