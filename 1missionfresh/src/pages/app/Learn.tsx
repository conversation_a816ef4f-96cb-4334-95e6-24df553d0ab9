import React, { useState, useEffect } from 'react';
import SEOHead from '../../components/common/SEOHead';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { quitGuides, QuitGuide } from '@/lib/quitGuidesData';
import { Label } from "@/components/ui/label";
import ErrorDisplay from '../../components/common/ErrorDisplay';
import { WifiOff, Terminal, Share2, Loader2, Lightbulb, Heart, ShieldCheck, BookOpen } from "lucide-react";
import NoGoalSet from "@/components/common/NoGoalSet";
import LearningModuleList from '@/components/app/learning/LearningModuleList';

const Learn: React.FC = () => {
  const [selectedProductType, setSelectedProductType] = useState<string | undefined>(undefined);
  const [displayedGuide, setDisplayedGuide] = useState<QuitGuide | undefined>(undefined);

  useEffect(() => {
    if (selectedProductType) {
      const guide = quitGuides.find(guide => guide.productType === selectedProductType);
      setDisplayedGuide(guide);
    } else {
      setDisplayedGuide(undefined);
    }
  }, [selectedProductType]);

  return (
    <>
      <SEOHead title="Learn" description="Educational resources for quitting smoking and managing nicotine addiction." />
      <div className="container max-w-7xl mx-auto px-6 md:px-8 py-10 space-y-10">

        {/* Hero Header */}
        <div className="mb-10">
          <div className="flex items-center gap-4 mb-6">
            <div className="flex items-center justify-center h-16 w-16 bg-secondary shadow-md" style={{borderRadius: '32px'}}>
              <BookOpen className="h-6 w-6 text-primary" />
            </div>
            <div className="space-y-2">
              <h1 className="text-4xl font-black tracking-tight text-foreground">Learn</h1>
              <p className="text-lg font-semibold leading-relaxed text-muted-foreground">Educational resources for your wellness journey</p>
            </div>
          </div>
        </div>

        <Card className="border border-primary shadow-md" style={{borderRadius: '32px'}}>
          <CardHeader className="pb-6">
            <CardTitle className="text-2xl font-black tracking-tight text-foreground">Tailored Quit Guides by Product</CardTitle>
            <CardDescription className="text-lg font-semibold text-muted-foreground">Select a product to get a specialized guide.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-6">
              <Label htmlFor="product-select" className="text-base font-medium">I'm quitting:</Label>
              <Select onValueChange={setSelectedProductType} value={selectedProductType}>
                <SelectTrigger id="product-select" className="w-full sm:w-[320px] h-12 text-base border-border focus:border-primary focus:ring-primary bg-background transition-all duration-300" style={{borderRadius: '24px'}}>
                  <SelectValue placeholder="Choose a product type" />
                </SelectTrigger>
                <SelectContent className="border-border shadow-lg" style={{borderRadius: '24px'}}>
                  {quitGuides.map((guide) => (
                    <SelectItem key={guide.productType} value={guide.productType} className="text-base">
                      {guide.productType.charAt(0).toUpperCase() + guide.productType.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
          {displayedGuide && (
            <CardFooter className="flex flex-col items-start p-6 border-t border-border bg-muted">
                <h3 className="text-xl font-bold mb-3 tracking-tight text-foreground">{displayedGuide.title}</h3>
                <p className="text-muted-foreground mb-4 text-base leading-relaxed">{displayedGuide.description}</p>
                <div className="prose prose-sm max-w-none text-foreground dark:prose-invert leading-relaxed">
                  <p>{displayedGuide.content}</p>
                </div>
            </CardFooter>
          )}
        </Card>

        <section className="space-y-8">
          <div className="space-y-2">
            <h2 className="text-2xl font-black tracking-tight text-foreground">All Learning Modules</h2>
            <p className="text-lg font-semibold leading-relaxed text-muted-foreground">Comprehensive educational content for your journey</p>
          </div>
          <LearningModuleList />
        </section>
      </div>
    </>
  );
};

export default Learn;
