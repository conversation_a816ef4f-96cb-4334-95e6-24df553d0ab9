import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardT<PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Bell, Clock, Award, MessageSquare, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { updateUserPreferences } from '@/services/userPreferencesService';

interface NotificationSettings {
  milestones: boolean;
  dailyCheckins: boolean;
  cravingSupport: boolean;
  communityUpdates: boolean;
  quietHoursEnabled: boolean;
  quietHoursStart: string;
  quietHoursEnd: string;
}

const Notifications = () => {
  const [settings, setSettings] = useState<NotificationSettings>({
    milestones: true,
    dailyCheckins: true,
    cravingSupport: true,
    communityUpdates: false,
    quietHoursEnabled: true,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
  });
  const [loading, setLoading] = useState(false);

  const handleSettingChange = (key: keyof NotificationSettings, value: boolean | string) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const saveSettings = async () => {
    setLoading(true);
    try {
      // Save notification settings to database
      await updateUserPreferences({
        notification_milestones: settings.milestones,
        notification_logs: settings.dailyCheckins,
        notification_cravings: settings.cravingSupport,
        notifications: {
          communityUpdates: settings.communityUpdates,
          quietHoursEnabled: settings.quietHoursEnabled,
          quietHoursStart: settings.quietHoursStart,
          quietHoursEnd: settings.quietHoursEnd
        }
      });
      
      toast.success("Notification settings saved.");
    } catch (error) {
      toast.error("Failed to save settings.");
    } finally {
      setLoading(false);
    }
  };

  const notificationTypes = [
    { key: 'milestones' as const, title: 'Milestone Celebrations', icon: Award },
    { key: 'dailyCheckins' as const, title: 'Daily Check-ins', icon: Clock },
    { key: 'cravingSupport' as const, title: 'Craving Support', icon: Bell },
    { key: 'communityUpdates' as const, title: 'Community Updates', icon: MessageSquare },
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-6">
          <CardTitle className="text-2xl font-black">Notification Types</CardTitle>
          <CardDescription className="text-lg font-semibold">Choose which notifications you'd like to receive.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {notificationTypes.map((type) => (
            <div key={type.key} className="flex items-center justify-between border p-4 bg-card hover:bg-accent/50 transition-colors" style={{ borderRadius: '24px' }}>
              <div className="flex items-center gap-3">
                <type.icon className="h-5 w-5 text-primary flex-shrink-0" />
                <Label htmlFor={type.key} className="font-medium text-sm cursor-pointer">{type.title}</Label>
              </div>
              <Switch
                id={type.key}
                checked={settings[type.key]}
                onCheckedChange={(checked) => handleSettingChange(type.key, checked)}
              />
            </div>
          ))}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-6">
          <CardTitle className="text-2xl font-black">Quiet Hours</CardTitle>
          <CardDescription className="text-lg font-semibold">Set times when you don't want to receive notifications.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between border p-4 bg-card hover:bg-accent/50 transition-colors" style={{ borderRadius: '24px' }}>
            <Label htmlFor="quiet-hours" className="font-medium text-sm cursor-pointer">Enable Quiet Hours</Label>
            <Switch
              id="quiet-hours"
              checked={settings.quietHoursEnabled}
              onCheckedChange={(checked) => handleSettingChange('quietHoursEnabled', checked)}
            />
          </div>
          {settings.quietHoursEnabled && (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start-time" className="text-sm font-medium">Start Time</Label>
                <Select value={settings.quietHoursStart} onValueChange={(value) => handleSettingChange('quietHoursStart', value)}>
                  <SelectTrigger id="start-time" className="h-10">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 24 }, (_, i) => `${i.toString().padStart(2, '0')}:00`).map(time => (
                      <SelectItem key={time} value={time}>{time}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="end-time" className="text-sm font-medium">End Time</Label>
                <Select value={settings.quietHoursEnd} onValueChange={(value) => handleSettingChange('quietHoursEnd', value)}>
                  <SelectTrigger id="end-time" className="h-10">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 24 }, (_, i) => `${i.toString().padStart(2, '0')}:00`).map(time => (
                      <SelectItem key={time} value={time}>{time}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="pt-6">
          <Button onClick={saveSettings} disabled={loading} size="lg" className="w-full h-10">
            {loading ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...</> : 'Save Settings'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default Notifications;
