import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Palette, Monitor, Sun, Moon, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { updateUserPreferences } from '@/services/userPreferencesService';

interface ThemeSettings {
  mode: 'light' | 'dark' | 'system';
  reducedMotion: boolean;
  highContrast: boolean;
}

const Appearance = () => {
  const [settings, setSettings] = useState<ThemeSettings>({
    mode: 'system',
    reducedMotion: false,
    highContrast: false,
  });
  const [loading, setLoading] = useState(false);

  const handleSettingChange = (key: keyof ThemeSettings, value: string | boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const saveSettings = async () => {
    setLoading(true);
    try {
      // Save theme and accessibility settings to database
      await updateUserPreferences({
        theme: settings.mode,
        notifications: {
          reducedMotion: settings.reducedMotion,
          highContrast: settings.highContrast
        }
      });
      
      // Apply theme immediately
      if (settings.mode === 'dark') {
        document.documentElement.classList.add('dark');
      } else if (settings.mode === 'light') {
        document.documentElement.classList.remove('dark');
      } else {
        // System preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        if (prefersDark) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      }
      
      toast.success("Appearance settings saved.");
    } catch (error) {
      toast.error("Failed to save settings.");
    } finally {
      setLoading(false);
    }
  };

  const themeOptions = [
    { value: 'light', label: 'Light', icon: Sun },
    { value: 'dark', label: 'Dark', icon: Moon },
    { value: 'system', label: 'System', icon: Monitor },
  ];

  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-black">Theme</CardTitle>
          <CardDescription className="text-lg font-semibold">Select the theme for the application.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            {themeOptions.map((option) => (
              <Button
                key={option.value}
                variant={settings.mode === option.value ? 'primary' : 'outline'}
                onClick={() => handleSettingChange('mode', option.value)}
                className="h-auto flex-col p-4"
                style={{ borderRadius: '24px' }}
              >
                <option.icon className="h-6 w-6 mb-2" />
                {option.label}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-black">Accessibility</CardTitle>
          <CardDescription className="text-lg font-semibold">Options to improve usability and comfort.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between border p-4 bg-card hover:bg-accent/50 transition-colors" style={{ borderRadius: '24px' }}>
            <Label htmlFor="reduced-motion" className="font-medium text-sm cursor-pointer">Reduce Motion</Label>
            <Switch
              id="reduced-motion"
              checked={settings.reducedMotion}
              onCheckedChange={(checked) => handleSettingChange('reducedMotion', checked)}
            />
          </div>
          <div className="flex items-center justify-between border p-4 bg-card hover:bg-accent/50 transition-colors" style={{ borderRadius: '24px' }}>
            <Label htmlFor="high-contrast" className="font-medium text-sm cursor-pointer">High Contrast</Label>
            <Switch
              id="high-contrast"
              checked={settings.highContrast}
              onCheckedChange={(checked) => handleSettingChange('highContrast', checked)}
            />
          </div>
        </CardContent>
        <CardFooter className="pt-6">
          <Button onClick={saveSettings} disabled={loading} className="w-full">
            {loading ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...</> : 'Save Settings'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default Appearance;
