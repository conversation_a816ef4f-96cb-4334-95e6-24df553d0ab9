import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { getProductCosts, updateProductCosts } from "@/services/userPreferencesService";
import { useQuery } from "@tanstack/react-query";
import { Loader2, Save } from "lucide-react";

const CostsPage = () => {
  const { user } = useAuth();
  const [cigaretteCost, setCigaretteCost] = useState("");
  const [vapeCost, setVapeCost] = useState("");
  const [pouchCost, setPouchCost] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  const { data: productCosts, isLoading } = useQuery({
    queryKey: ['product-costs'],
    queryFn: getProductCosts,
    enabled: !!user?.id
  });

  useEffect(() => {
    if (productCosts) {
      setCigaretteCost(productCosts.cigarette?.toString() || "");
      setVapeCost(productCosts.vape?.toString() || "");
      setPouchCost(productCosts.pouch?.toString() || "");
    }
  }, [productCosts]);

  const handleSubmitCosts = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsSaving(true);
    try {
      const costs = {
        cigarette: cigaretteCost ? parseFloat(cigaretteCost) : null,
        vape: vapeCost ? parseFloat(vapeCost) : null,
        pouch: pouchCost ? parseFloat(pouchCost) : null,
      };
      
      await updateProductCosts(costs);
      toast.success("Product costs updated successfully");
    } catch (error) {
      toast.error("Failed to update product costs");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl font-black">Product Costs</CardTitle>
        <CardDescription className="text-lg font-semibold">Update costs for accurate savings calculations. Enter the cost per item.</CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmitCosts}>
        <CardContent className="space-y-6">
          {isLoading ? (
            <div className="flex justify-center py-6">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <>
              <div className="space-y-2">
                <Label htmlFor="cigarette-cost">Cigarette Pack Cost ($)</Label>
                <Input
                  id="cigarette-cost"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="e.g., 8.50"
                  value={cigaretteCost}
                  onChange={(e) => setCigaretteCost(e.target.value)}
                  
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="vape-cost">Vape Pod/Cartridge Cost ($)</Label>
                <Input
                  id="vape-cost"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="e.g., 4.00"
                  value={vapeCost}
                  onChange={(e) => setVapeCost(e.target.value)}
                  
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="pouch-cost">Smokeless Tin/Pouch Cost ($)</Label>
                <Input
                  id="pouch-cost"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="e.g., 5.25"
                  value={pouchCost}
                  onChange={(e) => setPouchCost(e.target.value)}
                  
                />
              </div>
            </>
          )}
        </CardContent>
        <CardFooter className="pt-6">
          <Button type="submit" disabled={isLoading || isSaving} className="w-full">
            {isSaving ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...</> : <Save className="mr-2 h-4 w-4" />}
            Save Costs
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
};

export default CostsPage;
