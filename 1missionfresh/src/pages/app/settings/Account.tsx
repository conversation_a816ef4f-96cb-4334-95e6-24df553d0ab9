import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import AccountDeletionSetting from "@/components/settings/AccountDeletionSetting";
import { Shield, User, Mail, Calendar, CheckCircle, Clock } from 'lucide-react';

const AccountPage = () => {
  const { user } = useAuth();
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-8">
      {/* Account Overview */}
      <Card className="border-0 shadow-none bg-transparent">
        <CardHeader className="px-0 pb-6">
          <CardTitle className="text-2xl font-bold text-gray-900 mb-2">Account Overview</CardTitle>
          <CardDescription className="text-base text-gray-600 font-medium">
            Your account information and status
          </CardDescription>
        </CardHeader>
        <CardContent className="px-0 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 flex items-center justify-center" style={{ borderRadius: '24px' }}>
                  <Mail className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Email Address</p>
                  <p className="text-gray-600">{user?.email}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 flex items-center justify-center" style={{ borderRadius: '24px' }}>
                  <User className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">User ID</p>
                  <p className="text-gray-600 font-mono text-sm">{user?.id}</p>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 flex items-center justify-center" style={{ borderRadius: '24px' }}>
                  <Calendar className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Member Since</p>
                  <p className="text-gray-600">{user?.created_at ? formatDate(user.created_at) : 'Not available'}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 flex items-center justify-center" style={{ borderRadius: '24px' }}>
                  <CheckCircle className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Account Status</p>
                  <Badge className="bg-primary/10 text-primary hover:bg-primary/10">
                    Active
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Security Information */}
      <Card className="border-0 shadow-none bg-transparent">
        <CardHeader className="px-0 pb-6">
          <CardTitle className="text-2xl font-bold text-gray-900 mb-2">Security</CardTitle>
          <CardDescription className="text-base text-gray-600 font-medium">
            Manage your account security and authentication
          </CardDescription>
        </CardHeader>
        <CardContent className="px-0">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50" style={{ borderRadius: '16px' }}>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 flex items-center justify-center" style={{ borderRadius: '24px' }}>
                  <Shield className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Email Verification</p>
                  <p className="text-sm text-gray-600">Your email address is verified</p>
                </div>
              </div>
              <Badge className="bg-primary/10 text-primary hover:bg-primary/10">
                Verified
              </Badge>
            </div>
            
            <div className="flex items-center justify-between p-4 bg-gray-50" style={{ borderRadius: '16px' }}>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 flex items-center justify-center" style={{ borderRadius: '24px' }}>
                  <Clock className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Last Sign In</p>
                  <p className="text-sm text-gray-600">{user?.last_sign_in_at ? formatDate(user.last_sign_in_at) : 'Not available'}</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Account Deletion */}
      <AccountDeletionSetting />
    </div>
  );
};

export default AccountPage;
