import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getCategories, getPosts, CommunityTopic, CommunityPost } from '@/services/communityService';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, MessageSquare, PlusCircle, ThumbsUp, Tag } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '@/contexts/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';

const PostCard: React.FC<{ post: CommunityPost }> = ({ post }) => (
  <Link to={`/app/community/post/${post.id}`} className="block h-full">
    <Card className="card-unified h-full flex flex-col">
      <CardHeader className="p-8 pb-6">
        <CardTitle className="text-lg font-bold text-foreground leading-tight mb-2">{post.title}</CardTitle>
        <CardDescription className="text-sm text-muted-foreground">
          Posted by <span className="font-medium text-foreground">{post.profiles?.username || 'Anonymous'}</span> • {formatDistanceToNow(new Date(post.created_at), { addSuffix: true })}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-grow px-4 pb-4">
        <p className="text-sm text-foreground leading-relaxed line-clamp-4">{post.content}</p>
      </CardContent>
      <CardFooter className="flex items-center justify-between text-xs text-muted-foreground border-t border-border pt-4 px-4 pb-8 mt-auto">
        <div className="flex flex-wrap gap-2">
          <div className="flex items-center gap-1 hover:text-primary transition-colors cursor-pointer">
            <ThumbsUp className="h-4 w-4 flex-shrink-0" />
            <span className="font-medium">{post.like_count || 0}</span>
          </div>
          <div className="flex items-center gap-1 hover:text-primary transition-colors cursor-pointer">
            <MessageSquare className="h-4 w-4 flex-shrink-0" />
            <span className="font-medium">{post.comment_count || 0}</span>
          </div>
        </div>
      </CardFooter>
    </Card>
  </Link>
);

const CommunityPage: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { categorySlug } = useParams<{ categorySlug?: string }>();

  const { data: categories, isLoading: isLoadingCategories } = useQuery<CommunityTopic[], Error>({ 
    queryKey: ['communityCategories'], 
    queryFn: getCategories 
  });

  const activeTab = categorySlug ? categorySlug : 'all';

  const { data: posts, isLoading: isLoadingPosts } = useQuery<CommunityPost[], Error>({
    queryKey: ['communityPosts', activeTab],
    queryFn: () => getPosts({ categorySlug: activeTab === 'all' ? undefined : activeTab, sortBy: 'activity' }),
  });

  const handleTabChange = (newTab: string) => {
    navigate(newTab === 'all' ? '/app/community' : `/app/community/category/${newTab}`);
  };

  return (
    <div className="p-8 space-y-8 max-w-7xl mx-auto">
      <header className="text-center space-y-6 mb-8">
        <div className="flex items-center justify-center gap-4 mb-4">
          <div className="flex items-center justify-center h-12 w-12 bg-primary/10 shadow-sm" style={{borderRadius: '24px'}}>
            <MessageSquare className="h-6 w-6 text-primary" strokeWidth={2} />
          </div>
          <h1 className="text-2xl lg:text-3xl font-bold text-foreground leading-tight">Community Hub</h1>
        </div>
        <p className="text-sm text-muted-foreground leading-relaxed font-medium max-w-2xl mx-auto">
          Connect with fellow wellness warriors. Share your journey, celebrate victories, and find support in our thriving community.
        </p>
        <div className="flex justify-center pt-4">
          <Button onClick={() => navigate('/app/community/create')} className="btn-primary">
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Post
          </Button>
        </div>
      </header>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5 bg-muted/30 p-1 border border-border/40" style={{borderRadius: '24px'}}>
          <TabsTrigger value="all" className="data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm font-medium py-2 px-3 text-sm transition-all" style={{borderRadius: '32px'}}>All Posts</TabsTrigger>
          {isLoadingCategories ? (
            <Skeleton className="h-8 w-20" style={{borderRadius: '32px'}} />
          ) : (
            categories?.map(cat => (
              <TabsTrigger key={cat.slug} value={cat.slug} className="data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm font-medium py-2 px-3 text-sm transition-all" style={{borderRadius: '32px'}}>{cat.name}</TabsTrigger>
            ))
          )}
        </TabsList>
        
        <TabsContent value={activeTab} className="space-y-6">
          {isLoadingPosts ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="card-unified">
                  <CardHeader className="p-8 pb-6">
                    <Skeleton className="h-4 w-3/4 rounded" />
                    <Skeleton className="h-3 w-1/2 mt-2 rounded" />
                  </CardHeader>
                  <CardContent className="px-8 pb-4">
                    <Skeleton className="h-3 w-full rounded" />
                    <Skeleton className="h-3 w-5/6 mt-1 rounded" />
                    <Skeleton className="h-3 w-4/6 mt-1 rounded" />
                  </CardContent>
                  <CardFooter className="border-t border-border pt-4 px-8 pb-8">
                    <Skeleton className="h-3 w-1/3 rounded" />
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : posts && posts.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {posts.map((post) => <PostCard key={post.id} post={post} />)}
            </div>
          ) : (
            <div className="text-center py-20 space-y-6">
              <div className="mx-auto w-16 h-16 bg-muted flex items-center justify-center" style={{borderRadius: '50%'}}>
                <MessageSquare className="h-6 w-6 text-muted-foreground" />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold tracking-tight">No Posts Yet</h3>
                <p className="text-muted-foreground text-sm">Be the first to share something in this category!</p>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CommunityPage;
