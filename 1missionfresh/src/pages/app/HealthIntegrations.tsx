import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Heart, Activity, Watch, CheckCircle, Link as LinkIcon, Unlink, Loader2 } from "lucide-react";

type ConnectionStatus = 'disconnected' | 'connected' | 'pending';

interface IntegrationCardProps {
  title: string;
  description: string;
  Icon: React.ElementType;
  status: ConnectionStatus;
  onConnect: () => void;
  onDisconnect: () => void;
}

const IntegrationCard: React.FC<IntegrationCardProps> = ({ title, description, Icon, status, onConnect, onDisconnect }) => {
  const StatusBadge = () => {
    switch (status) {
      case 'connected':
        return <Badge variant="outline" className="text-success border-success">Connected</Badge>;
      case 'pending':
        return <Badge variant="outline">Pending</Badge>;
      default:
        return <Badge variant="secondary">Disconnected</Badge>;
    }
  };

  return (
    <Card className="flex flex-col">
      <CardHeader className="flex-row items-start justify-between gap-4">
        <div className="flex items-start gap-4">
          <Icon className="h-6 w-6 text-primary flex-shrink-0 mt-1" />
          <div>
            <CardTitle>{title}</CardTitle>
            <CardDescription className="mt-1">{description}</CardDescription>
          </div>
        </div>
        <StatusBadge />
      </CardHeader>
      <CardContent className="flex-grow flex flex-col justify-end">
        {status === 'connected' ? (
          <Button variant="outline" onClick={onDisconnect} className="w-full">
            <Unlink className="mr-2 h-4 w-4" /> Disconnect
          </Button>
        ) : (
          <Button onClick={onConnect} className="w-full" disabled={status === 'pending'}>
            {status === 'pending' ? (
              <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Connecting...</>
            ) : (
              <><LinkIcon className="mr-2 h-4 w-4" /> Connect</>
            )}
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

const HealthIntegrations = () => {
  const [statuses, setStatuses] = useState<Record<string, ConnectionStatus>>({
    apple: 'disconnected',
    google: 'disconnected',
    wearable: 'disconnected',
  });

  const handleConnect = (key: string) => {
    setStatuses(prev => ({ ...prev, [key]: 'pending' }));
    setTimeout(() => setStatuses(prev => ({ ...prev, [key]: 'connected' })), 1500);
  };

  const handleDisconnect = (key: string) => {
    setStatuses(prev => ({ ...prev, [key]: 'disconnected' }));
  };

  const integrations = [
    {
      key: 'apple',
      title: "Apple Health",
      description: "Sync steps, sleep, and other metrics from your iOS device.",
      Icon: Heart,
    },
    {
      key: 'google',
      title: "Google Fit",
      description: "Sync health data from your Android device.",
      Icon: Activity,
    },
    {
      key: 'wearable',
      title: "Wearable Devices",
      description: "Link Fitbit, Garmin, or other wearables to track metrics.",
      Icon: Watch,
    },
  ];

  return (
    <div className="p-4 md:p-6 space-y-6">
      <header>
        <h1 className="text-2xl font-semibold">Health Integrations</h1>
        <p className="text-muted-foreground mt-1">Sync your health data for a more holistic view of your journey.</p>
      </header>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {integrations.map((integration) => (
          <IntegrationCard
            key={integration.key}
            title={integration.title}
            description={integration.description}
            Icon={integration.Icon}
            status={statuses[integration.key]}
            onConnect={() => handleConnect(integration.key)}
            onDisconnect={() => handleDisconnect(integration.key)}
          />
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Your Data Privacy</CardTitle>
          <CardDescription>We take your privacy seriously. Your health data is always:</CardDescription>
        </CardHeader>
        <CardContent>
          <ul className="space-y-3 text-sm text-muted-foreground">
            {[ 'Encrypted and stored securely.', 'Never shared without your explicit consent.', 'Used only to provide you with personalized insights.', 'Deletable by you at any time.' ].map(item => (
              <li key={item} className="flex items-start">
                <CheckCircle className="h-4 w-4 mr-2.5 mt-0.5 text-primary flex-shrink-0" />
                <span>{item}</span>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default HealthIntegrations;
