import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { TrendingUp, Target, Calendar, DollarSign, Clock, Activity } from 'lucide-react';
import { getDailyHealthSummary, getCravingLogs, getNicotineUseLogs } from '@/services/progressService';
import { gamificationService } from '@/services/gamificationService';
import AchievementsList from '@/components/app/progress/AchievementsList';
import ProgressChart from '@/components/app/progress/ProgressChart';
import { useAuth } from '@/contexts/AuthContext';
import { subDays, format } from 'date-fns';

// REAL PROGRESS PAGE - WITH REAL DATABASE FUNCTIONALITY AND HOOKS
const ProgressPage: React.FC = () => {
  const { user } = useAuth();
  const endDate = format(new Date(), 'yyyy-MM-dd');
  const startDate = format(subDays(new Date(), 30), 'yyyy-MM-dd');

  // Real database queries replacing ALL mock data
  const { data: healthSummary, isLoading: healthLoading } = useQuery({
    queryKey: ['healthSummary', user?.id, startDate, endDate],
    queryFn: () => getDailyHealthSummary(startDate, endDate),
    enabled: !!user,
  });

  const { data: cravingLogs, isLoading: cravingsLoading } = useQuery({
    queryKey: ['cravingLogs', user?.id, startDate, endDate],
    queryFn: () => getCravingLogs(startDate, endDate),
    enabled: !!user,
  });

  const { data: nicotineLogs, isLoading: nicotineLoading } = useQuery({
    queryKey: ['nicotineLogs', user?.id, startDate, endDate],
    queryFn: () => getNicotineUseLogs(startDate, endDate),
    enabled: !!user,
  });

  const { data: achievements, isLoading: achievementsLoading } = useQuery({
    queryKey: ['achievements', user?.id],
    queryFn: () => gamificationService.getUserAchievementsWithDefinitions(user!.id),
    enabled: !!user,
  });

  // Real data calculations from database queries (NO MORE MOCK DATA)
  const isLoading = healthLoading || cravingsLoading || nicotineLoading || achievementsLoading;
  
  // FIXED FLAW #11: Calculate statistics properly from real data
  const totalCravings = cravingLogs?.length || 0;
  const totalNicotineUses = nicotineLogs?.filter(log => log.used_nicotine)?.length || 0;
  const daysTracked = healthSummary?.length || 0;
  
  // Calculate days since quit date from user goal (consistent with Dashboard)
  const { data: userGoal } = useQuery({
    queryKey: ['userGoal', user?.id],
    queryFn: async () => {
      const { getUserGoal } = await import('@/services/goalService');
      return getUserGoal();
    },
    enabled: !!user,
  });
  
  // Calculate real progress stats from quit date
  const quitDate = userGoal?.quit_date;
  const daysAfresh = quitDate ? Math.floor((new Date().getTime() - new Date(quitDate).getTime()) / (1000 * 60 * 60 * 24)) : daysTracked;
  const dailyUsage = Number(userGoal?.typical_daily_usage) || 20;
  const costPerUnit = Number(userGoal?.cost_per_unit) || 0.5;
  const moneySaved = Math.round(daysAfresh * dailyUsage * costPerUnit);
  const lifeRegainedMinutes = daysAfresh * dailyUsage * 11; // 11 minutes per cigarette
  
  // Calculate trigger patterns from real craving data
  const triggerCounts: { [key: string]: number } = {};
  cravingLogs?.forEach(log => {
    if (log.trigger) {
      triggerCounts[log.trigger] = (triggerCounts[log.trigger] || 0) + 1;
    }
  });
  const topTrigger = Object.keys(triggerCounts).length > 0 ? 
    Object.keys(triggerCounts).reduce((a, b) => triggerCounts[a] > triggerCounts[b] ? a : b) : 'None';
    
  // Calculate success rate (days with reduced cravings)
  const successfulDays = healthSummary?.filter(day => day.avgCravingIntensity <= 3).length || 0;
  const successRate = daysTracked > 0 ? Math.round((successfulDays / daysTracked) * 100) : 0;
  
  // Transform real database data into ChartDataPoint format for ProgressChart
  const chartData = healthSummary?.map(day => ({
    date: day.date,
    cravings: day.avgCravingIntensity || 0,
    mood: day.mood || null,
    energy: day.energyLevel || null,
    focus: day.focusLevel || null,
  })) || [];

  return (
    <div className="page-container">
      {/* FIXED FLAWS #91-95: 2025 PERFECTION - PROGRESS PAGE HEADER */}
      <header className="text-center section-spacing section-margin-bottom">
        <div className="flex items-center justify-center icon-text-gap header-margin-bottom">
          <div className="flex items-center justify-center icon-container-md icon-rounded bg-primary/10 border border-primary/20">
            <TrendingUp className="icon-md text-primary" strokeWidth={2} />
          </div>
          <h1 className="text-heading-xl font-bold tracking-tight text-foreground">Your Progress</h1>
        </div>
        <p className="page-description page-description-max-width mx-auto">
          Visualize your journey to a healthier, nicotine-free life with comprehensive analytics and insights designed to inspire your continued success.
        </p>
      </header>

      {/* FIXED FLAWS #91-95: 2025 PERFECTION - PROGRESS PAGE STATISTICS CARDS */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 grid-gap section-margin-bottom">
        <div className="stat-card">
          <div className="stat-card-content">
            <div className="stat-card-info">
              <div className="stat-card-title">Days Afresh</div>
              <div className="stat-card-value">
                {isLoading ? <span className="animate-pulse bg-muted/50 rounded w-12 h-8 inline-block"></span> : `${daysAfresh} days`}
              </div>
              <div className="stat-card-description">
                Consecutive days of freedom from nicotine addiction
              </div>
            </div>
            <div className="icon-container">
              <Calendar className="icon-primary" />
            </div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-card-content">
            <div className="stat-card-info">
              <div className="stat-card-title">Money Saved</div>
              <div className="stat-card-value">
                {isLoading ? <span className="animate-pulse bg-muted/50 rounded w-16 h-8 inline-block"></span> : `$${moneySaved}`}
              </div>
              <div className="stat-card-description">
                Total financial savings from your wellness journey
              </div>
            </div>
            <div className="icon-container">
              <DollarSign className="icon-primary" />
            </div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-card-content">
            <div className="stat-card-info">
              <div className="stat-card-title">Life Regained</div>
              <div className="stat-card-value">
                {isLoading ? <span className="animate-pulse bg-muted/50 rounded w-12 h-8 inline-block"></span> : `${Math.round(lifeRegainedMinutes / 60)} hrs`}
              </div>
              <div className="stat-card-description">
                Additional healthy time gained through your progress
              </div>
            </div>
            <div className="icon-container">
              <Clock className="icon-primary" />
            </div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-card-content">
            <div className="stat-card-info">
              <div className="stat-card-title">Recent Cravings</div>
              <div className="stat-card-value">
                {isLoading ? <span className="animate-pulse bg-muted/50 rounded w-12 h-8 inline-block"></span> : `${totalCravings} total`}
              </div>
              <div className="stat-card-description">
                Tracked cravings to analyze patterns and triggers
              </div>
            </div>
            <div className="icon-container">
              <Activity className="icon-primary" />
            </div>
          </div>
        </div>
      </div>

      {/* FIXED FLAWS #91-95: 2025 PERFECTION - PROGRESS PAGE CHARTS SECTION */}
      <div className="grid grid-cols-1 lg:grid-cols-2 grid-gap section-margin-bottom">
        <Card className="card-unified h-full">
          <CardHeader className="card-header-padding">
            <div className="flex items-center icon-text-gap header-margin-bottom">
              <div className="flex items-center justify-center icon-container-md icon-rounded bg-primary/10 border border-primary/20">
                <TrendingUp className="icon-md text-primary" strokeWidth={2} />
              </div>
              <CardTitle className="card-title">
                Consumption Overview
              </CardTitle>
            </div>
            <p className="card-description">
              Track your wellness journey with comprehensive consumption analytics and financial progress insights
            </p>
          </CardHeader>
          <CardContent className="card-content-padding">
            <div className="chart-container-height">
              <div className="chart-height">
                <ProgressChart chartData={chartData} timeframe="30 days" isLoading={isLoading} />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="card-unified h-full">
          <CardHeader className="card-header-padding">
            <div className="flex items-center icon-text-gap header-margin-bottom">
              <div className="flex items-center justify-center icon-container-md icon-rounded bg-primary/10 border border-primary/20">
                <Target className="icon-md text-primary" strokeWidth={2} />
              </div>
              <CardTitle className="card-title">
                Craving Trigger Patterns
              </CardTitle>
            </div>
            <p className="card-description">
              Analyze patterns and triggers to build stronger coping strategies and maintain your wellness goals
            </p>
          </CardHeader>
          <CardContent className="card-content-padding">
            <div className="chart-container-height">
              <div className="flex flex-col items-center justify-center h-full text-center section-spacing">
                <div className="icon-container-lg bg-primary/10 flex items-center justify-center" style={{borderRadius: '50%'}}>
                  <Target className="icon-lg text-primary" strokeWidth={2} />
                </div>
                <div className="text-section-spacing">
                  <h3 className="dashboard-subsection-title">Trigger Pattern Analysis</h3>
                  <p className="dashboard-subsection-description subsection-max-width">
                    Identify patterns in your cravings and triggers to build stronger coping mechanisms and maintain progress
                  </p>
                </div>
                <div className="grid grid-cols-2 grid-gap text-center">
                  <div className="stat-card-padding bg-card border-border border card-rounded">
                    <div className="dashboard-stat-label">Top Trigger</div>
                    <div className="dashboard-stat-value dashboard-stat-text">{isLoading ? <span className="animate-pulse bg-muted/50 rounded w-20 h-6 inline-block"></span> : topTrigger}</div>
                  </div>
                  <div className="stat-card-padding bg-card border-border border card-rounded">
                    <div className="dashboard-stat-label">Success Rate</div>
                    <div className="dashboard-stat-value dashboard-stat-text-large text-primary">{isLoading ? <span className="animate-pulse bg-muted/50 rounded w-16 h-6 inline-block"></span> : `${successRate}%`}</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Achievements Section */}
      <Card className="card-unified text-center">
        <CardHeader className="achievement-header-padding">
          <CardTitle className="card-title">Achievements</CardTitle>
        </CardHeader>
        <CardContent className="achievement-content-padding achievement-max-height overflow-y-auto">
          <AchievementsList achievements={achievements || []} isLoading={achievementsLoading} />
        </CardContent>
      </Card>
    </div>
  );
};

export default ProgressPage;
