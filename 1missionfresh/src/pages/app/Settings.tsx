import React from 'react';
import { Route, Routes, Navigate } from 'react-router-dom';
import { SettingsLayout } from '@/components/layout/SettingsLayout';
import ProfilePage from './settings/Profile';
import NotificationsPage from './settings/Notifications';
import AppearancePage from './settings/Appearance';
import CostsPage from './settings/Costs';
import RelapsePreventionPage from './settings/RelapsePrevention';
import AccountPage from './settings/Account';

const Settings: React.FC = () => {
  return (
    <SettingsLayout>
      <Routes>
        <Route index element={<Navigate to="profile" replace />} />
        <Route path="profile" element={<ProfilePage />} />
        <Route path="notifications" element={<NotificationsPage />} />
        <Route path="appearance" element={<AppearancePage />} />
        <Route path="costs" element={<CostsPage />} />
        <Route path="relapse-prevention" element={<RelapsePreventionPage />} />
        <Route path="account" element={<AccountPage />} />
      </Routes>
    </SettingsLayout>
  );
};

export default Settings;
