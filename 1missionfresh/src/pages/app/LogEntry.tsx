import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import NicotineUseTab from "@/components/log/NicotineUseTab";
import type { NicotineEntry } from "@/components/log/NicotineUseTab";
import WellnessTab from "@/components/log/WellnessTab";
import { CravingsTab } from "@/components/log/CravingsTab";
import type { CravingEntry } from "@/components/log/CravingsTab";
import JournalTab from "@/components/log/JournalTab";
import NotesTab from "@/components/log/NotesTab";
import { toast } from "sonner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle, Heart, Flame, <PERSON><PERSON><PERSON>, <PERSON>yNote, PenSquare } from "lucide-react";
import { useHaptics, HapticImpact } from "@/hooks/useHaptics";
import { useOfflineSupport } from "@/hooks/useOfflineSupport";
import { achievementsList } from "@/lib/achievementsData";
import { DailyCheckInInsert, CravingLogInsert, NicotineLogInsert, JournalEntryInsert, saveNicotineLog, saveDailyCheckIn, saveCraving, saveJournalEntry } from "@/services/logService";
import { gamificationService } from "@/services/gamificationService";
import { z } from "zod";
import { useQuery } from '@tanstack/react-query';
import { Database } from '@/lib/database.types';
import { supabase } from '@/lib/supabase';
import { format } from 'date-fns';

const LocalNicotineEntrySchema = z.object({
  id: z.string(),
  productType: z.string().min(1, "Product type is required"),
  quantity: z.string().min(1, "Quantity is required").refine(val => !isNaN(parseFloat(val)) && parseFloat(val) >= 0, {
    message: "Quantity must be a non-negative number",
  }),
  nicotineStrength: z.string().optional().refine(val => val === undefined || val === '' || (!isNaN(parseFloat(val)) && parseFloat(val) >= 0), {
     message: "Nicotine strength must be a non-negative number or empty",
  }),
  cost: z.string().optional().refine(val => val === undefined || val === '' || (!isNaN(parseFloat(val)) && parseFloat(val) >= 0), {
     message: "Cost must be a non-negative number or empty",
  }),
  trigger_id: z.string().nullable().optional(),
  coping_strategy_id: z.string().nullable().optional(),
});
const LocalNicotineEntriesDataSchema = z.array(LocalNicotineEntrySchema);

const LocalCravingEntrySchema = z.object({
  id: z.string(),
  intensity: z.number().min(0).max(10),
  trigger: z.string(),
  otherTrigger: z.string().optional(),
  trigger_id: z.string().nullable().optional(),
  coping_strategy_id: z.string().nullable().optional(),
}).refine(data => {
  if (data.trigger_id) return true;
  if (data.trigger === 'other') return !!data.otherTrigger && data.otherTrigger.trim() !== '';
  return !!data.trigger && data.trigger.trim() !== '';
}, {
  message: "Please select a trigger or specify 'Other'.",
  path: ["trigger"], 
});
const LocalCravingEntriesDataSchema = z.array(LocalCravingEntrySchema);

const WellnessAndNotesValidationSchema = z.object({
  mood: z.number().min(1, "Mood selection is required.").max(5, "Mood must be between 1 and 5."),
  energy: z.number().min(1, "Energy selection is required.").max(5, "Energy must be between 1 and 5."),
  focus: z.number().min(1, "Focus selection is required.").max(5, "Focus must be between 1 and 5."),
  sleepHours: z.string()
    .min(1, "Sleep hours are required.")
    .refine(val => !isNaN(parseFloat(val)) && parseFloat(val) >= 0 && parseFloat(val) <= 24, {
      message: "Sleep hours must be a valid number between 0 and 24.",
    }),
  sleepQuality: z.number().min(1, "Sleep quality selection is required.").max(5, "Sleep quality must be between 1 and 5."),
  dailyNotes: z.string().max(5000, "Notes must be 5000 characters or less.").optional(),
});

const LogEntry = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const { isOffline } = useOfflineSupport();
  const { triggerHaptic } = useHaptics();

  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'wellness');

  // State for all log types
  const [mood, setMood] = useState<number>(0);
  const [energy, setEnergy] = useState<number>(0);
  const [focus, setFocus] = useState<number>(0);
  const [sleepHours, setSleepHours] = useState<string>('');
  const [sleepQuality, setSleepQuality] = useState<number>(0);
  const [nicotineUse, setNicotineUse] = useState<boolean | null>(null);
  const [nicotineEntries, setNicotineEntries] = useState<NicotineEntry[]>([]);
  const [cravingEntries, setCravingEntries] = useState<CravingEntry[]>([]);
  const [journal, setJournal] = useState<string>('');
  const [dailyNotes, setDailyNotes] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<any>({});

  const { data: userTriggers } = useQuery<
    Database['public']['Tables']['user_triggers']['Row'][]
  >({
    queryKey: ['user_triggers', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const { data, error } = await supabase.from('user_triggers').select('*').eq('user_id', user.id);
      if (error) throw new Error(error.message);
      return data;
    },
    enabled: !!user?.id,
  });

  const { data: userStrategies } = useQuery<
    Database['public']['Tables']['coping_strategies']['Row'][]
  >({
    queryKey: ['coping_strategies', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const { data, error } = await supabase.from('coping_strategies').select('*').eq('user_id', user.id);
      if (error) throw new Error(error.message);
      return data;
    },
    enabled: !!user?.id,
  });

  const displayTriggers = userTriggers || [];
  const displayStrategies = userStrategies || [];

  useEffect(() => {
    const currentTab = searchParams.get('tab');
    if (currentTab) {
      setActiveTab(currentTab);
    }
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setValidationErrors({});
    triggerHaptic(HapticImpact.MEDIUM);

    let allEntriesValid = true;
    let currentValidationErrors: any = {};

    // Validate Wellness and Notes
    const wellnessResult = WellnessAndNotesValidationSchema.safeParse({
      mood, energy, focus, sleepHours, sleepQuality, dailyNotes
    });
    if (!wellnessResult.success) {
      const errors = wellnessResult.error.flatten().fieldErrors;
      currentValidationErrors = { ...currentValidationErrors, ...errors };
      allEntriesValid = false;
    }

    // Validate Nicotine Entries if used
    if (nicotineUse) {
      const nicotineResult = LocalNicotineEntriesDataSchema.safeParse(nicotineEntries);
      if (!nicotineResult.success) {
        const errors = nicotineResult.error.flatten().fieldErrors;
        currentValidationErrors.nicotineEntries = errors;
        allEntriesValid = false;
      }
    }

    // Validate Craving Entries
    const cravingResult = LocalCravingEntriesDataSchema.safeParse(cravingEntries);
    if (!cravingResult.success) {
      const errors = cravingResult.error.flatten().fieldErrors;
      currentValidationErrors.cravingEntries = errors;
      allEntriesValid = false;
    }

    if (!allEntriesValid) {
      setValidationErrors(currentValidationErrors);
      toast.error("Please fix the errors before submitting.");
      setIsSubmitting(false);
      return;
    }

    if (!user) {
      toast.error("You must be logged in to save an entry.");
      setIsSubmitting(false);
      return;
    }

    const today = new Date();
    const dateString = format(today, 'yyyy-MM-dd');

    try {
      let pointsEarned = 0;
      let achievementsUnlocked: any[] = [];

      // Save Daily Check-in
      const dailyCheckInData: DailyCheckInInsert = {
        user_id: user.id,
        date: dateString,
        mood: mood,
        energy_level: energy,
        focus_level: focus,
        sleep_hours: parseFloat(sleepHours),
        sleep_quality: sleepQuality,
        notes: dailyNotes,
      };
      const { data: checkIn, error: checkInError } = await saveDailyCheckIn(dailyCheckInData);
      if (checkInError) throw new Error(`Daily Check-in: ${checkInError.message}`);
      if (checkIn) {
        const { points, achievements } = await gamificationService.trackEvent(user.id, 'daily_check_in');
        pointsEarned += points;
        achievementsUnlocked.push(...achievements);
      }

      // Save Nicotine Logs
      if (nicotineUse && nicotineEntries.length > 0) {
        for (const entry of nicotineEntries) {
          const nicotineLogData: NicotineLogInsert = {
            user_id: user.id,
            date: dateString,
            product_type: entry.productType,
            quantity: parseFloat(entry.quantity),
            nicotine_strength: entry.nicotineStrength ? parseFloat(entry.nicotineStrength) : null,
            cost: entry.cost ? parseFloat(entry.cost) : null,
            trigger_id: entry.trigger_id,
            coping_strategy_id: entry.coping_strategy_id,
          };
          const { error: nicotineError } = await saveNicotineLog(nicotineLogData);
          if (nicotineError) throw new Error(`Nicotine Log: ${nicotineError.message}`);
        }
        const { points, achievements } = await gamificationService.trackEvent(user.id, 'log_nicotine_use');
        pointsEarned += points;
        achievementsUnlocked.push(...achievements);
      } else if (nicotineUse === false) {
        const { points, achievements } = await gamificationService.trackEvent(user.id, 'report_no_nicotine_use');
        pointsEarned += points;
        achievementsUnlocked.push(...achievements);
      }

      // Save Craving Logs
      if (cravingEntries.length > 0) {
        for (const entry of cravingEntries) {
          const cravingLogData: CravingLogInsert = {
            user_id: user.id,
            timestamp: new Date().toISOString(),
            intensity: entry.intensity,
            trigger_id: entry.trigger_id,
            coping_strategy_id: entry.coping_strategy_id,
          };
          const { error: cravingError } = await saveCraving(cravingLogData);
          if (cravingError) throw new Error(`Craving Log: ${cravingError.message}`);
        }
        const { points, achievements } = await gamificationService.trackEvent(user.id, 'log_craving');
        pointsEarned += points;
        achievementsUnlocked.push(...achievements);
      }

      // Save Journal Entry
      if (journal.trim()) {
        const journalEntryData: JournalEntryInsert = {
          user_id: user.id,
          content: journal,
          date: dateString,
        };
        const { error: journalError } = await saveJournalEntry(journalEntryData);
        if (journalError) throw new Error(`Journal Entry: ${journalError.message}`);
        const { points, achievements } = await gamificationService.trackEvent(user.id, 'write_journal_entry');
        pointsEarned += points;
        achievementsUnlocked.push(...achievements);
      }

      toast.success("Log entry saved successfully!", {
        description: `You earned ${pointsEarned} points.`,
      });

      achievementsUnlocked.forEach(ach => {
        const achievementMeta = achievementsList.find(a => a.id === ach.achievement_id);
        if (achievementMeta) {
          toast.info("Achievement Unlocked!", {
            description: `You've earned the \"${achievementMeta.name}\" achievement.`,
          });
        }
      });

      navigate('/app/dashboard');
    } catch (error) {
      console.error("Submission error:", error);
      toast.error("An error occurred while saving your entry.", {
        description: error instanceof Error ? error.message : 'Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setSearchParams({ tab: value }, { replace: true });
  };

  const tabs = [
    { value: 'wellness', label: 'Wellness', icon: Heart },
    { value: 'nicotine', label: 'Nicotine', icon: PenSquare },
    { value: 'cravings', label: 'Cravings', icon: Flame },
    { value: 'journal', label: 'Journal', icon: BookOpen },
    { value: 'notes', label: 'Notes', icon: StickyNote },
  ];

  return (
    <div className="p-4 md:p-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        <header className="text-center">
          <h1 className="text-heading-xl font-bold tracking-premium text-foreground">Daily Log</h1>
          <p className="text-muted-foreground mt-2 text-body-lg font-medium">Your daily check-in is a powerful tool for self-awareness.</p>
        </header>

        {isOffline && (
          <Alert variant="default">
            <WifiOff className="h-4 w-4" />
            <AlertTitle>You are offline</AlertTitle>
            <AlertDescription>
              Your data will be saved locally and sync when you're back online.
            </AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-5 h-10 bg-background">
            {tabs.map(({ value, label, icon: Icon }) => (
              <TabsTrigger key={value} value={value} className="flex items-center gap-1 h-8 px-2 text-sm font-medium transition-all duration-300 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-semibold">
                <Icon className="h-4 w-4" />
                {label}
              </TabsTrigger>
            ))}
          </TabsList>
          
          <div className="mt-6">
            <TabsContent value="wellness">
              <WellnessTab mood={mood} setMood={setMood} energy={energy} setEnergy={setEnergy} focus={focus} setFocus={setFocus} sleepHours={sleepHours} setSleepHours={setSleepHours} sleepQuality={sleepQuality} setSleepQuality={setSleepQuality} validationErrors={validationErrors} />
            </TabsContent>
            <TabsContent value="nicotine">
              <NicotineUseTab nicotineUse={nicotineUse} setNicotineUse={setNicotineUse} nicotineEntries={nicotineEntries} setNicotineEntries={setNicotineEntries} userTriggers={displayTriggers} userStrategies={displayStrategies} validationErrors={validationErrors} />
            </TabsContent>
            <TabsContent value="cravings">
              <CravingsTab cravingEntries={cravingEntries} setCravingEntries={setCravingEntries} userTriggers={displayTriggers} userStrategies={displayStrategies} validationErrors={validationErrors} />
            </TabsContent>
            <TabsContent value="journal">
              <JournalTab journal={journal} setJournal={setJournal} />
            </TabsContent>
            <TabsContent value="notes">
              <NotesTab dailyNotes={dailyNotes} setDailyNotes={setDailyNotes} validationError={validationErrors.dailyNotes} />
            </TabsContent>
          </div>
        </Tabs>

        <div className="card-padding border-t bg-background sticky bottom-0">
          <Button type="submit" size="lg" className="button-width-responsive button-height-lg text-body-lg font-semibold bg-primary hover:bg-primary/90 transition-all duration-200" disabled={isSubmitting}>
            {isSubmitting ? (
              <><Loader2 className="mr-2 h-5 w-5 animate-spin" /> Saving...</>
            ) : (
              <><CheckCircle className="mr-2 h-5 w-5" /> Save Entry</>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default LogEntry;
