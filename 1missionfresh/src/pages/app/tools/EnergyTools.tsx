import React, { useState } from 'react';
import ToolExerciseCard from '@/components/tools/ToolExerciseCard';
import { ExerciseModal } from '@/components/tools/ExerciseModal';
import EnergyExercise from '@/components/tools/energy/EnergyExercise';
import { useHaptics, HapticImpact } from '@/hooks/useHaptics';
import { energyExercises } from '@/lib/toolsData';
import { useAuth } from '@/contexts/AuthContext';
import { Zap } from 'lucide-react';

const EnergyTools = () => {
  const { impact } = useHaptics();
  const { user } = useAuth();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState<React.ReactNode | null>(null);
  const [modalTitle, setModalTitle] = useState('');
  const [modalDescription, setModalDescription] = useState<string | undefined>(undefined);

  const handleToolComplete = () => {
    impact(HapticImpact.MEDIUM);
    setIsModalOpen(false);
    setModalContent(null);
  };

  const openExerciseModal = (title: string, description: string | undefined, content: React.ReactNode) => {
    setModalTitle(title);
    setModalDescription(description);
    setModalContent(content);
    setIsModalOpen(true);
  };

  return (
    <>
      <div className="container max-w-7xl mx-auto px-6 md:px-8 py-10">
        <header className="text-center space-y-6 mb-12">
          <div className="flex items-center justify-center gap-4 mb-6">
            <div className="flex items-center justify-center h-16 w-16 bg-primary/10 shadow-sm" style={{borderRadius: '50%'}}>
              <Zap className="h-8 w-8 text-primary" />
            </div>
          </div>
          <div className="space-y-4">
            <h1 className="text-4xl lg:text-5xl font-black tracking-tight text-foreground">
              Energy Tools
            </h1>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed font-medium">
              Boost vitality and maintain sustainable energy levels with professional-grade wellness techniques. 
              These scientifically-designed tools provide quick energy boosts and help you build lasting healthy habits with precision and effectiveness.
            </p>
          </div>
        </header>

        <div className="space-y-10">
          <div className="space-y-4 text-center">
            <h2 className="text-3xl lg:text-4xl font-bold tracking-tight text-foreground">Available Energy Exercises</h2>
            <p className="text-muted-foreground text-lg leading-relaxed max-w-2xl mx-auto">Choose an exercise to boost your energy and vitality with scientifically-proven techniques.</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 pb-8">
            {Object.entries(energyExercises).map(([key, exercise]) => (
              <div key={key} className="animate-fade-in" style={{ animationDelay: `${Object.keys(energyExercises).indexOf(key) * 0.1}s` }}>
                <ToolExerciseCard
                  title={exercise.title}
                  description={exercise.description}
                  duration={exercise.duration}
                  difficulty={exercise.difficulty}
                  tags={exercise.tags || []}
                  onStart={() => openExerciseModal(
                    exercise.title,
                    exercise.description,
                    <EnergyExercise exerciseType={key as keyof typeof energyExercises} onComplete={handleToolComplete} />
                  )}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
      <ExerciseModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onComplete={handleToolComplete}
        title={modalTitle}
        description={modalDescription}
      >
        {modalContent}
      </ExerciseModal>
    </>
  );
};

export default EnergyTools;
