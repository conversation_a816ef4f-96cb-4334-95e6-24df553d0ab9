import React, { useState } from 'react';
import ToolExerciseCard from '@/components/tools/ToolExerciseCard';
import { ExerciseModal } from '@/components/tools/ExerciseModal';
import MoodExercise from '@/components/tools/mood/MoodExercise';
import { useHaptics, HapticImpact } from '@/hooks/useHaptics';
import { moodExercises } from '@/lib/toolsData';
import { useAuth } from '@/contexts/AuthContext';
import { Heart } from 'lucide-react';

const MoodTools = () => {
  const { impact } = useHaptics();
  const { user } = useAuth();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState<React.ReactNode | null>(null);
  const [modalTitle, setModalTitle] = useState('');
  const [modalDescription, setModalDescription] = useState<string | undefined>(undefined);

  const handleToolComplete = () => {
    impact(HapticImpact.MEDIUM);
    setIsModalOpen(false);
    setModalContent(null);
  };

  const openExerciseModal = (title: string, description: string | undefined, content: React.ReactNode) => {
    setModalTitle(title);
    setModalDescription(description);
    setModalContent(content);
    setIsModalOpen(true);
  };

  return (
    <>
      <div className="container max-w-7xl mx-auto px-6 md:px-8 py-10">
        <div className="mb-10">
          <div className="flex items-center gap-4 mb-6">
            <div className="relative group">
                              <div className="absolute inset-0 bg-secondary/20 transition-all duration-300" style={{borderRadius: '32px'}}></div>
              <div className="relative flex items-center justify-center bg-primary border border-primary hover:border-primary h-16 w-16 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105" style={{borderRadius: '32px'}}>
                <Heart className="text-primary-foreground h-6 w-6 group-hover:scale-110 transition-transform duration-300" strokeWidth={1.8} />
              </div>
            </div>
            <div className="space-y-2">
              <h1 className="text-heading-xl font-bold tracking-premium text-foreground">Mood Tools</h1>
              <p className="text-muted-foreground text-base leading-relaxed">Manage emotions and improve your mental wellbeing</p>
            </div>
          </div>
          <p className="text-muted-foreground leading-relaxed max-w-3xl text-lg">
            Nicotine withdrawal can affect your mood and emotional balance. These tools help you regulate emotions and maintain mental wellness.
          </p>
        </div>

        <div className="space-y-8">
          <div className="space-y-3">
            <h2 className="text-heading-lg font-semibold tracking-premium text-foreground">Available Mood Exercises</h2>
            <p className="text-muted-foreground text-base leading-relaxed">Choose an exercise to improve your emotional wellbeing.</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 pb-8">
            {Object.entries(moodExercises).map(([key, exercise]) => (
              <div key={key} className="animate-fade-in" style={{ animationDelay: `${Object.keys(moodExercises).indexOf(key) * 0.1}s` }}>
                <ToolExerciseCard
                  title={exercise.title}
                  description={exercise.description}
                  duration={exercise.duration}
                  difficulty={exercise.difficulty}
                  tags={exercise.tags || []}
                  onStart={() => openExerciseModal(
                    exercise.title,
                    exercise.description,
                    <MoodExercise exerciseType={key as keyof typeof moodExercises} onComplete={handleToolComplete} />
                  )}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
      <ExerciseModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onComplete={handleToolComplete}
        title={modalTitle}
        description={modalDescription}
      >
        {modalContent}
      </ExerciseModal>
    </>
  );
};

export default MoodTools;
