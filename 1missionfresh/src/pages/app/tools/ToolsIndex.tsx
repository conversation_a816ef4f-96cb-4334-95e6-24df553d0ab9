import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Brain,
  Zap,
  Focus,
  Heart,
  Wind,
  Sparkles,
  Battery,
  MessageCircle,
} from 'lucide-react';
import { Card } from '@/components/ui/card';

const tools = [
  {
    id: 'cravings',
    title: 'Craving Management',
    description: 'Immediate tools to help you overcome cravings and urges',
    icon: Brain,
    href: '/app/tools/cravings',
    category: 'Immediate Support',

  },
  {
    id: 'energy',
    title: 'Energy Boost',
    description: 'Natural ways to boost your energy without smoking',
    icon: Zap,
    href: '/app/tools/energy',
    category: 'Wellness',

  },
  {
    id: 'focus',
    title: 'Focus & Concentration',
    description: 'Improve your focus and mental clarity naturally',
    icon: Focus,
    href: '/app/tools/focus',
    category: 'Focus',

  },
  {
    id: 'mood',
    title: 'Mood Enhancement',
    description: 'Improve your mood and emotional wellbeing naturally',
    icon: Heart,
    href: '/app/tools/mood',
    category: 'Emotional Support',
  },
  {
    id: 'breathing',
    title: 'Breathing Exercises',
    description: 'Calming breathing techniques for stress relief',
    icon: Wind,
    href: '/app/tools/breathing',
    category: 'Relaxation',
  },
  {
    id: 'guided-meditation',
    title: 'Guided Meditation',
    description: 'Meditation sessions to reduce stress and anxiety',
    icon: Sparkles,
    href: '/app/tools/guided-meditation',
    category: 'Mindfulness',
  },
  {
    id: 'fatigue',
    title: 'Fatigue Management',
    description: 'Combat withdrawal fatigue naturally and effectively',
    icon: Battery,
    href: '/app/tools/fatigue',
    category: 'Recovery',
  },
  {
    id: 'ai-coach',
    title: 'AI Coach',
    description: 'Get personalized support and guidance naturally',
    icon: MessageCircle,
    href: '/app/tools/ai-coach',
    category: 'AI Support',
  },
];

const categories = [
  'Immediate Support',
  'Wellness',
  'Focus',
  'Emotional Support',
  'Relaxation',
  'Mindfulness',
  'Recovery',
  'AI Support',
];

const ToolsIndex: React.FC = () => {
  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center space-y-4 mb-6"
      >
        <div className="flex items-center justify-center gap-2 mb-2">
          <div className="flex items-center justify-center h-12 w-12 bg-primary/10 shadow-sm" style={{ borderRadius: '16px' }}>
            <Sparkles className="h-6 w-6 text-primary" strokeWidth={2} />
          </div>
          <h1 className="text-2xl lg:text-3xl font-bold tracking-tight text-foreground">
            Quit Smoking Tools
          </h1>
        </div>
        <p className="text-sm text-muted-foreground max-w-2xl mx-auto leading-relaxed font-medium">
          Access a comprehensive suite of professional-grade wellness tools designed to support your quit smoking journey. 
          Each tool is scientifically crafted to help you overcome specific challenges and build lasting healthy habits with precision.
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
      >
        {tools.map((tool, index) => (
          <motion.div
            key={tool.id}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.5,
              delay: index * 0.1,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
          >
            <Link to={tool.href} className="block group h-full">
              <Card className="h-full bg-card border border-border shadow-sm transition-all duration-200 hover:shadow-md hover:border-primary/30 flex flex-col" style={{ borderRadius: '16px' }}>
                <div className="flex flex-col h-full p-6">
                  <div className="w-12 h-12 bg-primary/10 flex items-center justify-center mb-4 shadow-sm" style={{ borderRadius: '16px' }}>
                    <tool.icon className="w-6 h-6 text-primary" strokeWidth={2} />
                  </div>
                  
                  <div className="flex-1 space-y-2">
                    <div className="mb-1">
                      <span className="text-xs font-semibold text-primary uppercase tracking-wider">
                        {tool.category}
                      </span>
                    </div>
                    <h3 className="text-lg font-bold text-foreground leading-tight">
                      {tool.title}
                    </h3>
                    <p className="text-muted-foreground text-sm leading-relaxed">
                      {tool.description}
                    </p>
                  </div>
                  
                  <div className="mt-4 pt-2 border-t border-border/40">
                    <span className="text-sm text-primary font-semibold transition-colors group-hover:text-primary-hover">
                      Start Tool →
                    </span>
                  </div>
                </div>
              </Card>
            </Link>
          </motion.div>
        ))}
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.8 }}
        className="mt-16 text-center"
      >
                    <div className="bg-card p-8 border border-border" style={{ borderRadius: '48px' }}>
          <h2 className="text-2xl font-bold mb-4 text-foreground">
            Need Help Choosing?
          </h2>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Not sure which tool to start with? Try our AI Coach for personalized recommendations 
            based on your current needs and quit smoking goals.
          </p>
          <Link
            to="/app/tools/ai-coach"
            className="inline-flex items-center gap-2 bg-primary hover:bg-primary-hover text-primary-foreground px-6 py-3 font-medium transition-all duration-300 hover:shadow-lg hover:shadow-primary/20"
            style={{ borderRadius: '16px' }}
          >
            <MessageCircle className="w-5 h-5" />
            Talk to AI Coach
          </Link>
        </div>
      </motion.div>
    </div>
  );
};

export default ToolsIndex; 