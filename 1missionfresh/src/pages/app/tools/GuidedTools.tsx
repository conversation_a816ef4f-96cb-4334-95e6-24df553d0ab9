import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
// REMOVED QuickToolCard import - NO MORE MOCKUP QUICK TOOLS IN PRODUCTION
import { Play, Compass, Moon, Sunrise, <PERSON>P<PERSON>, Heart } from 'lucide-react';
import { ExerciseModal } from '@/components/tools/ExerciseModal';
import CircadianReset from '@/components/tools/guided/CircadianReset';
import GuidedExercise from '@/components/tools/guided/GuidedExercise';
import { useHaptics, HapticImpact } from '@/hooks/useHaptics';
import { useAuth } from '@/contexts/AuthContext';
import { earnPoints } from '@/services/rewardsService';

const fadeInUp = {
  hidden: { y: 30, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.6, ease: [0.16, 1, 0.3, 1] },
  },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const GuidedTools = () => {
  const { impact } = useHaptics();
  const { user } = useAuth();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState<React.ReactNode | null>(null);
  const [modalTitle, setModalTitle] = useState('');
  const [modalDescription, setModalDescription] = useState<string | undefined>(undefined);

  const handleToolComplete = async () => {
    impact(HapticImpact.MEDIUM);

    const selfAwardingToolTitles = [
      "Circadian Reset", "Morning Energy", "Evening Wind-Down", "Nature Connection", "Heart Coherence"
    ];

    if (user?.id && !selfAwardingToolTitles.includes(modalTitle)) {
      try {
        const pointsToAward = 15;
        await earnPoints(user.id, pointsToAward, 'exercise', modalTitle || 'Guided Exercise');
        console.log(`Awarded ${pointsToAward} points for completing ${modalTitle || 'Guided Exercise'}`);
      } catch (error) {
        console.error('Failed to award points for tool:', modalTitle, error);
      }
    } else if (user?.id && selfAwardingToolTitles.includes(modalTitle)) {
      console.log(`Points for "${modalTitle}" are awarded by the component itself.`);
    } else if (!user?.id) {
      console.warn('User not authenticated, cannot track tool usage or award points.');
    }

    setIsModalOpen(false);
    setModalContent(null);
  };

  const openExerciseModal = (title: string, description: string | undefined, content: React.ReactNode) => {
    setModalTitle(title);
    setModalDescription(description);
    setModalContent(content);
    setIsModalOpen(true);
  };

  return (
    <>
      <div className="container max-w-7xl mx-auto px-6 md:px-8 py-10">
        <motion.div
          className="space-y-10"
          initial="hidden"
          animate="visible"
          variants={staggerContainer}
        >
          {/* Hero Header */}
          <motion.div 
            className="mb-10"
            variants={fadeInUp}
          >
            <div className="flex items-center gap-4 mb-6">
              <div className="flex items-center justify-center h-16 w-16 bg-secondary shadow-sm" style={{borderRadius: '32px'}}>
                <Compass className="h-6 w-6 text-primary" />
              </div>
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tight text-foreground">Guided Tools</h1>
                <p className="text-muted-foreground text-base leading-relaxed">Structured exercises for deeper wellness and recovery</p>
              </div>
            </div>
          </motion.div>

          {/* Welcome Card */}
          <motion.div variants={fadeInUp}>
            <Card className="card-elegant border border-border bg-card shadow-lg">
              <CardHeader className="pb-6">
                <div className="flex items-center gap-3">
                          <div className="icon-2xl bg-primary-subtle flex items-center justify-center border border-primary" style={{borderRadius: '24px'}}>
          <Play className="icon-base text-primary" />
                  </div>
                  <CardTitle className="text-xl font-bold tracking-tight text-foreground">
                    Immersive Wellness Experiences
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-base text-muted-foreground leading-relaxed">
                  These guided exercises provide structured support for your quit journey. Each session is designed to help you develop deeper wellness practices, manage withdrawal symptoms, and build lasting healthy habits.
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Featured Sessions */}
          <motion.div variants={fadeInUp}>
            <Card className="card-elegant border border-border bg-card shadow-lg">
              <CardHeader className="pb-6">
                <div className="flex items-center gap-3">
                                          <div className="icon-2xl bg-secondary flex items-center justify-center border border-secondary" style={{borderRadius: '24px'}}>
          <Play className="icon-base text-secondary-foreground" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-bold tracking-tight text-foreground">
                      Featured Sessions
                    </CardTitle>
                    <CardDescription className="text-base text-muted-foreground">
                      Expertly crafted experiences for your wellness journey
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Card className="card-elegant border border-border/40 bg-card hover:shadow-lg transition-all duration-300 cursor-pointer h-full"
                          onClick={() => openExerciseModal(
                            'Circadian Reset',
                            'Restore natural sleep-wake cycles disrupted by nicotine',
                            <CircadianReset onComplete={handleToolComplete} />
                          )}>
                      <CardContent className="p-6">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="icon-lg bg-secondary flex items-center justify-center border border-primary" style={{borderRadius: '24px'}}>
                            <Moon className="icon-sm text-primary" />
                          </div>
                          <h3 className="text-lg font-semibold text-foreground">Circadian Reset</h3>
                        </div>
                        <p className="text-sm text-muted-foreground leading-relaxed">Restore natural sleep-wake cycles disrupted by nicotine</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Card className="card-elegant border border-border/40 bg-card hover:shadow-lg transition-all duration-300 cursor-pointer h-full"
                          onClick={() => openExerciseModal(
                            'Morning Energy',
                            'Start your day with focused intention and vitality',
                            <GuidedExercise 
                              title="Morning Energy Session"
                              steps={[
                                "Take three deep breaths to center yourself",
                                "Set a positive intention for your smoke-free day",
                                "Visualize yourself handling challenges with ease",
                                "Feel gratitude for your commitment to health",
                                "Energize your body with gentle movement"
                              ]}
                              onComplete={handleToolComplete}
                            />
                          )}>
                      <CardContent className="p-6">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="icon-lg bg-secondary flex items-center justify-center border border-primary" style={{borderRadius: '24px'}}>
                            <Sunrise className="icon-sm text-primary" />
                          </div>
                          <h3 className="text-lg font-semibold text-foreground">Morning Energy</h3>
                        </div>
                        <p className="text-sm text-muted-foreground leading-relaxed">Start your day with focused intention and vitality</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Card className="card-elegant border border-border/40 bg-card hover:shadow-lg transition-all duration-300 cursor-pointer h-full"
                          onClick={() => openExerciseModal(
                            'Evening Wind-Down',
                            'Release the day\'s stress and prepare for restorative sleep',
                            <GuidedExercise 
                              title="Evening Wind-Down"
                              steps={[
                                "Reflect on the day's accomplishments",
                                "Release any tension or stress from your body",
                                "Practice gratitude for staying smoke-free",
                                "Set peaceful intentions for tomorrow",
                                "Prepare your mind and body for rest"
                              ]}
                              onComplete={handleToolComplete}
                            />
                          )}>
                      <CardContent className="p-6">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="icon-lg bg-secondary flex items-center justify-center border border-primary" style={{borderRadius: '24px'}}>
                            <Moon className="icon-sm text-primary" />
                          </div>
                          <h3 className="text-lg font-semibold text-foreground">Evening Wind-Down</h3>
                        </div>
                        <p className="text-sm text-muted-foreground leading-relaxed">Release the day's stress and prepare for restorative sleep</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Card className="card-elegant border border-border/40 bg-card hover:shadow-lg transition-all duration-300 cursor-pointer h-full"
                          onClick={() => openExerciseModal(
                            'Nature Connection',
                            'Ground yourself with natural imagery and sounds',
                            <GuidedExercise 
                              title="Nature Connection"
                              steps={[
                                "Imagine yourself in a peaceful natural setting",
                                "Feel the fresh, clean air filling your lungs",
                                "Connect with the healing energy of nature",
                                "Appreciate your body's natural healing process",
                                "Carry this sense of peace with you"
                              ]}
                              onComplete={handleToolComplete}
                            />
                          )}>
                      <CardContent className="p-6">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="icon-lg bg-secondary flex items-center justify-center border border-primary" style={{borderRadius: '24px'}}>
                            <TreePine className="icon-sm text-primary" />
                          </div>
                          <h3 className="text-lg font-semibold text-foreground">Nature Connection</h3>
                        </div>
                        <p className="text-sm text-muted-foreground leading-relaxed">Ground yourself with natural imagery and sounds</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Card className="card-elegant border border-border/40 bg-card hover:shadow-lg transition-all duration-300 cursor-pointer h-full"
                          onClick={() => openExerciseModal(
                            'Heart Coherence',
                            'Synchronize heart, mind, and emotions for deep calm',
                            <GuidedExercise 
                              title="Heart Coherence Practice"
                              steps={[
                                "Place your hand on your heart and breathe slowly",
                                "Focus on feelings of appreciation and care",
                                "Sync your breathing with your heartbeat",
                                "Generate positive emotions throughout your body",
                                "Maintain this coherent state for inner balance"
                              ]}
                              onComplete={handleToolComplete}
                            />
                          )}>
                      <CardContent className="p-6">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="icon-lg bg-secondary flex items-center justify-center border border-border" style={{borderRadius: '24px'}}>
                            <Heart className="icon-sm text-primary" />
                          </div>
                          <h3 className="text-lg font-semibold text-foreground">Heart Coherence</h3>
                        </div>
                        <p className="text-sm text-muted-foreground leading-relaxed">Synchronize heart, mind, and emotions for deep calm</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Card className="card-elegant border border-border/40 bg-card hover:shadow-lg transition-all duration-300 cursor-pointer h-full"
                          onClick={() => openExerciseModal(
                            'Stress Release',
                            'Progressive relaxation to release physical tension',
                            <GuidedExercise 
                              title="Progressive Stress Release"
                              steps={[
                                "Start by tensing and releasing your toes",
                                "Work your way up through each muscle group",
                                "Notice the contrast between tension and relaxation",
                                "Release all physical and mental stress",
                                "Feel completely relaxed and renewed"
                              ]}
                              onComplete={handleToolComplete}
                            />
                          )}>
                      <CardContent className="p-6">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="icon-lg bg-secondary flex items-center justify-center border border-border" style={{borderRadius: '24px'}}>
                            <Compass className="icon-sm text-primary" />
                          </div>
                          <h3 className="text-lg font-semibold text-foreground">Stress Release</h3>
                        </div>
                        <p className="text-sm text-muted-foreground leading-relaxed">Progressive relaxation to release physical tension</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Benefits */}
          <motion.div variants={fadeInUp}>
            <Card className="card-elegant border border-border/40 bg-card shadow-lg">
              <CardHeader className="pb-6">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-primary-subtle flex items-center justify-center border border-border" style={{ borderRadius: '16px' }}>
                    <Heart className="w-5 h-5 text-primary" />
                  </div>
                  <CardTitle className="text-xl font-bold tracking-tight text-foreground">
                    Why Guided Practice Works
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                                      <div className="w-8 h-8 bg-secondary flex items-center justify-center mt-1" style={{ borderRadius: '24px' }}>
                  <div className="w-2 h-2 bg-primary" style={{ borderRadius: '50%' }}></div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-foreground mb-1">Structured Support</h4>
                        <p className="text-muted-foreground text-sm">Clear guidance helps you stay focused and get maximum benefit</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                                              <div className="w-8 h-8 bg-primary-subtle flex items-center justify-center mt-1" style={{ borderRadius: '24px' }}>
                          <div className="w-2 h-2 bg-primary" style={{ borderRadius: '50%' }}></div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-foreground mb-1">Deeper Relaxation</h4>
                        <p className="text-muted-foreground text-sm">Guided sessions can achieve deeper states than solo practice</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                                      <div className="w-8 h-8 bg-secondary flex items-center justify-center mt-1" style={{ borderRadius: '24px' }}>
                  <div className="w-2 h-2 bg-secondary" style={{ borderRadius: '50%' }}></div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-foreground mb-1">Skill Building</h4>
                        <p className="text-muted-foreground text-sm">Learn techniques you can use independently over time</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                                      <div className="w-8 h-8 bg-accent flex items-center justify-center mt-1" style={{ borderRadius: '24px' }}>
                  <div className="w-2 h-2 bg-accent" style={{ borderRadius: '50%' }}></div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-foreground mb-1">Consistent Practice</h4>
                        <p className="text-muted-foreground text-sm">Regular guided sessions help establish healthy routines</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-primary flex items-center justify-center mt-1" style={{ borderRadius: '24px' }}>
                        <div className="w-2 h-2 bg-primary" style={{ borderRadius: '50%' }}></div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-foreground mb-1">Mind-Body Connection</h4>
                        <p className="text-muted-foreground text-sm">Strengthen awareness of how thoughts affect physical well-being</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Usage Tips */}
          <motion.div variants={fadeInUp}>
            <Card className="card-elegant border border-border/40 bg-card shadow-lg">
              <CardHeader className="pb-6">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-primary flex items-center justify-center border border-primary" style={{ borderRadius: '16px' }}>
                    <Compass className="w-5 h-5 text-primary-foreground" />
                  </div>
                  <CardTitle className="text-xl font-bold tracking-tight text-foreground">
                    Getting the Most from Your Practice
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                                <div className="flex items-center gap-4 p-4 bg-secondary border border-border" style={{ borderRadius: '16px' }}>
                <div className="w-3 h-3 bg-primary" style={{ borderRadius: '50%' }}></div>
                    <div>
                      <h4 className="font-semibold text-foreground">Find a Quiet Space</h4>
                      <p className="text-muted-foreground text-sm">Choose a comfortable, distraction-free environment for your practice</p>
                    </div>
                  </div>
                                <div className="flex items-center gap-4 p-4 bg-primary-subtle border border-border" style={{ borderRadius: '16px' }}>
                <div className="w-3 h-3 bg-primary" style={{ borderRadius: '50%' }}></div>
                    <div>
                      <h4 className="font-semibold text-foreground">Use Headphones</h4>
                      <p className="text-muted-foreground text-sm">For the best audio experience and deeper immersion</p>
                    </div>
                  </div>
                                <div className="flex items-center gap-4 p-4 bg-secondary border border-secondary" style={{ borderRadius: '16px' }}>
                <div className="w-3 h-3 bg-secondary" style={{ borderRadius: '50%' }}></div>
                    <div>
                      <h4 className="font-semibold text-foreground">Be Patient</h4>
                      <p className="text-muted-foreground text-sm">Benefits increase with regular practice - start with just a few minutes daily</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </div>
      
      <ExerciseModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onComplete={handleToolComplete}
        title={modalTitle}
        description={modalDescription}
      >
        {modalContent}
      </ExerciseModal>
    </>
  );
};

export default GuidedTools;
