import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Send, Bot, User as UserIcon, Loader2, Brain, Sparkles, MessageCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getAICoachResponse } from '@/services/aiService'; // We will create this function

import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';


interface Message {
  id: string;
  sender: 'user' | 'ai';
  text: string;
  timestamp: Date;
}

const AICoachPage: React.FC = () => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoadingAIResponse, setIsLoadingAIResponse] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Initial greeting from AI
  useEffect(() => {
    setMessages([
      {
        id: Date.now().toString(),
        sender: 'ai',
        text: "Hello! I'm your AI Quit Coach. How can I help you on your journey today?",
        timestamp: new Date(),
      },
    ]);
  }, []);

  // Scroll to bottom when new messages are added
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector('div');
      if (scrollElement) {
        scrollElement.scrollTop = scrollElement.scrollHeight;
      }
    }
  }, [messages]);

  const handleSendMessage = async () => {
    console.log('🔍 SIMPLE HANDLER: Function called');
    console.log('🔍 SIMPLE HANDLER: Input value:', inputValue);
    
    if (!inputValue.trim() || isLoadingAIResponse) {
      console.log('🔍 SIMPLE HANDLER: Early return');
      return;
    }

    const messageText = inputValue.trim();
    console.log('🔍 SIMPLE HANDLER: Message text:', messageText);
    
    // Clear input and set loading
    setInputValue('');
    setIsLoadingAIResponse(true);
    
    // Create and add user message
    const userMessage: Message = {
      id: Date.now().toString() + '_user',
      sender: 'user',
      text: messageText,
      timestamp: new Date(),
    };
    
    console.log('🔍 SIMPLE HANDLER: Adding user message');
    setMessages(prev => [...prev, userMessage]);

    try {
      console.log('🔍 SIMPLE HANDLER: Calling AI service');
      const aiResponseText = await getAICoachResponse(messageText, []);
      
      const aiMessage: Message = {
        id: Date.now().toString() + '_ai',
        sender: 'ai',
        text: aiResponseText || "I'm having trouble connecting right now. Please try again in a moment.",
        timestamp: new Date(),
      };
      
      console.log('🔍 SIMPLE HANDLER: Adding AI message');
      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('🔍 SIMPLE HANDLER: Error:', error);
      const errorMessage: Message = {
        id: Date.now().toString() + '_error',
        sender: 'ai',
        text: "I'm having trouble connecting right now. Please try again in a moment.",
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      console.log('🔍 SIMPLE HANDLER: Setting loading false');
      setIsLoadingAIResponse(false);
    }
  };

  return (
    <div className="container max-w-7xl mx-auto px-6 md:px-8 py-10 h-[calc(100vh-var(--header-height,80px)-2rem)]">
      
      {/* Hero Header */}
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div className="flex items-center gap-4 mb-6">
          <div className="flex items-center justify-center h-16 w-16 bg-secondary shadow-sm" style={{borderRadius: '32px'}}>
            <MessageCircle className="h-6 w-6 text-primary" />
          </div>
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tight text-foreground">AI Coach</h1>
            <p className="text-muted-foreground text-base leading-relaxed">Your personal wellness companion and guide</p>
          </div>
        </div>
      </motion.div>

      {/* Chat Container */}
                <Card className="flex-1 flex flex-col min-h-0 bg-card border border-border shadow-xl" style={{borderRadius: '32px'}}>
        <CardContent className="flex-1 flex flex-col p-0 min-h-0">
          
          {/* Messages Area */}
          <ScrollArea className="flex-1 min-h-0" ref={scrollAreaRef}>
            <div className="p-8 space-y-6">
              <AnimatePresence initial={false}>
                {messages.map((msg, index) => (
                  <motion.div
                    key={msg.id}
                    initial={{ opacity: 0, y: 20, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -20, scale: 0.95 }}
                    transition={{ 
                      duration: 0.4, 
                      ease: [0.16, 1, 0.3, 1],
                      delay: index * 0.05 
                    }}
                    className={cn(
                      "flex items-end gap-6",
                      msg.sender === 'user' ? 'justify-end' : 'justify-start'
                    )}
                  >
                    {msg.sender === 'ai' && (
                      <Avatar className="h-10 w-10 border-2 border-primary shadow-sm">
                        <AvatarFallback className="bg-primary">
                          <Bot className="h-5 w-5 text-primary" />
                        </AvatarFallback>
                      </Avatar>
                    )}
                    
                    <div
                      className={cn(
                        "relative group max-w-[75%] px-6 py-4 transition-all duration-300",
                        msg.sender === 'user'
                          ? "bg-primary text-primary-foreground shadow-lg hover:shadow-xl"
                          : "bg-card border border-border shadow-sm hover:shadow-md"
                      )}
                    >
                      {msg.sender === 'ai' && (
                        <div className="absolute -top-1 -left-1">
                          <Sparkles className="h-4 w-4 text-primary animate-pulse" />
                        </div>
                      )}
                      
                      <p className="text-base font-medium leading-relaxed whitespace-pre-wrap">
                        {msg.text}
                      </p>
                      
                      <p className={cn(
                        "text-xs mt-2 font-medium",
                        msg.sender === 'user' 
                          ? "text-primary-foreground text-right" 
                          : "text-muted-foreground text-left"
                      )}>
                        {new Date(msg.timestamp).toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </p>
                    </div>
                    
                    {msg.sender === 'user' && (
                      <Avatar className="h-10 w-10 border-2 border-primary shadow-sm">
                        <AvatarImage src={user?.user_metadata?.avatar_url} />
                        <AvatarFallback className="bg-primary font-semibold">
                          {user?.email?.charAt(0).toUpperCase() || <UserIcon className="h-5 w-5" />}
                        </AvatarFallback>
                      </Avatar>
                    )}
                  </motion.div>
                ))}
              </AnimatePresence>
              
              {/* Loading Indicator */}
              {isLoadingAIResponse && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="flex items-end gap-6 justify-start"
                >
                  <Avatar className="h-10 w-10 border-2 border-primary shadow-sm">
                    <AvatarFallback className="bg-primary">
                      <Bot className="h-5 w-5 text-primary" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="bg-card border border-border px-6 py-4 shadow-sm" style={{borderRadius: '32px'}}>
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-5 w-5 animate-spin text-primary" />
                      <span className="text-sm text-muted-foreground font-medium">
                        AI is thinking...
                      </span>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </ScrollArea>

          {/* Input Area */}
                      <div className="border-t border-border p-6 bg-background">
            <div className="flex items-center gap-6">
              <div className="relative flex-1">
                <MessageCircle className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Ask your AI Coach for guidance..."
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      console.log('🔍 Enter key pressed');
                      handleSendMessage();
                    }
                  }}
                  disabled={isLoadingAIResponse}
                  className="pl-12 pr-4 py-3 text-base border-border focus:border-primary focus:ring-primary bg-background transition-all duration-300" style={{borderRadius: '32px'}}
                />
              </div>
              <Button 
                onClick={() => {
                  console.log('🔍 Send button clicked');
                  handleSendMessage();
                }} 
                disabled={isLoadingAIResponse || inputValue.trim() === ''} 
                size="lg"
                className="h-12 w-12 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 active:scale-95" style={{borderRadius: '32px'}}
              >
                {isLoadingAIResponse ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  <Send className="h-5 w-5" />
                )}
              </Button>
            </div>
          </div>
          
        </CardContent>
      </Card>
    </div>
  );
};

export default AICoachPage;
