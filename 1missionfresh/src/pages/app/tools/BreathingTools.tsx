import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import BreathingExercise from '@/components/tools/breathing/BreathingExercise';
import ToolExerciseCard from '@/components/tools/ToolExerciseCard';
import { useHaptics, HapticImpact } from '@/hooks/useHaptics';
import { breathingExercises } from '@/lib/toolsData';
import { useAuth } from '@/contexts/AuthContext';
import { Wind, ArrowLeft } from 'lucide-react';

const BreathingTools = () => {
  const { impact } = useHaptics();
  const { user } = useAuth();
  const [activeExercise, setActiveExercise] = useState<string | null>(null);
  const [exerciseTitle, setExerciseTitle] = useState('');

  const handleToolComplete = useCallback(() => {
    impact(HapticImpact.MEDIUM);
    setActiveExercise(null);
    setExerciseTitle('');
  }, [impact]);

  const startExercise = useCallback((exerciseKey: string, title: string) => {
    console.log('🔧 Starting exercise:', exerciseKey, title);
    setActiveExercise(exerciseKey);
    setExerciseTitle(title);
  }, []);

  const goBack = useCallback(() => {
    setActiveExercise(null);
    setExerciseTitle('');
  }, []);

  // If an exercise is active, render it directly
  if (activeExercise) {
    return (
      <div className="container max-w-4xl mx-auto px-6 md:px-8 py-10">
        <div className="mb-8">
          <Button 
            onClick={goBack}
            variant="ghost" 
            className="mb-4 hover:bg-secondary"
          >
            <ArrowLeft className="h-6 w-6 mr-2" />
            Back to Exercises
          </Button>
          <h1 className="text-heading-xl font-bold tracking-premium text-foreground">{exerciseTitle}</h1>
        </div>
        <BreathingExercise 
          exerciseType={activeExercise as keyof typeof breathingExercises} 
          onComplete={handleToolComplete} 
        />
      </div>
    );
  }

  // Default view with exercise cards - DIRECT INLINE IMPLEMENTATION to bypass infinite loop
  return (
    <div className="container max-w-7xl mx-auto px-6 md:px-8 py-10">
      <div className="mb-10">
        <div className="flex items-center gap-4 mb-6">
          <div className="relative group">
                            <div className="absolute inset-0 bg-secondary/20 transition-all duration-300" style={{ borderRadius: '24px' }}></div>
            <div className="relative flex items-center justify-center bg-primary border border-primary hover:border-primary h-16 w-16 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105" style={{ borderRadius: '24px' }}>
              <Wind className="text-primary-foreground h-6 w-6 group-hover:scale-110 transition-transform duration-300" strokeWidth={1.8} />
            </div>
          </div>
          <div className="space-y-2">
            <h1 className="text-heading-xl font-bold tracking-premium text-foreground">Calm Your Breath</h1>
            <p className="text-muted-foreground text-base leading-relaxed">Find your center through mindful breathing</p>
          </div>
        </div>
        <p className="text-muted-foreground leading-relaxed max-w-3xl text-lg">
          Breathing exercises can help reduce stress, manage cravings, and improve focus by regulating your nervous system naturally.
        </p>
      </div>

      <div className="space-y-8">
        <div className="space-y-3">
          <h2 className="text-heading-lg font-semibold tracking-premium text-foreground">Available Breathing Exercises</h2>
          <p className="text-muted-foreground text-base leading-relaxed">Choose an exercise to begin your mindful breathing journey.</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 pb-8">
          {Object.entries(breathingExercises).map(([key, exercise]) => (
            <ToolExerciseCard
              key={key}
              title={exercise.title}
              description={exercise.description}
              duration={exercise.duration}
              difficulty={exercise.difficulty}
              tags={exercise.tags || []}
              onStart={() => {
                console.log('🌟 FUNCTIONAL: Starting breathing exercise:', exercise.title);
                startExercise(key, exercise.title);
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default BreathingTools;
