import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Timer, <PERSON>, Clock, Dumbbell, Lightbulb, ListChecks, Focus as FocusIcon, Headphones, Smile, Heart, Music, SunMoon, Flower, BookOpen, Hand } from 'lucide-react'; // Added icons
// import { MentalChallenges } from '@/components/tools/mental/MentalChallenges'; // Temporarily disabled to fix stack overflow
// import { HandExercises } from '@/components/tools/hand/HandExercises'; // Temporarily disabled to fix stack overflow
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { useHaptics, HapticImpact } from '@/hooks/useHaptics';
import ToolExerciseCard from '@/components/tools/ToolExerciseCard'; // Import ToolExerciseCard
// REMOVED QuickToolCard import - NO MORE <PERSON>OC<PERSON>UP QUICK TOOLS IN PRODUCTION
import { toast } from 'sonner'; // Cline: Added toast import
import { ExerciseModal } from '@/components/tools/ExerciseModal'; // Import ExerciseModal
import BreathingExercise from '@/components/tools/breathing/BreathingExercise'; // Re-enabled for modal system
// import GuidedExercise from '@/components/tools/guided/GuidedExercise'; // Temporarily disabled to fix stack overflow
// import QuickJournalingExercise from '@/components/tools/journaling/QuickJournalingExercise'; // Temporarily disabled to fix event handlers
import DelayTimer from '@/components/tools/timers/DelayTimer'; // Import DelayTimer
import { useAuth } from '@/contexts/AuthContext'; // Import useAuth
import { saveJournalEntry } from '@/services/logService'; // Import saveJournalEntry
import { earnPoints } from '@/services/rewardsService'; // Added import for earnPoints
import { guidedExerciseSteps, quickJournalingPrompts } from '@/lib/toolsData'; // Import data from toolsData
import { motion } from "framer-motion"; // Ensure motion is imported

const CravingTools = () => {
  const { impact } = useHaptics();
  const { user } = useAuth(); // Get the current user
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState<React.ReactNode | null>(null);
  const [modalTitle, setModalTitle] = useState('');
  const [modalDescription, setModalDescription] = useState<string | undefined>(undefined);
  const [activeTab, setActiveTab] = useState('quick');

  // FIXED FLAW #67: INFINITE LOOP BUG - Create simple content without interactive elements
  const sensoryGroundingContent = React.useMemo(() => (
    <div className="p-6 text-center">
      <h3 className="text-xl font-semibold mb-4">Sensory Grounding (5-4-3-2-1)</h3>
      <div className="space-y-4 mb-6">
        <p className="text-muted-foreground">Follow these steps to ground yourself:</p>
        <ol className="list-decimal text-left space-y-2 max-w-md mx-auto">
          <li>Name 5 things you can see</li>
          <li>Name 4 things you can touch</li>
          <li>Name 3 things you can hear</li>
          <li>Name 2 things you can smell</li>
          <li>Name 1 thing you can taste</li>
        </ol>
      </div>
      <p className="text-sm text-muted-foreground">Take your time with each step.</p>
    </div>
  ), []);

  const mentalChallengesContent = React.useMemo(() => (
    <div className="p-6 text-center">
      <h3 className="text-xl font-semibold mb-4">Mental Challenges</h3>
      <div className="space-y-4 mb-6">
        <p className="text-muted-foreground">Distract your mind with these mental tasks:</p>
        <ul className="list-disc text-left space-y-2 max-w-md mx-auto">
          <li>Count backwards from 100 by 7s</li>
          <li>Name all the countries you can think of starting with 'A'</li>
          <li>Recite the alphabet backwards</li>
          <li>Think of 10 words that rhyme with "peace"</li>
        </ul>
      </div>
      <p className="text-sm text-muted-foreground">Choose one challenge and focus completely on it.</p>
    </div>
  ), []);

  // FIXED FLAW #67: Additional memoized content for remaining exercises
  const handExercisesContent = React.useMemo(() => (
    <div className="p-6 text-center">
      <h3 className="text-xl font-semibold mb-4">Hand Exercises</h3>
      <div className="space-y-4 mb-6">
        <p className="text-muted-foreground">Keep your hands busy with these exercises:</p>
        <ul className="list-disc text-left space-y-2 max-w-md mx-auto">
          <li>Squeeze a stress ball for 10 seconds</li>
          <li>Roll a coin across your knuckles</li>
          <li>Play with a fidget spinner or cube</li>
          <li>Interlock your fingers and stretch</li>
          <li>Tap each finger to your thumb in sequence</li>
        </ul>
      </div>
      <p className="text-sm text-muted-foreground">Repeat these exercises until the craving passes.</p>
    </div>
  ), []);

  const quickJournalingContent = React.useMemo(() => (
    <div className="p-6 text-center">
      <h3 className="text-xl font-semibold mb-4">Quick Journaling</h3>
      <div className="space-y-4 mb-6">
        <p className="text-muted-foreground">Write down your thoughts to process the craving:</p>
        <ul className="list-disc text-left space-y-2 max-w-md mx-auto">
          <li>What triggered this craving?</li>
          <li>How am I feeling right now?</li>
          <li>What are three things I'm grateful for?</li>
          <li>How will I feel after resisting this craving?</li>
          <li>What healthy activity can I do instead?</li>
        </ul>
      </div>
      <p className="text-sm text-muted-foreground">Take your time to reflect and write.</p>
    </div>
  ), []);

  // FIXED FLAW #67: INFINITE LOOP BUG - Use useCallback to prevent function recreation
  const handleToolComplete = React.useCallback(async () => { // Made async to await trackToolUsage
    impact(HapticImpact.MEDIUM);

    // Titles of tools that award their own points internally
    const selfAwardingToolTitles = [
      "Box Breathing", "Pursed Lip Breathing", "Alternate Nostril Breathing",
      "Belly Breathing", "4-7-8 Breathing", "Power Breathing", // From BreathingExercise
      "Urge Surfing", "Sensory Grounding (5-4-3-2-1)", "Mental Challenges", "Mindfulness" // From GuidedExercise
      // QuickJournalingExercise handles its own initial points, then calls this, so it's okay for this to add more.
    ];

    if (user?.id && !selfAwardingToolTitles.includes(modalTitle)) {
      try {
        // Award points for tool engagement if not self-awarding
        const pointsToAward = 10;
        await earnPoints(user.id, pointsToAward, 'exercise', modalTitle || 'Craving Tool');
        console.log(`Awarded ${pointsToAward} points for completing ${modalTitle || 'Craving Tool'}`);
      } catch (error) {
        console.error('Failed to award points for tool:', modalTitle, error);
      }
    } else if (user?.id && selfAwardingToolTitles.includes(modalTitle)) {
      console.log(`Points for "${modalTitle}" are awarded by the component itself.`);
    } else if (!user?.id) {
      console.warn('User not authenticated, cannot track tool usage or award points.');
    }

    setIsModalOpen(false); // Close modal on completion
    setModalContent(null); // Clear modal content
  }, [user, modalTitle, impact]); // Dependencies for useCallback

  // New handler for Quick Journaling completion
  const handleJournalComplete = async (entry: string) => {
    // Save the journal entry
    if (user?.id) {
      await saveJournalEntry({
        user_id: user.id,
        content: entry,
        created_at: new Date().toISOString(),
      });
    } else {
      toast.error("You must be logged in to save journal entries.");
    }

    // Award points for journaling
    if (user?.id) {
      try {
        const pointsToAward = 5;
        await earnPoints(user.id, pointsToAward, 'journal', 'Quick Craving Journal');
        console.log(`Awarded ${pointsToAward} points for quick journaling.`);
        // toast.success(`+${pointsToAward} for journaling!`); // Optional toast for these specific points
      } catch (error) {
        console.error('Failed to award points for journaling:', error);
      }
    }

    // Then call the general tool complete handler
    handleToolComplete();
  };

  // FIXED FLAW #67: INFINITE LOOP BUG - Use useCallback to prevent function recreation
  const openExerciseModal = React.useCallback((title: string, description: string | undefined, content: React.ReactNode) => {
    console.log('🔧 openExerciseModal called with title:', title);
    console.log('🔧 Current modal state before:', isModalOpen);
    setModalTitle(title);
    setModalDescription(description);
    setModalContent(content);
    setIsModalOpen(true);
    console.log('🔧 Modal state should now be true');
  }, []); // Empty dependency array to prevent recreation

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="container max-w-7xl mx-auto px-6 md:px-8 py-10"
    >
      <div className="mb-8">
        {/* FIXED FLAW #57: PERFECT SECTION TYPOGRAPHY CONSISTENCY - MATCHES HOMEPAGE */}
        <div className="flex flex-col items-center text-center space-y-6 mb-12">
          <h1 className="text-heading-xl font-bold text-foreground leading-premium tracking-premium max-w-4xl">
            Craving Management <span className="text-primary font-bold">Tools</span>
          </h1>
          <p className="text-xl text-muted-foreground leading-elegant tracking-refined max-w-2xl">
            Tools and techniques to help you manage your cravings effectively the moment they strike.
          </p>
        </div>
      </div>
        
        <div className="space-y-8">
          {/* FIXED FLAW #58: PERFECT TAB BUTTON CONSISTENCY - MATCHES HOMEPAGE STYLING */}
          <div className="flex justify-center">
            <div className="inline-flex items-center bg-background border border-border p-1 shadow-sm" style={{ borderRadius: '24px' }}>
              <button
                onClick={() => setActiveTab('quick')}
                className={`px-6 py-2 font-semibold text-base transition-all duration-200 ${
                  activeTab === 'quick' 
                    ? 'bg-primary text-primary-foreground shadow-sm' 
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                }`}
                style={{ borderRadius: '16px' }}
              >
                Quick Relief
              </button>
              <button
                onClick={() => setActiveTab('exercises')}
                className={`px-6 py-2 font-semibold text-base transition-all duration-200 ${
                  activeTab === 'exercises' 
                    ? 'bg-primary text-primary-foreground shadow-sm' 
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                }`}
                style={{ borderRadius: '16px' }}
              >
                Breathing Exercises 
              </button>
              <button
                onClick={() => setActiveTab('distraction')}
                className={`px-6 py-2 font-semibold text-base transition-all duration-200 ${
                  activeTab === 'distraction' 
                    ? 'bg-primary text-primary-foreground shadow-sm' 
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                }`}
                style={{ borderRadius: '16px' }}
              >
                Distraction Techniques 
              </button>
            </div>
          </div>
          
          {activeTab === 'quick' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ToolExerciseCard 
                title="Sensory Grounding (5-4-3-2-1)"
                description="Use your senses to ground yourself in the present"
                duration="2 minutes"
                difficulty="easy"
                tags={['mindfulness', 'quick']}
                onStart={() => openExerciseModal(
                  "Sensory Grounding (5-4-3-2-1)",
                  "Follow the guided steps to ground yourself in the present moment.",
                  sensoryGroundingContent
                )}
              />
              <ToolExerciseCard 
                title="Mental Challenges"
                description="Distract your mind with these mental tasks"
                duration="5 minutes"
                difficulty="easy"
                tags={['quick', 'cognitive']}
                onStart={() => openExerciseModal(
                  "Mental Challenges",
                  "Complete these mental tasks to redirect your focus.",
                  mentalChallengesContent
                )}
              />
              <ToolExerciseCard 
                title="Hand Exercises"
                description="Keep your hands busy to manage cravings"
                duration="3 minutes"
                difficulty="easy"
                tags={['physical', 'quick']}
                onStart={() => openExerciseModal(
                  "Hand Exercises",
                  "Follow these hand exercises to stay focused.",
                  handExercisesContent
                )}
              />
              <ToolExerciseCard
                title="Quick Journaling"
                description="Write down your thoughts to process them"
                duration="2 minutes"
                difficulty="easy"
                tags={['journaling', 'quick']}
                onStart={() => openExerciseModal(
                  "Quick Journaling",
                  "Take a few minutes to write:",
                  quickJournalingContent
                )}
              />
            </div>
          </div>
          )}
          
          {activeTab === 'exercises' && (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 mt-8">
            <ToolExerciseCard 
              title="Box Breathing"
              description="Equal counts of inhale, hold, exhale, and hold"
              duration="2 minutes"
              difficulty="easy"
              tags={['breathing']}
               onStart={() => openExerciseModal(
                  "Box Breathing", 
                  "Follow the steps to complete the exercise.",
                  <BreathingExercise exerciseType="box" onComplete={handleToolComplete} />
                )}
            />
            <ToolExerciseCard 
              title="Pursed Lip Breathing"
              description="Slow breathing through pursed lips to reduce anxiety"
              duration="3 minutes"
              difficulty="easy"
              tags={['breathing']}
               onStart={() => openExerciseModal(
                  "Pursed Lip Breathing", 
                  "Follow the steps to complete the exercise.",
                  <BreathingExercise exerciseType="pursedLip" onComplete={handleToolComplete} />
                )}
            />
            <ToolExerciseCard 
              title="Alternate Nostril Breathing"
              description="Ancient breathing technique to balance energy"
              duration="5 minutes"
              difficulty="moderate"
              tags={['breathing']}
               onStart={() => openExerciseModal(
                  "Alternate Nostril Breathing", 
                  "Follow the steps to complete the exercise.",
                  <BreathingExercise exerciseType="alternateNostril" onComplete={handleToolComplete} />
                )}
            />
            <ToolExerciseCard 
              title="Belly Breathing"
              description="Deep diaphragmatic breathing to activate relaxation"
              duration="4 minutes"
              difficulty="easy"
              tags={['breathing']}
               onStart={() => openExerciseModal(
                  "Belly Breathing", 
                  "Follow the steps to complete the exercise.",
                  <BreathingExercise exerciseType="belly" onComplete={handleToolComplete} />
               )}
            />
            <ToolExerciseCard
              title="4-7-8 Breathing"
              description="Breathe in for 4 seconds, hold for 7, exhale for 8"
              duration="1 minute"
              difficulty="easy"
              tags={['breathing']}
               onStart={() => openExerciseModal(
                  "4-7-8 Breathing",
                  "Follow the steps to complete the exercise.",
                  <BreathingExercise exerciseType="fourSevenEight" onComplete={handleToolComplete} />
                )}
            />
            <ToolExerciseCard
              title="Power Breathing"
              description="Quick, energizing breaths"
              duration="1 minute"
              difficulty="moderate"
              tags={['breathing']}
               onStart={() => openExerciseModal(
                  "Power Breathing",
                  "Follow the steps to complete the exercise.",
                  <BreathingExercise exerciseType="power" onComplete={handleToolComplete} />
                )}
            />
          </div>
          )}

          {activeTab === 'distraction' && (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 mt-8">
            <ToolExerciseCard
              title="Music Therapy"
              description="Use music to shift your focus and mood"
              duration="3 minutes"
              difficulty="easy"
              tags={['audio', 'mood']}
              onStart={() => openExerciseModal(
                "Music Therapy",
                "Use music as a powerful distraction tool:",
                <div className="p-6 text-center">
                  <h3 className="text-xl font-semibold mb-4">Music Therapy</h3>
                  <div className="space-y-4 mb-6">
                    <p className="text-muted-foreground">Try these music-based distractions:</p>
                    <ul className="list-disc text-left space-y-2 max-w-md mx-auto">
                      <li>Listen to your favorite uplifting song</li>
                      <li>Play a calming nature sounds playlist</li>
                      <li>Hum or sing along to a familiar tune</li>
                      <li>Focus on identifying different instruments</li>
                      <li>Create a mental playlist of motivational songs</li>
                    </ul>
                  </div>
                  <Button onClick={handleToolComplete} className="w-full">
                    Complete Activity
                  </Button>
                </div>
              )}
            />
            <ToolExerciseCard
              title="Visual Focus"
              description="Engage your visual attention to redirect thoughts"
              duration="4 minutes"
              difficulty="easy"
              tags={['visual', 'focus']}
              onStart={() => openExerciseModal(
                "Visual Focus",
                "Use visual techniques to distract your mind:",
                <div className="p-6 text-center">
                  <h3 className="text-xl font-semibold mb-4">Visual Focus</h3>
                  <div className="space-y-4 mb-6">
                    <p className="text-muted-foreground">Try these visual distraction techniques:</p>
                    <ul className="list-disc text-left space-y-2 max-w-md mx-auto">
                      <li>Look out the window and describe what you see</li>
                      <li>Study an object in detail for 2 minutes</li>
                      <li>Watch clouds or moving water</li>
                      <li>Look at photos that make you happy</li>
                      <li>Trace patterns or shapes with your eyes</li>
                    </ul>
                  </div>
                  <Button onClick={handleToolComplete} className="w-full">
                    Complete Activity
                  </Button>
                </div>
              )}
            />
            <ToolExerciseCard
              title="Movement Breaks"
              description="Use physical movement to shift your energy"
              duration="5 minutes"
              difficulty="easy"
              tags={['physical', 'energy']}
              onStart={() => openExerciseModal(
                "Movement Breaks",
                "Use movement to distract and energize:",
                <div className="p-6 text-center">
                  <h3 className="text-xl font-semibold mb-4">Movement Breaks</h3>
                  <div className="space-y-4 mb-6">
                    <p className="text-muted-foreground">Try these movement distractions:</p>
                    <ul className="list-disc text-left space-y-2 max-w-md mx-auto">
                      <li>Take a 2-minute walk around your space</li>
                      <li>Do 10 jumping jacks or stretches</li>
                      <li>Dance to one favorite song</li>
                      <li>Practice yoga poses or balance</li>
                      <li>Clean or organize something nearby</li>
                    </ul>
                  </div>
                  <Button onClick={handleToolComplete} className="w-full">
                    Complete Activity
                  </Button>
                </div>
              )}
            />
            <ToolExerciseCard
              title="Creative Expression"
              description="Channel your energy into creative activities"
              duration="10 minutes"
              difficulty="easy"
              tags={['creative', 'expression']}
              onStart={() => openExerciseModal(
                "Creative Expression",
                "Express yourself creatively to redirect focus:",
                <div className="p-6 text-center">
                  <h3 className="text-xl font-semibold mb-4">Creative Expression</h3>
                  <div className="space-y-4 mb-6">
                    <p className="text-muted-foreground">Try these creative distractions:</p>
                    <ul className="list-disc text-left space-y-2 max-w-md mx-auto">
                      <li>Draw, doodle, or sketch something</li>
                      <li>Write a short poem or story</li>
                      <li>Take photos of interesting objects</li>
                      <li>Practice origami or paper folding</li>
                      <li>Plan a creative project for later</li>
                    </ul>
                  </div>
                  <Button onClick={handleToolComplete} className="w-full">
                    Complete Activity
                  </Button>
                </div>
              )}
            />
            <ToolExerciseCard
              title="Social Connection"
              description="Connect with others for positive distraction"
              duration="5 minutes"
              difficulty="easy"
              tags={['social', 'connection']}
              onStart={() => openExerciseModal(
                "Social Connection",
                "Use social interaction as a healthy distraction:",
                <div className="p-6 text-center">
                  <h3 className="text-xl font-semibold mb-4">Social Connection</h3>
                  <div className="space-y-4 mb-6">
                    <p className="text-muted-foreground">Try these social distractions:</p>
                    <ul className="list-disc text-left space-y-2 max-w-md mx-auto">
                      <li>Call or text a supportive friend</li>
                      <li>Share a positive memory with someone</li>
                      <li>Write a thank you message</li>
                      <li>Join an online support community</li>
                      <li>Plan a future activity with someone</li>
                    </ul>
                  </div>
                  <Button onClick={handleToolComplete} className="w-full">
                    Complete Activity
                  </Button>
                </div>
              )}
            />
            <ToolExerciseCard
              title="Learning Focus"
              description="Engage your mind with learning activities"
              duration="7 minutes"
              difficulty="moderate"
              tags={['mental', 'learning']}
              onStart={() => openExerciseModal(
                "Learning Focus",
                "Redirect your attention through learning:",
                <div className="p-6 text-center">
                  <h3 className="text-xl font-semibold mb-4">Learning Focus</h3>
                  <div className="space-y-4 mb-6">
                    <p className="text-muted-foreground">Try these learning distractions:</p>
                    <ul className="list-disc text-left space-y-2 max-w-md mx-auto">
                      <li>Read an interesting article</li>
                      <li>Watch a short educational video</li>
                      <li>Learn 5 new words in another language</li>
                      <li>Research a topic you're curious about</li>
                      <li>Practice a skill you want to improve</li>
                    </ul>
                  </div>
                  <Button onClick={handleToolComplete} className="w-full">
                    Complete Activity
                  </Button>
                </div>
              )}
            />
          </div>
          )}
        </div>

        <ExerciseModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onComplete={handleToolComplete}
          title={modalTitle}
          description={modalDescription}
        >
          {modalContent}
        </ExerciseModal>
      </motion.div>
  );
};

export default CravingTools;
