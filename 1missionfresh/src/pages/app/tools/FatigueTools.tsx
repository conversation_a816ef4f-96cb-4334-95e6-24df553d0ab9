import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
// REMOVED QuickToolCard import - NO MORE MOCKUP QUICK TOOLS IN PRODUCTION
import { Battery, Wind, Armchair, Eye, Coffee, Zap } from 'lucide-react';
import { ExerciseModal } from '@/components/tools/ExerciseModal';
import PowerBreathing from '@/components/tools/breathing/PowerBreathing';
import QuickStretchExercise from '@/components/tools/stretching/QuickStretchExercise';
import EyeExercise from '@/components/tools/eyeexercises/EyeExercise';
import { useHaptics, HapticImpact } from '@/hooks/useHaptics';
import { useAuth } from '@/contexts/AuthContext';
import { earnPoints } from '@/services/rewardsService';

const fadeInUp = {
  hidden: { y: 40, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.8 },
  },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.3,
    },
  },
};

const FatigueTools = () => {
  const { impact } = useHaptics();
  const { user } = useAuth();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState<React.ReactNode | null>(null);
  const [modalTitle, setModalTitle] = useState('');
  const [modalDescription, setModalDescription] = useState<string | undefined>(undefined);

  const handleToolComplete = async () => {
    impact(HapticImpact.MEDIUM);

    const selfAwardingToolTitles = [
      "Power Breathing", "Quick Stretch", "Eye Exercises"
    ];

    if (user?.id && !selfAwardingToolTitles.includes(modalTitle)) {
      try {
        const pointsToAward = 10;
        await earnPoints(user.id, pointsToAward, 'exercise', modalTitle || 'Fatigue Tool');
        console.log(`Awarded ${pointsToAward} points for completing ${modalTitle || 'Fatigue Tool'}`);
      } catch (error) {
        console.error('Failed to award points for tool:', modalTitle, error);
      }
    } else if (user?.id && selfAwardingToolTitles.includes(modalTitle)) {
      console.log(`Points for "${modalTitle}" are awarded by the component itself.`);
    } else if (!user?.id) {
      console.warn('User not authenticated, cannot track tool usage or award points.');
    }

    setIsModalOpen(false);
    setModalContent(null);
  };

  const openExerciseModal = (title: string, description: string | undefined, content: React.ReactNode) => {
    setModalTitle(title);
    setModalDescription(description);
    setModalContent(content);
    setIsModalOpen(true);
  };

  const quickTools = [
    {
      title: "Power Breathing",
      description: "Energizing breathing technique to boost alertness",
      icon: Wind,
      component: <PowerBreathing onComplete={handleToolComplete} />
    },
    {
      title: "Quick Stretch",
      description: "Gentle movements to increase blood flow and energy",
      icon: Armchair,
      component: <QuickStretchExercise onComplete={handleToolComplete} />
    },
    {
      title: "Eye Exercises",
      description: "Reduce eye strain and mental fatigue",
      icon: Eye,
      component: <EyeExercise onComplete={handleToolComplete} />
    }
  ];

  const energyTips = [
    {
      icon: "blue",
      title: "Stay Hydrated",
      description: "Dehydration is a major cause of fatigue. Drink water regularly throughout the day"
    },
    {
      icon: "green",
      title: "Smart Nutrition", 
      description: "Eat balanced meals with complex carbs and protein to maintain steady energy"
    },
    {
      icon: "purple",
      title: "Power Naps",
      description: "Short 10-20 minute naps can be incredibly restorative"
    },
    {
      icon: "orange",
      title: "Light Movement",
      description: "Even gentle walking can boost circulation and energy levels"
    },
    {
      icon: "pink",
      title: "Sleep Schedule",
      description: "Maintain regular sleep and wake times to regulate your energy"
    },
    {
      icon: "cyan",
      title: "Fresh Air",
      description: "Spend time outdoors or near open windows for natural energy boost"
    }
  ];

  // TODO: Move timeline data to database - currently using static educational content
  const timelineSteps = [
    {
      period: "Week 1-2",
      description: "Fatigue may be most intense as your body adjusts",
      color: "primary",
      bgColor: "bg-primary-subtle",
      borderColor: "border-border",
      dotColor: "bg-primary"
    },
    {
      period: "Week 3-4", 
      description: "Energy levels begin to stabilize and improve",
      color: "green",
      bgColor: "bg-primary-subtle",
      borderColor: "border-border",
      dotColor: "bg-primary"
    },
    {
      period: "Month 2+",
      description: "Natural energy returns stronger than before",
      color: "success",
      bgColor: "bg-success/5",
      borderColor: "border-success",
      dotColor: "bg-success"
    }
  ];

  const getIconColor = (color: string) => {
    const colorMap = {
      blue: "bg-primary",
      green: "bg-primary", 
      purple: "bg-success",
      orange: "bg-accent",
      pink: "bg-accent-hover",
      cyan: "bg-primary"
    };
    return colorMap[color as keyof typeof colorMap] || "bg-primary";
  };

  return (
    <>
      <div className="container max-w-7xl mx-auto px-6 md:px-8 py-10">
        <motion.div
          className="space-y-10"
          initial="hidden"
          animate="visible"
          variants={staggerContainer}
        >
          {/* Hero Header */}
          <motion.div 
            className="space-y-8"
            variants={fadeInUp}
          >
            <div className="flex items-center gap-4 mb-6">
              <div className="flex items-center justify-center h-16 w-16 bg-secondary shadow-sm" style={{borderRadius: '32px'}}>
                <Battery className="h-6 w-6 text-primary" />
              </div>
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tight text-foreground">Fatigue Tools</h1>
                <p className="text-muted-foreground text-base leading-relaxed">Combat tiredness and restore your natural energy</p>
              </div>
            </div>
          </motion.div>

          {/* Welcome Card */}
          <motion.div variants={fadeInUp}>
            <Card className="card-elegant border border-border bg-card shadow-2xl">
              <CardHeader className="pb-8">
                <div className="flex items-center gap-4">
                          <div className="icon-3xl bg-primary flex items-center justify-center border border-primary shadow-elegant" style={{borderRadius: '32px'}}>
          <Coffee className="icon-lg text-primary-foreground" />
                  </div>
                  <CardTitle className="text-2xl font-bold tracking-tight text-foreground">
                    Overcome Withdrawal Fatigue
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-lg text-muted-foreground leading-relaxed">
                  Feeling tired is one of the most common symptoms of nicotine withdrawal. Your body is working hard to readjust and heal. These tools will help you manage fatigue naturally and boost your energy levels during this important transition.
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Quick Energy Boosters */}
          <motion.div variants={fadeInUp}>
            <Card className="card-elegant border border-border bg-card shadow-2xl">
              <CardHeader className="pb-8">
                <div className="flex items-center gap-4">
                          <div className="icon-3xl bg-accent flex items-center justify-center border border-accent-hover shadow-elegant" style={{borderRadius: '32px'}}>
          <Zap className="icon-lg text-accent-foreground" />
                  </div>
                  <div className="space-y-2">
                    <CardTitle className="text-2xl font-bold tracking-tight text-foreground">
                      Quick Energy Revivers
                    </CardTitle>
                    <CardDescription className="text-lg text-muted-foreground">
                      Instant tools to combat fatigue and restore alertness
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {quickTools.map((tool, index) => {
                  const IconComponent = tool.icon;
                  return (
                    <motion.div
                      key={tool.title}
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ 
                        duration: 0.6, 
                        delay: 0.1 * index
                      }}
                    >
                      <Card className="card-elegant border border-border bg-card hover:shadow-lg transition-all duration-300 cursor-pointer h-full"
                            onClick={() => openExerciseModal(tool.title, tool.description, tool.component)}>
                        <CardContent className="p-6">
                          <div className="flex items-center gap-3 mb-3">
                            <div className="icon-lg bg-primary flex items-center justify-center border border-primary" style={{borderRadius: '24px'}}>
                              <IconComponent className="icon-sm text-primary-foreground" />
                            </div>
                            <h3 className="text-lg font-semibold text-foreground">{tool.title}</h3>
                          </div>
                          <p className="text-sm text-muted-foreground leading-relaxed">{tool.description}</p>
                        </CardContent>
                      </Card>
                    </motion.div>
                  );
                })}
              </CardContent>
            </Card>
          </motion.div>

          {/* Energy Management Tips */}
          <motion.div variants={fadeInUp}>
            <Card className="card-elegant border border-border bg-card shadow-2xl">
              <CardHeader className="pb-8">
                <div className="flex items-center gap-4">
                          <div className="icon-3xl bg-primary-subtle flex items-center justify-center border border-primary shadow-lg" style={{borderRadius: '32px'}}>
          <Battery className="icon-lg text-primary" />
                  </div>
                  <CardTitle className="text-2xl font-bold tracking-tight text-foreground">
                    Smart Energy Management
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {energyTips.map((tip, index) => (
                                         <motion.div
                       key={tip.title}
                       className="flex items-start gap-4 p-4 bg-card border border-border hover:border-primary transition-all duration-300 hover:shadow-lg" style={{borderRadius: '32px'}}
                       initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                       animate={{ opacity: 1, x: 0 }}
                       transition={{ 
                         duration: 0.6, 
                         delay: 0.1 * index
                       }}
                    >
                      <div className="icon-2xl bg-primary-subtle flex items-center justify-center mt-1 border border-primary shadow-elegant" style={{borderRadius: '24px'}}>
                        <div className={`w-3 h-3 ${getIconColor(tip.icon)}`} style={{borderRadius: '50%'}}></div>
                      </div>
                      <div className="space-y-2">
                        <h4 className="font-bold text-foreground text-lg">{tip.title}</h4>
                        <p className="text-muted-foreground leading-relaxed">{tip.description}</p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recovery Timeline */}
          <motion.div variants={fadeInUp}>
            <Card className="card-elegant border border-border bg-card shadow-2xl">
              <CardHeader className="pb-8">
                <div className="flex items-center gap-4">
                  <div className="icon-3xl bg-success flex items-center justify-center border border-success shadow-elegant" style={{borderRadius: '32px'}}>
                    <Battery className="icon-lg text-success" />
                  </div>
                  <CardTitle className="text-2xl font-bold tracking-tight text-foreground">
                    Your Energy Will Return
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {timelineSteps.map((step, index) => (
                                         <motion.div
                       key={step.period}
                       className={`flex items-center gap-6 p-6 ${step.bgColor} ${step.borderColor} border hover:shadow-lg transition-all duration-300 hover:scale-[1.02]`} style={{borderRadius: '32px'}}
                       initial={{ opacity: 0, x: -30 }}
                       animate={{ opacity: 1, x: 0 }}
                       transition={{ 
                         duration: 0.6, 
                         delay: 0.2 * index
                       }}
                    >
                      <div className={`icon-sm ${step.dotColor} shadow-lg`} style={{borderRadius: '50%'}}></div>
                      <div className="space-y-2">
                        <h4 className="font-bold text-foreground text-lg">{step.period}</h4>
                        <p className="text-muted-foreground leading-relaxed">{step.description}</p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </div>
      
      <ExerciseModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onComplete={handleToolComplete}
        title={modalTitle}
        description={modalDescription}
      >
        {modalContent}
      </ExerciseModal>
    </>
  );
};

export default FatigueTools;
