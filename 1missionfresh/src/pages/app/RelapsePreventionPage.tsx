import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import TriggerManagement from '@/components/app/relapse-prevention/TriggerManagement';
import CopingStrategyManagement from '@/components/app/relapse-prevention/CopingStrategyManagement';
import TriggerStrategyMapper from '@/components/app/relapse-prevention/TriggerStrategyMapper';
import { ShieldAlert, Zap, Link2 } from 'lucide-react';
import { motion } from "framer-motion"; 

const RelapsePreventionPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState("mapper");

  return (
    <div className="space-y-8">

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="overflow-x-auto pb-2">
          <TabsList className="p-1 bg-background h-auto" style={{borderRadius: '32px'}}>
            <TabsTrigger value="triggers" className="text-base px-4 py-2" style={{borderRadius: '16px'}}>
              <ShieldAlert className="mr-2 h-5 w-5" /> Triggers
            </TabsTrigger>
            <TabsTrigger value="strategies" className="text-base px-4 py-2" style={{borderRadius: '16px'}}>
              <Zap className="mr-2 h-5 w-5" /> Strategies
            </TabsTrigger>
            <TabsTrigger value="mapper" className="text-base px-4 py-2" style={{borderRadius: '16px'}}>
              <Link2 className="mr-2 h-5 w-5" /> Mapper
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="triggers" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, ease: [0.16, 1, 0.3, 1] }}
          >
            <TriggerManagement />
          </motion.div>
        </TabsContent>
        <TabsContent value="strategies" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, ease: [0.16, 1, 0.3, 1] }}
          >
            <CopingStrategyManagement />
          </motion.div>
        </TabsContent>
        <TabsContent value="mapper" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, ease: [0.16, 1, 0.3, 1] }}
          >
            <TriggerStrategyMapper />
          </motion.div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default RelapsePreventionPage;
