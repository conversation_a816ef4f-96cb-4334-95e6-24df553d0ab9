import React, { createContext, useContext, useEffect, useReducer, useCallback } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

// --- TYPE DEFINITIONS ---
interface AuthState {
  user: User | null;
  loading: boolean;
  initialized: boolean;
  error: string | null;
}

type AuthAction =
  | { type: 'AUTH_INIT' }
  | { type: 'AUTH_SUCCESS'; payload: User }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'AUTH_RESET' };

interface AuthContextType extends AuthState {
  signUp: (email: string, password: string, name: string) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
}

// --- CONTEXT AND INITIAL STATE ---
const AuthContext = createContext<AuthContextType | undefined>(undefined);

const initialState: AuthState = {
  user: null,
  loading: true, // Start as loading until the first auth check is complete
  initialized: false,
  error: null,
};

// --- REDUCER ---
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_INIT':
      return { ...state, loading: true, error: null };
    case 'AUTH_SUCCESS':
      return { ...state, loading: false, initialized: true, user: action.payload, error: null };
    case 'AUTH_FAILURE':
      return { ...state, loading: false, initialized: true, error: action.payload };
    case 'AUTH_RESET':
      return { ...initialState, user: null, loading: false, initialized: true };
    default:
      return state;
  }
};

// --- HELPER FUNCTIONS ---
const createProfile = async (userId: string, email: string, firstName: string, lastName: string) => {
  const { error } = await supabase
    .from('profiles')
    .insert({ id: userId, email, first_name: firstName, last_name: lastName });
  if (error) throw error;
};

// --- AUTH PROVIDER COMPONENT ---
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // --- AUTH ACTIONS ---
  const signUp = useCallback(async (email: string, password: string, name: string) => {

    dispatch({ type: 'AUTH_INIT' });
    try {
      const nameParts = name.trim().split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName,
          },
        },
      });

              if (error) {
        throw error;
      }

      if (data.user) {
        await createProfile(data.user.id, email, firstName, lastName);
        
        // CRITICAL FIX: Dispatch AUTH_SUCCESS when user has a session after signup
        if (data.session && data.user) {
          dispatch({ type: 'AUTH_SUCCESS', payload: data.user });
          toast.success('Account created successfully! Welcome to Mission Fresh!');
        } else {
          toast.info("Please check your email to confirm your account.");
          // Don't dispatch AUTH_SUCCESS if email confirmation is required
        }
      } else {
        throw new Error('Sign up did not return a user.');
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to create account';
      toast.error(errorMessage);
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      throw error; // Re-throw so AuthForm can catch and display the error
    }
  }, []);

  const signIn = useCallback(async (email: string, password: string) => {
    dispatch({ type: 'AUTH_INIT' });
    
    try {
      const { data, error } = await supabase.auth.signInWithPassword({ email, password });
      
      if (error) throw error;
      
      // CRITICAL FIX: Dispatch AUTH_SUCCESS with user data
      if (data.user) {
        dispatch({ type: 'AUTH_SUCCESS', payload: data.user });
      }
      
      toast.success('Signed in successfully!');
    } catch (error: any) {
      const errorMessage = error.message.includes('Invalid login credentials')
        ? 'Invalid email or password. Please try again.'
        : error.message.includes('Email not confirmed')
        ? 'Please confirm your email before signing in.'
        : error.message.includes('timed out')
        ? 'Connection timeout. Please check your internet connection and try again.'
        : error.message || 'Failed to sign in';
      
      toast.error(errorMessage);
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      throw new Error(errorMessage); // Re-throw so AuthForm can catch and display the error
    }
  }, []);

  const signOut = useCallback(async () => {
    dispatch({ type: 'AUTH_INIT' });
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      dispatch({ type: 'AUTH_RESET' });
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign out');
      dispatch({ type: 'AUTH_FAILURE', payload: error.message });
    }
  }, []);

  const resetPassword = useCallback(async (email: string) => {
    dispatch({ type: 'AUTH_INIT' });
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${typeof window !== 'undefined' ? window.location.origin : 'https://missionfresh.com'}/update-password`,
      });
      if (error) throw error;
      toast.success('Password reset link sent! Please check your email.');
    } catch (error: any) {
      toast.error(error.message || 'Failed to send reset link');
      dispatch({ type: 'AUTH_FAILURE', payload: error.message });
    }
  }, []);

  const signInWithGoogle = useCallback(async () => {
    dispatch({ type: 'AUTH_INIT' });
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
      });
      if (error) throw error;
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign in with Google');
      dispatch({ type: 'AUTH_FAILURE', payload: error.message });
    }
  }, []);

  // --- EFFECT FOR AUTH STATE CHANGES --- //
  useEffect(() => {
    // Get initial session
    const getSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      const user = session?.user ?? null;
      if (user) {
        dispatch({ type: 'AUTH_SUCCESS', payload: user });
      } else {
        dispatch({ type: 'AUTH_RESET' });
      }
    };

    getSession();

    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      const user = session?.user ?? null;
      
      // Only reset auth on explicit sign out events, not on token refresh or network issues
      if (event === 'SIGNED_OUT') {
        dispatch({ type: 'AUTH_RESET' });
      } else if (user) {
        dispatch({ type: 'AUTH_SUCCESS', payload: user });
      } else if (event === 'TOKEN_REFRESHED' && !session) {
        // Don't immediately reset - let the app try to recover
      }
    });

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  const value = { 
    ...state,
    signUp,
    signIn,
    signOut,
    resetPassword,
    signInWithGoogle,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// --- CUSTOM HOOK ---
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
