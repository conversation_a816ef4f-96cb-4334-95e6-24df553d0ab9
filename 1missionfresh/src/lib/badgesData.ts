import { Award, <PERSON><PERSON><PERSON><PERSON>, Star, TrendingUp, Zap } from 'lucide-react';

export interface Badge {
  id: string;
  name: string;
  description: string;
  threshold: number;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

export const badges: Badge[] = [
  {
    id: 'fresh-start',
    name: 'Fresh Start',
    description: 'Earned by accumulating your first 100 points.',
    threshold: 100,
    icon: Award,
  },
  {
    id: 'making-progress',
    name: 'Making Progress',
    description: 'Earned by accumulating 500 points.',
    threshold: 500,
    icon: TrendingUp,
  },
  {
    id: 'fresh-champion',
    name: 'Fresh Champion',
    description: 'Earned by accumulating 1000 points.',
    threshold: 1000,
    icon: Star,
  },
  {
    id: 'consistency-king',
    name: 'Consistency King',
    description: 'Log your progress for 7 consecutive days.',
    threshold: 7, // This would be handled by a different metric, e.g., consecutive_days_logged
    icon: ShieldCheck,
  },
  {
    id: 'power-user',
    name: 'Power User',
    description: 'Use 5 different tools in the app.',
    threshold: 5, // This would be handled by a different metric, e.g., distinct_tools_used
    icon: Zap,
  },
];
