import { createClient, type SupabaseClient } from '@supabase/supabase-js'
import { Database } from './database.types'

// Use real Supabase URL directly - proxy was causing authentication issues
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://yekarqanirdkdckimpna.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc';

console.log('🔧 SUPABASE CONFIG DEBUG:', {
  url: supabaseUrl,
  urlType: typeof supabaseUrl,
  urlDefined: !!supabaseUrl,
  keyLength: supabaseAnonKey?.length || 0,
  keyType: typeof supabaseAnonKey,
  keyDefined: !!supabase<PERSON>nonKey,
  keyPrefix: supabaseAnonKey?.substring(0, 30) + '...',
  keyIsJWT: supabaseAnonKey?.startsWith('eyJ'),
  envViteSupabaseUrl: import.meta.env.VITE_SUPABASE_URL,
  envViteSupabaseKey: import.meta.env.VITE_SUPABASE_ANON_KEY?.substring(0, 30) + '...'
});

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ SUPABASE CONFIG: Missing environment variables');
  throw new Error("Supabase URL and anon key are required. Make sure to set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in your .env file.");
}

// Let TypeScript infer the client type
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);
