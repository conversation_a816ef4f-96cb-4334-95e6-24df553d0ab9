import { z } from 'zod';

// Define the schema for a single TaperingStep
const taperingStepSchema = z.object({
  id: z.string().uuid().optional(), // id is optional as it might be generated on client before DB insert
  date: z.string().min(1, "Step date is required"), // Ensure date is a non-empty string
  units: z.number().min(0, "Units must be non-negative"), // Changed from target_quantity to units
  notes: z.string().optional(), // Added notes to schema
});

// Define the schema for the log entry form
export const logEntrySchema = z.object({
  nicotineUse: z.enum(["yes", "no"], {
    required_error: "Please indicate if you used nicotine",
  }),
  productType: z.string().optional(), // Required only if nicotineUse is 'yes'
  quantity: z.string().optional(), // Required only if nicotineUse is 'yes'
  nicotineStrength: z.string().optional(), // Required only if nicotineUse is 'yes' and productType is vape or pouch
  mood: z.number().min(1).max(5),
  energy: z.number().min(1).max(5),
  focus: z.number().min(1).max(5),
  sleepHours: z.string().min(1, "Sleep hours is required"),
  sleepQuality: z.number().min(1).max(5),
  cravingIntensity: z.number().min(1).max(10),
  cravingTrigger: z.string().min(1, "Craving trigger is required"),
  journal: z.string().optional(),
}).superRefine((data, ctx) => {
  if (data.nicotineUse === 'yes') {
    if (!data.productType) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Product type is required when nicotine is used",
        path: ['productType'],
      });
    }
    if (!data.quantity || parseFloat(data.quantity) <= 0) {
       ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Quantity is required and must be greater than 0 when nicotine is used",
        path: ['quantity'],
      });
    }
    if ((data.productType === 'vape' || data.productType === 'pouch') && (!data.nicotineStrength || parseFloat(data.nicotineStrength) <= 0)) {
       ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Nicotine strength is required and must be greater than 0 for vape or pouch",
        path: ['nicotineStrength'],
      });
    }
  }
});

// Helper function to validate data against a schema
export const validateData = <T>(schema: z.ZodSchema<T>, data: unknown): z.SafeParseError<T> | z.SafeParseSuccess<T> => {
  return schema.safeParse(data);
};

// Define the schema for method_details, allowing different structures based on method
const nrtProductsSchema = z.object({
  patch: z.boolean().optional(),
  gum: z.boolean().optional(),
  lozenge: z.boolean().optional(),
  inhaler: z.boolean().optional(),
  spray: z.boolean().optional(),
  other: z.string().optional(),
}).passthrough();

const methodDetailsSchema = z.object({
  taperingSteps: z.array(taperingStepSchema).optional(),
  nrtProducts: nrtProductsSchema.optional(),
  nrtDosage: z.string().optional(),
  reductionTarget: z.string().optional(),
  trackingMethod: z.string().optional(),
}).passthrough();

export const goalSchema = (requireCostUsage: boolean) => z.object({
  user_id: z.string().uuid("Invalid user ID"),
  goal_type: z.enum(["afresh", "fresher"], {
    required_error: "Goal type is required",
  }),
  method: z.enum(["cold-turkey", "gradual-reduction", "tapering", "nrt", "harm-reduction"], {
    required_error: "Method is required",
  }),
  product_type: z.string().optional(), // Made optional, will be refined below
  quit_date: z.string().datetime({ message: "Invalid date format" }).nullable(), // Allow null, superRefine will check if required
  reduction_percent: z.number().min(1).max(100).nullable().optional(), // For gradual-reduction
  timeline_days: z.number().min(1).nullable().optional(), // For gradual-reduction
  motivation: z.string().trim().max(500, "Motivation must be 500 characters or less").nullable().optional(),
  typical_daily_usage: requireCostUsage ? z.number({ required_error: "Typical daily usage is required" }).min(0, "Usage must be non-negative") : z.number().nullable().optional(),
  cost_per_unit: requireCostUsage ? z.number({ required_error: "Cost per unit is required" }).min(0, "Cost must be non-negative") : z.number().nullable().optional(),
  daily_step_goal: z.number().min(1000, "Step goal must be at least 1000").nullable().optional(),
  method_details: methodDetailsSchema.nullable().optional(),
}).superRefine((data, ctx) => {
  const methodRequiresQuitDate = ['cold-turkey', 'tapering', 'nrt'].includes(data.method || '');
  if (methodRequiresQuitDate && !data.quit_date) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Quit date is required for this method.",
      path: ["quit_date"],
    });
  }

  // Product type validation (conditionally required)
  if (data.method && data.method !== 'cold-turkey' && data.method !== 'tapering') { // Tapering might need product_type for unit labels, but not strictly for validation here
    if (!data.product_type || data.product_type.trim() === "") {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['product_type'],
        message: 'Product type is required for this method.',
      });
    }
  }
   // Product type is also needed for tapering to determine unit labels, though not strictly for saving the goal itself if not otherwise required.
   if (data.method === 'tapering' && (!data.product_type || data.product_type.trim() === "")) {
    ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['product_type'],
        message: 'Product type is required for tapering method to define units.',
    });
  }


  // NRT Method Validation
  if (data.method === 'nrt') {
    const nrtProducts = data.method_details?.nrtProducts;
    const nrtDosage = data.method_details?.nrtDosage;

    const hasSelectedProduct = nrtProducts && (
      nrtProducts.patch ||
      nrtProducts.gum ||
      nrtProducts.lozenge ||
      nrtProducts.inhaler ||
      nrtProducts.spray
    );
    const hasOtherProduct = nrtProducts && nrtProducts.other && nrtProducts.other.trim() !== '';
    const hasDosage = nrtDosage && nrtDosage.trim() !== '';

    if (!hasSelectedProduct && !hasOtherProduct && !hasDosage) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['method_details', 'nrtProducts'],
        message: "For NRT, select a product, specify 'Other', or provide dosage.",
      });
    }
    // No longer needed to check quit_date here as it's base-required
    // if (!data.quit_date) {
    //   ctx.addIssue({
    //     code: z.ZodIssueCode.custom,
    //     path: ['quit_date'],
    //     message: 'Quit date is required for NRT method.',
    //   });
    // }
  }

  // Gradual Reduction Method Validation
  if (data.method === 'gradual-reduction') {
    if (data.reduction_percent == null) { // Check for null or undefined explicitly
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['reduction_percent'],
        message: 'Reduction percentage is required for gradual reduction.',
      });
    }
    if (data.timeline_days == null) { // Check for null or undefined explicitly
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['timeline_days'],
        message: 'Timeline in days is required for gradual reduction.',
      });
    }
  }

  // Cold Turkey Method Validation
  // No longer needed as quit_date is base-required. Specific message might be useful but base validation handles it.
  // if (data.method === 'cold-turkey') {
  //   if (!data.quit_date) {
  //     ctx.addIssue({
  //       code: z.ZodIssueCode.custom,
  //       path: ['quit_date'],
  //       message: 'Quit date is required for cold turkey method.',
  //     });
  //   }
  // }

  // Tapering Method Validation
  if (data.method === 'tapering') {
    if (!data.method_details?.taperingSteps || data.method_details.taperingSteps.length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['method_details', 'taperingSteps', '_general'], // Use _general for array-level error
        message: 'At least one tapering step is required for the tapering method.',
      });
    } else {
      const steps = data.method_details.taperingSteps;
      for (let i = 0; i < steps.length; i++) {
        // Individual step validation (date and units) is handled by taperingStepSchema
        if (i > 0 && new Date(steps[i].date) <= new Date(steps[i-1].date)) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['method_details', 'taperingSteps', i, 'date'],
                message: 'Step dates must be in chronological order and not on the same day.',
            });
        }
        // Ensure units are decreasing or staying the same (allowing for plateaus)
        if (i > 0 && steps[i].units > steps[i-1].units) {
             ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['method_details', 'taperingSteps', i, 'units'],
                message: 'Units in a tapering schedule must decrease or stay the same, not increase.',
            });
        }
      }
    }
  }

  // Harm Reduction Method Validation
  if (data.method === 'harm-reduction') {
    if (!data.method_details?.reductionTarget?.trim()) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['method_details', 'reductionTarget'],
        message: 'Reduction target is required for harm reduction method.',
      });
    }
    if (!data.method_details?.trackingMethod?.trim()) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['method_details', 'trackingMethod'],
        message: 'Tracking method is required for harm reduction method.',
      });
    }
  }
});

export type FormattedValidationErrors = {
  [key: string]: string | FormattedValidationErrors | (string | FormattedValidationErrors)[];
};

// Helper function to format Zod errors into a nested object structure
export const getValidationErrors = (result: z.SafeParseError<unknown> | z.SafeParseSuccess<unknown>): FormattedValidationErrors => {
  if (result.success) {
    return {};
  }

  const formattedErrors: FormattedValidationErrors = {};
  for (const issue of result.error.issues) {
    let current: any = formattedErrors; // Using any for easier traversal
    const path = issue.path; // path is an array of strings or numbers (for array indices)

    for (let i = 0; i < path.length; i++) {
      const part = path[i];
      if (i === path.length - 1) {
        // Last part of the path, assign the message
        current[part] = issue.message;
      } else {
        // Not the last part, ensure a nested object/array exists
        // Check if next part is a number (array index) or string (object key)
        const nextPartIsNumber = typeof path[i+1] === 'number';
        if (!current[part]) {
          current[part] = nextPartIsNumber ? [] : {};
        }
        current = current[part];
      }
    }
  }
  return formattedErrors;
};

// Basic email format validation
export const validateEmail = (email: string): boolean => {
  // RFC 5322 compliant email regex that properly handles dots in local part
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  return emailRegex.test(email);
};
