import { differenceInDays, parseISO } from 'date-fns';
// Forcing a re-lint to check progressUtils.ts errors
import { Database } from "@/lib/database.types";

type UserGoal = Database['mission_fresh']['Tables']['user_goals']['Row'];

/**
 * Calculates the total money saved since the quit date based on user goals.
 *
 * @param goal The user's goal object.
 * @returns The total amount saved, or 0 if data is insufficient.
 */
export const calculateSavings = (
  goal: UserGoal | null | undefined,
): number => {
  // Check if necessary goal data is available
  if (!goal || !goal.quit_date || goal.cost_per_unit == null || goal.typical_daily_usage == null) {
    // console.warn('Cannot calculate savings: Missing quit_date, cost_per_unit, or typical_daily_usage in goal.');
    return 0;
  }

  // Ensure values are numbers
  const costPerUnit = Number(goal.cost_per_unit);
  const dailyUsage = Number(goal.typical_daily_usage);

  if (isNaN(costPerUnit) || isNaN(dailyUsage) || costPerUnit <= 0 || dailyUsage <= 0) {
    // console.warn('Cannot calculate savings: Invalid cost_per_unit or typical_daily_usage.');
    return 0;
  }

  try {
    const quitDate = parseISO(goal.quit_date);
    const today = new Date();

    // Calculate the number of full days since quitting
    // Add 1 to include the quit day itself if desired, or keep as is for full days passed
    const daysSinceQuit = differenceInDays(today, quitDate);

    // If quit date is in the future or today, no savings yet
    if (daysSinceQuit < 0) {
      return 0;
    }

    // Calculate total savings
    const dailyCost = costPerUnit * dailyUsage;
    const totalSavings = dailyCost * daysSinceQuit;

    // Return the total savings, ensuring it's not negative
    return Math.max(0, totalSavings);
  } catch (error) {
    console.error('Error parsing quit date for savings calculation:', error);
    return 0;
  }
};

/**
 * Calculates savings over different time periods (daily, weekly, etc.)
 * based on user goals.
 *
 * @param goal The user's goal object.
 * @returns An object containing savings for different periods, or null if data is insufficient.
 */
export const calculateSavingsPeriods = (
  goal: UserGoal | null | undefined,
): {
  daily: number;
  weekly: number;
  monthly: number;
  yearly: number;
  fiveYear: number;
} | null => {
  if (!goal || goal.cost_per_unit == null || goal.typical_daily_usage == null) {
    return null;
  }

  const costPerUnit = Number(goal.cost_per_unit);
  const dailyUsage = Number(goal.typical_daily_usage);

  if (isNaN(costPerUnit) || isNaN(dailyUsage) || costPerUnit <= 0 || dailyUsage <= 0) {
    return null;
  }

  const daily = costPerUnit * dailyUsage;
  const weekly = daily * 7;
  const monthly = daily * 30.44; // More accurate average days per month
  const yearly = daily * 365.25; // Account for leap years
  const fiveYear = yearly * 5;

  return {
    daily: Math.max(0, daily),
    weekly: Math.max(0, weekly),
    monthly: Math.max(0, monthly),
    yearly: Math.max(0, yearly),
    fiveYear: Math.max(0, fiveYear),
  };
}; 