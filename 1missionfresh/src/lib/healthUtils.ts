import { differenceInHours, parseISO } from 'date-fns';

// Define the structure for a health milestone
export interface HealthMilestone {
  timeframe: string;
  description: string;
  check: (hoursSinceQuit: number) => boolean; // Function to check if milestone is achieved
  achieved: boolean; // Flag indicating if the milestone has been achieved
}

// Define the standard health milestones
const milestonesDefinition: Omit<HealthMilestone, 'achieved'>[] = [
  { timeframe: "20 minutes", description: "Heart rate and blood pressure start to drop.", check: hours => hours >= (20 / 60) },
  { timeframe: "12 hours", description: "Carbon monoxide level in your blood drops to normal.", check: hours => hours >= 12 },
  { timeframe: "24 hours", description: "Risk of heart attack begins to decrease.", check: hours => hours >= 24 },
  { timeframe: "48 hours", description: "Nerve endings start regenerating; ability to smell and taste is enhanced.", check: hours => hours >= 48 },
  { timeframe: "72 hours", description: "Nicotine is eliminated from your body. Withdrawal symptoms may peak.", check: hours => hours >= 72 },
  { timeframe: "2 weeks", description: "Circulation improves; lung function increases.", check: hours => hours >= (14 * 24) },
  { timeframe: "1 month", description: "Coughing and shortness of breath decrease.", check: hours => hours >= (30 * 24) },
  { timeframe: "3 months", description: "Heart attack risk continues to drop. Lung function continues to improve.", check: hours => hours >= (3 * 30 * 24) }, // Approx
  { timeframe: "9 months", description: "Reduced coughing/shortness of breath. Cilia regrow, increasing ability to clean the lungs and reduce infection.", check: hours => hours >= (9 * 30 * 24) }, // Approx
  { timeframe: "1 year", description: "Risk of coronary heart disease is half that of a smoker's.", check: hours => hours >= (365 * 24) },
  { timeframe: "5 years", description: "Stroke risk is reduced to that of a nonsmoker 5-15 years after quitting.", check: hours => hours >= (5 * 365 * 24) },
  { timeframe: "10 years", description: "Risk of dying from lung cancer is about half that of a person who is still smoking.", check: hours => hours >= (10 * 365 * 24) },
  { timeframe: "15 years", description: "Risk of coronary heart disease is the same as a nonsmoker's.", check: hours => hours >= (15 * 365 * 24) },
];

/**
 * Calculates the achieved health milestones based on the quit date.
 *
 * @param quitDateString The quit date as an ISO string or Date object.
 * @returns An array of HealthMilestone objects with their achieved status.
 */
export const calculateHealthMilestones = (
  quitDateString: string | Date | null | undefined
): HealthMilestone[] => {
  if (!quitDateString) {
    return [];
  }

  try {
    const quitDate = typeof quitDateString === 'string' ? parseISO(quitDateString) : quitDateString;
    const now = new Date();
    
    // Ensure quitDate is valid
    if (isNaN(quitDate.getTime())) {
        console.error("Invalid quit date provided for health milestone calculation.");
        return [];
    }
    
    const hoursSinceQuit = differenceInHours(now, quitDate);

    // If quit date is in the future, no milestones achieved yet
    if (hoursSinceQuit < 0) {
      return milestonesDefinition.map(m => ({ ...m, achieved: false }));
    }

    // Determine achieved status for each milestone
    const calculatedMilestones = milestonesDefinition.map(milestone => ({
      ...milestone,
      achieved: milestone.check(hoursSinceQuit),
    }));

    return calculatedMilestones;
  } catch (error) {
    console.error("Error calculating health milestones:", error);
    return []; // Return empty array on error
  }
};

/**
 * Calculates the time regained by not engaging in the habit (e.g., smoking).
 * This is a simplified example and might need more specific inputs.
 *
 * @param goal The user's goal object containing quit_date.
 * @param timePerUnit Estimated time spent per unit of the habit (e.g., minutes per cigarette).
 * @param unitsPerDay Typical units used per day.
 * @returns The estimated time regained in minutes.
 */
export const calculateLifeRegained = (
  quitDateString: string | Date | null | undefined,
  timePerUnit: number | null | undefined = 5, // Default estimated time per unit (e.g., 5 mins per cigarette)
  unitsPerDay: number | null | undefined
): number => {
  if (!quitDateString || timePerUnit == null || unitsPerDay == null) {
    return 0;
  }

  const time = Number(timePerUnit);
  const usage = Number(unitsPerDay);

  if (isNaN(time) || isNaN(usage) || time <= 0 || usage <= 0) {
      return 0;
  }
  
  try {
    const quitDate = typeof quitDateString === 'string' ? parseISO(quitDateString) : quitDateString;
    const now = new Date();

    if (isNaN(quitDate.getTime())) return 0;

    const hoursSinceQuit = differenceInHours(now, quitDate);

    if (hoursSinceQuit < 0) {
      return 0;
    }

    const daysSinceQuit = hoursSinceQuit / 24;
    const totalUnitsAvoided = daysSinceQuit * usage;
    const totalMinutesRegained = totalUnitsAvoided * time;

    return Math.max(0, Math.floor(totalMinutesRegained));
  } catch (error) {
    console.error("Error calculating life regained:", error);
    return 0;
  }
}; 