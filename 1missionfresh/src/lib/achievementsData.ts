import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, MessageSquare, Flame, Target } from 'lucide-react'; // Added Target
import { Database } from '@/lib/database.types'; // Import Database type

// Define types based on database.types.ts
// Fucking refresh this goddamn file
type NicotineLog = Database['mission_fresh']['Tables']['nicotine_logs']['Row'];

// Placeholder types for additional data - ideally these would come from database.types.ts
// and reflect actual table structures
export interface CravingLog {
  id: string;
  user_id: string;
  timestamp: string;
  intensity: number;
  trigger?: string | null;
  coping_mechanism_used?: string | null;
  used_coping_mechanism?: boolean; // Assuming this field indicates if a coping mechanism was used
  notes?: string | null;
}

export interface ToolUsage {
  id: string;
  user_id: string;
  tool_type: string; // e.g., 'breathingExercise', 'guidedMeditation'
  timestamp: string;
  duration_minutes?: number | null;
}

// Represents a single community action event
export interface IndividualCommunityAction {
  id: string;
  user_id: string;
  action_type: 'post' | 'comment' | 'like';
  timestamp: string;
  target_id?: string | null; // e.g., post_id for a comment or like
}

// Represents a summary of community actions
export interface CommunityActionSummary {
  posts: number;
  comments: number;
  likes: number;
}

export interface LoginStreak {
  user_id: string;
  current_streak: number;
  longest_streak: number;
  last_login_date: string;
}


export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  check: (
    daysAfresh: number,
    logs: NicotineLog[],
    cravings?: CravingLog[],
    toolsUsed?: ToolUsage[],
    communityActions?: CommunityActionSummary, // Use the new summary type
    loginStreak?: LoginStreak
  ) => boolean;
  category: 'milestone' | 'logging' | 'engagement' | 'streak'; // Added category
  points?: number; // Optional points for gamification
}

// Define Achievements
export const achievementsList: Achievement[] = [
  {
    id: 'c4a3e2b1-b723-4b38-93e3-29e5a8a628a3',
    title: "First 24 Hours!",
    description: "You made it through the first day nicotine-free.",
    icon: Award,
    check: (days) => days >= 1,
    category: 'milestone',
    points: 10,
  },
  {
    id: 'f8b3e5a3-9b5a-4e1a-8e4a-3e2c1d0b5e4a',
    title: "72 Hours Strong",
    description: "Nicotine is likely out of your system.",
    icon: Award,
    check: (days) => days >= 3,
    category: 'milestone',
    points: 20,
  },
  {
    id: 'a2b4e6a7-3c1d-4f2a-9b5a-3e2c1d0b5e4a',
    title: "One Week Afresh",
    description: "Completed your first full week!",
    icon: Award,
    check: (days) => days >= 7,
    category: 'milestone',
    points: 50,
  },
  {
    id: 'b3c5d7a8-4e2a-4f3a-8b4a-3e2c1d0b5e4a',
    title: "Two Week Milestone",
    description: "Consistency is building.",
    icon: Award,
    check: (days) => days >= 14,
    category: 'milestone',
    points: 75,
  },
  {
    id: 'c4d6e8a9-5f3a-4e4a-9a3a-3e2c1d0b5e4a',
    title: "One Month Afresh!",
    description: "A major milestone achieved!",
    icon: Award,
    check: (days) => days >= 30,
    category: 'milestone',
    points: 100,
  },
  {
    id: 'd5e7f9ab-6f4a-4d5a-8b2a-3e2c1d0b5e4a',
    title: "Three Months Strong",
    description: "Cravings are likely much less frequent.",
    icon: Award,
    check: (days) => days >= 90,
    category: 'milestone',
    points: 150,
  },
  {
    id: 'e6f8a0bc-7f5a-4c6a-9a1a-3e2c1d0b5e4a',
    title: "Half Year Afresh!",
    description: "Incredible progress!",
    icon: Award,
    check: (days) => days >= 180,
    category: 'milestone',
    points: 200,
  },
  {
    id: 'f7a9b1cd-8f6a-4b7a-8a0a-3e2c1d0b5e4a',
    title: "One Year Afresh!",
    description: "Congratulations on a full year!",
    icon: Award,
    check: (days) => days >= 365,
    category: 'milestone',
    points: 500,
  },
  {
    id: 'a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6', // This is the actual UUID from achievement_definitions
    title: "Goal Setter", // This MUST match the 'name' in achievement_definitions
    description: "You've set your first goal!",
    icon: Target, // Assuming Target is a valid Icon component
    // The check for 'Goal Setter' should be more specific, e.g., based on user_goals existing.
    // For now, to fix the UUID error, we ensure this definition is correct.
    // A simple check: (days, logs, cravings, tools, community, streak, userGoalExists) => userGoalExists
    // This would require adding a new parameter to the check function signature.
    // For now, let's assume it's triggered by an event elsewhere or a simpler check.
    check: () => true, // Placeholder: This achievement is likely awarded programmatically after goal creation.
    category: 'milestone',
    points: 10,
  },
  {
    id: 'b2c3d4e5-f6a7-b8c9-d0e1-f2a3b4c5d6e7',
    title: "First Log Entry",
    description: "You started tracking your journey.",
    icon: CheckCircle,
    check: (_, logs) => Array.isArray(logs) && logs.length >= 1,
    category: 'logging',
    points: 5,
  },
  {
    id: 'c3d4e5f6-a7b8-c9d0-e1f2-a3b4c5d6e7f8',
    title: "Consistent Logger",
    description: "Logged entries for 7 different days.",
    icon: CheckCircle,
    check: (_, logs) => {
      if (!Array.isArray(logs)) return false;
      return new Set(logs.map(l => l.date?.split('T')[0])).size >= 7; // Ensure l.date is checked
    },
    category: 'logging',
    points: 25,
  },
  {
    id: 'd4e5f6a7-b8c9-d0e1-f2a3-b4c5d6e7f8a9',
    title: "Dedicated Tracker",
    description: "Logged entries for 30 different days.",
    icon: CheckCircle,
    check: (_, logs) => {
      if (!Array.isArray(logs)) return false;
      return new Set(logs.map(l => l.date?.split('T')[0])).size >= 30; // Ensure l.date is checked
    },
    category: 'logging',
    points: 75,
  },
  {
    id: 'af32f214-edba-4990-a665-4f5b69d19801',
    title: "Craving Conqueror",
    description: "Successfully used a coping mechanism during a craving.",
    icon: ShieldCheck,
    check: (days, logs, cravings) => Array.isArray(cravings) && cravings.some(c => c.used_coping_mechanism),
    category: 'engagement',
    points: 15
  },
  {
    id: 'e3164259-7eb7-4c14-a101-f9de0fa41e3c',
    title: "Tool Explorer",
    description: "Used 3 different quit tools.",
    icon: Wrench,
    check: (days, logs, cravings, toolsUsed) => {
      if (!Array.isArray(toolsUsed)) return false;
      return new Set(toolsUsed.map(t => t.tool_type)).size >= 3;
    },
    category: 'engagement',
    points: 20
  },
  {
    id: 'e1ee5369-ad78-4612-b835-43836e59ffa8',
    title: "Community Builder",
    description: "Made your first post in the community.",
    icon: MessageSquare,
    check: (days, logs, cravings, toolsUsed, communityActions) => !!communityActions && communityActions.posts > 0,
    category: 'engagement',
    points: 10
  },
  {
    id: 's7d0s0a0-0000-0000-0000-000000000007', // Unique ID for 7-Day Streak
    title: "7-Day Streak",
    description: "Logged in for 7 consecutive days.",
    icon: Flame,
    check: (days, logs, cravings, toolsUsed, communityActions, loginStreak) => !!loginStreak && loginStreak.current_streak >= 7,
    category: 'streak',
    points: 30
  },
];
