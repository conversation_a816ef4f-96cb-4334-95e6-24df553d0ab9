import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Function to format duration in days
export function formatDuration(days: number): string {
  if (days === 0) {
    return '0 Days';
  } else if (days === 1) {
    return '1 Day';
  } else {
    return `${days} Days`;
  }
}

// Function to format currency (assuming USD for now)
export function formatCurrency(amount: number): string {
  return `$${amount.toFixed(2)}`;
}
