import { User } from '@supabase/supabase-js';

export const getDisplayName = (user: User | null): string => {
  if (!user) return 'Guest';
  if (user.user_metadata?.name && user.user_metadata.name.length > 1) {
    return user.user_metadata.name;
  }
  const email = user.email || '';
  const emailPrefix = email.split('@')[0];

  const excludeWords = ['work', 'dev', 'test', 'admin', 'info', 'contact', 'support', 'hello', 'mail'];
  const nameParts = emailPrefix
    .split(/[._-]/)
    .filter(part => !excludeWords.includes(part.toLowerCase()) && isNaN(parseInt(part)))
    .map(part => part.charAt(0).toUpperCase() + part.slice(1));
    
  const name = nameParts.slice(0, 2).join(' ');
  return name.length > 0 ? name : email;
};

export const getInitials = (user: User | null): string => {
  if (!user) return 'G';
  const displayName = getDisplayName(user);
  const nameParts = displayName.split(' ').filter(Boolean);
  if (nameParts.length > 1) {
    return (nameParts[0][0] + nameParts[nameParts.length - 1][0]).toUpperCase();
  }
  if (displayName.length > 1) {
    return displayName.substring(0, 2).toUpperCase();
  }
  return displayName.toUpperCase() || 'G';
};
