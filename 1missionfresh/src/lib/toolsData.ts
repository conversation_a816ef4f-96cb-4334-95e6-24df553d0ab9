export const guidedExerciseSteps = {
  mindfulness: [
    "Find a comfortable seated position.",
    "Gently close your eyes or soften your gaze.",
    "Bring your attention to your breath. Notice the sensation of air entering and leaving your nostrils or the rise and fall of your abdomen.",
    "When your mind wanders, which it will, gently guide your attention back to your breath without judgment.",
    "Continue for a few minutes, simply observing your breath.",
    "When you are ready, gently open your eyes.",
  ],
  // Add steps for other guided exercises here
  urgeSurfing: [
    "Find a comfortable position, either sitting or lying down.",
    "Close your eyes or soften your gaze.",
    "Bring your attention to your breath. Notice the sensations of inhaling and exhaling.",
    "Now, bring to mind the craving you are experiencing. Notice where you feel it in your body.",
    "Observe the craving without judgment. Imagine it like a wave building in the ocean.",
    "Notice the intensity of the craving. Does it stay the same, increase, or decrease?",
    "Continue to breathe and observe the wave of craving. It will eventually crest and subside.",
    "Ride the wave until it passes. You don't need to act on it.",
    "When the craving has subsided, gently bring your attention back to your breath.",
    "Slowly open your eyes when you are ready.",
  ],
  sensoryGrounding: [
    "Look around and name 5 things you can see.",
    "Identify 4 things you can touch and feel.",
    "Listen for 3 things you can hear.",
    "Notice 2 things you can smell.",
    "Identify 1 thing you can taste.",
    "Take a deep breath and notice your body in the present moment.",
  ],
  mentalChallenges: [
    "Count backwards from 100 by 7s.",
    "Name all the objects of a certain color you can see.",
    "Recite the alphabet backwards.",
    "Think of as many words as you can that start with a specific letter.",
    "Try to remember the details of a pleasant memory.",
  ],
};

export const quickJournalingPrompts = [
  "What triggered this craving?",
  "How does your body feel right now?",
  "What would happen if you don't give in to the craving?",
  "One reason you're proud of your progress"
];

interface BreathingExercise {
  title: string;
  description: string;
  duration: string;
  difficulty: "easy" | "moderate" | "challenging";
  tags?: string[];
}

export const breathingExercises: Record<string, BreathingExercise> = {
  pursedLip: {
    title: "Pursed-Lip Breathing",
    description: "Helps slow down your breathing, making each breath more effective.",
    duration: "2-5 minutes",
    difficulty: "easy",
    tags: ["breathing", "calming", "stress"],
  },
  box: {
    title: "Box Breathing",
    description: "A simple technique to regulate your breath and calm your nervous system.",
    duration: "5-8 minutes",
    difficulty: "easy",
    tags: ["breathing", "focus", "calming"],
  },
  fourSevenEight: {
    title: "4-7-8 Breathing",
    description: "A relaxing breathing pattern that helps with anxiety and promotes sleep.",
    duration: "5-8 minutes",
    difficulty: "easy",
    tags: ["breathing", "relaxing", "sleep"],
  },
  alternateNostril: {
    title: "Alternate Nostril Breathing",
    description: "Balances the left and right sides of the brain and calms the mind.",
    duration: "8-12 minutes",
    difficulty: "moderate",
    tags: ["breathing", "balancing", "calming"],
  },
  belly: {
    title: "Belly Breathing",
    description: "Deep breathing that engages the diaphragm for maximum lung capacity.",
    duration: "8-12 minutes",
    difficulty: "easy",
    tags: ["breathing", "deep breathing", "relaxing"],
  },
  power: {
    title: "Power Breathing",
    description: "Energizing breaths to increase alertness and enhance vitality.",
    duration: "8-12 minutes",
    difficulty: "moderate",
    tags: ["breathing", "energizing", "focus"],
  },
};

interface FocusExercise {
  title: string;
  description: string;
  duration: string;
  difficulty: "easy" | "moderate" | "challenging";
  tags?: string[];
}

export const focusExercises: Record<string, FocusExercise> = {
  pomodoro: {
    title: "Pomodoro Technique",
    description: "Work in focused 25-minute intervals with 5-minute breaks",
    duration: "25 minutes",
    difficulty: "easy",
    tags: ["focus", "productivity", "time management"],
  },
  deepWork: {
    title: "Deep Work Block",
    description: "Extended 90-minute focus session for complex tasks",
    duration: "90 minutes",
    difficulty: "challenging",
    tags: ["focus", "deep work", "concentration"],
  },
  quickFocus: {
    title: "Quick Focus",
    description: "Short 15-minute concentration burst",
    duration: "15 minutes",
    difficulty: "easy",
    tags: ["focus", "quick session", "productivity"],
  },
  ultraFocus: {
    title: "Ultra Focus",
    description: "Extended 2-hour deep concentration session",
    duration: "120 minutes",
    difficulty: "challenging",
    tags: ["focus", "extended session", "deep work"],
  },
  mindfulFocus: {
    title: "Mindful Focus",
    description: "Combine mindfulness with focused attention training",
    duration: "30 minutes",
    difficulty: "moderate",
    tags: ["focus", "mindfulness", "attention"],
  },
  flowState: {
    title: "Flow State",
    description: "Enter a state of effortless concentration and peak performance",
    duration: "60 minutes",
    difficulty: "moderate",
    tags: ["focus", "flow", "performance"],
  },
};

interface EnergyExercise {
  title: string;
  description: string;
  duration: string;
  difficulty: "easy" | "moderate" | "challenging";
  tags?: string[];
}

export const energyExercises: Record<string, EnergyExercise> = {
  powerBreathing: {
    title: "Power Breathing",
    description: "Energizing breaths to increase alertness and boost vitality",
    duration: "5 minutes",
    difficulty: "easy",
    tags: ["breathing", "energy", "alertness"],
  },
  quickStretch: {
    title: "Quick Stretch",
    description: "Simple stretches to get your blood flowing and energize your body",
    duration: "7 minutes",
    difficulty: "easy",
    tags: ["stretching", "movement", "circulation"],
  },
  eyeExercise: {
    title: "Eye Exercises",
    description: "Simple exercises to relieve eye strain and improve focus",
    duration: "3 minutes",
    difficulty: "easy",
    tags: ["eyes", "focus", "strain relief"],
  },
  energizingBreath: {
    title: "Energizing Breath",
    description: "Dynamic breathing pattern to boost energy and mental clarity",
    duration: "8 minutes",
    difficulty: "moderate",
    tags: ["breathing", "energy", "mental clarity"],
  },
  morningWakeUp: {
    title: "Morning Wake-Up",
    description: "Perfect routine to kickstart your day with natural energy",
    duration: "10 minutes",
    difficulty: "moderate",
    tags: ["morning", "routine", "wake-up"],
  },
  midDayBoost: {
    title: "Mid-Day Boost",
    description: "Quick energy reset for afternoon slumps and mental fatigue",
    duration: "6 minutes",
    difficulty: "easy",
    tags: ["afternoon", "boost", "reset"],
  },
};

interface MoodExercise {
  title: string;
  description: string;
  duration: string;
  difficulty: "easy" | "moderate" | "challenging";
  tags?: string[];
}

export const moodExercises: Record<string, MoodExercise> = {
  boxBreathing: {
    title: "Box Breathing",
    description: "Structured breathing for immediate calm and anxiety relief",
    duration: "5 minutes",
    difficulty: "easy",
    tags: ["breathing", "anxiety", "calm"],
  },
  fourSevenEight: {
    title: "4-7-8 Breathing",
    description: "Deep relaxation technique for stress and sleep",
    duration: "8 minutes",
    difficulty: "easy",
    tags: ["breathing", "relaxation", "sleep"],
  },
  alternateNostril: {
    title: "Alternate Nostril Breathing",
    description: "Balance your nervous system and reduce stress",
    duration: "10 minutes",
    difficulty: "moderate",
    tags: ["breathing", "balance", "stress"],
  },
  bellyBreathing: {
    title: "Belly Breathing",
    description: "Gentle diaphragmatic breathing for emotional balance",
    duration: "6 minutes",
    difficulty: "easy",
    tags: ["breathing", "emotional", "gentle"],
  },
  pursedLip: {
    title: "Pursed Lip Breathing",
    description: "Controlled breathing to release tension and anger",
    duration: "4 minutes",
    difficulty: "easy",
    tags: ["breathing", "tension", "control"],
  },
  quickJournaling: {
    title: "Quick Journaling",
    description: "Express and process your emotions through writing",
    duration: "7 minutes",
    difficulty: "easy",
    tags: ["journaling", "emotions", "expression"],
  },
};

// You can add other tool-related data here as well
