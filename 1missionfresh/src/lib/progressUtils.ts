// Placeholder for progress calculations

import { differenceInDays, parseISO, format, formatDistanceToNow, startOfDay, endOfDay, isWithinInterval } from 'date-fns';
import { Database } from "@/lib/database.types"; // Remove Tables import to fix infinite recursion
import { calculateSavings } from './savingsUtils';
import { calculateLifeRegained } from './healthUtils';
import { DailyHealthSummaryEntry } from '@/services/progressService'; // IMPORT THE GODDAMN TYPE

// Define types from database
type UserGoal = Database['public']['Tables']['user_goals']['Row'];
// Removed estimated_daily_cost, estimated_daily_units, is_active as they are not in the base DB type
// and calculateProgressStats will use cost_per_unit and typical_daily_usage directly.

// Fix infinite recursion by using direct database types instead of problematic Tables<>
type NicotineDailyLog = Database['mission_fresh']['Tables']['nicotine_logs']['Row'];
type DailyCheckIn = Database['mission_fresh']['Tables']['health_metrics']['Row'];
type CravingLog = Database['mission_fresh']['Tables']['craving_logs']['Row'];

// Define structure for combined log data input - Updated to match AllLogEntries
interface LogData {
  nicotine_use_logs: NicotineDailyLog[]; // Updated to match AllLogEntries
  daily_check_ins: DailyCheckIn[];
  craving_logs: CravingLog[];
  journal_entries?: any[]; // Optional, not used in calculations
}

// Define structure for chart data points
interface ChartDataPoint {
  date: string; // Format: 'MMM dd'
  cravings: number;
  nicotineCount?: number; // Optional: Count of usage logs
  mood: number | null;
  energy: number | null;
  focus: number | null;
}

// Define structure for trigger analysis
interface TriggerData {
  triggers: { trigger: string; count: number }[];
  coping: { mechanism: string; count: number }[];
}

// Define structure for summary statistics
interface SummaryStats {
  daysAfresh: number;
  moneySaved: number;
  lifeRegained: number;
  cigarettesAvoided: number;
  nicotineFreeCount: number;
  lastCraving: {
    time: string;
    intensity: number;
  } | null;
  recentCravings: number;
  avgMood: number | null;
  avgEnergy: number | null;
  avgFocus: number | null;
  totalSteps: number;
  totalMeditationMinutes: number;
}

// Define structure for daily savings data
interface DailySavingsData {
  date: string; // Format: 'MMM dd'
  savings: number;
}

// Define the overall return type for the function
export interface ProgressStats {
  chartData: ChartDataPoint[];
  triggerData: TriggerData;
  summaryStats: SummaryStats;
  dailySavings: DailySavingsData[];
}

/**
 * Calculates various progress statistics based on user goal and aggregated daily health summary.
 *
 * @param goal The user's active goal.
 * @param logs An object containing arrays of nicotine logs and daily check-ins for the relevant period (still needed for trigger data).
 * @param dailyHealthSummary Aggregated daily health data.
 * @param timeframeDays The number of days in the selected timeframe.
 * @returns An object containing chart data, trigger analysis, summary statistics, and daily savings.
 */
export const calculateProgressStats = (
  goal: UserGoal | null | undefined,
  logs: LogData | null | undefined, // Keep logs for trigger data
  dailyHealthSummary: DailyHealthSummaryEntry[] | null | undefined, // NO MORE FUCKING ANY[]
  timeframeDays: number
): ProgressStats => {
  // Default structure in case of missing data
  const defaultStats: ProgressStats = {
    chartData: [],
    triggerData: { triggers: [], coping: [] },
    summaryStats: {
      daysAfresh: 0,
      moneySaved: 0,
      lifeRegained: 0,
      cigarettesAvoided: 0,
      nicotineFreeCount: 0,
      lastCraving: null,
      recentCravings: 0,
      avgMood: null,
      avgEnergy: null,
      avgFocus: null,
      totalSteps: 0,
      totalMeditationMinutes: 0,
    },
    dailySavings: [], // Initialize dailySavings
  };

  if (!goal) {
    return defaultStats;
  }

  // Handle case where logs or dailyHealthSummary might be missing
  const safeLog = logs || { nicotine_use_logs: [], daily_check_ins: [], craving_logs: [], journal_entries: [] };
  const safeDailyHealthSummary = dailyHealthSummary || [];

  const { nicotine_use_logs, daily_check_ins, craving_logs } = safeLog;

  // --- Basic Stats ---
  let daysAfresh = 0;
  if (goal.quit_date) {
      try {
          const quitDate = parseISO(goal.quit_date);
          const today = new Date();
          const rawDifference = differenceInDays(today, quitDate);
          
          // DEBUG: Log the calculation details
          console.log('🔍 DASHBOARD DEBUG - Date calculations:');
          console.log('Quit date string:', goal.quit_date);
          console.log('Parsed quit date:', quitDate);
          console.log('Today:', today);
          console.log('Raw difference (today - quit):', rawDifference);
          console.log('Days afresh (max 0):', Math.max(0, rawDifference));
          
          // Only show positive days if quit date has passed
          if (today >= quitDate) {
              daysAfresh = Math.max(0, rawDifference);
          } else {
              daysAfresh = 0; // Future quit date = 0 progress
              console.log('🔍 QUIT DATE IS IN FUTURE - Setting days to 0');
          }
      } catch (e) {
          console.error("Error parsing quit_date for daysAfresh:", e);
      }
  }

  // PRODUCTION READY: Calculate total money saved from REAL user data
  let moneySaved = 0;
  
  // PRODUCTION READY: Use ONLY REAL DATA - ZERO TOLERANCE FOR FAKE FALLBACKS
  // NO HARDCODED VALUES - IF NO GOAL DATA, RETURN ZERO
  const costPerUnit = goal.cost_per_unit ? parseFloat(String(goal.cost_per_unit)) : 0;
  const dailyUsage = goal.typical_daily_usage ? parseFloat(String(goal.typical_daily_usage)) : 0;
  
  console.log('🔍 DEBUGGING FAKE DATA - ACTUAL VALUES FROM GOAL:', {
    goal_cost_per_unit: goal.cost_per_unit,
    goal_typical_daily_usage: goal.typical_daily_usage,
    parsed_costPerUnit: costPerUnit,
    parsed_dailyUsage: dailyUsage,
    daysAfresh: daysAfresh,
    safeDailyHealthSummary_length: safeDailyHealthSummary.length,
    expected_money_saved: daysAfresh * parseFloat(String(goal.typical_daily_usage)) * parseFloat(String(goal.cost_per_unit))
  });
  
  // PRODUCTION READY: Calculate money saved - SIMPLIFIED LOGIC FOR QUIT SMOKING APP
  if (costPerUnit > 0 && dailyUsage > 0 && daysAfresh > 0) {
    // For a quit smoking app, we assume complete abstinence since quit date
    // This is the most appropriate calculation for this use case
    moneySaved = daysAfresh * dailyUsage * costPerUnit;
    console.log('🔍 MONEY SAVED CALCULATION - PRODUCTION READY:', {
      daysAfresh,
      dailyUsage,
      costPerUnit,
      calculation: `${daysAfresh} × ${dailyUsage} × ${costPerUnit}`,
      totalSaved: moneySaved
    });
  } else {
    console.log('🔍 MONEY SAVED CALCULATION - MISSING DATA:', {
      daysAfresh,
      dailyUsage,
      costPerUnit,
      reason: 'Missing required data for calculation'
    });
  }
  



  // PRODUCTION READY: Calculate life regained using CONSISTENT medical standard (11 minutes per cigarette)
  // FIXED FLAW #6 - Use same calculation as Dashboard for consistency
  let lifeRegained = 0;
  if (dailyUsage > 0 && daysAfresh > 0) {
    // Use same calculation as Dashboard: total cigarettes avoided * 11 minutes (medical standard)
    // This ensures consistent values across all pages
    const totalCigarettesAvoided = daysAfresh * dailyUsage;
    lifeRegained = totalCigarettesAvoided * 11; // Medical standard: 11 minutes per cigarette
    console.log('🔍 LIFE REGAINED CALCULATION - FIXED CONSISTENCY:', {
      daysAfresh,
      dailyUsage,
      totalCigarettesAvoided,
      calculation: `${totalCigarettesAvoided} × 11 minutes`,
      lifeRegained
    });
  }


  // --- Calculate Daily Savings ---
  const dailySavings: DailySavingsData[] = [];
  // PRODUCTION READY: Use ONLY real data - no fake fallback calculations
  if (costPerUnit > 0 && dailyUsage > 0 && safeDailyHealthSummary.length > 0) {
    safeDailyHealthSummary.forEach(day => {
        const actualUnitsUsed = day.nicotineUnits ?? 0;
        const unitsAvoided = Math.max(0, dailyUsage - actualUnitsUsed);
        const savings = unitsAvoided * costPerUnit;
        dailySavings.push({
            date: format(parseISO(day.date), 'MMM dd'),
            savings: savings,
        });
    });
  }


  // --- Log Processing (for Chart Data and Trigger Data) ---
  const dateMap = new Map<string, ChartDataPoint>(); // Key: 'YYYY-MM-DD'
  const triggerCounts: { [key: string]: number } = {};
  const copingCounts: { [key: string]: number } = {};

  // Use dailyHealthSummary for chart data
  const chartData = safeDailyHealthSummary.map(day => ({
    date: format(parseISO(day.date), 'MMM dd'), // Format date for display
    cravings: day.avgCravingIntensity ?? 0, // Use average intensity for chart, default to 0
    nicotineCount: day.nicotineUnits ?? 0, // Use nicotine units as count, default to 0
    mood: day.mood ?? null,
    energy: day.energyLevel ?? null,
    focus: day.focusLevel ?? null,
  }));

  // Process craving_logs for trigger and coping mechanism counts - with proper null checks
  if (craving_logs && Array.isArray(craving_logs)) {
    craving_logs.forEach((log: CravingLog) => { 
      try {
        // Tally triggers from craving_logs
        if (log.trigger && typeof log.trigger === 'string' && log.trigger.trim() !== "") {
          triggerCounts[log.trigger.trim()] = (triggerCounts[log.trigger.trim()] || 0) + 1;
        }

        // Tally coping mechanisms from craving_logs
        if (log.coping_mechanism && typeof log.coping_mechanism === 'string' && log.coping_mechanism.trim() !== "") {
          copingCounts[log.coping_mechanism.trim()] = (copingCounts[log.coping_mechanism.trim()] || 0) + 1;
        }
      } catch (e) {
        console.error("Error processing craving log for triggers/coping:", log, e);
      }
    });
  }


  // --- PRODUCTION READY: Calculate Nicotine Free Count from real data only ---
  // Calculate nicotine free days based ONLY on real dailyHealthSummary data
  let nicotineFreeCount = 0;
  if (safeDailyHealthSummary.length > 0) {
    nicotineFreeCount = safeDailyHealthSummary.filter(day => (day.nicotineUnits ?? 0) === 0).length;
  }
  // NO FALLBACK - if no data, count stays 0


  // --- Final Calculations & Formatting ---

  // Chart data is already prepared from dailyHealthSummary

  // Format trigger data
  const triggerData: TriggerData = {
      triggers: Object.entries(triggerCounts)
        .map(([trigger, count]) => ({ trigger, count }))
        .sort((a, b) => b.count - a.count),
      coping: Object.entries(copingCounts)
        .map(([mechanism, count]) => ({ mechanism, count }))
        .sort((a, b) => b.count - a.count),
  };

  // Calculate last craving info from raw craving logs (still needed) - with proper null checks
  const cravingLogsTyped: CravingLog[] = craving_logs || []; // Ensure type and handle null
  const sortedCravingLogs = cravingLogsTyped
    .filter(log => log.intensity != null && log.intensity > 0)
    .sort((a, b) => parseISO(b.created_at!).getTime() - parseISO(a.created_at!).getTime());

  const lastCravingLog: CravingLog | null = sortedCravingLogs.length > 0 ? sortedCravingLogs[0] : null;
  const lastCraving = lastCravingLog ? {
      time: formatDistanceToNow(parseISO(lastCravingLog.created_at!), { addSuffix: true }),
      intensity: lastCravingLog.intensity ?? 0 
  } : null;
  const recentCravings = sortedCravingLogs.length;

  // Calculate average mood, energy, focus from dailyHealthSummary
  const avgMood = safeDailyHealthSummary.length > 0 ? safeDailyHealthSummary.reduce((sum, day) => sum + (day.mood ?? 0), 0) / safeDailyHealthSummary.length : null;
  const avgEnergy = safeDailyHealthSummary.length > 0 ? safeDailyHealthSummary.reduce((sum, day) => sum + (day.energyLevel ?? 0), 0) / safeDailyHealthSummary.length : null;
  const avgFocus = safeDailyHealthSummary.length > 0 ? safeDailyHealthSummary.reduce((sum, day) => sum + (day.focusLevel ?? 0), 0) / safeDailyHealthSummary.length : null;

  // Sum total steps and meditation minutes from the dailyHealthSummary
  const totalSteps = safeDailyHealthSummary.reduce((sum, day) => sum + (day.totalSteps ?? 0), 0);
  const totalMeditationMinutes = safeDailyHealthSummary.reduce((sum, day) => sum + (day.totalMeditationMinutes ?? 0), 0);

  // PRODUCTION READY: Calculate cigarettes avoided - SIMPLIFIED LOGIC FOR QUIT SMOKING APP
  let cigarettesAvoided = 0;
  if (dailyUsage > 0 && daysAfresh > 0) {
    // For a quit smoking app, we assume complete abstinence since quit date
    cigarettesAvoided = daysAfresh * dailyUsage;
    console.log('🔍 CIGARETTES AVOIDED CALCULATION - PRODUCTION READY:', {
      daysAfresh,
      dailyUsage,
      calculation: `${daysAfresh} × ${dailyUsage}`,
      totalAvoided: cigarettesAvoided
    });
  } else {
    console.log('🔍 CIGARETTES AVOIDED CALCULATION - MISSING DATA:', {
      daysAfresh,
      dailyUsage,
      reason: 'Missing required data for calculation'
    });
  }
  


  // --- Assemble Final Result ---
  return {
    chartData,
    triggerData,
    summaryStats: {
      daysAfresh,
      moneySaved, // This is total money saved, calculated from total units avoided
      lifeRegained,
      cigarettesAvoided,
      nicotineFreeCount,
      lastCraving,
      recentCravings,
      // Use Math.round for cleaner display, handle NaN/null by defaulting to null
      avgMood: avgMood === null || isNaN(avgMood) ? null : Math.round(avgMood * 10) / 10,
      avgEnergy: avgEnergy === null || isNaN(avgEnergy) ? null : Math.round(avgEnergy * 10) / 10,
      avgFocus: avgFocus === null || isNaN(avgFocus) ? null : Math.round(avgFocus * 10) / 10,
      totalSteps,
      totalMeditationMinutes,
    },
    dailySavings, // Include dailySavings
  };
};
