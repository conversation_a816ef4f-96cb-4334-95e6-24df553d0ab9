/*
  ======================================================================================
  MISSION FRESH - COLORS AND FONTS ONLY
  Only color and font character styles - NO sizing, spacing, or layout
  ======================================================================================
*/

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Border Radius */
    --radius: 1.5rem;

    /* Layout Variables */
    --sidebar-width: 280px;
    --sidebar-width-collapsed: 64px;

    /* Base Colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* Primary Brand Colors - ONLY ONE GREEN ALLOWED */
    --primary: 160 84.2% 39.4%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 160 84.2% 39.4%;
    --primary-active: 160 84.2% 39.4%;
    --primary-subtle: 160 84.2% 95%;
    --primary-muted: 160 84.2% 39.4%;
    
    /* Secondary Colors */
    --secondary: 0 0% 100%;
    --secondary-foreground: 222.2 84% 4.9%;
    --secondary-hover: 214.3 31.8% 85%;
    --secondary-active: 214.3 31.8% 80%;
    
    /* Muted Colors */
    --muted: 0 0% 100%;
    --muted-foreground: 215.4 16.3% 35%;
    --muted-subtle: 160 84.2% 39.4%;
    
    /* Accent Colors */
    --accent: 0 0% 100%;
    --accent-foreground: 222.2 84% 4.9%;
    --accent-hover: 214.3 31.8% 95%;
    --accent-active: 214.3 31.8% 90%;
    
    /* Status Colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --success: 160 84.2% 39.4%;
    --success-foreground: 0 0% 100%;
    --warning: 35 85% 55%;
    --warning-foreground: 0 0% 100%;
    --info: 212 85% 60%;
    --info-foreground: 0 0% 100%;
    
    /* UI Element Colors */
    --border: 214.3 31.8% 91.4%;
    --input: 0 0% 100%;
    --ring: 160 84.2% 39.4%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Chart Colors - ALL SAME BRAND GREEN */
    --chart-1: 160 84.2% 39.4%;
    --chart-2: 160 84.2% 39.4%;
    --chart-3: 160 84.2% 39.4%;
    --chart-4: 160 84.2% 39.4%;
    --chart-5: 160 84.2% 39.4%;

    /* Success color should match primary */
    --success: 160 84.2% 39.4%;
    --success-foreground: 0 0% 100%;
  }

  /* Font Character Styles Only */
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: Inter, system-ui, -apple-system, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11", "ss01", "ss03";
  }

  /* Header Background Color Only */
  nav, header {
    background-color: hsl(var(--background));
  }

  /* UNIFIED BUTTON SYSTEM - ONLY BRAND GREEN ALLOWED */
  .btn-primary {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    border: none;
    border-radius: 24px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.2s ease;
  }

  .btn-primary:hover {
    background-color: hsl(var(--primary));
    opacity: 0.9;
    transform: translateY(-1px);
  }

  .btn-primary:disabled {
    background-color: hsl(var(--primary));
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  /* UNIFIED CARD SYSTEM */
  .card-unified {
    background: white;
    border: 1px solid hsl(var(--border));
    border-radius: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    padding: 24px;
    transition: all 0.2s ease;
  }

  .card-unified:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
  }

  /* UNIFIED TYPOGRAPHY */
  .heading-primary {
    font-size: 2rem;
    font-weight: 700;
    color: hsl(var(--foreground));
    line-height: 1.2;
    margin-bottom: 16px;
  }

  .heading-secondary {
    font-size: 1.5rem;
    font-weight: 600;
    color: hsl(var(--foreground));
    line-height: 1.3;
    margin-bottom: 12px;
  }

  .text-body {
    font-size: 1rem;
    font-weight: 400;
    color: hsl(var(--muted-foreground));
    line-height: 1.6;
  }

  /* COMPLETE BUTTON SYSTEM */
  .btn-secondary {
    background: white;
    color: hsl(var(--foreground));
    border: 2px solid hsl(var(--border));
    border-radius: 24px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.2s ease;
  }

  .btn-secondary:hover {
    border-color: hsl(var(--primary));
    color: hsl(var(--primary));
    transform: translateY(-1px);
  }

  .btn-destructive {
    background: white;
    color: #dc2626;
    border: 2px solid #fecaca;
    border-radius: 24px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.2s ease;
  }

  .btn-destructive:hover {
    background: #fef2f2;
    border-color: #dc2626;
    transform: translateY(-1px);
  }

  /* UNIFIED ICON SYSTEM */
  .icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    background: hsl(var(--primary) / 0.1);
    border: 1px solid hsl(var(--primary) / 0.2);
    border-radius: 12px;
    width: 48px;
    height: 48px;
  }

  .icon-primary {
    color: hsl(var(--primary));
    width: 24px;
    height: 24px;
    stroke-width: 2;
  }

  /* UNIFIED SPACING SYSTEM */
  .spacing-xs { margin: 4px; }
  .spacing-sm { margin: 8px; }
  .spacing-md { margin: 16px; }
  .spacing-lg { margin: 24px; }
  .spacing-xl { margin: 32px; }

  .gap-xs { gap: 4px; }
  .gap-sm { gap: 8px; }
  .gap-md { gap: 16px; }
  .gap-lg { gap: 24px; }
  .gap-xl { gap: 32px; }

  /* UNIFIED PAGE LAYOUT */
  .page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
  }

  .page-header {
    text-align: center;
    margin-bottom: 32px;
  }

  .page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: hsl(var(--foreground));
    margin-bottom: 8px;
  }

  .page-description {
    font-size: 1.125rem;
    color: hsl(var(--muted-foreground));
    max-width: 600px;
    margin: 0 auto;
  }

  /* STAT CARD SYSTEM */
  .stat-card {
    background: white;
    border: 1px solid hsl(var(--border));
    border-radius: 24px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.2s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .stat-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
  }

  .stat-card-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
  }

  .stat-card-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 8px;
  }

  .stat-card-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: hsl(var(--muted-foreground));
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .stat-card-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: hsl(var(--foreground));
    line-height: 1.2;
  }

  .stat-card-description {
    font-size: 0.75rem;
    color: hsl(var(--muted-foreground));
    line-height: 1.4;
  }

  /* ACHIEVEMENT CARD SYSTEM */
  .achievement-card {
    background: white;
    border: 1px solid hsl(var(--border));
    border-radius: 16px;
    padding: 16px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .achievement-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }

  .achievement-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    background: hsl(var(--primary) / 0.1);
    border: 1px solid hsl(var(--primary) / 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .achievement-content {
    flex: 1;
    min-width: 0;
  }

  .achievement-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: hsl(var(--foreground));
    margin-bottom: 2px;
  }

  .achievement-description {
    font-size: 0.75rem;
    color: hsl(var(--muted-foreground));
    line-height: 1.3;
  }

  .achievement-date {
    font-size: 0.75rem;
    color: hsl(var(--muted-foreground));
    margin-top: 4px;
  }
}
