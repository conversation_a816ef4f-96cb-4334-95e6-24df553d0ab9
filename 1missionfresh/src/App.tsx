import React from 'react';
import { Route, Routes, Navigate } from 'react-router-dom';
import { AuthHand<PERSON> } from './components/auth/AuthHandler';
import { PublicLayout } from './components/layout/PublicLayout';
import AppLayout from './components/layout/AppLayout';
import WebToolsLayout from './components/layout/WebToolsLayout';
import AuthGuard from './components/auth/AuthGuard';

// Public Pages
import LandingPage from './pages/LandingPage';
import FeaturesPage from './pages/Features';
import HowItWorks from './pages/HowItWorks';
import AboutUsPage from './pages/AboutUsPage';
import PrivacyPolicyPage from './pages/PrivacyPolicyPage';
import TermsOfServicePage from './pages/TermsOfServicePage';
import HelpCenterPage from './pages/HelpCenterPage';
import ContactPage from './pages/ContactPage';
import FAQPage from './pages/FAQPage';
import FeedbackPage from './pages/FeedbackPage';
import AuthPage from './pages/AuthPage';
import UpdatePasswordPage from './pages/UpdatePasswordPage';
import NotFoundPage from './pages/NotFoundPage';

// Tool Pages
import WebToolsIndex from './pages/tools/WebToolsIndex';
import NRTGuide from './pages/tools/NRTGuide';
import SmokelessDirectory from './pages/tools/SmokelessDirectory';
import QuitMethods from './pages/tools/QuitMethods';
import CalculatorsPage from './pages/tools/CalculatorsPage';
import HolisticHealth from './pages/tools/HolisticHealth';
import ProductDetails from './pages/tools/ProductDetails';

// App Pages
import Dashboard from './pages/app/Dashboard';
import ProgressPage from './pages/app/ProgressPage';
import LogEntry from './pages/app/LogEntry';
import Goals from './pages/app/Goals';
import Rewards from './pages/app/Rewards';
import Settings from './pages/app/Settings';
import HealthIntegrations from './pages/app/HealthIntegrations';
import CravingTools from "./pages/app/tools/CravingTools";
import EnergyTools from "./pages/app/tools/EnergyTools";
import FocusTools from "./pages/app/tools/FocusTools";
import MoodTools from "./pages/app/tools/MoodTools";
import HealthTimeline from "./pages/app/progress/HealthTimeline";
import BreathingTools from "./pages/app/tools/BreathingTools";
import GuidedTools from "./pages/app/tools/GuidedTools";
import FatigueTools from "./pages/app/tools/FatigueTools";
import Learn from "./pages/app/Learn";
import JournalPage from './pages/app/JournalPage';
import SupportPage from './pages/app/SupportPage';
import LearningModuleDetail from './components/app/learning/LearningModuleDetail';
import RelapsePreventionPage from './pages/app/RelapsePreventionPage';
import LogHistoryPage from './pages/app/LogHistoryPage';
import CommunityPage from './pages/app/CommunityPage';
import AICoachPage from './pages/app/tools/AICoachPage';
import ToolsIndex from './pages/app/tools/ToolsIndex';
import CreatePostPage from './pages/app/community/CreatePostPage';
import PostViewPage from './pages/app/community/PostViewPage';
import Cravings from './pages/app/Cravings';

const App: React.FC = () => {
  return (
    <>
      <AuthHandler />
      <Routes>
        {/* Public Routes with PublicLayout */}
        <Route element={<PublicLayout />}>
          <Route path="/" element={<LandingPage />} />
          <Route path="/features" element={<FeaturesPage />} />
          <Route path="/how-it-works" element={<HowItWorks />} />
          <Route path="/about-us" element={<AboutUsPage />} />
          <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
          <Route path="/terms-of-service" element={<TermsOfServicePage />} />
          <Route path="/help" element={<HelpCenterPage />} />
          <Route path="/contact" element={<ContactPage />} />
          <Route path="/faq" element={<FAQPage />} />
          <Route path="/feedback" element={<FeedbackPage />} />
        </Route>

        {/* Auth Routes - No Layout */}
        <Route path="/auth" element={<AuthPage />} />
        <Route path="/update-password" element={<UpdatePasswordPage />} />

        {/* Tools Routes with WebToolsLayout */}
        <Route path="/tools" element={<WebToolsLayout />}>
          <Route index element={<WebToolsIndex />} />
          <Route path="nrt-guide" element={<NRTGuide />} />
          <Route path="smokeless-directory" element={<SmokelessDirectory />} />
          <Route path="quit-methods" element={<QuitMethods />} />
          <Route path="calculators" element={<CalculatorsPage />} />
          <Route path="wellness-calculator" element={<CalculatorsPage />} />
          <Route path="holistic-health" element={<HolisticHealth />} />
          <Route path="smokeless-directory/product/:id" element={<ProductDetails />} />
        </Route>

        {/* Protected App Routes with AppLayout */}
        <Route path="/app" element={
          <AuthGuard>
            <AppLayout />
          </AuthGuard>
        }>
          <Route index element={<Navigate to="dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="log" element={<LogEntry />} />
          <Route path="goals" element={<Goals />} />
          <Route path="progress" element={<ProgressPage />} />
          <Route path="rewards" element={<Rewards />} />
          <Route path="settings/*" element={<Settings />} />
          <Route path="health-integrations" element={<HealthIntegrations />} />
          <Route path="journal" element={<JournalPage />} />
          <Route path="support" element={<SupportPage />} />
          <Route path="relapse-prevention" element={<RelapsePreventionPage />} />
          <Route path="log-history" element={<LogHistoryPage />} />
          <Route path="community" element={<CommunityPage />} />
          <Route path="community/category/:categorySlug" element={<CommunityPage />} />
          <Route path="community/create" element={<CreatePostPage />} />
          <Route path="community/post/:id" element={<PostViewPage />} />
          <Route path="cravings" element={<Cravings />} />
          <Route path="tools" element={<ToolsIndex />} />
          <Route path="tools/cravings" element={<CravingTools />} />
          <Route path="tools/energy" element={<EnergyTools />} />
          <Route path="tools/focus" element={<FocusTools />} />
          <Route path="tools/mood" element={<MoodTools />} />
          <Route path="tools/breathing" element={<BreathingTools />} />
          <Route path="tools/guided" element={<GuidedTools />} />
          <Route path="tools/fatigue" element={<FatigueTools />} />
          <Route path="tools/ai-coach" element={<AICoachPage />} />
          <Route path="learn" element={<Learn />} />
          <Route path="learn/module/:id" element={<LearningModuleDetail />} />
          <Route path="progress/timeline" element={<HealthTimeline />} />
        </Route>

        {/* 404 Route */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </>
  );
};

export default App;
