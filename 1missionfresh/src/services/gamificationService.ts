console.log('🚨🚨🚨 GAMIFICATION SERVICE LOADING - THIS SHOULD APPEAR ON PAGE LOAD! 🚨🚨🚨');

import { supabase } from '@/lib/supabase';
import { Database } from '@/lib/database.types';
import { achievementsList, Achievement, CravingLog, Tool<PERSON>sage, LoginStreak, CommunityActionSummary } from '@/lib/achievementsData'; // Removed IndividualCommunityAction as it's not used directly here
import { sendPushNotification } from './pushNotificationService';
import { earnPoints as earnPointsFromRewardsService } from './rewardsService'; // Import earnPoints
import { Award } from 'lucide-react'; // Import Award icon

// Types from database.types.ts
// Corrected table name from 'gamification_stats' to 'user_gamification_stats' if that's the actual table name in your DB schema
type GamificationStats = Database['mission_fresh']['Tables']['user_gamification_stats']['Row'];
export type UserAchievement = Database['mission_fresh']['Tables']['user_achievements']['Row'];
type NicotineLog = Database['mission_fresh']['Tables']['nicotine_logs']['Row'];
type DbAchievementDefinition = Database['mission_fresh']['Tables']['achievement_definitions']['Row']; // Added back DbAchievementDefinition

// Combined type for display, using Achievement from achievementsData
export interface UserAchievementDisplay extends Achievement {
  user_id?: string;
  achieved_at?: string | null;
  // 'progress' can be added if needed for UI display of partial progress
}

// Placeholder for community actions data structure - now using CommunityAction from achievementsData
// If CommunityAction from achievementsData is sufficient, CommunityActionsSummary might not be needed
// or could be a summary type derived from an array of CommunityAction items.
// For now, let's assume CommunityAction from achievementsData.ts is the detailed type
// and we might need a summary type if the check function expects it.
// UserStreakInfo and AwardedStreakAchievementInfo might be specific to older RPC logic.
// If the new achievement system handles streaks differently, these might change or be removed.
// For now, keeping them if they are still relevant to any part of the service.
export interface UserStreakInfo {
  current_streak: number;
  longest_streak: number;
}

export interface AwardedStreakAchievementInfo {
  achievement_id: string;
  name: string;
  description: string;
  points_awarded: number;
}

export const gamificationService = {
  /**
   * Initializes gamification stats for a new user.
   * @param userId The ID of the user.
   * @returns The created gamification stats or null on error.
   */
  async initializeUserStats(userId: string): Promise<GamificationStats | null> {
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('user_gamification_stats')
      .upsert({ user_id: userId, current_points: 0, current_level: 1, current_streak: 0, longest_streak: 0, last_login_date: new Date().toISOString().split('T')[0] }) // Provide all non-nullable fields or those with defaults
      .select()
      .single();

    if (error) {
      console.error('Error upserting/initializing user gamification stats:', error);
      return null;
    }

    return data;
  },

  /**
   * Adds points to a user's gamification stats using an RPC for atomicity.
   * @param userId The ID of the user.
   * @param pointsToAdd The number of points to add.
   * @returns The updated gamification stats or null on error.
   */
  // Commenting out this local addPoints as rewardsService.earnPoints should be the canonical way
  // async addPoints(userId: string, pointsToAdd: number): Promise<GamificationStats | null> {
  //   const { data: updatedStatsResult, error } = await supabase
  //     .rpc('increment_user_points', {
  //       user_id_param: userId,
  //       points_to_add: pointsToAdd,
  //     })
  //     .single();
  //   if (error) {
  //     console.error('Error calling increment_user_points RPC:', error.message);
  //     return this.getUserStats(userId);
  //   }
  //   if (updatedStatsResult === null || updatedStatsResult === undefined) {
  //     console.error('increment_user_points RPC returned null or undefined data. Attempting fallback.');
  //     return this.getUserStats(userId);
  //   }
  //   if (
  //     !updatedStatsResult ||
  //     typeof updatedStatsResult.user_id !== 'string' ||
  //     typeof updatedStatsResult.current_points !== 'number' ||
  //     typeof updatedStatsResult.current_level !== 'number' ||
  //     updatedStatsResult.created_at === undefined ||
  //     updatedStatsResult.updated_at === undefined
  //   ) {
  //     console.error(
  //       'increment_user_points RPC did not return the expected GamificationStats object structure.',
  //       updatedStatsResult,
  //     );
  //     return this.getUserStats(userId);
  //   }
  //   return updatedStatsResult as GamificationStats;
  // },

  /**
   * Gets a user's current gamification stats.
   * @param userId The ID of the user.
   * @returns The user's gamification stats or null if not found/error.
   */
  async getUserStats(userId: string): Promise<GamificationStats | null> { // Changed UserGamificationStats to GamificationStats
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('user_gamification_stats')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (error) {
      console.error('Error fetching user gamification stats:', error);
      return null;
    }

    return data;
  },

  /**
   * Fetches all available achievement definitions.
   * @returns An array of achievement definitions or null on error.
   */
  async getAllAchievementDefinitions(): Promise<DbAchievementDefinition[] | null> {
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('achievement_definitions')
      .select('*');

    if (error) {
      console.error('Error fetching all achievement definitions:', error);
      return null;
    }
    return data;
  },

  /**
   * Fetches a user's unlocked achievements (raw data from user_achievements table).
   * @param userId The ID of the user.
   * @returns An array of unlocked user achievements or null on error.
   */
  async getRawUserAchievements(userId: string): Promise<UserAchievement[] | null> {
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('user_achievements')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      console.error('Error fetching user achievements:', error);
      return null;
    }
    return data;
  },

  /**
   * REAL DATABASE-DRIVEN ACHIEVEMENT SYSTEM - NO FAKE ACHIEVEMENTS!
   * @param userId The ID of the user.
   * @returns Only REAL achievements the user has actually earned from database
   */
  async getUserAchievementsWithDefinitions(userId: string): Promise<UserAchievementDisplay[]> {
    try {
      console.log('🔥 REAL ACHIEVEMENT SYSTEM: Fetching achievements for userId:', userId);
      
      // Get user's actual earned achievements from database
      const { data: userEarnedAchievements, error: userError } = await supabase
        .schema('mission_fresh')
        .from('user_achievements')
        .select('*')
        .eq('user_id', userId);

      if (userError) {
        console.error('Error fetching user achievements:', userError);
        return []; // Return empty array - no fake fallbacks!
      }

      console.log('🔥 User has earned', userEarnedAchievements?.length || 0, 'real achievements');
      
      if (!userEarnedAchievements || userEarnedAchievements.length === 0) {
        console.log('🔥 User has ZERO achievements - returning empty array (no fake achievements!)');
        return []; // User has no achievements - return empty array, NO FAKE ACHIEVEMENTS!
      }

      // Get achievement definitions for the earned achievements
      const achievementIds = userEarnedAchievements.map(ua => ua.achievement_id);
      const { data: achievementDefinitions, error: definitionsError } = await supabase
        .schema('mission_fresh')
        .from('achievement_definitions')
        .select('*')
        .in('id', achievementIds);

      if (definitionsError) {
        console.error('Error fetching achievement definitions:', definitionsError);
        return []; // Return empty array - no fake fallbacks!
      }

      // Convert to UserAchievementDisplay format
      const realAchievements: UserAchievementDisplay[] = achievementDefinitions?.map(def => {
        const userAchievement = userEarnedAchievements.find(ua => ua.achievement_id === def.id);
        return {
          id: def.id,
          title: def.name,
          description: def.description,
          icon: def.icon || '🏆',
          points: def.points || 0,
          category: def.category || 'general',
          check: () => true, // Already earned
          user_id: userId,
          achieved_at: userAchievement?.unlocked_at || null
        };
      }) || [];

      console.log('🔥 Returning', realAchievements.length, 'REAL achievements from database');
      return realAchievements;
    } catch (error) {
      console.error('❌ ERROR in getUserAchievementsWithDefinitions:', error);
      return []; // Return empty array on error - NO FAKE ACHIEVEMENTS!
    }
  },

  /**
   * Unlocks an achievement for a user if not already unlocked.
   * Uses achievement details from achievementsData.ts.
   * @param userId The ID of the user.
   * @param achievementToUnlock The Achievement object from achievementsData.ts.
   * @returns The unlocked UserAchievement record from DB, or null if already unlocked/error.
   */
  async unlockAchievement(userId: string, achievementToUnlock: Achievement): Promise<UserAchievement | null> {
    try {
      // 1. Get the UUID for the achievement from achievement_definitions table
      //    We'll use achievementToUnlock.id directly as the UUID.
      console.log(`[unlockAchievement] Attempting to unlock: Title='${achievementToUnlock.title}', UUID='${achievementToUnlock.id}' for UserID='${userId}'`);

      const achievementExists = achievementsList.some(a => a.id === achievementToUnlock.id);
      if (!achievementExists) {
        console.error(`[unlockAchievement] Achievement with ID "${achievementToUnlock.id}" not found in achievementsList. Cannot unlock.`);
        return null;
      }
      
      const achievementDefinitionUUID = achievementToUnlock.id;

      // Ensure achievementDefinitionUUID is a valid UUID before proceeding
      if (!achievementDefinitionUUID || typeof achievementDefinitionUUID !== 'string' || !/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(achievementDefinitionUUID)) {
        console.error(`[unlockAchievement] Achievement definition for title "${achievementToUnlock.title}" has an invalid UUID: '${achievementDefinitionUUID}'. Cannot unlock.`);
        return null;
      }

      // 2. Check if already unlocked using the fetched UUID
      console.log(`[unlockAchievement] Checking if UserID='${userId}' already has AchievementUUID='${achievementDefinitionUUID}'`);
      const { data: existing, error: checkError } = await supabase
        .schema('mission_fresh')
        .from('user_achievements')
        .select('id')
        .eq('user_id', userId)
        .eq('achievement_id', achievementDefinitionUUID)
        .maybeSingle();

      if (checkError) {
        console.error(`[unlockAchievement] Error checking if achievement "${achievementToUnlock.title}" (UUID: ${achievementDefinitionUUID}) is already unlocked:`, checkError.message, checkError);
        return null;
      }

      if (existing) {
        console.warn(`[unlockAchievement] Achievement "${achievementToUnlock.title}" (UUID: ${achievementDefinitionUUID}) already unlocked for user ${userId}.`);
        return null; // Not an error, just already achieved.
      }

      // 3. Insert the new achievement using the UUID
      const { data: newlyEarned, error: insertError } = await supabase
        .schema('mission_fresh')
        .from('user_achievements')
        .insert([{ user_id: userId, achievement_id: achievementDefinitionUUID, unlocked_at: new Date().toISOString() }])
        .select()
        .single();

      if (insertError) {
        if (insertError.code === '23503') {
          console.error(`[unlockAchievement] Foreign key violation for achievement "${achievementToUnlock.title}" (UUID: ${achievementDefinitionUUID}). The achievement does not exist in the achievement_definitions table.`);
          return null;
        }
        // Handle potential race conditions where the achievement was inserted between the check and this insert.
        if (insertError.code === '23505') { // Unique violation
          console.warn(`Achievement "${achievementToUnlock.title}" (UUID: ${achievementDefinitionUUID}) was likely unlocked by a concurrent process for user ${userId}.`);
          return null;
        }
        console.error(`Error unlocking achievement "${achievementToUnlock.title}" (UUID: ${achievementDefinitionUUID}):`, insertError);
        return null;
      }

      if (newlyEarned) {
        sendPushNotification(userId, 'Achievement Unlocked!', `You unlocked "${achievementToUnlock.title}"! ${achievementToUnlock.description}`);

        if (achievementToUnlock.points && achievementToUnlock.points > 0) {
          await earnPointsFromRewardsService(userId, achievementToUnlock.points, 'achievement_unlock', achievementToUnlock.title);
          console.log(`Awarded ${achievementToUnlock.points} points for achievement: ${achievementToUnlock.title} via rewardsService`);
        }
        return newlyEarned;
      }
      return null; // Should not be reached if insert was successful and returned data.
    } catch (error) {
      console.error(`[unlockAchievement] Unexpected error unlocking achievement "${achievementToUnlock.title}":`, error);
      return null;
    }
  },

  // --- Live data fetching functions ---
  async _getDaysAfresh(userId: string): Promise<number> {
    // console.log('Checking Supabase session in _getDaysAfresh:', await supabase.auth.getSession());
    const { data: goal, error } = await supabase
      .schema('mission_fresh')
      .from('user_goals')
      .select('quit_date')
      .eq('user_id', userId)
      .maybeSingle();

    if (error) {
      console.error('Error fetching quit date for achievements (_getDaysAfresh):', error);
      return 0; // Return default if there's an error
    }
    if (!goal) {
      // console.warn(`No user_goal record found for user ${userId} in _getDaysAfresh. Returning 0 days.`);
      return 0; // Return default if no goal record found
    }
    if (goal.quit_date) {
      try {
        // Ensure quit_date is a valid date string before parsing
        const quitDate = new Date(goal.quit_date);
        if (isNaN(quitDate.getTime())) {
          // console.error(`Invalid quit_date format for user ${userId}: ${goal.quit_date}`);
          return 0;
        }
        return Math.floor((new Date().getTime() - quitDate.getTime()) / (1000 * 60 * 60 * 24));
      } catch (e) {
        // console.error(`Error parsing quit_date for user ${userId}: ${goal.quit_date}`, e);
        return 0;
      }
    }
    return 0;
  },

  async _getNicotineLogs(userId: string): Promise<NicotineLog[]> {
    const { data, error } = await supabase.schema('mission_fresh').from('nicotine_logs').select('*').eq('user_id', userId);
    if (error) {
      console.error('Error fetching nicotine logs for achievements:', error);
      return [];
    }
    return data || [];
  },

  async _getCravingLogs(userId: string): Promise<CravingLog[]> {
    const { data, error } = await supabase.schema('mission_fresh').from('craving_logs').select('*').eq('user_id', userId);
     if (error) {
      console.error('Error fetching craving logs for achievements:', error);
      return [];
    }
    // The 'craving_logs' table might not map perfectly to CravingLog type.
    // This is a potential area for adjustment based on the actual table structure.
    // For now, we assume it's compatible enough.
    return (data as any) || [];
  },

  async _getToolUsages(userId: string): Promise<ToolUsage[]> {
    const { data, error } = await supabase.schema('mission_fresh').from('activity_points_log').select('activity_type, created_at').eq('user_id', userId);
     if (error) {
      console.error('Error fetching tool usages for achievements:', error);
      return [];
    }
    // Mapping from activity_points_log to ToolUsage
    return data?.map(log => ({
        id: log.activity_type, // Not a real ID, but using what's available
        user_id: userId,
        tool_type: log.activity_type,
        timestamp: log.created_at,
    })) || [];
  },

  async _getCommunityActions(userId: string): Promise<CommunityActionSummary> {
    const { count: posts, error: postsError } = await supabase
      .schema('mission_fresh')
      .from('community_posts')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    const { count: comments, error: commentsError } = await supabase
      .schema('mission_fresh')
      .from('success_story_comments')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    const { count: likes, error: likesError } = await supabase
      .schema('mission_fresh')
      .from('post_likes')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);
      
    const { count: storyLikes, error: storyLikesError } = await supabase
      .schema('mission_fresh')
      .from('success_story_likes')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (postsError || commentsError || likesError || storyLikesError) {
      console.error('Error fetching community actions:', postsError, commentsError, likesError, storyLikesError);
      return { posts: 0, comments: 0, likes: 0 };
    }

    return {
      posts: posts || 0,
      comments: comments || 0,
      likes: (likes || 0) + (storyLikes || 0),
    };
  },

  async _getLoginStreak(userId: string): Promise<LoginStreak | null> {
    const streakInfo = await this.getStreakInfo(userId);
    if (streakInfo && streakInfo.last_login_date) {
      return {
        user_id: userId,
        current_streak: streakInfo.current_streak,
        longest_streak: streakInfo.longest_streak,
        last_login_date: streakInfo.last_login_date, // Use actual last_login_date
      };
    } else if (streakInfo) { // Has streak info but no last_login_date (should not happen if DB column exists and is populated)
        console.warn(`_getLoginStreak for ${userId} received streak info but last_login_date was null/undefined. Using current date as fallback.`);
        return {
            user_id: userId,
            current_streak: streakInfo.current_streak,
            longest_streak: streakInfo.longest_streak,
            last_login_date: new Date().toISOString(),
        }
    }
    return null;
  },
  // --- End of placeholder data fetching functions ---

  /**
   * Checks all defined achievements against the user's current data and awards them if criteria are met.
   * This is the new central function for awarding achievements.
   * @param userId The ID of the user.
   */
  async checkAndAwardAllAchievements(userId: string): Promise<void> {
    console.log(`Starting achievement check for user ${userId}`);
    try {
      const [
        daysAfresh,
        nicotineLogs,
        cravingLogs,
        toolUsages,
        communityActions,
        loginStreak,
        earnedDbAchievementsRaw
      ] = await Promise.all([
        this._getDaysAfresh(userId),
        this._getNicotineLogs(userId),
        this._getCravingLogs(userId),
        this._getToolUsages(userId),
        this._getCommunityActions(userId),
        this._getLoginStreak(userId),
        this.getRawUserAchievements(userId) // Fetch currently earned achievements
      ]);

      const earnedAchievementIds = new Set(earnedDbAchievementsRaw?.map(ach => ach.achievement_id) || []);

      for (const achievement of achievementsList) {
        if (!earnedAchievementIds.has(achievement.id)) {
          const isAchieved = achievement.check(
            daysAfresh,
            nicotineLogs,
            cravingLogs,
            toolUsages,
            communityActions,
            loginStreak
          );

          if (isAchieved) {
            console.log(`User ${userId} qualifies for achievement: ${achievement.title}`);
            await this.unlockAchievement(userId, achievement);
          }
        }
      }
      console.log(`Finished achievement check for user ${userId}`);
    } catch (error) {
      console.error(`Error during checkAndAwardAllAchievements for user ${userId}:`, error);
    }
  },

  /**
   * Gets the user's current and longest streak information.
   * IMPORTANT ASSUMPTION: Assumes the 'get_user_streak' RPC returns an object
   * with { current_streak: number, longest_streak: number }.
   * If it only returns a number (current_streak), this function or the RPC needs adjustment.
   * @param userId The ID of the user.
   * @returns The user's streak information or null on error.
   */
  async getStreakInfo(userId: string): Promise<UserStreakInfo & { last_login_date: string | null }> {
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('user_gamification_stats')
      .select('current_streak, longest_streak, last_login_date')
      .eq('user_id', userId)
      .maybeSingle();

    if (error) {
      // This is where PGRST116 might occur if RLS is problematic or if the query is malformed for PostgREST's expectations.
      // However, with .maybeSingle(), PGRST116 (no rows) should ideally result in data being null, not an error object here.
      // If an error object *is* present, it's a more severe issue than just "no row found".
      console.error(`Error fetching user streak info for user ${userId} (getStreakInfo):`, error);
      console.warn(`Returning default streak info for user ${userId} due to explicit database query error.`);
      return { current_streak: 0, longest_streak: 0, last_login_date: null };
    }

    if (data && typeof data.current_streak === 'number' && typeof data.longest_streak === 'number') {
      // Successfully fetched data
      return {
        current_streak: data.current_streak,
        longest_streak: data.longest_streak,
        last_login_date: data.last_login_date, 
      };
    }
    
    // This means data is null (no row found by maybeSingle) or the row exists but current_streak/longest_streak are not numbers.
    // console.warn(`No gamification stats row found, or streak data is invalid, for user ${userId} (getStreakInfo). Returning default streak info.`);
    return { current_streak: 0, longest_streak: 0, last_login_date: null };
  },

  /**
   * Calls the backend to update the user's streak.
   * This should be called after events like daily check-in or logging nicotine use.
   * IMPORTANT ASSUMPTION: Assumes 'update_user_streak' RPC returns the new current_streak value.
   * @param userId The ID of the user.
   * @returns The new current streak value, or null on error.
   */
  async recordStreakUpdate(userId: string): Promise<number | null> {
    const { data, error } = await supabase
      .rpc('update_user_streak', {
        p_user_id: userId,
      })
      .single();

    if (error) {
      console.error('Error updating user streak:', error);
      return null;
    }

    // Assuming the RPC returns the new streak value directly as a number
    if (typeof data === 'number') {
      return data;
    }

    if (data !== null) {
        console.error('Unexpected data structure from update_user_streak RPC:', data);
    }
    return null;
  },

  /**
   * Checks for and awards streak-based achievements after a streak update.
   * Sends push notifications for newly awarded achievements.
   * IMPORTANT ASSUMPTION: Assumes 'check_and_award_streak_achievements' RPC returns
   * an array of newly awarded achievements with details like { achievement_id, name, description, points_awarded }.
   * @param userId The ID of the user.
   * @param currentStreak The user's current streak after an update.
   */
  async checkAndAwardStreakAchievements(userId: string, currentStreak: number): Promise<void> {
    if (currentStreak <= 0) { // No point checking for achievements if streak is 0 or less
      return;
    }

    const { data: awardedAchievements, error } = await supabase.rpc('check_and_award_streak_achievements', {
      p_user_id: userId,
      p_current_streak: currentStreak,
    });

    if (error) {
      console.error('Error checking/awarding streak achievements:', error);
      return;
    }

    if (awardedAchievements && Array.isArray(awardedAchievements)) {
      for (const achievement of awardedAchievements as unknown as AwardedStreakAchievementInfo[]) {
        if (achievement.name && achievement.description) {
          sendPushNotification(
            userId,
            'Streak Achievement Unlocked!',
            `You unlocked "${achievement.name}" for reaching a ${currentStreak}-day streak! ${achievement.description}`
          );
          // Optionally, add points if this RPC doesn't handle it
          // For example: if (achievement.points_awarded > 0) await this.addPoints(userId, achievement.points_awarded);
        } else {
          console.warn('Awarded streak achievement details incomplete, cannot send notification:', achievement);
        }
      }
    }
  },

  /**
   * A comprehensive function to call after a user action that affects their streak.
   * It updates the streak and then checks for/awards achievements.
   * @param userId The ID of the user.
   */
  async processStreakUpdate(userId: string): Promise<void> {
    const newStreakValue = await this.recordStreakUpdate(userId); // This updates the streak in DB via RPC

    if (newStreakValue !== null) {
      // After streak is updated (or reset), run the comprehensive achievement check
      // This will cover streak achievements defined in achievementsData.ts and any others.
      // await this.checkAndAwardAllAchievements(userId);

      if (newStreakValue === 0) {
        console.log(`Streak reset for user ${userId}. Full achievement check run.`);
        // Potentially send a specific "Streak Lost" notification if not covered by an achievement
      } else {
        console.log(`Streak updated to ${newStreakValue} for user ${userId}. Full achievement check run.`);
      }
    } else {
      console.error(`Failed to update streak for user ${userId}, achievement check might be based on stale data.`);
      // Still, attempt to check achievements as other non-streak ones might be pending
      // await this.checkAndAwardAllAchievements(userId);
    }
  },

  /**
   * Tracks tool usage for a user and unlocks the 'Tool Explorer' achievement if criteria are met.
   * @param userId The ID of the user.
   */
  async trackToolUsage(userId: string): Promise<void> {
    // const TOOL_EXPLORER_ACHIEVEMENT_ID = '3c37356a-6f92-44fc-a201-cddd1014c575'; // 'Tool Explorer' achievement ID
    // const TOOL_USAGE_THRESHOLD = 5; // Example threshold
    // This function's logic is now largely covered by checkAndAwardAllAchievements
    // if _getToolUsages provides the necessary data.
    // For now, this function might just record a tool usage event if we had a separate table.
    // Or, it could directly call checkAndAwardAllAchievements.

    console.log(`Tool usage tracked for user ${userId}. Running comprehensive achievement check.`);

    // Placeholder: If you have a specific table for tool usage, insert a record here.
    // Example: await supabase.from('user_tool_usages').insert({ user_id: userId, tool_id: 'some_tool_id', timestamp: new Date() });

    // Then, re-evaluate all achievements.
    // await this.checkAndAwardAllAchievements(userId);

    // The old logic for incrementing tool_usage_count in user_gamification_stats
    // and checking a hardcoded achievement ID is superseded by the dynamic system.
    // If tool_usage_count is still needed for other purposes, it can be updated,
    // but achievement unlocking should go through checkAndAwardAllAchievements.
  },
};
