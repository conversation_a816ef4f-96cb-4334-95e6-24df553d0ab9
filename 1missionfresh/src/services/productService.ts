import { supabase } from "@/lib/supabase"; // Use the canonical supabase client import
import { Database, Tables, TablesInsert, TablesUpdate } from "@/lib/database.types"; // Import Database type and Tables
import { toast } from "sonner";

type Product = Tables<{ schema: 'mission_fresh' }, 'smokeless_products'>;
export type Vendor = Tables<{ schema: 'mission_fresh' }, 'smokeless_vendors'>;
type ProductVendorLink = Tables<{ schema: 'mission_fresh' }, 'smokeless_product_vendors'>;

type Review = Tables<{ schema: 'mission_fresh' }, 'smokeless_product_reviews'>;
type ReviewInsert = TablesInsert<{ schema: 'mission_fresh' }, 'smokeless_product_reviews'>;

// Type for review with user profile data (adjust based on actual profile fields you need)
export type ProductReviewWithUser = Review & {
  profiles: {
    username: string | null;
    avatar_url: string | null;
    // Add other profile fields if needed, e.g., first_name
  } | null;
};

// Extended product type that includes its vendors from the join table
export type ProductWithVendors = Product & {
  smokeless_product_vendors: (ProductVendorLink & {
    smokeless_vendors: Vendor | null; // The actual vendor details
  })[];
};


export const getProducts = async (
  searchTerm?: string,
  flavor?: string,
  brand?: string
  // minNicotine?: number, // Temporarily removed for fixing query error
  // maxNicotine?: number  // Temporarily removed for fixing query error
): Promise<Product[]> => {
  try {
    let query = supabase
      .schema('mission_fresh')
      .from('smokeless_products')
      .select('*');

    // ENHANCED SEARCH: Search both name and brand fields for comprehensive results
    if (searchTerm && searchTerm.trim() !== '') {
      console.log('Searching for:', searchTerm.trim());
      const searchPattern = `%${searchTerm.trim()}%`;
      query = query.or(`name.ilike.${searchPattern},brand.ilike.${searchPattern}`);
    }
    
    // If no search term, use the original query approach
    if (flavor && flavor !== 'all') {
      query = query.contains('flavors', [flavor]);
    }
    if (brand && brand !== 'all') {
      query = query.eq('brand', brand);
    }

    query = query.order('name', { ascending: true });

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching products:', error);
      toast.error('Failed to load products.');
      throw error;
    }
    
    return data || [];
  } catch (error) {
    console.error('Catch block error fetching products:', error);
    // toast.error('An unexpected error occurred while loading products.'); // Already toasted
    return [];
  }
};

// NEW FUNCTION: Get products with vendor pricing data for directory display
export const getProductsWithVendors = async (
  searchTerm?: string,
  flavor?: string,
  brand?: string
): Promise<ProductWithVendors[]> => {
  try {
    let query = supabase
      .schema('mission_fresh')
      .from('smokeless_products')
      .select(`
        id,
        name,
        brand,
        category,
        description,
        image_url,
        nicotine_strengths,
        flavors,
        ingredients,
        user_rating_avg,
        user_rating_count,
        is_verified,
        country_of_origin,
        manufacturer,
        tags,
        created_at,
        updated_at,
        smokeless_product_vendors (
          price,
          product_url_on_vendor_site,
          is_available,
          smokeless_vendors (
            id,
            name,
            website_url,
            logo_url,
            description
          )
        )
      `);

    // ENHANCED SEARCH: Search both name and brand fields for comprehensive results
    if (searchTerm && searchTerm.trim() !== '') {
      console.log('Searching for:', searchTerm.trim());
      const searchPattern = `%${searchTerm.trim()}%`;
      query = query.or(`name.ilike.${searchPattern},brand.ilike.${searchPattern}`);
    }
    
    // If no search term, use the original query approach
    if (flavor && flavor !== 'all') {
      query = query.contains('flavors', [flavor]);
    }
    if (brand && brand !== 'all') {
      query = query.eq('brand', brand);
    }

    query = query.order('name', { ascending: true });

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching products with vendors:', error);
      toast.error('Failed to load products.');
      throw error;
    }
    
    return (data as ProductWithVendors[]) || [];
  } catch (error) {
    console.error('Catch block error fetching products with vendors:', error);
    return [];
  }
};

export const getVendors = async (searchTerm?: string): Promise<Vendor[]> => {
  try {
    // CRITICAL FIX: Always fetch all vendors and filter client-side to avoid Supabase search issues
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('smokeless_vendors')
      .select('*')
      .order('name', { ascending: true });

    if (error) {
      console.error('Error fetching vendors:', error);
      toast.error('Failed to load vendors.');
      throw error;
    }

    let vendors = data || [];

    // Client-side filtering for reliable search functionality
    if (searchTerm && searchTerm.trim() !== '') {
      const searchLower = searchTerm.trim().toLowerCase();
      vendors = vendors.filter(vendor => 
        (vendor.name && vendor.name.toLowerCase().includes(searchLower)) ||
        (vendor.description && vendor.description.toLowerCase().includes(searchLower))
      );
    }

    return vendors;
  } catch (error) {
    console.error('Catch block error fetching vendors:', error);
    // toast.error('An unexpected error occurred while loading vendors.'); // Already toasted
    return [];
  }
};

export const getProductById = async (productId: string): Promise<ProductWithVendors | null> => {
  if (!productId) return null;
  try {
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('smokeless_products')
      .select(`
        id,
        name,
        brand,
        category,
        description,
        image_url,
        nicotine_strengths,
        flavors,
        ingredients,
        user_rating_avg,
        user_rating_count,
        is_verified,
        country_of_origin,
        manufacturer,
        tags,
        created_at,
        updated_at,
        smokeless_product_vendors (
          price,
          product_url_on_vendor_site,
          is_available,
          smokeless_vendors (
            id,
            name,
            website_url,
            logo_url,
            description
          )
        )
      `)
      .eq('id', productId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // Not found
        toast.info('Product not found.'); // Changed from toast.warn
        return null;
      }
      console.error(`Error fetching product by ID (${productId}):`, error);
      toast.error('Failed to load product details.');
      throw error;
    }
    return data as ProductWithVendors | null;
  } catch (error) {
    console.error('Catch block error fetching product by ID:', error);
    // toast.error('An unexpected error occurred while loading product details.'); // Already toasted
    return null;
  }
};

export const getProductReviews = async (productId: string): Promise<ProductReviewWithUser[]> => {
  if (!productId) return [];
  try {
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('smokeless_product_reviews')
      .select(`
        *,
        profiles (
          username,
          avatar_url
        )
      `)
      .eq('product_id', productId)
      .eq('moderation_status', 'approved') // Only fetch approved reviews
      .order('created_at', { ascending: false });

    if (error) {
      console.error(`Error fetching reviews for product ID (${productId}):`, error);
      toast.error('Failed to load reviews.');
      throw error;
    }
    return (data as ProductReviewWithUser[]) || [];
  } catch (error) {
    console.error('Catch block error fetching product reviews:', error);
    return [];
  }
};

export const addProductReview = async (
  reviewData: Omit<ReviewInsert, 'id' | 'created_at' | 'user_id' | 'moderation_status'> & { user_id?: string}
): Promise<Review | null> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    toast.error("You must be logged in to submit a review.");
    throw new Error("User not authenticated");
  }

  try {
    const reviewToInsert: ReviewInsert = {
      ...reviewData,
      user_id: user.id,
      product_id: reviewData.product_id, // Ensure product_id is passed
      rating: reviewData.rating,
      review_text: reviewData.review_text,
      moderation_status: 'pending', // Default to pending moderation
    };

    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('smokeless_product_reviews')
      .insert(reviewToInsert)
      .select()
      .single();

    if (error) {
      console.error('Error adding product review:', error);
      toast.error(`Failed to submit review: ${error.message}`);
      throw error;
    }
    toast.success('Review submitted for moderation!');
    return data;
  } catch (error) {
    console.error('Catch block error adding product review:', error);
    // Toasting is handled above or by the caller
    throw error;
  }
};

// --- Smokeless Product CRUD ---

export const createProduct = async (
  productData: Omit<TablesInsert<{ schema: 'mission_fresh' }, 'smokeless_products'>, 'id' | 'created_at' | 'updated_at' | 'user_rating_avg' | 'user_rating_count'>
): Promise<Product | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) { // Though RLS should also prevent unauthenticated access for CUD
      toast.error("Authentication required to create a product.");
      throw new Error("User not authenticated");
    }

    // Add any default or server-set values if necessary before insert
    // For example, if user_id should be associated (though not in current schema for products)
    // const dataToInsert = { ...productData, user_id: user.id }; 

    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('smokeless_products')
      .insert(productData) // productData should conform to the insert type
      .select()
      .single();

    if (error) {
      console.error('Error creating product:', error);
      toast.error(`Failed to create product: ${error.message}`);
      throw error;
    }
    toast.success(`Product "${data.name}" created successfully!`);
    return data;
  } catch (error) {
    console.error('Catch block error creating product:', error);
    // Toasting handled above or by caller
    throw error; // Re-throw to be caught by react-query's useMutation if used
  }
};

export const updateProduct = async (
  productId: string,
  productData: Partial<Omit<TablesUpdate<{ schema: 'mission_fresh' }, 'smokeless_products'>, 'id' | 'created_at' | 'updated_at'>>
): Promise<Product | null> => {
  if (!productId) {
    toast.error("Product ID is required for an update.");
    return null;
  }
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      toast.error("Authentication required to update a product.");
      throw new Error("User not authenticated");
    }

    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('smokeless_products')
      .update(productData)
      .eq('id', productId)
      .select()
      .single();

    if (error) {
      console.error(`Error updating product ${productId}:`, error);
      toast.error(`Failed to update product: ${error.message}`);
      throw error;
    }
    toast.success(`Product "${data.name}" updated successfully!`);
    return data;
  } catch (error) {
    console.error('Catch block error updating product:', error);
    throw error;
  }
};

export const deleteProduct = async (productId: string): Promise<boolean> => {
  if (!productId) {
    toast.error("Product ID is required for deletion.");
    return false;
  }
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      toast.error("Authentication required to delete a product.");
      throw new Error("User not authenticated");
    }

    const { error } = await supabase
      .schema('mission_fresh')
      .from('smokeless_products')
      .delete()
      .eq('id', productId);

    if (error) {
      console.error(`Error deleting product ${productId}:`, error);
      toast.error(`Failed to delete product: ${error.message}`);
      throw error;
    }
    toast.success("Product deleted successfully!");
    return true;
  } catch (error) {
    console.error('Catch block error deleting product:', error);
    throw error;
  }
};

// --- Smokeless Vendor CRUD ---

export const createVendor = async (
  vendorData: Omit<TablesInsert<{ schema: 'mission_fresh' }, 'smokeless_vendors'>, 'id' | 'created_at' | 'updated_at' | 'user_rating_avg' | 'user_rating_count'>
): Promise<Vendor | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      toast.error("Authentication required to create a vendor.");
      throw new Error("User not authenticated");
    }

    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('smokeless_vendors')
      .insert(vendorData)
      .select()
      .single();

    if (error) {
      console.error('Error creating vendor:', error);
      toast.error(`Failed to create vendor: ${error.message}`);
      throw error;
    }
    toast.success(`Vendor "${data.name}" created successfully!`);
    return data;
  } catch (error) {
    console.error('Catch block error creating vendor:', error);
    throw error;
  }
};

export const updateVendor = async (
  vendorId: string,
  vendorData: Partial<Omit<TablesUpdate<{ schema: 'mission_fresh' }, 'smokeless_vendors'>, 'id' | 'created_at' | 'updated_at'>>
): Promise<Vendor | null> => {
  if (!vendorId) {
    toast.error("Vendor ID is required for an update.");
    return null;
  }
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      toast.error("Authentication required to update a vendor.");
      throw new Error("User not authenticated");
    }

    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('smokeless_vendors')
      .update(vendorData)
      .eq('id', vendorId)
      .select()
      .single();

    if (error) {
      console.error(`Error updating vendor ${vendorId}:`, error);
      toast.error(`Failed to update vendor: ${error.message}`);
      throw error;
    }
    toast.success(`Vendor "${data.name}" updated successfully!`);
    return data;
  } catch (error) {
    console.error('Catch block error updating vendor:', error);
    throw error;
  }
};

export const deleteVendor = async (vendorId: string): Promise<boolean> => {
  if (!vendorId) {
    toast.error("Vendor ID is required for deletion.");
    return false;
  }
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      toast.error("Authentication required to delete a vendor.");
      throw new Error("User not authenticated");
    }

    const { error } = await supabase
      .schema('mission_fresh')
      .from('smokeless_vendors')
      .delete()
      .eq('id', vendorId);

    if (error) {
      console.error(`Error deleting vendor ${vendorId}:`, error);
      toast.error(`Failed to delete vendor: ${error.message}`);
      throw error;
    }
    toast.success("Vendor deleted successfully!");
    return true;
  } catch (error) {
    console.error('Catch block error deleting vendor:', error);
    throw error;
  }
};

// --- Product-Vendor Link Management ---

export const linkProductToVendor = async (
  linkData: TablesInsert<{ schema: 'mission_fresh' }, 'smokeless_product_vendors'>
): Promise<ProductVendorLink | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      toast.error("Authentication required to link product to vendor.");
      throw new Error("User not authenticated");
    }

    // Ensure product_id and vendor_id are present
    if (!linkData.product_id || !linkData.vendor_id) {
      toast.error("Product ID and Vendor ID are required to create a link.");
      return null;
    }

    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('smokeless_product_vendors')
      .insert(linkData)
      .select()
      .single();

    if (error) {
      console.error('Error linking product to vendor:', error);
      toast.error(`Failed to link product to vendor: ${error.message}`);
      throw error;
    }
    toast.success("Product successfully linked to vendor!");
    return data;
  } catch (error) {
    console.error('Catch block error linking product to vendor:', error);
    throw error;
  }
};

export const unlinkProductFromVendor = async (
  productId: string,
  vendorId: string
): Promise<boolean> => {
  if (!productId || !vendorId) {
    toast.error("Product ID and Vendor ID are required for unlinking.");
    return false;
  }
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      toast.error("Authentication required to unlink product from vendor.");
      throw new Error("User not authenticated");
    }

    const { error } = await supabase
      .schema('mission_fresh')
      .from('smokeless_product_vendors')
      .delete()
      .eq('product_id', productId)
      .eq('vendor_id', vendorId);

    if (error) {
      console.error(`Error unlinking product ${productId} from vendor ${vendorId}:`, error);
      toast.error(`Failed to unlink product from vendor: ${error.message}`);
      throw error;
    }
    toast.success("Product successfully unlinked from vendor.");
    return true;
  } catch (error) {
    console.error('Catch block error unlinking product from vendor:', error);
    throw error;
  }
};

export const updateProductVendorLink = async (
  productId: string,
  vendorId: string,
  linkUpdateData: Partial<Omit<TablesUpdate<{ schema: 'mission_fresh' }, 'smokeless_product_vendors'>, 'product_id' | 'vendor_id' | 'created_at'>>
): Promise<ProductVendorLink | null> => {
  if (!productId || !vendorId) {
    toast.error("Product ID and Vendor ID are required for updating the link.");
    return null;
  }
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      toast.error("Authentication required to update product-vendor link.");
      throw new Error("User not authenticated");
    }

    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('smokeless_product_vendors')
      .update(linkUpdateData)
      .eq('product_id', productId)
      .eq('vendor_id', vendorId)
      .select()
      .single();

    if (error) {
      console.error(`Error updating link for product ${productId} and vendor ${vendorId}:`, error);
      toast.error(`Failed to update product-vendor link: ${error.message}`);
      throw error;
    }
    toast.success("Product-vendor link updated successfully!");
    return data;
  } catch (error) {
    console.error('Catch block error updating product-vendor link:', error);
    throw error;
  }
};

// Database cleanup function - removes corrupted data
export const cleanupCorruptedData = async (): Promise<boolean> => {
  try {
    console.log('Starting database cleanup...');
    
    // Delete all existing products - use proper deletion syntax
    const { error: deleteProductsError } = await supabase
      .schema('mission_fresh')
      .from('smokeless_products')
      .delete()
      .not('id', 'is', null); // Delete all records where id is not null
      
    if (deleteProductsError) {
      console.error('Error deleting corrupted products:', deleteProductsError);
      throw deleteProductsError;
    }
    
    // Delete all existing vendors - use proper deletion syntax
    const { error: deleteVendorsError } = await supabase
      .schema('mission_fresh')
      .from('smokeless_vendors')
      .delete()
      .not('id', 'is', null); // Delete all records where id is not null
      
    if (deleteVendorsError) {
      console.error('Error deleting corrupted vendors:', deleteVendorsError);
      throw deleteVendorsError;
    }
    
    console.log('Database cleanup completed successfully');
    return true;
  } catch (error) {
    console.error('Database cleanup failed:', error);
    return false;
  }
};

// Database seeding function - creates real data in Supabase
export const seedProductsAndVendors = async (): Promise<boolean> => {
  try {
    console.log('Starting database seeding...');
    
    // Check if data already exists to prevent duplicate seeding
    const { data: existingProducts } = await supabase
      .schema('mission_fresh')
      .from('smokeless_products')
      .select('id')
      .limit(1);
      
    if (existingProducts && existingProducts.length > 0) {
      console.log('Database already seeded, skipping...');
      return true;
    }

    // Also check vendors to ensure complete seeding check
    const { data: existingVendors } = await supabase
      .schema('mission_fresh')
      .from('smokeless_vendors')
      .select('id')
      .limit(1);
      
    if (existingVendors && existingVendors.length > 0) {
      console.log('Database already has vendors, skipping seeding...');
      return true;
    }

    // Seed vendors first
    const vendorsToInsert = [
      {
        name: 'NRT Direct',
        description: 'Leading online retailer of nicotine replacement therapy products. Certified by health authorities with fast, discreet shipping.',
        website_url: 'https://nrtdirect.com',
        is_partner: true,
        shipping_info: 'Free shipping on orders over $50. Ships within 24 hours.',
        customer_service_contact: '<EMAIL>',
        countries_shipped_to: ['US', 'CA', 'UK', 'AU']
      },
      {
        name: 'Quit Smart Pharmacy',
        description: 'Licensed pharmacy specializing in smoking cessation products. Professional consultation available.',
        website_url: 'https://quitsmartpharmacy.com',
        is_partner: true,
        shipping_info: 'Express delivery available. Prescription verification for medical products.',
        customer_service_contact: '1-800-QUIT-NOW',
        countries_shipped_to: ['US', 'CA']
      },
      {
        name: 'Wellness Hub Store',
        description: 'Comprehensive wellness retailer offering natural and medical cessation aids. Expert guidance and support.',
        website_url: 'https://wellnesshubstore.com',
        is_partner: false,
        shipping_info: 'Standard and expedited shipping options. International delivery available.',
        customer_service_contact: '<EMAIL>',
        countries_shipped_to: ['US', 'CA', 'UK', 'EU', 'AU', 'NZ']
      }
    ];

    const { data: insertedVendors, error: vendorError } = await supabase
      .schema('mission_fresh')
      .from('smokeless_vendors')
      .insert(vendorsToInsert)
      .select();

    if (vendorError) {
      console.error('Error seeding vendors:', vendorError);
      throw vendorError;
    }

    // Seed products - FIXED DATA WITH CORRECT BRANDS AND UNIQUE PRODUCTS
    const productsToInsert = [
      {
        name: 'ZYN Nicotine Pouches - Mint',
        brand: 'ZYN',
        category: 'pouches',
        description: 'Premium nicotine pouches with refreshing mint flavor. Tobacco-free and discreet.',
        image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400&h=300&fit=crop&crop=center',
        flavors: ['Mint', 'Cool Mint'],
        ingredients: 'Nicotine, Plant-based fibers, Natural flavors',
        user_rating_avg: 4.5,
        user_rating_count: 127,
        is_verified: true,
        country_of_origin: 'Sweden',
        manufacturer: 'Swedish Match',
        tags: ['tobacco-free', 'discreet', 'mint'],
        nicotine_strengths: [{ value: 3, unit: 'mg' }, { value: 6, unit: 'mg' }]
      },
      {
        name: 'Nicorette Nicotine Gum - Cinnamon',
        brand: 'Nicorette',
        category: 'gum',
        description: 'Medical-grade nicotine gum with warming cinnamon flavor. Clinically proven for cessation.',
        image_url: 'https://images.unsplash.com/photo-**********-edd951b55104?w=400&h=300&fit=crop&crop=center',
        flavors: ['Cinnamon', 'Original'],
        ingredients: 'Nicotine polacrilex, Gum base, Cinnamon flavoring',
        user_rating_avg: 4.2,
        user_rating_count: 89,
        is_verified: true,
        country_of_origin: 'USA',
        manufacturer: 'Johnson & Johnson',
        tags: ['medical-grade', 'cessation', 'gum'],
        nicotine_strengths: [{ value: 2, unit: 'mg' }, { value: 4, unit: 'mg' }]
      },
      {
        name: 'Commit Nicotine Lozenges - Fruit',
        brand: 'Commit',
        category: 'lozenges',
        description: 'Slow-dissolving nicotine lozenges with natural fruit flavors. Long-lasting satisfaction.',
        image_url: 'https://images.unsplash.com/photo-1587854692152-cbe660dbde88?w=400&h=300&fit=crop&crop=center',
        flavors: ['Cherry', 'Orange', 'Mixed Fruit'],
        ingredients: 'Nicotine polacrilex, Sorbitol, Natural fruit flavors',
        user_rating_avg: 4.0,
        user_rating_count: 64,
        is_verified: true,
        country_of_origin: 'Canada',
        manufacturer: 'GlaxoSmithKline',
        tags: ['lozenges', 'fruit', 'long-lasting'],
        nicotine_strengths: [{ value: 2, unit: 'mg' }, { value: 4, unit: 'mg' }]
      },
      {
        name: 'VELO Nicotine Pouches - Citrus',
        brand: 'VELO',
        category: 'pouches',
        description: 'Modern nicotine pouches with zesty citrus flavor. Slim design for ultimate discretion.',
        image_url: 'https://images.unsplash.com/photo-1471864190281-a93a3070b6de?w=400&h=300&fit=crop&crop=center',
        flavors: ['Citrus', 'Lemon', 'Orange'],
        ingredients: 'Nicotine, Plant fibers, Citrus extracts',
        user_rating_avg: 4.3,
        user_rating_count: 156,
        is_verified: true,
        country_of_origin: 'Denmark',
        manufacturer: 'British American Tobacco',
        tags: ['slim', 'citrus', 'modern'],
        nicotine_strengths: [{ value: 4, unit: 'mg' }, { value: 8, unit: 'mg' }]
      }
    ];

    const { data: insertedProducts, error: productError } = await supabase
      .schema('mission_fresh')
      .from('smokeless_products')
      .insert(productsToInsert)
      .select();

    if (productError) {
      console.error('Error seeding products:', productError);
      throw productError;
    }

    // Step 3: Create product-vendor links with pricing
    if (insertedProducts && insertedVendors && insertedProducts.length > 0 && insertedVendors.length > 0) {
      const productVendorLinks = [
        // ZYN Nicotine Pouches links
        {
          product_id: insertedProducts[0].id,
          vendor_id: insertedVendors[0].id,
          price: 8.99,
          product_url_on_vendor_site: 'https://nrtdirect.com/zyn-mint-pouches',
          is_available: true
        },
        {
          product_id: insertedProducts[0].id,
          vendor_id: insertedVendors[1].id,
          price: 9.49,
          product_url_on_vendor_site: 'https://quitsmartpharmacy.com/zyn-mint',
          is_available: true
        },
        // Nicorette Gum links
        {
          product_id: insertedProducts[1].id,
          vendor_id: insertedVendors[0].id,
          price: 12.99,
          product_url_on_vendor_site: 'https://nrtdirect.com/nicorette-cinnamon-gum',
          is_available: true
        },
        {
          product_id: insertedProducts[1].id,
          vendor_id: insertedVendors[2].id,
          price: 13.49,
          product_url_on_vendor_site: 'https://wellnesshubstore.com/nicorette-gum',
          is_available: true
        },
        // Commit Lozenges links
        {
          product_id: insertedProducts[2].id,
          vendor_id: insertedVendors[1].id,
          price: 11.99,
          product_url_on_vendor_site: 'https://quitsmartpharmacy.com/commit-lozenges',
          is_available: true
        },
        {
          product_id: insertedProducts[2].id,
          vendor_id: insertedVendors[2].id,
          price: 11.49,
          product_url_on_vendor_site: 'https://wellnesshubstore.com/commit-fruit-lozenges',
          is_available: false
        },
        // VELO Pouches links
        {
          product_id: insertedProducts[3].id,
          vendor_id: insertedVendors[0].id,
          price: 9.99,
          product_url_on_vendor_site: 'https://nrtdirect.com/velo-citrus-pouches',
          is_available: true
        },
        {
          product_id: insertedProducts[3].id,
          vendor_id: insertedVendors[2].id,
          price: 10.49,
          product_url_on_vendor_site: 'https://wellnesshubstore.com/velo-citrus',
          is_available: true
        }
      ];

      const { error: linkError } = await supabase
        .schema('mission_fresh')
        .from('smokeless_product_vendors')
        .insert(productVendorLinks);

      if (linkError) {
        console.error('Error creating product-vendor links:', linkError);
        throw linkError;
      }

      console.log(`Created ${productVendorLinks.length} product-vendor links with pricing`);
    }

    console.log('Database seeding completed successfully');
    console.log(`Inserted ${insertedVendors?.length || 0} vendors and ${insertedProducts?.length || 0} products`);
    
    return true;
  } catch (error) {
    console.error('Database seeding failed:', error);
    return false;
  }
};

// Function to add missing product-vendor links to existing data
export const addMissingProductVendorLinks = async (): Promise<boolean> => {
  try {
    console.log('Adding missing product-vendor links...');
    
    // Get existing products and vendors
    const { data: products, error: productError } = await supabase
      .schema('mission_fresh')
      .from('smokeless_products')
      .select('*')
      .order('created_at', { ascending: true });
      
    if (productError) {
      console.error('Error fetching products:', productError);
      return false;
    }
    
    const { data: vendors, error: vendorError } = await supabase
      .schema('mission_fresh')
      .from('smokeless_vendors')
      .select('*')
      .order('created_at', { ascending: true });
      
    if (vendorError) {
      console.error('Error fetching vendors:', vendorError);
      return false;
    }
    
    if (!products || products.length === 0 || !vendors || vendors.length === 0) {
      console.error('No products or vendors found');
      return false;
    }
    
    // Create product-vendor links with pricing
    const productVendorLinks = [
      // ZYN Nicotine Pouches links (assuming first product is ZYN)
      {
        product_id: products[0].id,
        vendor_id: vendors[0].id,
        price: 8.99,
        product_url_on_vendor_site: 'https://nrtdirect.com/zyn-mint-pouches',
        is_available: true
      },
      {
        product_id: products[0].id,
        vendor_id: vendors[1].id,
        price: 9.49,
        product_url_on_vendor_site: 'https://quitsmartpharmacy.com/zyn-mint',
        is_available: true
      },
      // Add links for other products if they exist
      ...(products.length > 1 ? [
        {
          product_id: products[1].id,
          vendor_id: vendors[0].id,
          price: 12.99,
          product_url_on_vendor_site: 'https://nrtdirect.com/nicorette-cinnamon-gum',
          is_available: true
        },
        {
          product_id: products[1].id,
          vendor_id: vendors[2] ? vendors[2].id : vendors[0].id,
          price: 13.49,
          product_url_on_vendor_site: 'https://wellnesshubstore.com/nicorette-gum',
          is_available: true
        }
      ] : []),
      ...(products.length > 2 ? [
        {
          product_id: products[2].id,
          vendor_id: vendors[1].id,
          price: 11.99,
          product_url_on_vendor_site: 'https://quitsmartpharmacy.com/commit-lozenges',
          is_available: true
        },
        {
          product_id: products[2].id,
          vendor_id: vendors[2] ? vendors[2].id : vendors[0].id,
          price: 11.49,
          product_url_on_vendor_site: 'https://wellnesshubstore.com/commit-fruit-lozenges',
          is_available: false
        }
      ] : []),
      ...(products.length > 3 ? [
        {
          product_id: products[3].id,
          vendor_id: vendors[0].id,
          price: 9.99,
          product_url_on_vendor_site: 'https://nrtdirect.com/velo-citrus-pouches',
          is_available: true
        },
        {
          product_id: products[3].id,
          vendor_id: vendors[2] ? vendors[2].id : vendors[1].id,
          price: 10.49,
          product_url_on_vendor_site: 'https://wellnesshubstore.com/velo-citrus',
          is_available: true
        }
      ] : [])
    ];

    const { error: linkError } = await supabase
      .schema('mission_fresh')
      .from('smokeless_product_vendors')
      .insert(productVendorLinks);

    if (linkError) {
      console.error('Error creating product-vendor links:', linkError);
      return false;
    }

    console.log(`Successfully created ${productVendorLinks.length} product-vendor links`);
    return true;
  } catch (error) {
    console.error('Error adding product-vendor links:', error);
    return false;
  }
};

// Combined cleanup and reseed function
export const fixDatabaseCorruption = async (): Promise<boolean> => {
  try {
    console.log('Starting database corruption fix...');
    
    // Step 1: Clean corrupted data
    const cleanupSuccess = await cleanupCorruptedData();
    if (!cleanupSuccess) {
      throw new Error('Database cleanup failed');
    }
    
    // Step 2: Reseed with correct data
    const seedSuccess = await seedProductsAndVendors();
    if (!seedSuccess) {
      throw new Error('Database seeding failed');
    }
    
    console.log('Database corruption fix completed successfully');
    return true;
  } catch (error) {
    console.error('Database corruption fix failed:', error);
    return false;
  }
};
