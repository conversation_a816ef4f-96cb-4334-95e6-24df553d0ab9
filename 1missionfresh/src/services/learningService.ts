import { supabase } from '../lib/supabase';
import type { Tables } from '../lib/database.types';

// Define the learning module type using the proper schema-aware type
export type LearningModule = Tables<{ schema: 'mission_fresh' }, 'learning_modules'>;

// Create a schema-specific client for mission_fresh
const missionFreshClient = supabase.schema('mission_fresh');

export const learningService = {
  // Get all learning modules using the database function
  async getAllModules(): Promise<LearningModule[]> {
    try {
      console.log('🔄 Learning service: Starting to fetch modules...');
      
      const { data, error } = await (supabase as any)
        .rpc('get_learning_modules');
      
      console.log('🔍 Learning service: RPC response:', { data, error });
      
      if (error) {
        console.error('❌ Error fetching learning modules:', error);
        throw error;
      }

      console.log('✅ Learning service: Successfully fetched', data?.length || 0, 'modules');
      console.log('📋 Learning service: First module:', data?.[0]);
      return data || [];
    } catch (err) {
      console.error('💥 Learning service error:', err);
      throw err;
    }
  },

  // Get a specific learning module by ID
  async getModuleById(id: string): Promise<LearningModule | null> {
    try {
      const { data, error } = await (supabase as any)
        .rpc('get_learning_module_by_id', { module_id: id });

      if (error) {
        console.error('Error fetching learning module:', error);
        throw error;
      }

      return data && data.length > 0 ? data[0] : null;
    } catch (err) {
      console.error('Learning service error:', err);
      throw err;
    }
  },

  // Get learning modules by category
  async getModulesByCategory(category: string): Promise<LearningModule[]> {
    try {
      const { data, error } = await (supabase as any)
        .rpc('get_learning_modules_by_category', { module_category: category });

      if (error) {
        console.error('Error fetching learning modules by category:', error);
        throw error;
      }

      return data || [];
    } catch (err) {
      console.error('Learning service error:', err);
      throw err;
    }
  },

  // Mark a module as completed for a user
  async markModuleCompleted(moduleId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await missionFreshClient
        .from('user_learning_progress')
        .upsert({
          user_id: userId,
          module_id: moduleId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error marking module as completed:', error);
        return false;
      }
      
      console.log('✅ Module marked as completed successfully');
      return true;
    } catch (err) {
      console.error('Learning service error:', err);
      return false;
    }
  },

  // Get user's learning progress
  async getUserProgress(userId: string): Promise<string[]> {
    try {
      const { data, error } = await missionFreshClient
        .from('user_learning_progress')
        .select('module_id')
        .eq('user_id', userId);

      if (error) {
        console.error('Error fetching user learning progress:', error);
        throw error;
      }

      return data ? data.map((row: any) => row.module_id) : [];
    } catch (err) {
      console.error('Learning service error:', err);
      throw err;
    }
  }
};

// Export individual functions for backward compatibility
export const fetchLearningModules = learningService.getAllModules;
export const getUserLearningProgress = learningService.getUserProgress;
export const markModuleAsComplete = learningService.markModuleCompleted;

// Export function to get module by slug
export const fetchLearningModuleBySlug = async (slug: string): Promise<LearningModule | null> => {
  try {
    const { data, error } = await (supabase as any)
      .rpc('get_learning_module_by_slug', { module_slug: slug });

    if (error) {
      console.error('Error fetching learning module by slug:', error);
      throw error;
    }

    return data && data.length > 0 ? data[0] : null;
  } catch (err) {
    console.error('Learning service error:', err);
    throw err;
  }
};
