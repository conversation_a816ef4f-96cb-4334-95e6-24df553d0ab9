import { supabase } from "@/lib/supabase";
import { Database } from "@/lib/database.types";
import { toast } from "sonner";

// Define UserGoal type based on the public schema (where the data actually exists)
export type UserGoal = Database['public']['Tables']['user_goals']['Row'];
export type UserGoalInsert = Database['public']['Tables']['user_goals']['Insert'];
export type UserGoalUpdate = Database['public']['Tables']['user_goals']['Update'];
export type GoalMilestone = Database['public']['Tables']['goal_milestones']['Row'];
export type GoalMilestoneInsert = Database['public']['Tables']['goal_milestones']['Insert'];
export type GoalMilestoneUpdate = Database['public']['Tables']['goal_milestones']['Update'];


/**
 * Get the current active goal for the authenticated user
 */
export const getUserGoal = async (): Promise<UserGoal | null> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) return null;

  const { data, error } = await supabase
    .from('user_goals')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })
    .limit(1)
    .maybeSingle();

  if (error) {
    console.error('Error fetching user goal:', error);
    throw error;
  }

  return data;
};

/**
 * Get all goals for the authenticated user
 */
export const getUserGoals = async (): Promise<UserGoal[]> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) return [];

  const { data, error } = await supabase
    .from('user_goals')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching goals', error);
    throw error;
  }

  return data || [];
};

/**
 * Get a specific goal by ID for the authenticated user
 */
export const getGoalById = async (goalId: string): Promise<UserGoal> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  const { data, error } = await supabase
    .from('user_goals')
    .select('*')
    .eq('id', goalId)
    .eq('user_id', user.id)
    .single();

  if (error) {
    console.error('Error fetching goal by ID:', error);
    throw error;
  }

  return data;
};

/**
 * Add a new goal for the authenticated user
 */
export const saveUserGoal = async (goal: Omit<UserGoalInsert, 'id' | 'created_at' | 'updated_at' | 'user_id'>, userId?: string): Promise<UserGoal> => {
  try {
    let finalUserId: string;
    if (userId) {
      finalUserId = userId;
    } else {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");
      finalUserId = user.id;
    }

    const goalToInsert: UserGoalInsert = {
      ...goal,
      user_id: finalUserId,
    };

    const { data, error } = await supabase
      .from('user_goals')
      .insert(goalToInsert)
      .select()
      .single();

    if (error) {
      console.error('Error adding goal:', error);
      throw error;
    }

    return data;
  } catch (error: unknown) {
    toast.error((error as Error).message || "Failed to save goal");
    throw error;
  }
};

/**
 * Update an existing goal for the authenticated user
 */
export const updateUserGoal = async (goalId: string, updates: UserGoalUpdate, userId?: string): Promise<UserGoal | null> => {
  try {
    let finalUserId: string;
    if (userId) {
      finalUserId = userId;
    } else {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");
      finalUserId = user.id;
    }
    
    const { data, error } = await supabase
      .from('user_goals')
      .update(updates)
      .eq('id', goalId)
      .eq('user_id', finalUserId)
      .select()
      .single();

    if (error) {
      console.error('Error updating user goal:', error);
      throw error;
    }
    
    return data;
  } catch (error: unknown) {
    console.error('Error in updateUserGoal:', error);
    toast.error((error as Error).message || "Failed to update goal");
    return null;
  }
};

/**
 * Delete a goal for the authenticated user
 */
export const deleteUserGoal = async (goalId: string, userId?: string): Promise<boolean> => {
  try {
    let finalUserId: string;
    if (userId) {
      finalUserId = userId;
    } else {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");
      finalUserId = user.id;
    }

    const { error } = await supabase
      .from('user_goals')
      .delete()
      .eq('id', goalId)
      .eq('user_id', finalUserId);
    
    if (error) throw error;
    
    toast.success("Goal deleted successfully!");
    return true;
  } catch (error: unknown) {
    console.error('Error deleting goal:', error);
    toast.error((error as Error).message || "Failed to delete goal");
    throw error;
  }
};

// --- Goal Milestones ---

/**
 * Get all milestones for a specific goal
 */
export const getGoalMilestones = async (goalId: string): Promise<GoalMilestone[]> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  // First, verify the user owns the parent goal
  const { data: parentGoal, error: parentGoalError } = await supabase
    .from('user_goals')
    .select('id, user_id')
    .eq('id', goalId)
    .eq('user_id', user.id)
    .single();

  if (parentGoalError || !parentGoal) {
    console.error('Error fetching parent goal or goal not found/owned by user:', parentGoalError);
    throw new Error("Parent goal not found or user does not have access.");
  }

  const { data, error } = await supabase
    .from('goal_milestones')
    .select('*')
    .eq('goal_id', goalId)
    .order('created_at', { ascending: true });

  if (error) {
    console.error('Error fetching goal milestones:', error);
    throw error;
  }

  return data || [];
};

/**
 * Add a new milestone to a goal
 */
export const addGoalMilestone = async (milestoneData: Omit<GoalMilestoneInsert, 'id' | 'created_at' | 'updated_at' | 'user_id'>): Promise<GoalMilestone | null> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  // Verify the user owns the parent goal
  const { data: parentGoal, error: parentGoalError } = await supabase
    .from('user_goals')
    .select('id')
    .eq('id', milestoneData.goal_id)
    .eq('user_id', user.id)
    .single();

  if (parentGoalError || !parentGoal) {
    toast.error("Cannot add milestone: Parent goal not found or not owned by user.");
    console.error('Error verifying parent goal ownership for adding milestone:', parentGoalError);
    return null;
  }

  try {
    const milestoneToInsert: GoalMilestoneInsert = {
        ...milestoneData,
        user_id: user.id,
    }
    const { data, error } = await supabase
      .from('goal_milestones')
      .insert(milestoneToInsert)
      .select()
      .single();

    if (error) throw error;
    toast.success("Milestone added successfully!");
    return data;
  } catch (error: unknown) {
    console.error('Error adding milestone:', error);
    toast.error((error as Error).message || "Failed to add milestone.");
    return null;
  }
};

/**
 * Update an existing milestone
 */
export const updateGoalMilestone = async (milestoneId: string, updates: Partial<Omit<GoalMilestoneUpdate, 'id' | 'created_at' | 'goal_id' | 'user_id'>>): Promise<GoalMilestone | null> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  try {
    // Fetch the milestone to get its goal_id
    const { data: milestone, error: fetchError } = await supabase
      .from('goal_milestones')
      .select('goal_id')
      .eq('id', milestoneId)
      .single();

    if (fetchError || !milestone) {
      toast.error("Milestone not found.");
      console.error('Error fetching milestone for update:', fetchError);
      return null;
    }

    // Verify the user owns the parent goal
    const { data: parentGoal, error: parentGoalError } = await supabase
      .from('user_goals')
      .select('id')
      .eq('id', milestone.goal_id)
      .eq('user_id', user.id)
      .single();

    if (parentGoalError || !parentGoal) {
      toast.error("Cannot update milestone: Parent goal not found or not owned by user.");
      console.error('Error verifying parent goal ownership for updating milestone:', parentGoalError);
      return null;
    }

    const { data, error } = await supabase
      .from('goal_milestones')
      .update(updates)
      .eq('id', milestoneId)
      .select()
      .single();

    if (error) throw error;
    toast.success("Milestone updated successfully!");
    return data;
  } catch (error: unknown) {
    console.error('Error updating milestone:', error);
    toast.error((error as Error).message || "Failed to update milestone.");
    return null;
  }
};

/**
 * Delete a milestone
 */
export const deleteGoalMilestone = async (milestoneId: string): Promise<boolean> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  try {
    // Fetch the milestone to get its goal_id
    const { data: milestone, error: fetchError } = await supabase
      .from('goal_milestones')
      .select('goal_id')
      .eq('id', milestoneId)
      .single();

    if (fetchError || !milestone) {
      toast.error("Milestone not found.");
      console.error('Error fetching milestone for deletion:', fetchError);
      return false;
    }

    // Verify the user owns the parent goal
    const { data: parentGoal, error: parentGoalError } = await supabase
      .from('user_goals')
      .select('id')
      .eq('id', milestone.goal_id)
      .eq('user_id', user.id)
      .single();

    if (parentGoalError || !parentGoal) {
      toast.error("Cannot delete milestone: Parent goal not found or not owned by user.");
      console.error('Error verifying parent goal ownership for deleting milestone:', parentGoalError);
      return false;
    }

    const { error } = await supabase
      .from('goal_milestones')
      .delete()
      .eq('id', milestoneId);

    if (error) throw error;
    toast.success("Milestone deleted successfully!");
    return true;
  } catch (error: unknown) {
    console.error('Error deleting milestone:', error);
    toast.error((error as Error).message || "Failed to delete milestone.");
    return false;
  }
};
