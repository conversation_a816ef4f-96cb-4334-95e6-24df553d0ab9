// src/services/aiService.ts

// This service handles AI-related functionalities, such as generating text for sharing.

// This service will now call our own Supabase Edge Function, which then calls the AI service.
// This keeps the AI API key secure on the server-side.

const GET_GEMINI_SUGGESTIONS_URL = '/functions/v1/get-gemini-suggestions';

/**
 * Generates personalized text for sharing a badge using AI.
 * @param badgeName The name of the earned badge.
 * @param badgeDescription The description of the earned badge.
 * @param userName The name of the user (optional, use cautiously for privacy).
 * @returns A promise that resolves with the generated shareable text.
 */
export const generateShareTextForBadge = async (
  badgeName: string,
  badgeDescription: string,
  userName: string = 'a user' // Default to 'a user' for privacy if name is not available
): Promise<string> => {
  // Construct a prompt for the AI
  const prompt = \`Generate a short, encouraging social media post (under 280 characters) for \${userName} who just earned the "\${badgeName}" badge in a quit smoking app. The badge description is: "\${badgeDescription}". Focus on the achievement and motivation. Do not include hashtags.\`;
  const fallbackText = \`Just unlocked the "\${badgeName}" badge! "\${badgeDescription}". Feeling motivated!\`;

  try {
    const response = await fetch(GET_GEMINI_SUGGESTIONS_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Authorization header might be needed if your Edge Function is protected
        // 'Authorization': \`Bearer \${your_auth_token_if_needed}\`
      },
      body: JSON.stringify({
        prompt: prompt,
        max_tokens: 60, // Adjusted for a short social media post
        temperature: 0.7
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: "Failed to parse error response" }));
      console.error('Error from get-gemini-suggestions Edge Function (badge):', response.status, errorData);
      throw new Error(errorData.error || \`AI service request failed with status \${response.status}\`);
    }

    const data = await response.json();
    const generatedText = data?.suggestion || fallbackText;

    return generatedText.substring(0, 280);

  } catch (error) {
    console.error('Error calling get-gemini-suggestions Edge Function (badge):', error);
    return fallbackText;
  }
};

/**
 * Generates personalized text for sharing progress using AI.
 * @param daysQuit The number of days the user has quit.
 * @param moneySaved The amount of money saved.
 * @param unitsNotUsed The number of units (e.g., cigarettes) not used.
 * @param userName The name of the user (optional, use cautiously for privacy).
 * @returns A promise that resolves with the generated shareable text.
 */
export const generateShareTextForProgress = async (
  daysQuit: number,
  moneySaved: number,
  unitsNotUsed: number,
  userName: string = 'a user' // Default to 'a user' for privacy if name is not available
): Promise<string> => {
  // Construct a prompt for the AI
  const prompt = \`Generate a short, encouraging social media post (under 280 characters) for \${userName} who has quit smoking for \${daysQuit} days, saved $\${moneySaved.toFixed(2)}, and avoided using \${unitsNotUsed} units (like cigarettes) in a quit smoking app. Focus on the achievement and motivation. Do not include hashtags.\`;
  const fallbackText = \`I've quit for \${daysQuit} days, saved $\${moneySaved.toFixed(2)}, and avoided \${unitsNotUsed} units! Feeling great!\`;

  try {
    const response = await fetch(GET_GEMINI_SUGGESTIONS_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Authorization header might be needed if your Edge Function is protected
        // 'Authorization': \`Bearer \${your_auth_token_if_needed}\`
      },
      body: JSON.stringify({
        prompt: prompt,
        max_tokens: 70, // Adjusted for a short social media post
        temperature: 0.7
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: "Failed to parse error response" }));
      console.error('Error from get-gemini-suggestions Edge Function (progress):', response.status, errorData);
      throw new Error(errorData.error || \`AI service request failed with status \${response.status}\`);
    }

    const data = await response.json();
    const generatedText = data?.suggestion || fallbackText;

    return generatedText.substring(0, 280);

  } catch (error) {
    console.error('Error calling get-gemini-suggestions Edge Function (progress):', error);
    return fallbackText;
  }
};

/**
 * Gets a response from the AI coach based on user input and conversation history.
 * @param userMessage The user's current message.
 * @param conversationHistory An array of previous messages in the conversation.
 * @returns A promise that resolves with the AI coach's response string.
 */
export const getAICoachResponse = async (
  userMessage: string,
  conversationHistory?: Array<{ role: string; content: string }>; // Matched to Gemini's expected format
): Promise<string> => {
  let prompt = \`You are "FreshAI", a friendly, empathetic, and highly knowledgeable AI quit smoking coach. Your goal is to support users in their journey to quit nicotine. Be encouraging, provide practical advice, and help users explore their feelings and triggers. Keep responses concise and actionable. Avoid making medical claims or giving medical advice; instead, suggest users consult healthcare professionals for medical concerns.

Current user message:
"\${userMessage}"

Conversation context (if any):\`;

  if (conversationHistory &&&& conversationHistory.length > 0) {
    // Summarize or append relevant parts of history. For now, let's take the last few turns.
    const historySnippet = conversationHistory
      .slice(-4) // Take last 4 turns (2 user, 2 AI)
      .map(msg => \`\${msg.role === 'user' ? 'User' : 'Coach'}: \${msg.content}\`)
      .join('\\n');
    prompt += \`\\n\${historySnippet}\`;
  } else {
    prompt += "\\nNo previous conversation context for this session.";
  }

  const fallbackText = "I'm here to help! Could you tell me a bit more about what's on your mind regarding your quit journey?";

  try {
    const response = await fetch(GET_GEMINI_SUGGESTIONS_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: prompt,
        max_tokens: 250, // Allow for more detailed coach responses
        temperature: 0.75, // Slightly more creative but still grounded
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: "Failed to parse error response from AI coach" }));
      console.error('Error from get-gemini-suggestions Edge Function (AI Coach):', response.status, errorData);
      throw new Error(errorData.error || \`AI coach service request failed with status \${response.status}\`);
    }

    const data = await response.json();
    return data?.suggestion || fallbackText;

  } catch (error) {
    console.error('Error calling get-gemini-suggestions Edge Function (AI Coach):', error);
    return "I'm having a little trouble thinking right now. Please try asking again in a moment.";
  }
};
// Add other AI related functions here in the future
