import { supabase } from '@/lib/supabase';
import { Database } from '@/lib/database.types';

export type RelapseInsert = Database['mission_fresh']['Tables']['relapses']['Insert'];
export type Relapse = Database['mission_fresh']['Tables']['relapses']['Row'];

export type Trigger = Database['mission_fresh']['Tables']['triggers']['Row'];
export type TriggerInsert = Database['mission_fresh']['Tables']['triggers']['Insert'];

export type CopingStrategy = Database['mission_fresh']['Tables']['coping_strategies']['Row'];
export type CopingStrategyInsert = Database['mission_fresh']['Tables']['coping_strategies']['Insert'];

export type UserTriggerStrategyMap = Database['mission_fresh']['Tables']['user_trigger_strategy_map']['Row'];
export type UserTriggerStrategyMapInsert = Database['mission_fresh']['Tables']['user_trigger_strategy_map']['Insert'];

/**
 * Records a new relapse event for the currently authenticated user.
 * @param relapseData - The details of the relapse event.
 * @returns The newly created relapse record.
 * @throws If there is an error inserting the data or if no user is authenticated.
 */
export const addRelapse = async (relapseData: Omit<RelapseInsert, 'user_id' | 'id' | 'created_at'>): Promise<Relapse> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User must be authenticated to report a relapse.');
  }

  const { data, error } = await supabase
    .schema('mission_fresh')
    .from('relapses')
    .insert({ ...relapseData, user_id: user.id })
    .select()
    .single();

  if (error) {
    console.error('Error adding relapse:', error);
    throw new Error(`Failed to add relapse: ${error.message}`);
  }

  if (!data) {
    throw new Error('Failed to add relapse, no data returned.');
  }

  return data;
};

/**
 * Fetches all global triggers and triggers specific to the authenticated user.
 * @returns An array of trigger records.
 * @throws If there is an error fetching the data or if no user is authenticated.
 */
export const getTriggers = async (): Promise<Trigger[]> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User must be authenticated to fetch triggers.');
  }

  const { data, error } = await supabase
    .schema('mission_fresh')
    .from('triggers')
    .select('*')
    .or(`user_id.eq.${user.id},is_global.eq.true`)
    .order('name', { ascending: true });

  if (error) {
    console.error('Error fetching triggers:', error);
    throw new Error(`Failed to fetch triggers: ${error.message}`);
  }

  return data || [];
};

/**
 * Adds a new custom trigger for the authenticated user.
 * @param triggerData - The data for the new trigger (name and optional description).
 * @returns The newly created trigger record.
 * @throws If there is an error inserting the data or if no user is authenticated.
 */
export const addTrigger = async (triggerData: Omit<TriggerInsert, 'id' | 'user_id' | 'created_at' | 'is_global' | 'description'> & { description?: string | null }): Promise<Trigger> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User must be authenticated to add a trigger.');
  }

  const newTriggerData: TriggerInsert = {
    ...triggerData,
    user_id: user.id,
    is_global: false,
  };

  const { data, error } = await supabase
    .schema('mission_fresh')
    .from('triggers')
    .insert(newTriggerData)
    .select()
    .single();

  if (error) {
    console.error('Error adding trigger:', error);
    throw new Error(`Failed to add trigger: ${error.message}`);
  }

  if (!data) {
    throw new Error('Failed to add trigger, no data returned.');
  }

  return data;
};

/**
 * Updates an existing custom trigger for the authenticated user.
 * Users can only update their own non-global triggers.
 * @param triggerId - The ID of the trigger to update.
 * @param triggerData - The data to update (name and/or description).
 * @returns The updated trigger record.
 * @throws If there is an error, if no user is authenticated, or if the trigger is not found/not owned by the user/is global.
 */
export const updateTrigger = async (triggerId: string, triggerData: Partial<Omit<TriggerInsert, 'id' | 'user_id' | 'created_at' | 'is_global'>>): Promise<Trigger> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User must be authenticated to update a trigger.');
  }

  const { data: existingTrigger, error: fetchError } = await supabase
    .schema('mission_fresh')
    .from('triggers')
    .select('id, user_id, is_global')
    .eq('id', triggerId)
    .eq('user_id', user.id)
    .single();

  if (fetchError || !existingTrigger) {
    console.error('Error fetching trigger for update or trigger not found:', fetchError);
    throw new Error('Trigger not found or you do not have permission to update it.');
  }

  if (existingTrigger.is_global) {
    throw new Error('Global triggers cannot be modified by users.');
  }

  const { data, error } = await supabase
    .schema('mission_fresh')
    .from('triggers')
    .update(triggerData)
    .eq('id', triggerId)
    .select()
    .single();

  if (error) {
    console.error('Error updating trigger:', error);
    throw new Error(`Failed to update trigger: ${error.message}`);
  }

  if (!data) {
    throw new Error('Failed to update trigger, no data returned.');
  }

  return data;
};

/**
 * Deletes a custom trigger for the authenticated user.
 * Also deletes any associations in user_trigger_strategy_map.
 * Users can only delete their own non-global triggers.
 * @param triggerId - The ID of the trigger to delete.
 * @returns void
 * @throws If there is an error, if no user is authenticated, or if the trigger is not found/not owned by the user/is global.
 */
export const deleteTrigger = async (triggerId: string): Promise<void> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User must be authenticated to delete a trigger.');
  }

  const { data: existingTrigger, error: fetchError } = await supabase
    .schema('mission_fresh')
    .from('triggers')
    .select('id, user_id, is_global')
    .eq('id', triggerId)
    .eq('user_id', user.id)
    .single();

  if (fetchError || !existingTrigger) {
    console.error('Error fetching trigger for deletion or trigger not found:', fetchError);
    throw new Error('Trigger not found or you do not have permission to delete it.');
  }

  if (existingTrigger.is_global) {
    throw new Error('Global triggers cannot be deleted by users.');
  }

  const { error: mapDeleteError } = await supabase
    .schema('mission_fresh')
    .from('user_trigger_strategy_map')
    .delete()
    .eq('user_id', user.id)
    .eq('trigger_id', triggerId);

  if (mapDeleteError) {
    console.error('Error deleting trigger strategy mappings:', mapDeleteError);
    throw new Error(`Failed to delete trigger strategy mappings: ${mapDeleteError.message}`);
  }

  const { error: triggerDeleteError } = await supabase
    .schema('mission_fresh')
    .from('triggers')
    .delete()
    .eq('id', triggerId);

  if (triggerDeleteError) {
    console.error('Error deleting trigger:', triggerDeleteError);
    throw new Error(`Failed to delete trigger: ${triggerDeleteError.message}`);
  }
};

/**
 * Fetches all global coping strategies and strategies specific to the authenticated user.
 * @returns An array of coping strategy records.
 * @throws If there is an error fetching the data or if no user is authenticated.
 */
export const getCopingStrategies = async (): Promise<CopingStrategy[]> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User must be authenticated to fetch coping strategies.');
  }

  const { data, error } = await supabase
    .schema('mission_fresh')
    .from('coping_strategies')
    .select('*')
    .or(`user_id.eq.${user.id},is_global.eq.true`)
    .order('name', { ascending: true });

  if (error) {
    console.error('Error fetching coping strategies:', error);
    throw new Error(`Failed to fetch coping strategies: ${error.message}`);
  }

  return data || [];
};

/**
 * Adds a new custom coping strategy for the authenticated user.
 * @param strategyData - The data for the new strategy (name and optional description).
 * @returns The newly created coping strategy record.
 * @throws If there is an error inserting the data or if no user is authenticated.
 */
export const addCopingStrategy = async (strategyData: Omit<CopingStrategyInsert, 'id' | 'user_id' | 'created_at' | 'is_global' | 'description'> & { description?: string | null }): Promise<CopingStrategy> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User must be authenticated to add a coping strategy.');
  }

  const newStrategyData: CopingStrategyInsert = {
    ...strategyData,
    user_id: user.id,
    is_global: false,
  };

  const { data, error } = await supabase
    .schema('mission_fresh')
    .from('coping_strategies')
    .insert(newStrategyData)
    .select()
    .single();

  if (error) {
    console.error('Error adding coping strategy:', error);
    throw new Error(`Failed to add coping strategy: ${error.message}`);
  }

  if (!data) {
    throw new Error('Failed to add coping strategy, no data returned.');
  }

  return data;
};

/**
 * Updates an existing custom coping strategy for the authenticated user.
 * Users can only update their own non-global strategies.
 * @param strategyId - The ID of the coping strategy to update.
 * @param strategyData - The data to update (name and/or description).
 * @returns The updated coping strategy record.
 * @throws If there is an error, if no user is authenticated, or if the strategy is not found/not owned by the user/is global.
 */
export const updateCopingStrategy = async (strategyId: string, strategyData: Partial<Omit<CopingStrategyInsert, 'id' | 'user_id' | 'created_at' | 'is_global'>>): Promise<CopingStrategy> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User must be authenticated to update a coping strategy.');
  }

  const { data: existingStrategy, error: fetchError } = await supabase
    .schema('mission_fresh')
    .from('coping_strategies')
    .select('id, user_id, is_global')
    .eq('id', strategyId)
    .eq('user_id', user.id)
    .single();

  if (fetchError || !existingStrategy) {
    console.error('Error fetching coping strategy for update or strategy not found:', fetchError);
    throw new Error('Coping strategy not found or you do not have permission to update it.');
  }

  if (existingStrategy.is_global) {
    throw new Error('Global coping strategies cannot be modified by users.');
  }

  const { data, error } = await supabase
    .schema('mission_fresh')
    .from('coping_strategies')
    .update(strategyData)
    .eq('id', strategyId)
    .select()
    .single();

  if (error) {
    console.error('Error updating coping strategy:', error);
    throw new Error(`Failed to update coping strategy: ${error.message}`);
  }

  if (!data) {
    throw new Error('Failed to update coping strategy, no data returned.');
  }

  return data;
};

/**
 * Deletes a custom coping strategy for the authenticated user.
 * Also deletes any associations in user_trigger_strategy_map.
 * Users can only delete their own non-global strategies.
 * @param strategyId - The ID of the coping strategy to delete.
 * @returns void
 * @throws If there is an error, if no user is authenticated, or if the strategy is not found/not owned by the user/is global.
 */
export const deleteCopingStrategy = async (strategyId: string): Promise<void> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User must be authenticated to delete a coping strategy.');
  }

  const { data: existingStrategy, error: fetchError } = await supabase
    .schema('mission_fresh')
    .from('coping_strategies')
    .select('id, user_id, is_global')
    .eq('id', strategyId)
    .eq('user_id', user.id)
    .single();

  if (fetchError || !existingStrategy) {
    console.error('Error fetching coping strategy for deletion or strategy not found:', fetchError);
    throw new Error('Coping strategy not found or you do not have permission to delete it.');
  }

  if (existingStrategy.is_global) {
    throw new Error('Global coping strategies cannot be deleted by users.');
  }

  const { error: mapDeleteError } = await supabase
    .schema('mission_fresh')
    .from('user_trigger_strategy_map')
    .delete()
    .eq('user_id', user.id)
    .eq('strategy_id', strategyId);

  if (mapDeleteError) {
    console.error('Error deleting trigger strategy mappings for coping strategy:', mapDeleteError);
    throw new Error(`Failed to delete trigger strategy mappings for coping strategy: ${mapDeleteError.message}`);
  }

  const { error: strategyDeleteError } = await supabase
    .schema('mission_fresh')
    .from('coping_strategies')
    .delete()
    .eq('id', strategyId);

  if (strategyDeleteError) {
    console.error('Error deleting coping strategy:', strategyDeleteError);
    throw new Error(`Failed to delete coping strategy: ${strategyDeleteError.message}`);
  }
};

/**
 * Links a coping strategy to a trigger for the authenticated user.
 * @param triggerId - The ID of the trigger.
 * @param strategyId - The ID of the coping strategy.
 * @returns The newly created mapping record.
 * @throws If there is an error or if no user is authenticated.
 */
export const linkTriggerToStrategy = async (triggerId: string, strategyId: string): Promise<UserTriggerStrategyMap> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User must be authenticated to link trigger to strategy.');
  }

  const newMappingData: UserTriggerStrategyMapInsert = {
    user_id: user.id,
    trigger_id: triggerId,
    strategy_id: strategyId,
  };

  const { data, error } = await supabase
    .schema('mission_fresh')
    .from('user_trigger_strategy_map')
    .insert(newMappingData)
    .select()
    .single();

  if (error) {
    console.error('Error linking trigger to strategy:', error);
    throw new Error(`Failed to link trigger to strategy: ${error.message}`);
  }

  if (!data) {
    throw new Error('Failed to link trigger to strategy, no data returned.');
  }

  return data;
};

/**
 * Fetches the relapse history for the currently authenticated user.
 * @returns An array of relapse records, ordered by relapse date descending.
 * @throws If there is an error fetching the data or if no user is authenticated.
 */
export const getRelapses = async (): Promise<Relapse[]> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User must be authenticated to view relapse history.');
  }

  const { data, error } = await supabase
    .schema('mission_fresh')
    .from('relapses')
    .select('*')
    .eq('user_id', user.id)
    .order('relapse_at', { ascending: false });

  if (error) {
    console.error('Error fetching relapses:', error);
    throw new Error(`Failed to fetch relapses: ${error.message}`);
  }

  return data || [];
};

/**
 * Fetches the most recent relapse for the currently authenticated user.
 * @returns The most recent relapse record, or null if none exist.
 * @throws If there is an error fetching the data or if no user is authenticated.
 */
export const getMostRecentRelapse = async (): Promise<Relapse | null> => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User must be authenticated to view relapse history.');
    }
  
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('relapses')
      .select('*')
      .eq('user_id', user.id)
      .order('relapse_at', { ascending: false })
      .limit(1)
      .maybeSingle(); 
  
    if (error) {
      console.error('Error fetching most recent relapse:', error);
      throw new Error(`Failed to fetch most recent relapse: ${error.message}`);
    }
  
    return data;
  };
