import { supabase } from "@/lib/supabase"; // Corrected import path

export type Quote = {
  id: string;
  text: string;
  author: string;
};

/**
 * Fetch a random motivational quote
 * PRODUCTION HEALTH APP: NO FAKE FALLBACKS - return null if database is unavailable
 */
export const getRandomQuote = async (): Promise<Quote | null> => {
  try {
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('quotes')
      .select('id, text, author');

    if (error || !data || data.length === 0) {
      console.warn("Failed to fetch quotes from DB or no quotes found. No fallback - returning null for production app:", error);
      return null;
    }

    // Pick a random quote from the fetched data
    const randomIndex = Math.floor(Math.random() * data.length);
    const randomQuoteFromDb = data[randomIndex];

    // Ensure the selected quote has the required fields
    if (!randomQuoteFromDb || !randomQuoteFromDb.id || !randomQuoteFromDb.text || !randomQuoteFromDb.author) {
        console.warn("Incomplete quote data from DB. No fallback - returning null for production app:", randomQuoteFromDb);
        return null;
    }
    
    return {
      id: randomQuoteFromDb.id,
      text: randomQuoteFromDb.text,
      author: randomQuoteFromDb.author,
    };

  } catch (error) {
    console.error("Error fetching random quote via client query. No fallback - returning null for production app:", error);
    return null;
  }
};
