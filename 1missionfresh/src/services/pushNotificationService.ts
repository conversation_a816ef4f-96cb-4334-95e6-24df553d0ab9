import { supabase } from '@/lib/supabase';

export const savePushSubscription = async (
  tokenOrSubscription: string | PushSubscription,
  platform: 'web' | 'ios' | 'android' | string
) => {
  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.error('No authenticated user found.');
      return { success: false, error: 'No authenticated user found.' };
    }

    let tokenToStore: string;
    if (platform === 'web' && typeof tokenOrSubscription === 'object') {
      tokenToStore = JSON.stringify(tokenOrSubscription);
    } else if (typeof tokenOrSubscription === 'string') {
      tokenToStore = tokenOrSubscription;
    } else {
      console.error('Invalid tokenOrSubscription type for the given platform.');
      return { success: false, error: 'Invalid token or subscription format for the platform.' };
    }

    // Save the new subscription, upserting based on the token to handle existing subscriptions
    const { error: saveError } = await supabase
      .schema('mission_fresh')
      .from('push_tokens')
      .upsert(
        {
          user_id: user.id,
          token: tokenToStore,
          platform: platform,
        },
        { onConflict: 'token' } // Use token as the conflict key
      );

    if (saveError) {
      console.error('Error saving push subscription:', saveError);
      return { success: false, error: saveError.message || 'Unknown error saving push subscription' };
    }

    console.log('Push subscription saved successfully.');
    return { success: true, message: 'Push subscription saved successfully.' };
  } catch (error: any) {
    console.error('Error in savePushSubscription:', error);
    return { success: false, error: error.message || 'Unknown error in savePushSubscription' };
  }
};

// Placeholder function to schedule a daily push notification
// Actual implementation needs to be done on the backend (e.g., in a Supabase Edge Function or a separate service)
export const scheduleDailyPushNotification = async (id: string, time: string, title: string, body: string): Promise<void> => {
  console.log(`[PushNotificationService] Attempting to schedule daily notification with ID: ${id} at ${time} - Title: ${title}, Body: ${body}`);
  console.warn("[PushNotificationService] Backend implementation required for scheduling daily push notifications.");
  // TODO: Implement backend logic to schedule recurring notifications
};

// Placeholder function to cancel a push notification
// Actual implementation needs to be done on the backend
export const cancelPushNotification = async (id: string): Promise<void> => {
  console.log(`[PushNotificationService] Attempting to cancel notification with ID: ${id}`);
  console.warn("[PushNotificationService] Backend implementation required for cancelling push notifications.");
  // TODO: Implement backend logic to cancel notifications
};

// Function to send a push notification (calls the Edge Function)
export const sendPushNotification = async (userId: string, title: string, body: string, url?: string) => {
  try {
    const { data, error } = await supabase.functions.invoke('send-push-notification', {
      body: { user_id: userId, title, body, url },
    });

    if (error) {
      console.error('Error invoking send-push-notification function:', error);
      return { success: false, error: error.message || 'Unknown error in savePushSubscription' };
    }

    console.log('Push notification sent successfully:', data);
    return { success: true, message: 'Push notification sent successfully.' };
  } catch (error: any) {
    console.error('Error in sendPushNotification:', error);
    return { success: false, error: error.message || 'Unknown error in savePushSubscription' };
  }
};
