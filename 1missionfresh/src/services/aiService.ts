// src/services/aiService.ts

// This service handles AI-related functionalities, such as generating text for sharing.

// Direct Gemini API integration for production use
const GEMINI_API_KEY = 'AIzaSyB8LgJ4EVFha5oOn-EonJtYD41KDQmFqj0';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent';

// Get the Supabase URL from environment variables (for backward compatibility)
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || 'https://yekarqanirdkdckimpna.supabase.co';
const GET_GEMINI_SUGGESTIONS_URL = `${SUPABASE_URL}/functions/v1/get-gemini-suggestions`;

/**
 * Calls the Gemini API directly for AI responses
 * @param prompt The prompt to send to Gemini
 * @returns Promise with the AI response
 */
// This function is deprecated - we now use Supabase Edge Functions for AI calls
async function callGeminiAPI(prompt: string): Promise<string | null> {
  console.warn('Direct Gemini API calls are deprecated. Use Supabase Edge Functions instead.');
  return null;
}

/**
 * Generates personalized text for sharing a badge using AI.
 * @param badgeName The name of the earned badge.
 * @param badgeDescription The description of the earned badge.
 * @param userName The name of the user (optional, use cautiously for privacy).
 * @returns A promise that resolves with the generated shareable text.
 */
export const generateShareTextForBadge = async (
  badgeName: string,
  badgeDescription: string,
  userName: string = 'a user' // Default to 'a user' for privacy if name is not available
): Promise<string | null> => {
  // Construct a prompt for the AI
  const prompt = `Generate a short, encouraging social media post (under 280 characters) for ${userName} who just earned the "${badgeName}" badge in a quit smoking app. The badge description is: "${badgeDescription}". Focus on the achievement and motivation. Do not include hashtags.`;
  try {
    console.log('🏆 Calling Gemini API for badge sharing text...');
    const geminiResponse = await callGeminiAPI(prompt);

    if (!geminiResponse) {
      console.error('Gemini API returned empty response for badge');
      return null;
    }

    return geminiResponse.substring(0, 280);

  } catch (error) {
    console.error('Error calling Gemini API (badge):', error);
    // PRODUCTION HEALTH APP: NO FAKE FALLBACKS - return null if AI is unavailable
    return null;
  }
};

/**
 * Generates personalized text for sharing progress using AI.
 * @param daysQuit The number of days the user has quit.
 * @param moneySaved The amount of money saved.
 * @param unitsNotUsed The number of units (e.g., cigarettes) not used.
 * @param userName The name of the user (optional, use cautiously for privacy).
 * @returns A promise that resolves with the generated shareable text.
 */
export const generateShareTextForProgress = async (
  daysQuit: number,
  moneySaved: number,
  unitsNotUsed: number,
  userName: string = 'a user'
): Promise<string | null> => {
  const prompt = `Generate a short, encouraging social media post (under 280 characters) for ${userName} who has quit smoking for ${daysQuit} days, saved $${moneySaved.toFixed(2)}, and avoided using ${unitsNotUsed} units (like cigarettes) in a quit smoking app. Focus on the achievement and motivation. Do not include hashtags.`;
  
  try {
    console.log('📈 Calling Gemini API for progress sharing text...');
    const geminiResponse = await callGeminiAPI(prompt);

    if (!geminiResponse) {
      console.error('Gemini API returned empty response for progress');
      return null;
    }

    return geminiResponse.substring(0, 280);
  } catch (error) {
    console.error('Error calling Gemini API (progress):', error);
    return null;
  }
};

// Temporary AI response generator for quit smoking coaching
const generateLocalAIResponse = (userMessage: string, conversationHistory?: Array<{ role: string; content: string }>): string => {
  const message = userMessage.toLowerCase();
  
  // Contextual responses based on common quit smoking scenarios
  if (message.includes('craving') || message.includes('urge') || message.includes('want to smoke')) {
    return "I understand you're experiencing a craving right now. Remember, cravings are temporary and typically last only 3-5 minutes. Try the 4-7-8 breathing technique: breathe in for 4 counts, hold for 7, exhale for 8. You can also try drinking water, chewing gum, or doing quick hand exercises. You've got this! 💪";
  }
  
  if (message.includes('stressed') || message.includes('anxiety') || message.includes('anxious')) {
    return "Stress is a common trigger, but you're learning healthier ways to cope! Try our breathing exercises or take a 5-minute walk. Remember why you started this journey - your health, your family, your future. Each day without smoking is a victory. What specific situation is causing stress right now?";
  }
  
  if (message.includes('difficult') || message.includes('hard') || message.includes('struggle')) {
    return "Quitting is one of the hardest things you'll ever do, and that makes you incredibly strong for trying. Every struggle is proof that you're fighting for your health. It's okay to have difficult moments - they don't define your journey. What's one small thing you can do right now to feel better?";
  }
  
  if (message.includes('relapse') || message.includes('smoked') || message.includes('slipped')) {
    return "A slip doesn't erase your progress - it's part of many people's quit journey. What matters is getting back on track right now. You've learned something valuable about your triggers. Let's focus on tomorrow and the strategies that will help you succeed. You're not starting over, you're continuing forward.";
  }
  
  if (message.includes('motivation') || message.includes('why') || message.includes('reason')) {
    return "Your motivation is your superpower! Think about your health improving every day - your lungs healing, your energy increasing, your risk of disease dropping. Think about the money you're saving and the example you're setting. What originally motivated you to quit? Hold onto that feeling.";
  }
  
  if (message.includes('support') || message.includes('help') || message.includes('alone')) {
    return "You're never alone in this journey! I'm here to support you 24/7, and there are millions of people who understand exactly what you're going through. Consider joining online support groups or talking to friends and family. Your courage to quit inspires others. How can I best support you today?";
  }
  
  if (message.includes('progress') || message.includes('day') || message.includes('time')) {
    return "Every single day smoke-free is an amazing achievement! Your body is healing, your lungs are clearing, and you're proving to yourself how strong you are. Progress isn't always linear - some days are harder than others, and that's completely normal. Celebrate every milestone, no matter how small.";
  }
  
  // Default encouraging response
  return "Thank you for reaching out! I'm FreshAI, your quit smoking coach, and I'm here to support you every step of the way. Whether you're dealing with cravings, stress, or just need encouragement, we can work through it together. What's on your mind today? Remember, every moment you choose not to smoke is a victory! 🌟";
};

/**
 * Gets a response from the AI coach based on user input and conversation history.
 * @param userMessage The user's current message.
 * @param conversationHistory An array of previous messages in the conversation.
 * @returns A promise that resolves with the AI coach's response string.
 */
export const getAICoachResponse = async (
  userMessage: string,
  conversationHistory?: Array<{ role: string; content: string }>
): Promise<string | null> => {
  if (!userMessage || typeof userMessage !== 'string') {
    return "I'd love to help you! Please share what's on your mind about your quit journey.";
  }

  try {
    // Use direct Gemini API
    const prompt = `You are "FreshAI", a friendly, empathetic, and highly knowledgeable AI quit smoking coach. Your goal is to support users in their journey to quit nicotine. Be encouraging, provide practical advice, and help users explore their feelings and triggers. Keep responses concise and actionable (under 200 words). Avoid making medical claims or giving medical advice; instead, suggest users consult healthcare professionals for medical concerns.

Current user message:
"${userMessage}"

Conversation context (if any):`;

    let fullPrompt = prompt;
    if (conversationHistory && conversationHistory.length > 0) {
      const historySnippet = conversationHistory
        .slice(-4)
        .map(msg => `${msg.role === 'user' ? 'User' : 'Coach'}: ${msg.content}`)
        .join('\n');
      fullPrompt += `\n${historySnippet}`;
    } else {
      fullPrompt += "\nNo previous conversation context for this session.";
    }

    fullPrompt += `\n\nPlease provide a supportive, encouraging response as FreshAI, the quit smoking coach. Be warm, understanding, and offer practical advice.`;

    console.log('🤖 Calling Gemini API for AI Coach response...');
    const geminiResponse = await callGeminiAPI(fullPrompt);

    if (geminiResponse) {
      console.log('✅ Gemini API response received successfully');
      return geminiResponse;
    } else {
      console.log('❌ Gemini API failed, using local AI coach');
      return generateLocalAIResponse(userMessage, conversationHistory);
    }

  } catch (error) {
    console.log('Error calling Gemini API, using local AI coach:', error);
    // Use local AI response generator as fallback
    return generateLocalAIResponse(userMessage, conversationHistory);
  }
};
// Add other AI related functions here in the future
