import { supabase } from "@/lib/supabase";
import { Database } from "@/lib/database.types"; // Import Database
import { toast } from "sonner";

const MISSION_FRESH_SCHEMA = 'mission_fresh';

// Correctly reference tables from the Database type
export type CommunityTopic = Database[typeof MISSION_FRESH_SCHEMA]["Tables"]["community_topics"]["Row"];
export type CommunityPost = Database[typeof MISSION_FRESH_SCHEMA]["Tables"]["community_posts"]["Row"] & {
  profiles: Database[typeof MISSION_FRESH_SCHEMA]["Tables"]["profiles"]["Row"] | null; 
  community_topics: CommunityTopic | null;
};
// Ensure all required fields for insert are present, excluding auto-generated or default ones.
// user_id, topic_id, title, content are mandatory from the frontend. tags is optional.
export type CommunityPostInsert = Pick<
  Database[typeof MISSION_FRESH_SCHEMA]["Tables"]["community_posts"]["Insert"],
  "user_id" | "topic_id" | "title" | "content" | "tags"
>;


export type CommunityComment = Database[typeof MISSION_FRESH_SCHEMA]["Tables"]["community_comments"]["Row"] & {
  profiles: Database[typeof MISSION_FRESH_SCHEMA]["Tables"]["profiles"]["Row"] | null;
};
// user_id, post_id, content are mandatory. parent_comment_id is optional.
export type CommunityCommentInsert = Pick<
  Database[typeof MISSION_FRESH_SCHEMA]["Tables"]["community_comments"]["Insert"],
  "user_id" | "post_id" | "content" | "parent_comment_id"
>;


/**
 * Fetches all community topics.
 */
export const getCategories = async (): Promise<CommunityTopic[]> => {
  const { data, error } = await supabase
    .schema(MISSION_FRESH_SCHEMA)
    .from('community_topics')
    .select('*')
    .order('order_index', { ascending: true });

  if (error) {
    console.error("Error fetching community topics:", error);
    toast.error("Failed to load community topics.", { description: error.message });
    throw error;
  }
  return data || [];
};

/**
 * Fetches posts, optionally filtered and paginated.
 */
interface GetPostsOptions {
  categorySlug?: string;
  page?: number;
  limit?: number;
  sortBy?: 'newest' | 'activity';
  userId?: string;
}

export const getPosts = async (options: GetPostsOptions = {}): Promise<CommunityPost[]> => {
  const { categorySlug, page = 1, limit = 10, sortBy = 'activity', userId } = options;
  const offset = (page - 1) * limit;

  let query = supabase
    .schema(MISSION_FRESH_SCHEMA)
    .from('community_posts')
    .select(`
      *,
      profiles!inner (username, avatar_url),
      community_topics!inner (name, slug)
    `)
    .eq('is_deleted', false);

  if (categorySlug) {
    query = query.eq('community_topics.slug', categorySlug);
  }

  if (userId) {
    query = query.eq('user_id', userId);
  }

  if (sortBy === 'newest') {
    query = query.order('created_at', { ascending: false });
  } else if (sortBy === 'activity') {
    query = query.order('last_activity_at', { ascending: false });
  }

  query = query.range(offset, offset + limit - 1);

  const { data, error } = await query;

  if (error) {
    console.error("Error fetching posts:", error);
    toast.error("Failed to load posts.", { description: error.message });
    throw error;
  }
  
  // In development mode, show all posts including test posts
  // In production, you might want to re-enable filtering
  const isDevelopment = import.meta.env?.MODE === 'development' || window.location.hostname === 'localhost';
  
  if (isDevelopment) {
    // Show all posts in development
    return data as any[] as CommunityPost[] || [];
  }
  
  // Filter out test, mock, and development posts for production
  const filteredPosts = data?.filter(post => {
    // Check for test/mock indicators in title or content
    const testIndicators = [
      'mock', 'placeholder', 'sample', 'demo', 'e2e',
      'playwright', 'automation'
    ];
    
    const titleCheck = post.title?.toLowerCase() || '';
    const contentCheck = post.content?.toLowerCase() || '';
    
    // Return false if any test indicators are found
    return !testIndicators.some(indicator => 
      titleCheck.includes(indicator) || contentCheck.includes(indicator)
    );
  }) || [];

  // Log how many test posts were filtered out
  console.log(`Filtered out ${(data?.length || 0) - filteredPosts.length} test/mock posts from community`);
  
  return filteredPosts as any[] as CommunityPost[] || []; 
};


/**
 * Fetches a single post by its ID, including author and category details.
 */
export const getPostById = async (postId: string): Promise<CommunityPost | null> => {
  if (!postId) return null;

  const { data, error } = await supabase
    .schema(MISSION_FRESH_SCHEMA)
    .from('community_posts')
    .select(`
      *,
      profiles!inner (username, avatar_url),
      community_topics!inner (name, slug)
    `)
    .eq('id', postId)
    .eq('is_deleted', false)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      toast.error("Post not found.");
      return null;
    }
    console.error(`Error fetching post ${postId}:`, error);
    toast.error("Failed to load post.", { description: error.message });
    throw error;
  }
  return data as any as CommunityPost | null; 
};

/**
 * Creates a new community post.
 */
export const createPost = async (
  postData: CommunityPostInsert
): Promise<CommunityPost | null> => {
  const { data, error } = await supabase
    .schema(MISSION_FRESH_SCHEMA)
    .from('community_posts')
    .insert(postData) // topic_id is part of CommunityPostInsert now
    .select(`
        *,
        profiles!inner (username, avatar_url),
        community_topics!inner (name, slug)
    `)
    .single();

  if (error) {
    console.error("Error creating post:", error);
    toast.error("Failed to create post.", { description: error.message });
    throw error;
  }
  toast.success("Post created successfully!");
  return data as any as CommunityPost | null; 
};


/**
 * Fetches comments for a given post, paginated, with author details.
 */
export const getCommentsForPost = async (
  postId: string,
  page: number = 1,
  limit: number = 10
): Promise<CommunityComment[]> => {
  if (!postId) return [];
  const offset = (page - 1) * limit;

  const { data, error } = await supabase
    .schema(MISSION_FRESH_SCHEMA)
    .from('community_comments')
    .select(`*`) // Fetch base comment data first
    .eq('post_id', postId)
    .order('created_at', { ascending: true })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error(`Error fetching comments for post ${postId}:`, error);
    toast.error("Failed to load comments.", { description: error.message });
    throw error;
  }

  if (!data) return [];

  const commentsWithProfiles = await Promise.all(
    data.map(async (comment) => {
      const { data: profileData, error: profileError } = await supabase
        .schema(MISSION_FRESH_SCHEMA)
        .from('profiles')
        .select('username, avatar_url')
        .eq('id', comment.user_id)
        .single();
      if (profileError) {
        console.warn(`Could not fetch profile for comment author ${comment.user_id}`, profileError.message);
      }
      return { ...comment, profiles: profileData || null };
    })
  );

  return commentsWithProfiles as CommunityComment[];
};

/**
 * Adds a comment to a post.
 */
export const addComment = async (
  commentData: CommunityCommentInsert
): Promise<CommunityComment | null> => {
  const { data, error } = await supabase
    .schema(MISSION_FRESH_SCHEMA)
    .from('community_comments')
    .insert(commentData)
    .select(`*`) // Select base data
    .single();

  if (error) {
    console.error("Error adding comment:", error);
    toast.error("Failed to add comment.", { description: error.message });
    throw error;
  }
  toast.success("Comment added!");
  
  if (data) {
    const { data: profileData, error: profileError } = await supabase
      .schema(MISSION_FRESH_SCHEMA)
      .from('profiles')
      .select('username, avatar_url')
      .eq('id', data.user_id)
      .single();
    if (profileError) console.warn('Failed to fetch profile for new comment author', profileError.message);
    return { ...data, profiles: profileData || null } as CommunityComment;
  }
  return null;
};

/**
 * Likes a post.
 */
export const likePost = async (postId: string, userId: string): Promise<boolean> => {
  const { error } = await supabase
    .schema(MISSION_FRESH_SCHEMA)
    .from('community_post_likes')
    .insert({ post_id: postId, user_id: userId });

  if (error) {
    if (error.code === '23505') { 
      // toast.info("You've already liked this post."); // Can be noisy, handle in UI
      return true; 
    }
    console.error(`Error liking post ${postId}:`, error);
    toast.error("Failed to like post.", { description: error.message });
    throw error;
  }
  return true;
};

/**
 * Unlikes a post.
 */
export const unlikePost = async (postId: string, userId: string): Promise<boolean> => {
  const { error } = await supabase
    .schema(MISSION_FRESH_SCHEMA)
    .from('community_post_likes')
    .delete()
    .eq('post_id', postId)
    .eq('user_id', userId);

  if (error) {
    console.error(`Error unliking post ${postId}:`, error);
    toast.error("Failed to unlike post.", { description: error.message });
    throw error;
  }
  return true;
};


/**
 * Likes a comment.
 */
export const likeComment = async (commentId: string, userId: string): Promise<boolean> => {
  const { error } = await supabase
    .schema(MISSION_FRESH_SCHEMA)
    .from('community_comment_likes')
    .insert({ comment_id: commentId, user_id: userId });

  if (error) {
    if (error.code === '23505') {
      // toast.info("You've already liked this comment."); // Can be noisy
      return true;
    }
    console.error(`Error liking comment ${commentId}:`, error);
    toast.error("Failed to like comment.", { description: error.message });
    throw error;
  }
  return true;
};

/**
 * Unlikes a comment.
 */
export const unlikeComment = async (commentId: string, userId: string): Promise<boolean> => {
  const { error } = await supabase
    .schema(MISSION_FRESH_SCHEMA)
    .from('community_comment_likes')
    .delete()
    .eq('comment_id', commentId)
    .eq('user_id', userId);

  if (error) {
    console.error(`Error unliking comment ${commentId}:`, error);
    toast.error("Failed to unlike comment.", { description: error.message });
    throw error;
  }
  return true;
};
/**
 * Checks if a user has liked a specific post.
 */
export const getPostLikeStatus = async (postId: string, userId: string): Promise<boolean> => {
  if (!postId || !userId) return false;
  const { data, error } = await supabase
    .schema(MISSION_FRESH_SCHEMA)
    .from('community_post_likes')
    .select('post_id')
    .eq('post_id', postId)
    .eq('user_id', userId)
    .maybeSingle();

  if (error) {
    console.error(`Error checking like status for post ${postId}, user ${userId}:`, error);
    // Don't throw, just return false, UI can handle it gracefully
    return false;
  }
  return !!data;
};

/**
 * Checks which comments a user has liked from a list of comment IDs.
 * Returns a record mapping commentId to a boolean (true if liked).
 */
export const getCommentLikeStatus = async (commentIds: string[], userId: string): Promise<Record<string, boolean>> => {
  if (!userId || commentIds.length === 0) return {};
  const { data, error } = await supabase
    .schema(MISSION_FRESH_SCHEMA)
    .from('community_comment_likes')
    .select('comment_id')
    .eq('user_id', userId)
    .in('comment_id', commentIds);

  if (error) {
    console.error(`Error checking like status for comments, user ${userId}:`, error);
    return {};
  }
  
  const likedCommentIds = new Set(data?.map(like => like.comment_id) || []);
  const statusRecord: Record<string, boolean> = {};
  commentIds.forEach(id => {
    statusRecord[id] = likedCommentIds.has(id);
  });
  return statusRecord;
};