import { supabase } from "@/lib/supabase";

export interface HealthMilestone {
  id: string;
  time_value: number;
  time_unit: 'minutes' | 'hours' | 'days' | 'weeks' | 'months' | 'years';
  description: string;
  medical_source?: string;
  order_index: number;
  is_active: boolean;
}

export interface CalculatedHealthMilestone extends HealthMilestone {
  date: Date;
  achieved: boolean;
  time_display: string;
}

/**
 * Fetch health milestones from database
 * PRODUCTION HEALTH APP: All medical data sourced from database with proper medical sourcing
 */
export const getHealthMilestones = async (): Promise<HealthMilestone[]> => {
  try {
    console.log('🔍 DEBUG: Starting getHealthMilestones...');
    console.log('🔍 DEBUG: Supabase client:', supabase);
    
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('health_milestones')
      .select('*')
      .eq('is_active', true)
      .order('order_index', { ascending: true });

    console.log('🔍 DEBUG: Query result:', { data, error });

    if (error) {
      console.error("❌ Failed to fetch health milestones from database:", error);
      return [];
    }

    console.log('✅ DEBUG: Successfully fetched health milestones:', data?.length || 0);
    return data || [];
  } catch (error) {
    console.error("❌ Error fetching health milestones:", error);
    return [];
  }
};

/**
 * Calculate health milestone dates based on quit date
 * PRODUCTION HEALTH APP: Uses real database data, no hardcoded medical information
 */
export const calculateHealthMilestones = async (quitDate: Date): Promise<CalculatedHealthMilestone[]> => {
  try {
    const milestones = await getHealthMilestones();
    
    if (milestones.length === 0) {
      console.warn("No health milestones found in database");
      return [];
    }

    const now = new Date();
    
    return milestones.map(milestone => {
      let date = new Date(quitDate);
      
      // Calculate milestone date based on time unit
      switch (milestone.time_unit) {
        case 'minutes':
          date = new Date(quitDate.getTime() + milestone.time_value * 60000);
          break;
        case 'hours':
          date = new Date(quitDate.getTime() + milestone.time_value * 3600000);
          break;
        case 'days':
          date = new Date(quitDate.getTime() + milestone.time_value * 24 * 3600000);
          break;
        case 'weeks':
          date = new Date(quitDate.getTime() + milestone.time_value * 7 * 24 * 3600000);
          break;
        case 'months':
          date = new Date(quitDate);
          date.setMonth(date.getMonth() + milestone.time_value);
          break;
        case 'years':
          date = new Date(quitDate);
          date.setFullYear(date.getFullYear() + milestone.time_value);
          break;
      }

      // Format time display
      const timeDisplay = milestone.time_value === 1 
        ? `${milestone.time_value} ${milestone.time_unit.slice(0, -1)}`
        : `${milestone.time_value} ${milestone.time_unit}`;

      return {
        ...milestone,
        date,
        achieved: now >= date,
        time_display: timeDisplay
      };
    });
  } catch (error) {
    console.error("Error calculating health milestones:", error);
    return [];
  }
};