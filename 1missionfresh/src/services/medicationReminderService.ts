import { supabase } from '@/lib/supabase';
import { Database, Tables, TablesInsert, TablesUpdate } from '@/lib/database.types';
import { LocalNotifications, Schedule, NotificationChannel } from '@capacitor/local-notifications';
import { toast } from 'sonner';

type MedicationReminder = Tables<{ schema: 'mission_fresh' }, 'medication_reminders'>;
// Ensure InsertMedicationReminder includes the new optional local_notification_ids field
type InsertMedicationReminder = TablesInsert<{ schema: 'mission_fresh' }, 'medication_reminders'> & { local_notification_ids?: string[] | null };
type UpdateMedicationReminder = TablesUpdate<{ schema: 'mission_fresh' }, 'medication_reminders'>;

export const getMedicationReminders = async (userId: string): Promise<MedicationReminder[] | null> => {
  const { data, error } = await supabase
    .schema('mission_fresh')
    .from('medication_reminders')
    .select('*')
    .eq('user_id', userId)
    .order('reminder_time', { ascending: true });

  if (error) {
    console.error('Error fetching medication reminders:', error);
    return null;
  }

  return data;
};

export const addMedicationReminder = async (reminder: InsertMedicationReminder): Promise<MedicationReminder | null> => {
  const { data, error } = await supabase
    .schema('mission_fresh')
    .from('medication_reminders')
    .insert(reminder)
    .select()
    .single();

  if (error) {
    console.error('Error adding medication reminder:', error);
    return null;
  }

  // Schedule local notification
  if (data) {
    try {
      const scheduledIds = await _scheduleLocalNotificationFromReminder(data);
      if (scheduledIds && scheduledIds.length > 0) {
        // Update the reminder in DB with local notification IDs
        const { data: updatedDataWithLocalIds, error: updateError } = await supabase
          .schema('mission_fresh')
          .from('medication_reminders')
          .update({ local_notification_ids: scheduledIds.map(String) })
          .eq('id', data.id)
          .select()
          .single();
        if (updateError) {
          console.error('Error updating reminder with local notification IDs:', updateError);
          // Optionally, try to cancel the just scheduled local notifications if DB update fails
          await _cancelLocalNotifications(scheduledIds.map(id => String(id)));
          toast.error('Failed to fully save reminder with notification schedule.');
          return null; // Indicate partial failure
        }
        return updatedDataWithLocalIds; // Return the reminder with local_notification_ids
      }
    } catch (scheduleError) {
      console.error('Error scheduling local notification:', scheduleError);
      toast.error('Reminder saved, but failed to schedule notification.');
      // Reminder is saved in DB, but notification scheduling failed.
      // Depending on desired behavior, you might want to delete the DB record or leave it.
      // For now, we return the data as the DB record was created.
    }
  }
  return data; // Return original data if scheduling failed or no IDs returned
};

export const updateMedicationReminder = async (id: string, reminder: UpdateMedicationReminder): Promise<MedicationReminder | null> => {
  // First, get the existing reminder to cancel the old notification
  const { data: existingReminderData, error: fetchError } = await supabase
    .schema('mission_fresh')
    .from('medication_reminders')
    .select('id, reminder_time, local_notification_ids')
    .eq('id', id)
    .single();
  const existingReminder = existingReminderData as MedicationReminder | null;

  if (fetchError) {
    console.error('Error fetching existing reminder for update:', fetchError);
    return null;
  }

  // Cancel old local notifications if they exist
  if (existingReminder?.local_notification_ids && existingReminder.local_notification_ids.length > 0) {
    await _cancelLocalNotifications(existingReminder.local_notification_ids as string[]);
  }

  const { data, error } = await supabase
    .schema('mission_fresh')
    .from('medication_reminders')
    .update(reminder)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating medication reminder:', error);
    return null;
  }

  // Schedule new local notification
  if (data) {
    try {
      const scheduledIds = await _scheduleLocalNotificationFromReminder(data);
      if (scheduledIds && scheduledIds.length > 0) {
        // Update the reminder in DB with new local notification IDs
         const { data: updatedDataWithLocalIds, error: updateError } = await supabase
          .schema('mission_fresh')
          .from('medication_reminders')
          .update({ local_notification_ids: scheduledIds.map(String) })
          .eq('id', data.id)
          .select()
          .single();
        if (updateError) {
          console.error('Error updating reminder with new local notification IDs:', updateError);
          await _cancelLocalNotifications(scheduledIds.map(id => String(id)));
          toast.error('Failed to fully update reminder with new notification schedule.');
          return null;
        }
        return updatedDataWithLocalIds;
      }
    } catch (scheduleError) {
      console.error('Error scheduling new local notification:', scheduleError);
      toast.error('Reminder updated, but failed to reschedule notification.');
    }
  }
  return data; // Return original updated data if rescheduling failed
};

export const deleteMedicationReminder = async (id: string): Promise<boolean> => {
   // First, get the existing reminder to cancel local notifications
   const { data: existingReminder, error: fetchError } = await supabase
    .schema('mission_fresh')
    .from('medication_reminders')
    .select('id, local_notification_ids') // Select local_notification_ids
    .eq('id', id)
    .single();

  if (fetchError) {
    console.error('Error fetching existing reminder for deletion:', fetchError);
    // Proceed with DB deletion even if fetching fails, to ensure data consistency
  }

  if (existingReminder?.local_notification_ids && existingReminder.local_notification_ids.length > 0) {
    await _cancelLocalNotifications(existingReminder.local_notification_ids as string[]);
  }

  const { error } = await supabase
    .schema('mission_fresh')
    .from('medication_reminders')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting medication reminder:', error);
    return false;
  }

  return true;
};

// Function to schedule a daily push notification (placeholder - actual implementation needed in pushNotificationService)
// This function would likely interact with a native device API or a backend service
// Helper function to schedule a local notification based on reminder data
const _scheduleLocalNotificationFromReminder = async (reminder: MedicationReminder): Promise<number[] | null> => {
  try {
    const permissions = await LocalNotifications.checkPermissions();
    if (permissions.display !== 'granted') {
      const request = await LocalNotifications.requestPermissions();
      if (request.display !== 'granted') {
        toast.error('Notification permission denied. Cannot schedule reminders.');
        return null;
      }
    }

    // Create a notification channel (Android specific, but good practice)
    try {
        await LocalNotifications.createChannel({
            id: 'medication_reminders_channel',
            name: 'Medication Reminders',
            description: 'Reminders for your medication or NRT',
            importance: 4, // Or other importance level (4 is High)
            visibility: 1, // Public visibility
            sound: 'default', // Use default sound or specify custom
            vibration: true,
        });
    } catch (channelError) {
        console.warn("Could not create notification channel (may already exist or not supported):", channelError);
    }


    const [hours, minutes] = reminder.reminder_time.split(':').map(Number);
    const scheduleOptions: Schedule = {
      on: {
        hour: hours,
        minute: minutes,
      },
      repeats: true, // For daily reminders
      // every: 'day' // This might be how you specify daily for some interpretations of 'repeats: true'
                     // Or, if `on` with `repeats: true` implies daily, this is fine.
                     // Capacitor docs: "When a specific time is provided via on, repeats: true will repeat daily at that time."
    };
    
    // Handle start_date and end_date if they exist
    // Note: LocalNotifications.schedule `on` doesn't directly support start/end dates for repeats.
    // This logic would need to be handled by scheduling individual notifications or by a more complex backend.
    // For MVP, we'll schedule it daily starting from now if start_date is today or past,
    // and assume it runs indefinitely or until manually deleted/updated.
    // Proper handling of start/end dates for recurring local notifications is complex.

    const notificationId = parseInt(reminder.id.substring(0, 8), 16); // Create a numeric ID from UUID

    const scheduleResult = await LocalNotifications.schedule({
      notifications: [
        {
          title: `Reminder: ${reminder.medication_name}`,
          body: reminder.dosage ? `Time to take your ${reminder.medication_name} (${reminder.dosage}).` : `Time to take your ${reminder.medication_name}.`,
          id: notificationId, // Ensure ID is a number
          schedule: scheduleOptions,
          smallIcon: 'ic_stat_icon_config_sample', // Ensure this icon exists in android/app/src/main/res/drawable
          // sound: 'default', // Handled by channel on Android 8+
          channelId: 'medication_reminders_channel', // For Android
          // attachments: undefined,
          // actionTypeId: "",
          // extra: null
        },
      ],
    });
    console.log('Local notifications scheduled', scheduleResult.notifications);
    return scheduleResult.notifications.map(n => n.id);
  } catch (e) {
    console.error('Error scheduling local notification:', e);
    toast.error('Failed to schedule local notification.');
    return null;
  }
};

// Helper function to cancel local notifications
const _cancelLocalNotifications = async (notificationIds: string[] | number[]) => {
  if (!notificationIds || notificationIds.length === 0) return;
  try {
    const idsToCancel = notificationIds.map(id => ({ id: Number(id) })); // Ensure IDs are numbers
    await LocalNotifications.cancel({ notifications: idsToCancel });
    console.log('Cancelled local notifications:', idsToCancel);
  } catch (e) {
    console.error('Error cancelling local notifications:', e);
  }
};
