import { supabase } from "../lib/supabase";

export interface CartItem {
  id: string;
  user_id: string;
  product_id: string;
  quantity: number;
  price: number;
  vendor_id?: string;
  created_at: string;
  updated_at: string;
}

export interface AddToCartParams {
  productId: string;
  quantity?: number;
  price: number;
  vendorId?: string;
}

export const cartService = {
  async addToCart(params: AddToCartParams): Promise<CartItem> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error("User must be authenticated to add items to cart");
    }

    const { data, error } = await supabase
      .from("cart_items")
      .insert({
        user_id: user.id,
        product_id: params.productId,
        quantity: params.quantity || 1,
        price: params.price,
        vendor_id: params.vendorId
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to add item to cart: ${error.message}`);
    }

    return data;
  },

  async getCartItems(userId: string): Promise<CartItem[]> {
    const { data, error } = await supabase
      .from("cart_items")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch cart items: ${error.message}`);
    }

    return data || [];
  }
};
