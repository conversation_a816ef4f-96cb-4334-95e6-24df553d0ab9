import { supabase } from "@/lib/supabase"; // Corrected import path
import { toast } from "sonner";
import { Database, Tables, TablesInsert } from '@/lib/database.types'; // Import TablesInsert

export interface UserProfile {
  id: string;
  username?: string | null;
  first_name?: string | null;
  last_name?: string | null;
  full_name?: string | null;
  avatar_url?: string | null;
  bio?: string | null;
  country?: string | null;
  date_of_birth?: string | null;
  gender?: string | null;
  timezone?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
  shareable_code?: string;
}

export async function getUserProfile(): Promise<UserProfile | null> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) return null;
    
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .maybeSingle();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    console.error('Error fetching profile:', error);
    return null;
  }
}

export async function updateUserProfile(profile: Partial<UserProfile>): Promise<boolean> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      toast.error("You must be logged in to update your profile.");
      return false;
    }

    // Use object destructuring to remove 'id' from the update payload
    // and ensure we only update the authenticated user's profile.
    const { id, ...updateData } = profile;

    const { error } = await supabase
      .schema('public')
      .from('profiles')
      .update(updateData)
      .eq('id', user.id);
    
    if (error) throw error;
    
    toast.success("Profile updated successfully!");
    return true;
  } catch (error) {
    console.error('Error updating profile:', error);
    toast.error('Failed to update profile');
    return false;
  }
}

export async function createProfile(userId: string, emailForUsername?: string, firstName?: string, lastName?: string): Promise<boolean> {
  try {
    const username = emailForUsername ? emailForUsername.split('@')[0] : `user_${userId.substring(0, 8)}`;
    const fullName = [firstName, lastName].filter(Boolean).join(' ') || null;

    const profileData: TablesInsert<{ schema: 'mission_fresh' }, 'profiles'> = {
      id: userId,
      full_name: fullName,
      username: username,
    };

    const { error } = await supabase
      .schema('mission_fresh')
      .from('profiles')
      .insert(profileData);

    if (error) {
      console.error('Error creating profile:', error);
      toast.error(`Failed to create profile: ${error.message}`);
      return false;
    }

    console.log('Profile created successfully for user:', userId);
    return true;
  } catch (error) {
    console.error('Error creating profile:', error);
    toast.error('An unexpected error occurred while creating profile.');
    return false;
  }
}

export async function savePushToken(userId: string, token: string, platform?: string): Promise<boolean> {
  if (!userId || !token) {
    console.error('User ID and token are required to save push token');
    return false;
  }
  
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user || user.id !== userId) {
      console.error('Attempt to save push token for another user denied.');
      toast.error('You can only save your own notification settings.');
      return false;
    }

    const tokenData: TablesInsert<{ schema: 'public' }, 'push_tokens'> = {
      user_id: userId,
      token: token,
      platform: platform,
    };

    const { error } = await supabase
      .schema('public')
      .from('push_tokens')
      .upsert(tokenData, { onConflict: 'token' }); // Upsert based on the token to avoid duplicates

    if (error) throw error;
    
    console.log('Push token saved successfully for user:', userId);
    toast.success('Notification settings saved.');
    return true;
  } catch (error) {
    console.error('Error saving push token:', error);
    toast.error('Failed to save notification settings.');
    return false;
  }
}
