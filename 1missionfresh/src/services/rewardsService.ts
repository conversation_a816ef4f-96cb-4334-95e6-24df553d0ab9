import { supabase } from "@/lib/supabase";

// Create a schema-specific client for mission_fresh
const missionFreshClient = supabase.schema('mission_fresh');
// Import Database type from generated types
import { Database } from '../lib/database.types';
import { TablesInsert } from '@/integrations/supabase/types';
import { badges } from '@/lib/badgesData'; // Import badges data
import { toast } from "sonner"; // Ensure toast is imported

// Define types based on actual database structure
interface Reward {
  id: string;
  name: string;
  description: string;
  points_required: number;
  active: boolean;
  created_at: string;
}

interface ClaimedReward {
  id: string;
  user_id: string;
  reward_id: string;
  claimed_at: string;
  points_deducted: number;
  reward_name: string;
  reward_description: string;
}

interface StepReward {
  id: string;
  user_id: string;
  steps: number;
  date: string;
  points_earned: number;
  created_at: string;
}

export interface UserBadge {
  id: string;
  user_id: string;
  badge_id: string;
  earned_at: string;
}

type Profile = Database['mission_fresh']['Tables']['profiles']['Row'];

// Test function to check database connectivity
export const testDatabaseConnection = async () => {
  try {
    console.log('🧪 Testing database connection...');
    
    // Test 1: Check if we can connect to the database at all
    const { data: testData, error: testError } = await missionFreshClient
      .from('rewards')
      .select('count')
      .limit(1);
    
    console.log('Test 1 - Basic connection:', { testData, testError });
    
    // Test 2: Try to get any rewards (active or inactive)
    const { data: allRewards, error: allError } = await missionFreshClient
      .from('rewards')
      .select('*')
      .limit(5);
    
    console.log('Test 2 - All rewards:', { allRewards, allError });
    
    // Test 3: Check schema access
    const { data: schemaTest, error: schemaError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    console.log('Test 3 - Public schema access:', { schemaTest, schemaError });
    
    return {
      missionFreshAccess: !testError,
      publicSchemaAccess: !schemaError,
      rewardsCount: allRewards?.length || 0,
      errors: { testError, allError, schemaError }
    };
  } catch (error) {
    console.error('💥 Database connection test failed:', error);
    return { error };
  }
};

// Define Reward type
export type RewardType = {
  id: string;
  name: string;
  description: string;
  points_required: number;
  active: boolean;
  type: 'database' | 'tangible'; // Added type to differentiate reward sources
};


/**
 * Type for reward history items returned by getRewardHistory.
 */
export interface RewardHistoryItem {
  id: string;
  date: string;
  type: 'reward' | 'step' | 'exercise'; // Added 'step' and 'exercise' types
  points: number;
  name: string; // Name of the reward, 'Steps', or 'Breathing Exercise'
  steps?: number; // Added steps property for step history items
}

/**
 * Fetch the list of available rewards (from database and hardcoded)
 */
export const getAvailableRewards = async (): Promise<RewardType[]> => {
  try {
    console.log('🔍 getAvailableRewards: Starting to fetch rewards from database...');
    
    const { data, error } = await missionFreshClient
      .from('rewards')
      .select('*')
      .eq('active', true)
      .order('points_required', { ascending: true });

    if (error) {
      console.error('❌ Error fetching rewards from database:', error);
      console.error('Error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
      throw new Error(`Failed to fetch rewards: ${error.message}`);
    }

    console.log('✅ Successfully fetched rewards from database:', {
      count: data?.length || 0,
      rewards: data
    });

    // Return all active rewards from database - these are real, production rewards
    console.log(`Found ${data?.length || 0} active rewards in database`);
    
    const formattedRewards = (data || []).map(reward => ({
      ...reward,
      type: (reward.reward_type || 'database') as 'database' | 'tangible',
    }));

    // PRODUCTION APP: NO FALLBACK REWARDS - If no rewards in database, return empty array
    // This is a production-ready health & wellness app - we cannot serve fake mockup data to users
    if (formattedRewards.length === 0) {
      console.log('⚠️ No rewards found in database - returning empty array (production behavior)');
      return [];
    }

    console.log('🎯 Formatted rewards for return:', formattedRewards);
    
    return formattedRewards;
  } catch (error) {
    console.error('💥 Unexpected error in getAvailableRewards:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    throw error;
  }
};

/**
 * Fetch the user's reward history (claimed rewards and points earned from activities).
 */
export const getRewardHistory = async (): Promise<RewardHistoryItem[]> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  try {
    // Try to get from database tables, if they don't exist, create history from journal entries
    const [claimedRewardsRes, activityLogRes] = await Promise.all([
      supabase
        .from('claimed_rewards')
        .select('id, claimed_at, reward_name, points_deducted')
        .eq('user_id', user.id)
        .order('claimed_at', { ascending: false }),
      supabase
        .from('activity_points_log')
        .select('id, created_at, points_earned, activity_name, activity_type')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
    ]);

    const history: RewardHistoryItem[] = [];

    // If tables don't exist, create history from journal entries
    if (claimedRewardsRes.error && activityLogRes.error) {
      console.log('Reward tables not found, creating history from journal entries');
      
             const { data: journalEntries, error: journalError } = await supabase
         .from('journal_entries')
         .select('id, created_at, content')
         .eq('user_id', user.id)
         .order('created_at', { ascending: false })
         .limit(10);

      if (!journalError && journalEntries) {
        journalEntries.forEach((entry, index) => {
          // Check if it's a support ticket
          if (entry.content.includes('SUPPORT TICKET')) {
            history.push({
              id: `support-${entry.id}`,
              date: entry.created_at,
              type: 'exercise',
              points: 5,
              name: 'Support Ticket Submitted'
            });
          } else {
            // Regular journal entry
            history.push({
              id: `journal-${entry.id}`,
              date: entry.created_at,
              type: 'exercise',
              points: 10,
              name: 'Journal Entry Created'
            });
          }
        });
      }
      
      return history;
    }

    // Process claimed rewards if available
    if (!claimedRewardsRes.error && claimedRewardsRes.data) {
      claimedRewardsRes.data.forEach(cr => {
        history.push({
          id: `claimed-${cr.id}`,
          date: new Date(cr.claimed_at).toISOString(),
          type: 'reward',
          points: (cr.points_deducted || 0) * -1, // Points deducted are negative
          name: cr.reward_name || 'Reward Claimed',
        });
      });
    }

    // Process activity log if available
    if (!activityLogRes.error && activityLogRes.data) {
      activityLogRes.data.forEach(log => {
        let itemType: 'reward' | 'step' | 'exercise' = 'exercise';
        if (log.activity_type === 'daily_step_goal' || log.activity_type === 'step_tracking') {
          itemType = 'step';
        }

        history.push({
          id: `activity-${log.id}`,
          date: new Date(log.created_at).toISOString(),
          type: itemType,
          points: log.points_earned,
          name: log.activity_name || log.activity_type || 'Activity',
        });
      });
    }

    // Sort combined history by date, descending
    history.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    return history;

  } catch (error) {
    console.error('Error constructing reward history:', error);
    // Return empty array on error instead of throwing
    return [];
  }
};

/**
 * Save step count for the day and calculate points earned
 * This function records daily steps and calculates points based on a simple conversion.
 * It updates or creates an entry in the 'step_rewards' table.
 */
export const recordStepCount = async (steps: number, date: string): Promise<StepReward | null> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");

  // Define points calculation logic here (e.g., 1 point per 1000 steps)
  const pointsAwarded = Math.floor(steps / 1000);

  try {
    const stepRewardData: TablesInsert<{ schema: 'mission_fresh' }, 'step_rewards'> = { // CORRECTED USAGE
      user_id: user.id,
      date: date,
      steps: steps,
      points_awarded: pointsAwarded,
      // created_at will be set by default by the database
    };

    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('step_rewards')
      .upsert(stepRewardData, { onConflict: 'user_id, date' })
      .select()
      .single();

    if (error) {
      console.error('Error upserting step count:', error);
      throw error;
    }

    // If points were awarded, also call earnPoints to log activity and update total points
    if (data && pointsAwarded > 0) {
      await earnPoints(user.id, pointsAwarded, 'step_tracking', `${steps} steps recorded`);
    }
    
    console.log('Step count recorded/updated:', data);
    return data;

  } catch (error) {
    console.error('Error in recordStepCount:', error);
    throw error;
  }
};

/**
 * Fetches the IDs of all badges a user has earned.
 */
export const getEarnedBadges = async (userId: string): Promise<UserBadge[]> => {
  if (!userId) return [];

  try {
    const { data, error } = await supabase
      .from('user_badges')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      console.error(`Error fetching user earned badges for user ${userId}:`, error);
      throw new Error(`Failed to fetch user earned badges: ${error.message}`);
    }
    return data || [];
  } catch (error: any) {
    console.error("Unexpected error in getEarnedBadges:", error);
    throw error; // Re-throw to be caught by calling component/page
  }
};

/**
 * Claim a specific reward by its ID.
 * This function inserts a record into 'claimed_rewards'.
 * Point deduction logic is assumed to be handled by a database trigger or function
 * associated with the 'claimed_rewards' table insert.
 */
export const claimSpecificReward = async (rewardToClaim: RewardType): Promise<ClaimedReward | null> => {
  console.log('🎯 CLAIM MUTATION STARTED:', rewardToClaim);
  
  try {
    // 1. CRITICAL: Verify user authentication with detailed logging
    console.log('🔍 DEBUG: Checking user authentication...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('❌ DEBUG: Authentication error:', authError);
      throw new Error(`Authentication failed: ${authError.message}`);
    }
    
    if (!user) {
      console.error('❌ DEBUG: No authenticated user found');
      throw new Error("User not authenticated");
    }
    
    console.log('✅ DEBUG: User authenticated:', { id: user.id, email: user.email });

    // 2. Fetch user's current points
    console.log('🔍 DEBUG: Fetching current points...');
    const currentPoints = await getTotalPoints();
    console.log('✅ DEBUG: Current points:', currentPoints);

    // 3. Check if user has enough points
    if (currentPoints < rewardToClaim.points_required) {
      console.error('❌ DEBUG: Insufficient points:', { current: currentPoints, required: rewardToClaim.points_required });
      throw new Error("Not enough points to claim this reward.");
    }

    // 3. Deduct points from user's account
    console.log('🔍 DEBUG: Deducting points from user account...');
    const pointsToDeduct = rewardToClaim.points_required * -1;
    const updatedStats = await earnPoints(user.id, pointsToDeduct, 'reward_claim_deduction', `Claimed: ${rewardToClaim.name}`);
    
    if (!updatedStats) {
      throw new Error("Failed to deduct points. Reward not claimed.");
    }
    console.log('✅ DEBUG: Points deducted successfully:', pointsToDeduct);

    // 4. ADDITIONAL DEBUG: Verify reward exists in database before attempting insert
    console.log('🔍 DEBUG: Verifying reward exists in database...');
    const { data: rewardExists, error: rewardCheckError } = await missionFreshClient
      .from('rewards')
      .select('id, name, points_required')
      .eq('id', rewardToClaim.id)
      .single();
    
    console.log('🔍 DEBUG: Reward verification result:', { rewardExists, rewardCheckError });
    
    if (rewardCheckError || !rewardExists) {
      console.error('❌ CRITICAL: Reward does not exist in database!', { 
        rewardId: rewardToClaim.id, 
        error: rewardCheckError 
      });
      throw new Error(`Reward ${rewardToClaim.id} does not exist in database`);
    }
    
    // 5. ADDITIONAL DEBUG: Verify user authentication and ID
    console.log('🔍 DEBUG: User authentication check:', {
      userId: user.id,
      userEmail: user.email,
      isAuthenticated: !!user.id
    });

    // 6. Insert into claimed_rewards with detailed debugging
    console.log('🔍 DEBUG: About to insert claimed reward data...');
    const claimedRewardData = {
      user_id: user.id,
      reward_id: rewardToClaim.id,
      claimed_at: new Date().toISOString(),
      points_deducted: rewardToClaim.points_required,
      reward_name: rewardToClaim.name,
      reward_description: rewardToClaim.description,
    };
    console.log('🔍 DEBUG: Claimed reward data:', claimedRewardData);

    // 7. ADDITIONAL DEBUG: Test table access first with a simple select
    console.log('🔍 DEBUG: Testing claimed_rewards table access...');
    const { data: tableTest, error: tableTestError } = await missionFreshClient
      .from('claimed_rewards')
      .select('id')
      .limit(1);
    
    console.log('🔍 DEBUG: Table access test result:', { tableTest, tableTestError });
    
    if (tableTestError) {
      console.error('❌ CRITICAL: Cannot access claimed_rewards table!', tableTestError);
      throw new Error(`Cannot access claimed_rewards table: ${tableTestError.message}`);
    }

    console.log('🔍 DEBUG: Calling missionFreshClient.from(claimed_rewards).insert()...');
    const { data: newClaimedReward, error: insertError } = await missionFreshClient
      .from('claimed_rewards')
      .insert(claimedRewardData)
      .select()
      .single();

    console.log('🔍 DEBUG: Insert completed. Result:', { newClaimedReward, insertError });

    if (insertError) {
      console.error('❌ Error inserting into claimed_rewards:', insertError);
      console.error('❌ Error details:', {
        message: insertError.message,
        code: insertError.code,
        details: insertError.details,
        hint: insertError.hint
      });
      throw new Error(`Failed to record reward claim: ${insertError.message}`);
    }
    
    console.log('Reward claimed successfully:', newClaimedReward);
    return newClaimedReward;

  } catch (error: any) {
    console.error('Error in claimSpecificReward:', error);
    // Ensure the error is re-thrown so the UI can catch it
    throw error;
  }
};

/**
 * Get the user's current point total.
 * This function fetches the REAL points directly from the user_gamification_stats table.
 */
export const getTotalPoints = async (): Promise<number> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) throw new Error("User not authenticated");

    console.log('🔍 DEBUG: Fetching REAL points from user_gamification_stats table...');
    
    // Get REAL current_points from user_gamification_stats table
    const { data: userStats, error: statsError } = await missionFreshClient
      .from('user_gamification_stats')
      .select('current_points')
      .eq('user_id', user.id)
      .single();

    if (statsError) {
      console.error('Error fetching user gamification stats:', statsError);
      throw new Error(`Failed to fetch user points: ${statsError.message}`);
    }

    const realPoints = userStats?.current_points || 0;
    console.log(`✅ REAL POINTS: User ${user.id} has ${realPoints} current_points from database`);
    return realPoints;

  } catch (error: any) {
    console.error('Error fetching total points:', error);
    throw error;
  }
};

/**
 * Add points to a user's profile.
 * This function updates the 'points' column in the 'profiles' table.
 */
export const earnPoints = async (userId: string, points: number, activityType: string, activityName?: string): Promise<any> => {
  try {
    if (points === 0) {
      console.log('No points to award (0 points requested)');
      return null;
    }
    
    if (points < 0) {
      console.log('Processing negative points (point deduction):', points);
      // Allow negative points for deductions (reward claiming, etc.)
    }

    // Add a log entry for the points earned
    const { error: logError } = await missionFreshClient
        .from('activity_points_log')
        .insert({
            user_id: userId,
            points_earned: points,
            activity_type: activityType,
            activity_name: activityName,
            created_at: new Date().toISOString()
        });

    if (logError) {
        console.error('Error logging activity points:', logError);
        // Continue with updating profile points even if logging fails
    }

    // Call the RPC function to atomically increment user points
    const { data: updatedStats, error: rpcError } = await missionFreshClient.rpc('increment_user_points', { user_id_param: userId, points_to_add: points }).single();

    if (rpcError) {
        console.error('Error incrementing user points via RPC:', rpcError);
        throw rpcError; // Throw the RPC error
    }

    // Check for and award badges if criteria met
    if (updatedStats && (updatedStats as any).current_points !== undefined) {
        const currentPoints = (updatedStats as any).current_points;
        const earnedBadges = await getEarnedBadges(userId);
        const earnedBadgeIds = new Set(earnedBadges.map(b => b.badge_id));

        for (const badge of badges) {
            if (currentPoints >= badge.threshold && !earnedBadgeIds.has(badge.id)) {
                // Award the badge
                const { error: insertBadgeError } = await missionFreshClient
                    .from('user_badges')
                    .insert({ user_id: userId, badge_id: badge.id });

                if (insertBadgeError) {
                    console.error(`Error awarding badge ${badge.name} to user ${userId}:`, insertBadgeError);
                    // Continue even if badge insert fails, points were still awarded
                } else {
                    console.log(`User ${userId} earned new badge: ${badge.name}`);
                    // Note: Toast notifications for badges should be handled in the UI layer, not in service functions
                    // to avoid infinite render loops during React component lifecycle
                }
            }
        }
    }

    return updatedStats;

  } catch (error: any) {
    console.error('Error in earnPoints:', error);
    throw error;
  }
};


/**
 * Update the user's profile with new steps from a native health tracker
 * This function calls recordStepCount to save the daily steps.
 */
export const syncNativeSteps = async (steps: number) => {
  try {
    // Format today's date
    const today = new Date().toISOString().split('T')[0];

    // Record the steps
    return await recordStepCount(steps, today);
  } catch (error: any) {
    console.error('Error syncing native steps:', error);
    throw error;
  }
};

/**
 * Retroactively award badges for existing points
 * This function checks if the user should have earned badges based on their current points
 * and awards any missing badges
 */
export const awardMissingBadges = async (userId: string): Promise<void> => {
  try {
    const currentPoints = await getTotalPoints();
    const earnedBadges = await getEarnedBadges(userId);
    const earnedBadgeIds = new Set(earnedBadges.map(b => b.badge_id));

    console.log(`Checking badges for user ${userId} with ${currentPoints} points`);
    console.log(`Currently earned badges:`, earnedBadgeIds);

    for (const badge of badges) {
      if (currentPoints >= badge.threshold && !earnedBadgeIds.has(badge.id)) {
        console.log(`Awarding missing badge: ${badge.name} (threshold: ${badge.threshold})`);
        
        // Award the badge
        const { error: insertBadgeError } = await missionFreshClient
          .from('user_badges')
          .insert({ user_id: userId, badge_id: badge.id });

        if (insertBadgeError) {
          console.error(`Error awarding badge ${badge.name} to user ${userId}:`, insertBadgeError);
        } else {
          console.log(`Successfully awarded badge: ${badge.name}`);
        }
      }
    }
  } catch (error: any) {
    console.error('Error in awardMissingBadges:', error);
    // Don't throw - this is a background process
  }
};

export const getRewardsPageData = async () => {
  console.log('🚀 getRewardsPageData: Starting to fetch all rewards page data...');
  
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.warn("❌ getRewardsPageData called without authenticated user for fetching earned badges.");
    return { userPoints: 0, availableRewards: [], rewardHistory: [], earnedBadges: [] };
  }

  console.log('✅ User authenticated:', user.id);

  try {
    // First, award any missing badges for existing points
    console.log('🏆 Awarding missing badges...');
    await awardMissingBadges(user.id);

    console.log('📊 Fetching all data in parallel...');
    const [userPoints, availableRewards, rewardHistory, earnedBadgesResult] = await Promise.all([
      getTotalPoints(),
      getAvailableRewards(),
      getRewardHistory(),
      getEarnedBadges(user.id),
    ]);
    
    console.log('✅ All data fetched successfully:', {
      userPoints,
      availableRewards: availableRewards.length,
      rewardHistory: rewardHistory.length,
      earnedBadges: earnedBadgesResult.length
    });
    
    return { userPoints, availableRewards, rewardHistory, earnedBadges: earnedBadgesResult };
  } catch (error) {
    console.error('💥 Error in getRewardsPageData:', error);
    throw error;
  }
};
