import { supabase } from "@/lib/supabase";
import { Database, Tables } from "@/lib/database.types";
import { format, isValid, parseISO } from 'date-fns';

export interface DailyHealthSummaryEntry {
  date: string;
  nicotineUnits: number;
  avgCravingIntensity: number;
  mood?: number;
  energyLevel?: number;
  focusLevel?: number;
  sleepHours?: number;
  stressLevel?: number;
  totalSteps: number;
  totalMeditationMinutes: number;
  bloodPressureSys?: number;
  bloodPressureDia?: number;
  heartRate?: number;
}

type NicotineLogRow = Tables<{ schema: 'mission_fresh' }, 'nicotine_logs'>;
type CravingRow = Tables<{ schema: 'mission_fresh' }, 'craving_logs'>;
type DailyCheckInRow = Tables<{ schema: 'mission_fresh' }, 'health_metrics'>;
type StepRewardRow = Tables<{ schema: 'mission_fresh' }, 'step_rewards'>;
type MeditationSessionRow = Tables<{ schema: 'mission_fresh'}, 'meditation_sessions'>;

export const getNicotineUseLogs = async (startDate?: string, endDate?: string, productType?: string): Promise<NicotineLogRow[]> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return [];

    let query = supabase
      .schema('mission_fresh')
      .from('nicotine_logs')
      .select('*')
      .eq('user_id', user.id);

    if (startDate) {
      query = query.gte('date', startDate);
    }
    if (endDate) {
      query = query.lte('date', endDate);
    }
    if (productType) {
      query = query.eq('product_type', productType);
    }

    query = query.order('date', { ascending: true });

    const { data, error } = await query;

    if (error) throw error;
    return data || [];

  } catch (error: any) {
    console.error("Error fetching nicotine logs:", error);
    throw new Error(`Failed to fetch nicotine logs: ${error.message || error}`);
  }
};

export const getCravingLogs = async (startDate?: string, endDate?: string, trigger?: string): Promise<CravingRow[]> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return [];

    let query = supabase
      .schema('mission_fresh')
      .from('craving_logs')
      .select('*')
      .eq('user_id', user.id);

    if (startDate) {
      query = query.gte('timestamp', startDate);
    }
    if (endDate) {
      query = query.lte('timestamp', endDate);
    }
    if (trigger) {
      query = query.eq('trigger', trigger);
    }

    query = query.order('timestamp', { ascending: true });

    const { data, error } = await query;

    if (error) throw error;
    return data || [];

  } catch (error: any) {
    console.error("Error fetching craving logs:", error);
    throw new Error(`Failed to fetch craving logs: ${error.message || error}`);
  }
};

export const getDailyCheckIns = async (startDate?: string, endDate?: string): Promise<DailyCheckInRow[]> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return [];

    let query = supabase
      .schema('mission_fresh')
      .from('health_metrics')
      .select('*')
      .eq('user_id', user.id);

    if (startDate) {
      query = query.gte('date', startDate);
    }
    if (endDate) {
      query = query.lte('date', endDate);
    }

    query = query.order('date', { ascending: true });

    const { data, error } = await query;

    if (error) throw error;
    return data || [];

  } catch (error: any) {
    console.error("Error fetching daily check-ins:", error);
    throw new Error(`Failed to fetch daily check-ins: ${error.message || error}`);
  }
};

export const getStepRewards = async (startDate?: string, endDate?: string): Promise<StepRewardRow[]> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return [];

    let query = supabase
      .schema('mission_fresh')
      .from('step_rewards')
      .select('*')
      .eq('user_id', user.id);

    if (startDate) {
      query = query.gte('date', startDate);
    }
    if (endDate) {
      query = query.lte('date', endDate);
    }

    query = query.order('date', { ascending: true });

    const { data, error } = await query;

    if (error) throw error;
    return data || [];

  } catch (error: any) {
    console.error("Error fetching step rewards:", error);
    throw new Error(`Failed to fetch step rewards: ${error.message || error}`);
  }
};

export const getMeditationSessions = async (startDate?: string, endDate?: string): Promise<MeditationSessionRow[]> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return [];

    let query = supabase
      .schema('mission_fresh')
      .from('meditation_sessions')
      .select('*')
      .eq('user_id', user.id);

    // Assuming completed_at is the relevant timestamp for date range filtering
    if (startDate) {
      query = query.gte('completed_at', `${startDate}T00:00:00.000Z`);
    }
    if (endDate) {
      query = query.lte('completed_at', `${endDate}T23:59:59.999Z`);
    }

    query = query.order('completed_at', { ascending: true });

    const { data, error } = await query;

    if (error) throw error;
    return data || [];

  } catch (error: any) {
    console.error("Error fetching meditation sessions:", error);
    throw new Error(`Failed to fetch meditation sessions: ${error.message || error}`);
  }
};

export const getLatestStepCount = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) return null;

  const { data, error } = await supabase
    .schema('mission_fresh')
    .from('step_rewards')
    .select('*')
    .eq('user_id', user.id)
    .order('date', { ascending: false })
    .limit(1)
    .maybeSingle();

  if (error) {
    console.error('Error fetching latest step count:', error);
    throw error;
  }

  return data;
};

export const getDailyHealthSummary = async (startDate?: string, endDate?: string, filters?: { productType?: string, trigger?: string }): Promise<DailyHealthSummaryEntry[]> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return [];

    const nicotineLogs = await getNicotineUseLogs(startDate, endDate, filters?.productType);
    const cravingLogs = await getCravingLogs(startDate, endDate, filters?.trigger);
    const dailyCheckIns = await getDailyCheckIns(startDate, endDate);
    const stepRewards = await getStepRewards(startDate, endDate);
    const meditationSessions = await getMeditationSessions(startDate, endDate);

    interface AggregationHelper {
      date: string;
      nicotineUnits: number;
      cravingIntensitySum: number;
      cravingCount: number;
      mood?: number;
      energyLevel?: number;
      focusLevel?: number;
      sleepHours?: number;
      stressLevel?: number;
      totalSteps: number;
      totalMeditationMinutes: number;
      bloodPressureSys?: number;
      bloodPressureDia?: number;
      heartRate?: number;
    }
    const dailyData: { [key: string]: AggregationHelper } = {};

    const ensureDayData = (dateStr: string) => {
      if (!dailyData[dateStr]) {
        dailyData[dateStr] = {
          date: dateStr,
          nicotineUnits: 0,
          cravingIntensitySum: 0,
          cravingCount: 0,
          totalSteps: 0,
          totalMeditationMinutes: 0,
        };
      }
    };
    
    const safeFormatDate = (dateInput: string | Date): string | null => {
      try {
        const dateObj = typeof dateInput === 'string' ? parseISO(dateInput) : dateInput;
        if (isValid(dateObj)) {
          return format(dateObj, 'yyyy-MM-dd');
        }
        return null;
      } catch (e) {
        console.warn("Invalid date encountered during formatting:", dateInput, e);
        return null;
      }
    };

    nicotineLogs.forEach(log => {
      const dateStr = safeFormatDate(log.date);
      if (!dateStr) return;
      ensureDayData(dateStr);

      if (log.used_nicotine && typeof log.quantity === 'number') {
        dailyData[dateStr].nicotineUnits += log.quantity;
      }
    });

    cravingLogs.forEach(log => {
      const dateStr = safeFormatDate(log.timestamp);
      if (!dateStr) return;
      ensureDayData(dateStr);

      if (typeof log.intensity === 'number') {
        dailyData[dateStr].cravingIntensitySum += log.intensity;
        dailyData[dateStr].cravingCount++;
      }
    });

    dailyCheckIns.forEach(checkIn => {
      const dateStr = safeFormatDate(checkIn.date);
      if (!dateStr) return;
      ensureDayData(dateStr);
      
      if (typeof checkIn.mood === 'number') dailyData[dateStr].mood = checkIn.mood;
      if (typeof checkIn.energy_level === 'number') dailyData[dateStr].energyLevel = checkIn.energy_level;
      if (typeof checkIn.focus_level === 'number') dailyData[dateStr].focusLevel = checkIn.focus_level;
      if (typeof checkIn.sleep_hours === 'number') dailyData[dateStr].sleepHours = checkIn.sleep_hours;
      if (typeof checkIn.stress_level === 'number') dailyData[dateStr].stressLevel = checkIn.stress_level;
    });

    stepRewards.forEach(log => {
      const dateStr = safeFormatDate(log.date);
      if (!dateStr) return;
      ensureDayData(dateStr);
      if (typeof log.steps === 'number') {
        dailyData[dateStr].totalSteps += log.steps;
      }
    });

    meditationSessions.forEach(session => {
      // Assuming completed_at is a full timestamp string
      if (!session.completed_at) return;
      const dateStr = safeFormatDate(session.completed_at);
      if (!dateStr) return;
      ensureDayData(dateStr);
      if (typeof session.duration === 'number') { // duration is in minutes
        dailyData[dateStr].totalMeditationMinutes += session.duration;
      }
    });

    const aggregatedData: DailyHealthSummaryEntry[] = Object.values(dailyData).map(day => ({
      date: day.date,
      nicotineUnits: day.nicotineUnits,
      avgCravingIntensity: day.cravingCount > 0 ? day.cravingIntensitySum / day.cravingCount : 0,
      mood: day.mood,
      energyLevel: day.energyLevel,
      focusLevel: day.focusLevel,
      sleepHours: day.sleepHours,
      stressLevel: day.stressLevel,
      totalSteps: day.totalSteps,
      totalMeditationMinutes: day.totalMeditationMinutes,
      bloodPressureSys: day.bloodPressureSys,
      bloodPressureDia: day.bloodPressureDia,
      heartRate: day.heartRate,
    }));

    aggregatedData.sort((a, b) => {
        const dateA = parseISO(a.date).getTime();
        const dateB = parseISO(b.date).getTime();
        return dateA - dateB;
    });

    return aggregatedData;

  } catch (error: any) {
    console.error("Error fetching daily health summary:", error.message || error);
    throw new Error(`Failed to fetch daily health summary: ${error.message || error}`);
  }
};
