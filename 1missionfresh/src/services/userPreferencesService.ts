import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { Tables, TablesInsert, TablesUpdate } from '@/lib/database.types';

export type UserPreferencesRow = Tables<'user_preferences'>;
export type UserPreferencesInsert = TablesInsert<'user_preferences'>;
export type UserPreferencesUpdate = TablesUpdate<'user_preferences'>;

export const getUserPreferences = async (): Promise<UserPreferencesRow | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return null;
    
    const { data, error } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();
    
    if (error) {
        if (error.code === 'PGRST116') {
            return createDefaultPreferences(user.id);
        }
        throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Error fetching user preferences:', error);
    throw error;
  }
};

export const createDefaultPreferences = async (userId: string): Promise<UserPreferencesRow> => {
    try {
        const defaultPrefsInsert: UserPreferencesInsert = {
          user_id: userId,
          theme: 'system',
          notification_logs: true,
          notification_milestones: true,
          notification_cravings: true,
          dashboard_widgets: ['overview', 'holisticChart', 'cravingChart', 'milestones', 'achievements', 'logActions', 'steps', 'quote', 'aiInsights', 'communityStats', 'quickTools', 'panicButton'],
          notifications: {},
        };

        const { data, error } = await supabase
          .from('user_preferences')
          .insert(defaultPrefsInsert)
          .select()
          .single();

        if (error) throw error;
        return data;
    } catch (error) {
        console.error('Error creating default preferences:', error);
        throw error;
    }
};

export const updateUserPreferences = async (preferences: UserPreferencesUpdate): Promise<UserPreferencesRow> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error("User not authenticated");
    
    const { data, error } = await supabase
      .from('user_preferences')
      .update(preferences)
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) throw error;

    toast.success("Preferences updated!");
    return data;
  } catch (error) {
    console.error('Error updating user preferences:', error);
    toast.error("Failed to save preferences");
    throw error;
  }
};

export const getProductCosts = async (): Promise<{ [key: string]: number }> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) return { cigarette: 0, vape: 0, pouch: 0 };

  try {
    const { data, error } = await supabase
      .from('user_cost_of_products')
      .select('product_type, cost')
      .eq('user_id', user.id);

    if (error) throw error;

    const costs = data.reduce((acc, item) => {
      acc[item.product_type] = item.cost;
      return acc;
    }, {} as { [key: string]: number });

    return {
      cigarette: costs.cigarette || 0,
      vape: costs.vape || 0,
      pouch: costs.pouch || 0,
    };
  } catch (error) {
    console.error('Error fetching product costs:', error);
    return { cigarette: 0, vape: 0, pouch: 0 };
  }
};

export const updateProductCosts = async (costs: { [key: string]: number }): Promise<void> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error("User not authenticated");

    const upsertData = Object.entries(costs).map(([product_type, cost]) => ({
      user_id: user.id,
      product_type,
      cost: cost || 0,
    }));

    const { error } = await supabase
      .from('user_cost_of_products')
      .upsert(upsertData, { onConflict: 'user_id, product_type' });

    if (error) throw error;
  } catch (error) {
    console.error('Error updating product costs:', error);
    throw error;
  }
};
