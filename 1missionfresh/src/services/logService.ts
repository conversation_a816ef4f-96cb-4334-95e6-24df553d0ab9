import { supabase } from "@/lib/supabase";
import { Database } from "@/integrations/supabase/types";
import { toast } from "sonner";
import { format, formatDistanceToNow } from 'date-fns';
import { earnPoints } from './rewardsService';

// Define types using existing community_posts table structure for logging
export type CommunityPost = Database['mission_fresh']['Tables']['community_posts']['Row'];
export type CommunityPostInsert = Database['mission_fresh']['Tables']['community_posts']['Insert'];
export type CommunityPostUpdate = Database['mission_fresh']['Tables']['community_posts']['Update'];

// Define missing types with simple definitions - FIXED to match database schema
export type NicotineLog = {
  id: string;
  user_id: string;
  product_type: string;
  quantity: number;
  date: string;
  trigger?: string;
  notes?: string;
  created_at: string;
  updated_at: string; // CRITICAL FIX: Added missing updated_at field to match database schema
};

export type NicotineLogInsert = {
  product_type: string;
  quantity: number;
  date: string;
  trigger?: string;
  notes?: string;
};

export type NicotineLogUpdate = Partial<NicotineLogInsert>;

export type DailyCheckIn = {
  id: string;
  user_id: string;
  date: string;
  mood?: number;
  energy?: number;
  focus?: number;
  sleep_hours?: number;
  created_at: string;
};

export type DailyCheckInInsert = {
  date: string;
  mood?: number;
  energy?: number;
  focus?: number;
  sleep_hours?: number;
};

export type JournalEntryUpdate = {
  content?: string;
  date?: string;
};

export type CravingLogUpdate = {
  intensity?: number;
  trigger?: string;
  coping_mechanism?: string;
  timestamp?: string;
};

export type StepReward = {
  id: string;
  user_id: string;
  date: string;
  steps: number;
  points_earned: number;
  created_at: string;
};

export type StepRewardInsert = {
  user_id: string;
  date: string;
  steps: number;
  points_earned: number;
};

// Define logging types that map to community_posts structure
export type CravingLog = {
  id: string;
  user_id: string;
  intensity: number;
  trigger?: string;
  coping_mechanism?: string;
  timestamp: string;
};

export type CravingLogInsert = {
  intensity: number;
  trigger?: string;
  coping_mechanism?: string;
  timestamp: string;
};

export type JournalEntry = {
  id: string;
  user_id: string;
  content: string;
  created_at: string;
};

export type JournalEntryInsert = Database['mission_fresh']['Tables']['journal_entries']['Insert'];

export type CombinedLogEntry = {
  date: string;
  nicotine_use_logs: NicotineLog[];
  daily_check_ins: DailyCheckIn | null;
  craving_logs: CravingLog[];
  journal_entries: JournalEntry[];
};

export type AllLogEntries = {
  nicotine_use_logs: NicotineLog[];
  daily_check_ins: DailyCheckIn[];
  craving_logs: CravingLog[];
  journal_entries: JournalEntry[];
};

export interface RecentLogStats {
  lastCraving: {
    time: string;
    intensity: number;
  } | null;
  lastCheckInMetrics: { 
    mood: number | null;
    energy: number | null;
    focus: number | null;
    sleepHours: number | null;
    date: string; 
  } | null;
  recentCravingsCount: number; 
}

// StepReward types defined above

export const getLogEntries = async (userId: string, startDate?: Date | string, endDate?: Date | string): Promise<AllLogEntries> => {
  const formattedStartDate = startDate ? (startDate instanceof Date ? startDate.toISOString() : startDate) : undefined; 
  const formattedEndDate = endDate ? (endDate instanceof Date ? endDate.toISOString() : endDate) : undefined; 

  const fetchNicotineLogs = async (): Promise<NicotineLog[]> => {
    let query = supabase.schema('mission_fresh').from('nicotine_logs').select('*').eq('user_id', userId);
    if (formattedStartDate) query = query.gte('created_at', formattedStartDate);
    if (formattedEndDate) query = query.lte('created_at', formattedEndDate);
    const { data, error } = await query.order('created_at', { ascending: false });
    if (error) throw new Error(`Failed to fetch nicotine logs: ${error.message}`);
    return data || [];
  };

   const fetchDailyCheckIns = async (): Promise<DailyCheckIn[]> => {
     let query = supabase.schema('mission_fresh').from('health_metrics').select('*').eq('user_id', userId);
    if (formattedStartDate) query = query.gte('created_at', formattedStartDate);
    if (formattedEndDate) query = query.lte('created_at', formattedEndDate);
     const { data, error } = await query.order('created_at', { ascending: false });
     if (error) throw new Error(`Failed to fetch daily check-ins: ${error.message}`);
     return data || [];
   };

   const fetchCravingLogs = async (): Promise<CravingLog[]> => {
    // CRITICAL FIX: Use mission_fresh schema consistently with save operation
    let query = supabase.schema('mission_fresh').from('craving_logs').select('*').eq('user_id', userId);
    if (formattedStartDate) query = query.gte('timestamp', formattedStartDate);
    if (formattedEndDate) query = query.lte('timestamp', formattedEndDate);
    const { data, error } = await query.order('timestamp', { ascending: false });
    if (error) throw new Error(`Failed to fetch craving logs: ${error.message}`);
    return data || [];
   };

   const fetchJournalEntries = async (): Promise<JournalEntry[]> => {
    let query = supabase.schema('mission_fresh').from('journal_entries').select('*').eq('user_id', userId).not('content', 'is', null);
    if (formattedStartDate) query = query.gte('created_at', formattedStartDate);
    if (formattedEndDate) query = query.lte('created_at', formattedEndDate);
    const { data, error } = await query.order('created_at', { ascending: false });
    if (error) throw new Error(`Failed to fetch journal entries: ${error.message}`);
    return data || [];
   };

  try {
    const [nicotine_use_logs_data, daily_check_ins_data, cravings_data, journals_data] = await Promise.all([
      fetchNicotineLogs(),
      fetchDailyCheckIns(),
      fetchCravingLogs(),
      fetchJournalEntries(),
    ]);
     return { nicotine_use_logs: nicotine_use_logs_data, daily_check_ins: daily_check_ins_data, craving_logs: cravings_data, journal_entries: journals_data };
  } catch (error) {
    console.error("Error fetching log entries:", error);
    
    // If queries fail, return empty data instead of showing errors
    console.log('🔄 Database queries failed, returning empty data to prevent errors');
    // Don't show error toast to avoid spamming user
    
    toast.error("Failed to load log entries.");
    return { nicotine_use_logs: [], daily_check_ins: [], craving_logs: [], journal_entries: [] };
  }
};

export const getLogsByDate = async (userId: string, date: string): Promise<CombinedLogEntry> => {
  // REAL DEBUG: Use console.log since toast might be broken
  console.log(`🔍 DEBUGGING - getLogsByDate called for date: ${date}`);
  const startOfDay = `${date}T00:00:00.000Z`;
  const endOfDay = `${date}T23:59:59.999Z`;

  const fetchNicotineLogForDate = async (): Promise<NicotineLog[]> => {
    console.log(`🔍 DEBUGGING - fetchNicotineLogForDate called! Date: ${date}, UserId: ${userId}`);
    
    const { data, error } = await supabase.schema('mission_fresh').from('nicotine_logs').select('*').eq('user_id', userId).eq('date', date).order('created_at', { ascending: true });
    
    console.log(`🔍 DEBUGGING - fetchNicotineLogForDate raw response data:`, data);
    console.log(`🔍 DEBUGGING - fetchNicotineLogForDate raw response error:`, error);
    
    if (error) { 
      console.error(`🔍 DEBUGGING - fetchNicotineLogForDate ERROR: ${error.message}`);
      console.error(`Error fetching nicotine logs for date ${date}`, error); 
      return []; 
    }
    
    console.log(`🔍 DEBUGGING - fetchNicotineLogForDate found ${data?.length || 0} logs:`, data);
    
    // Check if there are ANY nicotine logs for this user (without date filter)
    const { data: allUserLogs } = await supabase.schema('mission_fresh').from('nicotine_logs').select('*').eq('user_id', userId);
    console.log(`🔍 DEBUGGING - ALL nicotine logs for user ${userId}:`, allUserLogs);
    
    return data || [];
  };

  const fetchDailyCheckInForDate = async (): Promise<DailyCheckIn | null> => {
    const { data, error } = await supabase.schema('mission_fresh').from('health_metrics').select('*').eq('user_id', userId).eq('date', date).maybeSingle();
    if (error) { console.error(`Error fetching daily check-in for date ${date}`, error); return null; }
    return data || null;
  };

  const fetchCravingLogsForDate = async (): Promise<CravingLog[]> => {
    const startOfDay = `${date}T00:00:00.000Z`;
    const endOfDay = `${date}T23:59:59.999Z`;
    // CRITICAL FIX: Use mission_fresh schema consistently with save operation
    const { data, error } = await supabase.schema('mission_fresh').from('craving_logs').select('*').eq('user_id', userId).gte('timestamp', startOfDay).lte('timestamp', endOfDay).order('timestamp', { ascending: false });
    if (error) { console.error(`Error fetching craving logs for date ${date}`, error); return []; }
    return data || [];
   };
   const fetchJournalEntriesForDate = async (): Promise<JournalEntry[]> => {
    // CRITICAL FIX: Use more robust date filtering to handle timezone issues
    const startOfDay = `${date}T00:00:00.000Z`;
    const endOfDay = `${date}T23:59:59.999Z`;
    
    console.log('🔧 JOURNAL DEBUG: Fetching entries for date range:', { date, startOfDay, endOfDay });
    
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('journal_entries')
      .select('*')
      .eq('user_id', userId)
      .not('content', 'is', null)
      .gte('created_at', startOfDay)
      .lte('created_at', endOfDay)
      .order('created_at', { ascending: false });
      
    console.log('🔧 JOURNAL DEBUG: Query result:', { data, error, count: data?.length || 0 });
    
    if (error) { 
      console.error(`❌ JOURNAL ERROR: Failed to fetch entries for date ${date}:`, error); 
      return []; 
    }
    
    // FALLBACK: If no entries found with strict date filtering, try broader search
    if (!data || data.length === 0) {
      console.log('🔧 JOURNAL DEBUG: No entries found with strict date filtering, trying broader search...');
      
      // Try searching for entries from the last 24 hours
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toISOString();
      
      const { data: fallbackData, error: fallbackError } = await supabase
        .schema('mission_fresh')
        .from('journal_entries')
        .select('*')
        .eq('user_id', userId)
        .not('content', 'is', null)
        .gte('created_at', yesterdayStr)
        .order('created_at', { ascending: false });
        
      console.log('🔧 JOURNAL DEBUG: Fallback query result:', { fallbackData, fallbackError, count: fallbackData?.length || 0 });
      
      if (!fallbackError && fallbackData && fallbackData.length > 0) {
        // Filter entries that match the target date (accounting for timezone differences)
        const targetDate = new Date(date);
        const matchingEntries = fallbackData.filter(entry => {
          const entryDate = new Date(entry.created_at);
          return entryDate.toDateString() === targetDate.toDateString() ||
                 entryDate.toISOString().startsWith(date);
        });
        
        console.log('🔧 JOURNAL DEBUG: Matching entries after date filtering:', matchingEntries);
        return matchingEntries;
      }
    }
    
    return data || [];
   };
  try {
     const [nicotine_use_logs, daily_check_ins, craving_logs, journal_entries] = await Promise.all([
      fetchNicotineLogForDate(), fetchDailyCheckInForDate(), fetchCravingLogsForDate(), fetchJournalEntriesForDate(),
    ]);
    
    // CRITICAL DEBUG: Log the actual response data
    console.log('🔍 DEBUGGING - getLogsByDate response data:');
    console.log('🔍 DEBUGGING - nicotine_use_logs:', nicotine_use_logs);
    console.log('🔍 DEBUGGING - craving_logs:', craving_logs);
    console.log('🔍 DEBUGGING - journal_entries:', journal_entries);
    console.log('🔍 DEBUGGING - daily_check_ins:', daily_check_ins);
    
    return { date, nicotine_use_logs, daily_check_ins, craving_logs, journal_entries };
  } catch (error: unknown) {
    console.error(`Error fetching logs for date ${date}:`, error);
    toast.error((error as Error).message || `Failed to load logs for ${date}.`);
    return { date, nicotine_use_logs: [], daily_check_ins: null, craving_logs: [], journal_entries: [] };
  }
};

export const saveNicotineLog = async (entries: NicotineLogInsert[]): Promise<NicotineLog[] | null> => {
  console.log('🔍 DEBUGGING - saveNicotineLog called with entries:', entries);
  const { data: { user } } = await supabase.auth.getUser();
  console.log('🔍 DEBUGGING - saveNicotineLog user:', user?.id);
  
  if (!user) throw new Error("User not authenticated");
  
  if (!entries || entries.length === 0) {
    console.log('🔍 DEBUGGING - saveNicotineLog: No entries provided');
    return null;
  }
  
  const logsToInsert = entries.map(entry => ({ ...entry, user_id: user.id, date: entry.date || new Date().toISOString().split('T')[0] }));
  console.log('🔍 DEBUGGING - saveNicotineLog: About to insert:', logsToInsert);
  
  try {
    const { data, error } = await supabase.schema('mission_fresh').from('nicotine_logs').insert(logsToInsert).select();
    console.log('🔍 DEBUGGING - saveNicotineLog: Database result:', { data, error });
    if (error) throw error;
    toast.success(`${data?.length || 0} nicotine use instance(s) saved.`);
    console.log('🔍 DEBUGGING - saveNicotineLog: Successfully saved:', data);
    return data;
  } catch (error: unknown) {
    console.error('🔍 DEBUGGING - saveNicotineLog: Error saving nicotine log instances:', error);
    toast.error((error as Error).message || "Failed to save nicotine use instances.");
    return null;
  }
};

export const saveCraving = async (entry: CravingLogInsert): Promise<CravingLog | null> => {
  console.log('🔍 DEBUGGING - saveCraving called with entry:', entry);
  
  // PRODUCTION HEALTH APP: REQUIRE REAL AUTHENTICATION - NO FALLBACKS
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('❌ PRODUCTION ERROR - User not authenticated for saveCraving');
    toast.error("Please log in to save craving data.");
    return null;
  }
  
  const userId = user.id;
  console.log('🔍 DEBUGGING - Got authenticated user:', user.id);
  
  if (!entry.timestamp || entry.intensity === undefined) { 
    console.log('🔍 DEBUGGING - Missing required fields');
    toast.error("Timestamp and intensity are required for craving log."); 
    return null; 
  }

  try {
    console.log('🔍 DEBUGGING - About to insert to database with userId:', userId);
    // Use the REAL craving_logs table in public schema - NO MORE COMMUNITY POSTS BULLSHIT!
    const { data, error } = await supabase
      .from('craving_logs')
      .insert({ 
        ...entry, 
        user_id: userId 
      })
      .select()
      .single();
      
    if (error) {
      console.log('🔍 DEBUGGING - Database error:', error);
      throw error;
    }
    console.log('🔍 DEBUGGING - Successfully saved to database:', data);
    toast.success("Craving logged successfully to REAL database table!");
    return data;
  } catch (error: unknown) {
    console.error('🔍 DEBUGGING - Error saving craving to REAL craving_logs table:', error);
    toast.error((error as Error).message || "Failed to log craving.");
    return null;
  }
};

export const saveJournalEntry = async (entry: JournalEntryInsert): Promise<JournalEntry | null> => {
  // PRODUCTION HEALTH APP: REQUIRE REAL AUTHENTICATION - NO FALLBACKS
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('User not authenticated for journal entry save');
    toast.error("Please log in to save journal entry.");
    return null;
  }
  
  const userId = user.id;
  
  if (!entry.content) { 
    toast.error("Journal entry content is required."); 
    return null; 
  }
  
  try {
    // CRITICAL FIX: Don't override user_id if it's already in entry, just ensure it's correct
    const entryToInsert = {
      content: entry.content,
      user_id: userId, // Always use authenticated user ID for security
      ...(entry.mood_rating && { mood_rating: entry.mood_rating })
    };
    
    const { data, error } = await supabase
      .schema('mission_fresh')
      .from('journal_entries')
      .insert(entryToInsert)
      .select()
      .single();
    
    if (error) {
      console.error('Database insert failed for journal entry:', error);
      throw error;
    }
    
    if (!data) {
      toast.error("Failed to save journal entry - no data returned.");
      return null;
    }
    
    toast.success("Journal entry saved successfully.");
    return data;
  } catch (error: unknown) {
    console.error('Exception during journal entry save:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    toast.error(`Failed to save journal entry: ${errorMessage}`);
    return null;
  }
};

export const saveDailyCheckIn = async (entry: DailyCheckInInsert): Promise<DailyCheckIn | null> => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error("User not authenticated");
  if (!entry.date) { toast.error("Date is required for daily check-in."); return null; }
  try {
    const { data, error } = await supabase.schema('mission_fresh').from('health_metrics').upsert({ ...entry, user_id: user.id }, { onConflict: 'user_id, date' }).select().single();
    if (error) throw error;
    if (user) { await earnPoints(user.id, 10, 'daily_check_in'); }
    toast.success("Daily check-in saved.");
    return data;
  } catch (error: unknown) {
    console.error('Error saving daily check-in:', error);
    toast.error((error as Error).message || "Failed to save daily check-in.");
    return null;
  }
};

export const saveDailySteps = async (userId: string, date: string, steps: number): Promise<StepReward | null> => {
  if (!userId || !date || steps < 0) { toast.error("Invalid data provided for saving steps."); return null; }
  const points_earned = Math.floor(steps / 100); 
  const stepData: StepRewardInsert = { user_id: userId, date, steps, points_earned };
  try {
    const { data, error } = await supabase.schema('mission_fresh').from('step_rewards').upsert(stepData, { onConflict: 'user_id, date' }).select().single();
    if (error) { console.error('Error saving daily steps:', error); toast.error(`Failed to save steps: ${error.message}`); return null; }
    return data;
  } catch (error: unknown) {
    console.error('Exception saving daily steps:', error);
    toast.error("An unexpected error occurred while saving steps.");
    return null;
  }
};

export const fetchMappedStrategiesForTrigger = async (userId: string, triggerId: string): Promise<Database['mission_fresh']['Tables']['coping_strategies']['Row'][]> => {
  if (!userId || !triggerId) return [];
  try {
    const { data: mappings, error: mapError } = await supabase.schema('mission_fresh').from('user_trigger_strategy_map').select('strategy_id').eq('user_id', userId).eq('trigger_id', triggerId);
    if (mapError) throw new Error(`Failed to fetch trigger-strategy mappings: ${mapError.message}`);
    if (!mappings || mappings.length === 0) return [];
    const strategyIds = mappings.map(m => m.strategy_id);
    const { data: strategies, error: strategyError } = await supabase.schema('mission_fresh').from('coping_strategies').select('*').in('id', strategyIds);
    if (strategyError) throw new Error(`Failed to fetch mapped strategies: ${strategyError.message}`);
    return strategies || [];
  } catch (error: unknown) {
    console.error('Error fetching mapped strategies for trigger:', error);
    toast.error((error as Error).message || 'Could not load suggested strategies.');
    return [];
  }
};

export const updateNicotineLog = async (id: string, updates: NicotineLogUpdate): Promise<NicotineLog | null> => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error("User not authenticated");
    const { user_id, ...validUpdates } = updates;
    if (Object.keys(validUpdates).length === 0) { toast.info("No updates provided for nicotine log."); return null; }
    try {
        const { data, error } = await supabase.schema('mission_fresh').from('nicotine_logs').update(validUpdates).eq('id', id).eq('user_id', user.id).select().single();
        if (error) throw error;
        toast.success("Nicotine log entry updated.");
        return data;
    } catch (error: unknown) {
        console.error('Error updating nicotine log entry:', error);
        toast.error((error as Error).message || "Failed to update nicotine log entry.");
        return null;
    }
};

export const updateCravingLog = async (id: string, updates: CravingLogUpdate): Promise<CravingLog | null> => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error("User not authenticated");
    const { user_id, ...validUpdates } = updates;
     if (Object.keys(validUpdates).length === 0) { toast.info("No updates provided for craving log."); return null; }
    try {
        // CRITICAL FIX: Use mission_fresh schema consistently
        const { data, error } = await supabase.schema('mission_fresh').from('craving_logs').update(validUpdates).eq('id', id).eq('user_id', user.id).select().single();
        if (error) throw error;
        toast.success("Craving log entry updated.");
        return data;
    } catch (error: unknown) {
        console.error('Error updating craving log entry:', error);
        toast.error((error as Error).message || "Failed to update craving log entry.");
        return null;
    }
};

export const updateJournalEntry = async (id: string, updates: JournalEntryUpdate): Promise<JournalEntry | null> => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error("User not authenticated");
    const { user_id, ...validUpdates } = updates; 
    if (Object.keys(validUpdates).length === 0 || !validUpdates.content) { toast.info("No valid updates (content is required) provided for journal entry."); return null; }
    try {
        const { data, error } = await supabase.schema('mission_fresh').from('journal_entries').update(validUpdates).eq('id', id).eq('user_id', user.id).select().single();
        if (error) throw error;
        toast.success("Journal entry updated.");
        return data;
    } catch (error: unknown) {
        console.error('Error updating journal entry:', error);
        toast.error((error as Error).message || "Failed to update journal entry.");
        return null;
    }
};

export const deleteNicotineLog = async (id: string): Promise<boolean> => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error("User not authenticated");
    try {
        const { error } = await supabase.schema('mission_fresh').from('nicotine_logs').delete().eq('id', id).eq('user_id', user.id); 
        if (error) throw error;
        toast.success("Nicotine log entry deleted.");
        return true;
    } catch (error: unknown) {
        console.error('Error deleting nicotine log entry:', error);
        toast.error((error as Error).message || "Failed to delete entry.");
        return false;
    }
};

export const deleteCravingLog = async (id: string): Promise<boolean> => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error("User not authenticated");
    try {
        // CRITICAL FIX: Use mission_fresh schema consistently
        const { error } = await supabase.schema('mission_fresh').from('craving_logs').delete().eq('id', id).eq('user_id', user.id); 
        if (error) throw error;
        toast.success("Craving log entry deleted.");
        return true;
    } catch (error: unknown) {
        console.error('Error deleting craving log entry:', error);
        toast.error((error as Error).message || "Failed to delete entry.");
        return false;
    }
};

export const deleteJournalEntry = async (id: string): Promise<boolean> => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error("User not authenticated");
    try {
        const { error } = await supabase.schema('mission_fresh').from('journal_entries').delete().eq('id', id).eq('user_id', user.id);
        if (error) throw error;
        toast.success("Journal entry deleted.");
        return true;
    } catch (error: unknown) {
        console.error('Error deleting journal entry:', error);
        toast.error((error as Error).message || "Failed to delete entry.");
        return false;
    }
};

export const deleteDailyCheckIn = async (id: string): Promise<boolean> => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error("User not authenticated");
    try {
        const { error } = await supabase.schema('mission_fresh').from('health_metrics').delete().eq('id', id).eq('user_id', user.id);
        if (error) throw error;
        toast.success("Daily check-in entry deleted.");
        return true;
    } catch (error: unknown) {
        console.error('Error deleting daily check-in entry:', error);
        toast.error((error as Error).message || "Failed to delete daily check-in.");
        return false;
    }
};

export const getRecentLogStats = async (userId: string, days: number = 7): Promise<RecentLogStats> => {
  try {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);
    const { craving_logs, daily_check_ins }: AllLogEntries = await getLogEntries(userId, startDate, endDate);
    const sortedCravingLogs = craving_logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()); 
    let lastCraving = null;
    if (sortedCravingLogs.length > 0) {
        const latestCraving = sortedCravingLogs[0];
        if (latestCraving.timestamp && typeof latestCraving.timestamp === 'string') {
            const cravingDate = new Date(latestCraving.timestamp);
            if (!isNaN(cravingDate.getTime())) {
                lastCraving = { time: formatDistanceToNow(cravingDate, { addSuffix: true }), intensity: latestCraving.intensity };
            } else { console.warn("Invalid timestamp string for latest craving:", latestCraving.timestamp); }
        } else { console.warn("Missing or invalid timestamp for latest craving:", latestCraving); }
    }
    const recentCravingsCount = sortedCravingLogs.filter(c => c.intensity > 5).length; 
    const sortedCheckIns = daily_check_ins.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()); 
    let lastCheckInMetrics = null;
    if (sortedCheckIns.length > 0) {
        const lastCheckIn = sortedCheckIns[0];
        if (lastCheckIn.date && typeof lastCheckIn.date === 'string') {
            const checkInDate = new Date(lastCheckIn.date);
             if (!isNaN(checkInDate.getTime())) {
                lastCheckInMetrics = { mood: lastCheckIn.mood, energy: lastCheckIn.energy_level, focus: lastCheckIn.focus_level, sleepHours: lastCheckIn.sleep_hours, date: lastCheckIn.date };
              } else { console.warn("Invalid date string for last check-in:", lastCheckIn.date); }
        } else { console.warn("Missing or invalid date for last check-in:", lastCheckIn); }
    }
    return { lastCraving, lastCheckInMetrics, recentCravingsCount };
  } catch (error: any) {
    console.error("Error calculating simplified log statistics:", error);
    toast.error("Failed to calculate recent log stats.");
    return { lastCraving: null, lastCheckInMetrics: null, recentCravingsCount: 0 };
  }
};
