import { supabase } from "@/lib/supabase";

export interface DashboardStats {
  daysAfresh: number;
  moneySaved: number;
  lifeRegained: string;
  cigarettesNotSmoked: number;
}

export const getDashboardStats = async (userId?: string): Promise<DashboardStats> => {
  try {
    // Get the current authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      // PRODUCTION READY: No fallback data for unauthenticated users
      // Return zero stats instead of demo/fake data
      return {
        daysAfresh: 0,
        moneySaved: 0,
        lifeRegained: "0",
        cigarettesNotSmoked: 0,
      };
    }

    // OPTIMIZED: Single query to get user's quit goal with minimal data needed
    const { data: goal, error: goalError } = await supabase
      .from('user_goals')
      .select('quit_date, typical_daily_usage, cost_per_unit')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (goalError) {
      console.error("Database error fetching goal:", goalError);
      throw goalError;
    }

    let daysAfresh = 0;
    let cigarettesNotSmoked = 0;
    let moneySaved = 0;
    let lifeRegainedMinutes = 0;

    if (goal?.quit_date) {
      const startDate = new Date(goal.quit_date);
      const today = new Date();
      
      // Only calculate positive days if quit date has passed
      if (today >= startDate) {
        daysAfresh = Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        
        // Use REAL user data for calculations - no fallbacks for production app
        const dailyUsage = parseFloat(String(goal.typical_daily_usage)) || 0;
        const costPerUnit = parseFloat(String(goal.cost_per_unit)) || 0;
        
        // Calculate based on REAL user data, not hardcoded assumptions
        cigarettesNotSmoked = daysAfresh * dailyUsage;
        moneySaved = daysAfresh * (dailyUsage * costPerUnit);
        
        // Calculate life regained (11 minutes per cigarette is medical standard)
        lifeRegainedMinutes = cigarettesNotSmoked * 11;
      }
      // If quit date is in the future, all stats remain 0
    }
    
    const lifeRegained = lifeRegainedMinutes.toString();

    return {
      daysAfresh,
      moneySaved,
      lifeRegained,
      cigarettesNotSmoked,
    };
  } catch (error) {
    console.error("Failed to fetch dashboard stats:", error);
    throw error;
  }
};

// OPTIMIZED: New fast function to get essential dashboard data in one query
export const getOptimizedDashboardData = async () => {
  try {
    console.log('🔍 Getting dashboard data...');
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    console.log('🔍 Auth check result:', { 
      hasUser: !!user, 
      userId: user?.id, 
      userEmail: user?.email,
      authError: authError?.message 
    });
    
    if (authError || !user) {
      console.error('🔥 Authentication failed:', authError);
      // PRODUCTION READY: No fallback data for unauthenticated users
      // Return null goal data instead of demo/fake data
      return { goal: null, userId: null };
    }

    console.log('🔍 Querying goals for user:', user.id);

    // Single optimized query for goal data only - calculations done client-side for speed
    const { data: goal, error: goalError } = await supabase
      .from('user_goals')
      .select('quit_date, typical_daily_usage, cost_per_unit')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    console.log('🔍 Goal query result:', { 
      hasGoal: !!goal, 
      goalData: goal,
      goalError: goalError?.message 
    });

    if (goalError) {
      console.error("🔥 Database error fetching goal:", goalError);
      throw goalError;
    }

    console.log('🔍 Returning dashboard data:', { goal, userId: user.id });
    return { goal, userId: user.id };
  } catch (error) {
    console.error("🔥 Failed to fetch optimized dashboard data:", error);
    throw error;
  }
};
