import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Check, XCircle, Loader2 } from 'lucide-react'; // Added Loader2 icon
import { useToast } from '@/hooks/use-toast'; // Import useToast

interface QuickJournalingExerciseProps {
  prompts: string[];
  onComplete: (entry: string) => Promise<void>; // onComplete is now expected to be async
}

const QuickJournalingExercise: React.FC<QuickJournalingExerciseProps> = ({ prompts, onComplete }) => {
  const [journalEntry, setJournalEntry] = useState('');
  const [isSaved, setIsSaved] = useState(false);
  const [isLoading, setIsLoading] = useState(false); // Added loading state
  const { toast } = useToast(); // Initialize useToast

  const handleSave = async () => { // Made handleSave async
    if (journalEntry.trim() !== '' && !isLoading) { // Prevent multiple saves while loading
      setIsLoading(true); // Set loading state
      try {
        await onComplete(journalEntry); // Await the async onComplete
        setIsSaved(true); // Set saved state to true on success
        toast({ // Show success toast
          title: "Journal Entry Saved",
          description: "Your thoughts have been recorded.",
        });
      } catch (error) {
        console.error("Error saving journal entry:", error);
        toast({ // Show error toast
          title: "Error Saving Entry",
          description: "Could not save your journal entry. Please try again.",
          variant: "destructive",
        });
        setIsSaved(false); // Ensure saved state is false on error
      } finally {
        setIsLoading(false); // Reset loading state
      }
    }
  };

  const handleEntryChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setJournalEntry(e.target.value);
    if (isSaved) setIsSaved(false); // Reset saved state if the user starts typing again after saving
  };

  const handleClear = () => {
    setJournalEntry('');
    setIsSaved(false);
    setIsLoading(false); // Also reset loading state on clear
  };

  const placeholderText = prompts.length > 0
    ? `Consider the following:\n${prompts.map(p => `- ${p}`).join('\n')}\n\nWrite your thoughts here...`
    : "Write your thoughts here...";

  return (
    <div className="flex flex-col p-4">
      <div className="mb-6">
        <h3 className="text-xl font-semibold mb-2">Quick Journaling</h3>
        <p className="text-muted-foreground">Write down your thoughts to process them.</p>
      </div>

      <div className="space-y-4 mb-6">
        {/* Prompts displayed clearly */}
        {prompts.length > 0 && (
          <div className="border border-border p-4 bg-muted" style={{borderRadius: '8px'}}>
            <p className="text-sm font-medium mb-2 text-foreground">Prompts:</p>
            <ul className="list-disc list-inside text-muted-foreground text-sm">
              {prompts.map((prompt, index) => (
                <li key={index}>{prompt}</li>
              ))}
            </ul>
          </div>
        )}

         <Textarea
            id="quick-journal-entry"
            placeholder={placeholderText}
            value={journalEntry}
            onChange={handleEntryChange}
            className="min-h-[150px]"
            disabled={isSaved || isLoading} // Disable textarea after saving or while loading
          />
      </div>

      <div className="flex justify-end items-center gap-4">
        {isSaved && <span className="text-sm text-success">Entry saved!</span>}
        <Button onClick={handleClear} variant="outline" disabled={journalEntry.trim() === '' || isSaved || isLoading}> {/* Disable clear while loading */}
           <XCircle className="mr-2 h-4 w-4" /> Clear
        </Button>
        <Button onClick={handleSave} disabled={journalEntry.trim() === '' || isSaved || isLoading}> {/* Disable save while loading or saved */}
          {isLoading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : isSaved ? (
            <Check className="mr-2 h-4 w-4" />
          ) : (
            <Check className="mr-2 h-4 w-4" />
          )}
          {isSaved ? 'Saved' : (isLoading ? 'Saving...' : 'Save Entry')}
        </Button>
      </div>
    </div>
  );
};

export default QuickJournalingExercise;
