import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { PlayCircle, PauseCircle, RotateCcw, CheckCircle } from 'lucide-react';
import { useTimer } from '@/hooks/useTimer';
import { useHaptics, HapticImpact } from '@/hooks/useHaptics';
import { moodExercises } from '@/lib/toolsData';

interface MoodExerciseProps {
  exerciseType: keyof typeof moodExercises;
  onComplete: () => void;
}

const MoodExercise: React.FC<MoodExerciseProps> = ({ exerciseType, onComplete }) => {
  const { impact } = useHaptics();
  const exercise = moodExercises[exerciseType];
  const [sessionComplete, setSessionComplete] = useState(false);
  
  // Extract duration in minutes from string like "5 minutes"
  const durationMinutes = parseInt(exercise.duration.split(' ')[0]);
  
  const {
    timeLeft,
    isRunning,
    progress,
    start,
    pause,
    reset
  } = useTimer(durationMinutes * 60, () => {
    setSessionComplete(true);
    impact(HapticImpact.HEAVY);
  });

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleComplete = () => {
    impact(HapticImpact.MEDIUM);
    onComplete();
  };

  return (
    <div className="flex flex-col items-center space-y-6 p-6">
      {/* Exercise Info */}
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold text-foreground">{exercise.title}</h3>
        <p className="text-muted-foreground text-sm">{exercise.description}</p>
      </div>

      {/* Timer Display */}
      <div className="text-center space-y-4">
        <div className="text-4xl font-mono font-bold text-foreground">
          {formatTime(timeLeft)}
        </div>
        <Progress value={progress} className="w-64 h-2" />
      </div>

      {/* Control Buttons */}
      <div className="flex space-x-4">
        {!isRunning ? (
          <Button
            onClick={start}
            size="lg"
            className="flex items-center space-x-2"
            disabled={sessionComplete}
          >
            <PlayCircle className="h-5 w-5" />
            <span>Start</span>
          </Button>
        ) : (
          <Button
            onClick={pause}
            size="lg"
            variant="outline"
            className="flex items-center space-x-2"
          >
            <PauseCircle className="h-5 w-5" />
            <span>Pause</span>
          </Button>
        )}
        
        <Button
          onClick={reset}
          size="lg"
          variant="outline"
          className="flex items-center space-x-2"
        >
          <RotateCcw className="h-5 w-5" />
          <span>Reset</span>
        </Button>
      </div>

      {/* Completion State */}
      {sessionComplete && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center space-y-4"
        >
          <div className="flex justify-center">
            <CheckCircle className="h-16 w-16 text-primary" />
          </div>
          <div className="space-y-2">
            <h4 className="text-lg font-semibold text-foreground">Exercise Complete!</h4>
            <p className="text-muted-foreground text-sm">
              Great job completing your mood exercise. Take a moment to notice how you feel.
            </p>
          </div>
          <Button onClick={handleComplete} size="lg" className="w-full">
            Finish Exercise
          </Button>
        </motion.div>
      )}

      {/* Exercise Instructions */}
      {!sessionComplete && (
        <div className="max-w-md text-center space-y-3">
          <h4 className="font-medium text-foreground">Instructions:</h4>
          <div className="text-sm text-muted-foreground space-y-2">
            {exerciseType === 'boxBreathing' && (
              <>
                <p>• Breathe in for 4 counts</p>
                <p>• Hold for 4 counts</p>
                <p>• Breathe out for 4 counts</p>
                <p>• Hold for 4 counts</p>
                <p>• Repeat this pattern</p>
              </>
            )}
            {exerciseType === 'fourSevenEight' && (
              <>
                <p>• Breathe in through nose for 4 counts</p>
                <p>• Hold breath for 7 counts</p>
                <p>• Exhale through mouth for 8 counts</p>
                <p>• Repeat this cycle</p>
              </>
            )}
            {exerciseType === 'alternateNostril' && (
              <>
                <p>• Use thumb to close right nostril</p>
                <p>• Breathe in through left nostril</p>
                <p>• Close left nostril, open right</p>
                <p>• Breathe out through right nostril</p>
                <p>• Continue alternating</p>
              </>
            )}
            {exerciseType === 'bellyBreathing' && (
              <>
                <p>• Place one hand on chest, one on belly</p>
                <p>• Breathe slowly through nose</p>
                <p>• Feel belly rise, chest stays still</p>
                <p>• Exhale slowly through mouth</p>
              </>
            )}
            {exerciseType === 'pursedLip' && (
              <>
                <p>• Breathe in through nose for 2 counts</p>
                <p>• Purse lips like blowing out candles</p>
                <p>• Breathe out slowly for 4 counts</p>
                <p>• Keep breathing controlled and steady</p>
              </>
            )}
            {exerciseType === 'quickJournaling' && (
              <>
                <p>• Write about your current emotions</p>
                <p>• Don't worry about grammar or structure</p>
                <p>• Focus on expressing how you feel</p>
                <p>• Let thoughts flow freely onto paper</p>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MoodExercise;
