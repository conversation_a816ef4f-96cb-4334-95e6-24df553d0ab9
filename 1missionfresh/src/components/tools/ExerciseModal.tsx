import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface ExerciseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
  title: string;
  description?: string;
  children: React.ReactNode;
}

export const ExerciseModal = ({
  isOpen,
  onClose,
  onComplete,
  title,
  description,
  children
}: ExerciseModalProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md w-full flex flex-col max-h-[90vh] border-border shadow-md bg-background">
        <DialogHeader>
          <DialogTitle className="text-foreground">{title}</DialogTitle>
          {description && <DialogDescription className="text-muted-foreground">{description}</DialogDescription>}
        </DialogHeader>
        
        <div className="flex-grow overflow-y-auto py-4">
          {children}
        </div>
      </DialogContent>
    </Dialog>
  );
};
