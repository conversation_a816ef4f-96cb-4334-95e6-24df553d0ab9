import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, CheckCircle } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useHaptics } from '@/hooks/useHaptics';
import { useToast } from '@/hooks/use-toast'; // Import useToast
import { useAuth } from '@/contexts/AuthContext'; // Import useAuth
import { earnPoints } from '@/services/rewardsService'; // Import earnPoints

interface DelayTimerProps {
  initialDurationInMinutes: number;
  onComplete: () => void;
}

const DelayTimer: React.FC<DelayTimerProps> = ({ initialDurationInMinutes, onComplete }) => {
  const [durationInMinutes, setDurationInMinutes] = useState(initialDurationInMinutes);
  const [timeLeft, setTimeLeft] = useState(durationInMinutes * 60);
  const [isActive, setIsActive] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const completionSoundRef = useRef<HTMLAudioElement | null>(null);
  const { vibrate } = useHaptics();
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    if (typeof Audio !== 'undefined') {
      completionSoundRef.current = new Audio('/sounds/completion.mp3');
      completionSoundRef.current.onerror = () => {
        console.warn('Failed to load completion sound at /sounds/completion.mp3');
      };
    } else {
      console.warn('Audio API not available, completion sound disabled.');
    }
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (isActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft((prevTime) => prevTime - 1);
      }, 1000);
    } else if (timeLeft === 0 && isActive) {
      setIsActive(false);
      setIsComplete(true); // Set complete, then call onComplete
      onComplete();      // Call original onComplete immediately
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive, timeLeft, onComplete]);

  // Effect to handle actions when timer completes
  useEffect(() => {
    if (isComplete) {
      completionSoundRef.current?.play().catch(error => console.error('Error playing sound:', error));
      vibrate(500);

      if (user?.id) {
        const pointsToAward = 5;
        earnPoints(user.id, pointsToAward, 'exercise', 'Delay Timer')
          .then(() => {
            toast({
              title: "Timer Complete!",
              description: `You successfully delayed the craving. +${pointsToAward} points!`,
            });
          })
          .catch(err => {
            console.error("Failed to award points for delay timer", err);
            toast({
              title: "Timer Complete!",
              description: "You successfully delayed the craving. Points could not be awarded.",
            });
          });
      } else {
        toast({
          title: "Timer Complete!",
          description: "You successfully delayed the craving.",
        });
      }
    }
  }, [isComplete, user, vibrate, toast]); // Add user, vibrate, toast to dependencies


  // Effect to reset timer when duration changes
  useEffect(() => {
    setTimeLeft(durationInMinutes * 60);
    setIsActive(false);
    setIsComplete(false);
  }, [durationInMinutes]);

  const toggleTimer = () => {
    if (!isActive && timeLeft === durationInMinutes * 60) { // Starting afresh
      setIsComplete(false); // Reset completion state
      vibrate(200);
      toast({
        title: "Timer Started",
        description: `Delaying for ${durationInMinutes} minutes.`,
      });
    } else if (isActive) { // Pausing
       vibrate(200);
       toast({
         title: "Timer Paused",
         description: `Time left: ${formatTime(timeLeft)}`,
       });
    } else { // Resuming
       setIsComplete(false); // Ensure not in complete state if resuming
    }
    setIsActive(!isActive);
  };

  const resetTimer = () => {
    setIsActive(false);
    setIsComplete(false);
    setTimeLeft(durationInMinutes * 60);
    if (completionSoundRef.current) {
      completionSoundRef.current.pause();
      completionSoundRef.current.currentTime = 0;
    }
    toast({
      title: "Timer Reset",
      description: "The timer has been reset.",
    });
  };

  const handleDurationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value, 10);
    if (!isNaN(value) && value > 0) {
      setDurationInMinutes(value);
    } else if (e.target.value === '') {
      setDurationInMinutes(0); // Allow clearing the input
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
  };

  const progress = durationInMinutes * 60 > 0 ? (durationInMinutes * 60 - timeLeft) / (durationInMinutes * 60) : 0;
  const circumference = 2 * Math.PI * 54;
  const strokeDashoffset = circumference * (1 - progress);

  // Determine stroke color based on time left
  const strokeColor = timeLeft <= 10 && isActive ? 'stroke-destructive' : 'stroke-primary';

  return (
    <div className="flex flex-col items-center justify-center p-4 min-h-[350px]">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold mb-2 tracking-tight">Delay Tactic</h3>
        <p className="text-muted-foreground">Wait out the craving.</p>
      </div>

      {/* Custom Duration Input */}
      <div className="flex items-center gap-2 mb-4">
         <Label htmlFor="duration">Duration (minutes):</Label>
         <Input
           id="duration"
           type="number"
           value={durationInMinutes === 0 ? '' : durationInMinutes}
           onChange={handleDurationChange}
           className="w-20 text-center"
           min="1"
           disabled={isActive}
         />
      </div>


      <div className="relative w-36 h-36 mb-8">
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 120 120">
          <circle
            cx="60"
            cy="60"
            r="54"
            fill="none"
            strokeWidth="8"
            className="stroke-border"
          />
          <circle
            cx="60"
            cy="60"
            r="54"
            fill="none"
            strokeWidth="8"
            className={`${strokeColor === 'stroke-destructive' ? 'stroke-destructive' : 'stroke-primary'} transition-all duration-1000 ease-linear`}
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            transform="rotate(-90 60 60)"
            strokeLinecap="round"
          />
        </svg>
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          {isComplete ? (
            <CheckCircle className="w-16 h-16 text-primary" />
          ) : (
            <div className="text-4xl font-bold">{formatTime(timeLeft)}</div>
          )}
        </div>
      </div>

      {isComplete && (
        <p className="text-lg font-medium text-primary mb-6">Time's Up! You did it!</p>
      )}

      <div className="flex gap-4">
        {!isComplete && (
          <Button onClick={toggleTimer} className="min-w-[100px]" disabled={durationInMinutes <= 0}>
            {isActive ? 'Pause' : (timeLeft === durationInMinutes * 60 ? 'Start' : 'Resume')}
          </Button>
        )}
        <Button onClick={resetTimer} variant={isComplete ? 'default' : 'ghost'} className="min-w-[100px]">
          {isComplete ? 'Restart' : 'Reset'}
        </Button>
      </div>
    </div>
  );
};

export default DelayTimer;
