import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { CheckCircle, Coffee, Lightbulb } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useHaptics } from '@/hooks/useHaptics'; // Import useHaptics

interface PomodoroTimerProps {
  initialFocusDuration: number; // Renamed prop
  initialBreakDuration: number; // Renamed prop
  onSessionComplete: () => void;
}

const PomodoroTimer: React.FC<PomodoroTimerProps> = ({ initialFocusDuration, initialBreakDuration, onSessionComplete }) => {
  // Read durations from local storage on component mount
  const localStorageFocusDuration = localStorage.getItem('pomodoroFocusDuration');
  const localStorageBreakDuration = localStorage.getItem('pomodoroBreakDuration');

  const [focusDuration, setFocusDuration] = useState(
    localStorageFocusDuration ? parseInt(localStorageFocusDuration, 10) : initialFocusDuration
  );
  const [breakDuration, setBreakDuration] = useState(
    localStorageBreakDuration ? parseInt(localStorageBreakDuration, 10) : initialBreakDuration
  );

  const [isFocusSession, setIsFocusSession] = useState(true);
  const [isActive, setIsActive] = useState(false);
  const [isComplete, setIsComplete] = useState(false);

  const initialTime = (isFocus: boolean) => (isFocus ? focusDuration : breakDuration) * 60;
  const [timeLeft, setTimeLeft] = useState(initialTime(true));

  const focusCompletionSoundRef = useRef<HTMLAudioElement | null>(null);
  const breakCompletionSoundRef = useRef<HTMLAudioElement | null>(null);
  const { vibrate } = useHaptics();

  // Effect to save durations to local storage whenever they change
  useEffect(() => {
    localStorage.setItem('pomodoroFocusDuration', focusDuration.toString());
  }, [focusDuration]);

  useEffect(() => {
    localStorage.setItem('pomodoroBreakDuration', breakDuration.toString());
  }, [breakDuration]);


  useEffect(() => {
    // Initialize audio elements with error handling
    if (typeof Audio !== 'undefined') {
      focusCompletionSoundRef.current = new Audio('/sounds/focus_complete.mp3');
      focusCompletionSoundRef.current.onerror = () => {
        console.warn('Failed to load focus completion sound at /sounds/focus_complete.mp3');
      };
      breakCompletionSoundRef.current = new Audio('/sounds/break_complete.mp3');
      breakCompletionSoundRef.current.onerror = () => {
        console.warn('Failed to load break completion sound at /sounds/break_complete.mp3');
      };
    } else {
      console.warn('Audio API not available, completion sounds disabled.');
    }

    let interval: NodeJS.Timeout | null = null;

    if (isActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft((prevTime) => prevTime - 1);
      }, 1000);
    } else if (timeLeft === 0 && isActive) {
      // Session complete
      setIsActive(false);
      setIsComplete(true);
      if (isFocusSession) {
        focusCompletionSoundRef.current?.play().catch(error => console.error('Error playing focus sound:', error));
      } else {
        breakCompletionSoundRef.current?.play().catch(error => console.error('Error playing break sound:', error));
      }
      vibrate(500); // Vibrate on session completion
      onSessionComplete();
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive, timeLeft, isFocusSession, onSessionComplete, vibrate]);

  // Effect to reset timer when durations change
  useEffect(() => {
    resetTimer();
  }, [focusDuration, breakDuration]);


  const toggleTimer = () => {
     if (!isActive && timeLeft === initialTime(isFocusSession)) {
       vibrate(200); // Vibrate on start
     } else if (isActive) {
       vibrate(200); // Vibrate on pause
     }
    setIsActive(!isActive);
    if (isComplete) setIsComplete(false);
  };

  const startNextSession = () => {
    const nextIsFocus = !isFocusSession;
    setIsFocusSession(nextIsFocus);
    setTimeLeft(initialTime(nextIsFocus));
    setIsActive(true);
    setIsComplete(false);
    // Stop and reset sounds
    focusCompletionSoundRef.current?.pause();
    if (focusCompletionSoundRef.current) focusCompletionSoundRef.current.currentTime = 0;
    breakCompletionSoundRef.current?.pause();
    if (breakCompletionSoundRef.current) breakCompletionSoundRef.current.currentTime = 0;
  };

  const resetTimer = () => {
    setIsActive(false);
    setIsComplete(false);
    setIsFocusSession(true);
    setTimeLeft(initialTime(true));
    // Stop and reset sounds
    focusCompletionSoundRef.current?.pause();
    if (focusCompletionSoundRef.current) focusCompletionSoundRef.current.currentTime = 0;
    breakCompletionSoundRef.current?.pause();
    if (breakCompletionSoundRef.current) breakCompletionSoundRef.current.currentTime = 0;
  };

  const handleDurationChange = (e: React.ChangeEvent<HTMLInputElement>, type: 'focus' | 'break') => {
    const value = parseInt(e.target.value, 10);
    if (!isNaN(value) && value > 0) {
      if (type === 'focus') {
        setFocusDuration(value);
      } else {
        setBreakDuration(value);
      }
    } else if (e.target.value === '') {
       if (type === 'focus') {
         setFocusDuration(0); // Allow clearing the input
       } else {
         setBreakDuration(0); // Allow clearing the input
       }
    }
  };


  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
  };

  const currentDurationSeconds = initialTime(isFocusSession);
  const progress = currentDurationSeconds > 0 ? (currentDurationSeconds - timeLeft) / currentDurationSeconds : 0;
  const circumference = 2 * Math.PI * 54;
  const strokeDashoffset = circumference * (1 - progress);

  const sessionType = isFocusSession ? 'Focus' : 'Break';
  const sessionDuration = isFocusSession ? focusDuration : breakDuration;
  const progressColor = 'stroke-primary';

  return (
    <div className="flex flex-col items-center justify-center p-4 min-h-[350px]">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold mb-2 tracking-tight">Pomodoro Timer</h3>
        <p className="text-muted-foreground">
          {sessionType} Session ({sessionDuration} min)
        </p>
      </div>

      {/* Custom Duration Inputs */}
      <div className="flex gap-4 mb-6">
         <div className="flex items-center gap-2">
            <Label htmlFor="focus-duration">Focus (min):</Label>
            <Input
              id="focus-duration"
              type="number"
              value={focusDuration === 0 ? '' : focusDuration}
              onChange={(e) => handleDurationChange(e, 'focus')}
              className="w-16 text-center"
              min="1"
              disabled={isActive}
            />
         </div>
         <div className="flex items-center gap-2">
            <Label htmlFor="break-duration">Break (min):</Label>
            <Input
              id="break-duration"
              type="number"
              value={breakDuration === 0 ? '' : breakDuration}
              onChange={(e) => handleDurationChange(e, 'break')}
              className="w-16 text-center"
              min="1"
              disabled={isActive}
            />
         </div>
      </div>


      {/* Progress Circle & Time */}
      <div className="relative w-36 h-36 mb-6">
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 120 120">
          <circle cx="60" cy="60" r="54" fill="none" strokeWidth="8" className="stroke-border" />
          <circle
            cx="60"
            cy="60"
            r="54"
            fill="none"
            strokeWidth="8"
            className={`${progressColor} transition-all duration-1000 ease-linear`}
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            transform="rotate(-90 60 60)"
            strokeLinecap="round"
          />
        </svg>
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          {isComplete ? (
             isFocusSession ?
               <CheckCircle className="w-16 h-16 text-primary" />
               : <CheckCircle className="w-16 h-16 text-primary" />
          ) : (
            <div className="text-4xl font-bold">{formatTime(timeLeft)}</div>
          )}
        </div>
      </div>

      {/* Completion Message */}
      {isComplete && (
        <p className={`text-lg font-medium mb-6 text-primary`}>
          {isFocusSession ? 'Focus session complete! Time for a break.' : 'Break finished! Ready to focus?'}
        </p>
      )}

      {/* Controls */}
      <div className="flex gap-4 mt-auto pt-4">
        {!isActive && !isComplete && (
          <Button onClick={toggleTimer} className="min-w-[100px]" disabled={focusDuration <= 0 || breakDuration <= 0}>
            {timeLeft === currentDurationSeconds ? `Start ${sessionType}` : 'Resume'}
          </Button>
        )}
        {isActive && (
          <Button onClick={toggleTimer} variant="outline" className="min-w-[100px]">
            Pause
          </Button>
        )}
        {isComplete && (
          <Button onClick={startNextSession} className="min-w-[100px]">
            {isFocusSession ? 'Start Break' : 'Start Focus'}
          </Button>
        )}
        <Button onClick={resetTimer} variant="ghost" className="min-w-[100px]">
          Reset
        </Button>
      </div>
    </div>
  );
};

export default PomodoroTimer;
