import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ArrowLeft, Grid3X3 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

const quickTools = [
  { name: 'Cravings', path: '/app/tools/cravings', current: false },
  { name: 'Energy', path: '/app/tools/energy', current: false },
  { name: 'Focus', path: '/app/tools/focus', current: false },
  { name: 'Mo<PERSON>', path: '/app/tools/mood', current: false },
  { name: 'Breathing', path: '/app/tools/breathing', current: false },
  { name: 'AI Coach', path: '/app/tools/ai-coach', current: false },
];

interface ToolNavigationProps {
  currentTool?: string;
  className?: string;
}

const ToolNavigation: React.FC<ToolNavigationProps> = ({ currentTool, className }) => {
  const location = useLocation();

  const toolsWithCurrent = quickTools.map(tool => ({
    ...tool,
    current: location.pathname === tool.path
  }));

  return (
    <div className={cn("flex items-center justify-between mb-6 p-4 bg-background border border-border", className)} style={{borderRadius: '24px'}}>
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link to="/app/tools" className="flex items-center gap-2 text-muted-foreground hover:text-foreground">
            <ArrowLeft className="w-4 h-4" />
            Back to Tools
          </Link>
        </Button>
        
        <div className="h-4 w-px bg-border" />
        
        <Button variant="ghost" size="sm" asChild>
          <Link to="/app/tools" className="flex items-center gap-2 text-muted-foreground hover:text-foreground">
            <Grid3X3 className="w-4 h-4" />
            All Tools
          </Link>
        </Button>
      </div>

      <div className="hidden md:flex items-center gap-2">
        <span className="text-xs font-medium text-muted-foreground mr-2">Quick Switch:</span>
        {toolsWithCurrent.slice(0, 4).map((tool) => (
          <Button
            key={tool.name}
            variant={tool.current ? "secondary" : "ghost"}
            size="sm"
            asChild
            className={cn(
              "text-xs",
              tool.current && "pointer-events-none"
            )}
          >
            <Link to={tool.path}>{tool.name}</Link>
          </Button>
        ))}
      </div>
    </div>
  );
};

export default ToolNavigation; 