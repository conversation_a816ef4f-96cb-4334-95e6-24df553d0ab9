import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Star } from 'lucide-react';
import type { ProductReviewWithUser } from '@/services/productService'; // Cline: Import ProductReviewWithUser
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"; // Cline: Import Avatar components

interface ReviewItemProps {
  review: ProductReviewWithUser; // Cline: Use ProductReviewWithUser
}

const ReviewItem: React.FC<ReviewItemProps> = ({ review }) => {
  const displayName = review.profiles?.username || 'Anonymous';
  const avatarUrl = review.profiles?.avatar_url;
  const initials = displayName.charAt(0).toUpperCase();

  return (
    <Card key={review.id} className="border border-border bg-card" style={{borderRadius: '16px'}}>
      <CardContent className="pt-6">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            {Array.from({ length: 5 }).map((_, index) => (
              <Star
                key={index}
                className={`h-4 w-4 ${index < review.rating ? "fill-primary text-primary" : "text-border"}`} // Cline: Replaced opacity styles
              />
            ))}
          </div>
          <span className="text-sm text-muted-foreground">
            {new Date(review.created_at).toLocaleDateString()}
          </span>
        </div>
        
        <div className="flex items-start space-x-3 mb-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={avatarUrl || undefined} alt={displayName} />
            <AvatarFallback>{initials}</AvatarFallback>
          </Avatar>
          <div>
            <p className="text-sm font-semibold text-foreground">{displayName}</p>
            {/* Optionally, if email is available and desired: 
            {review.profiles?.email && <p className="text-xs text-muted-foreground">{review.profiles.email}</p>} 
            For now, sticking to username as per ProductReviewWithUser definition */}
          </div>
        </div>

        {review.review_text && (
          <p className="text-muted-foreground leading-relaxed">{review.review_text}</p>
        )}
      </CardContent>
    </Card>
  );
};

export default ReviewItem;
