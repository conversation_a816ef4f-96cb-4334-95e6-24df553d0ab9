import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/contexts/AuthContext';
import { earnPoints } from '@/services/rewardsService';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2 } from 'lucide-react';

interface HandExercisesProps {
  onComplete: () => void;
}

// TODO: Move hand exercise data to database for dynamic content management and customization
const exercises = [
  {
    type: 'finger_taps',
    instruction: 'Tap each finger to your thumb, one at a time',
    duration: 20,
    points: 5,
  },
  {
    type: 'wrist_circles',
    instruction: 'Make gentle circles with your wrists',
    duration: 20,
    points: 5,
  },
  {
    type: 'finger_stretches',
    instruction: 'Spread your fingers wide, then make a fist',
    duration: 20,
    points: 5,
  },
  {
    type: 'hand_waves',
    instruction: 'Wave your hands back and forth slowly',
    duration: 20,
    points: 5,
  },
  {
    type: 'finger_walks',
    instruction: 'Walk your fingers across a surface like tiny legs',
    duration: 20,
    points: 5,
  }
];

export const HandExercises: React.FC<HandExercisesProps> = ({ onComplete }) => {
  const { user } = useAuth();
  const [currentExercise, setCurrentExercise] = useState(exercises[Math.floor(Math.random() * exercises.length)]);
  const [timeLeft, setTimeLeft] = useState(currentExercise.duration);
  const [isActive, setIsActive] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(prev => {
          const newTime = prev - 1;
          setProgress((currentExercise.duration - newTime) / currentExercise.duration * 100);
          return newTime;
        });
      }, 1000);
    } else if (timeLeft === 0 && isActive) {
      setIsActive(false);
      setIsCompleted(true);
      handleExerciseComplete();
    }

    return () => clearInterval(interval);
  }, [isActive, timeLeft]);

  const handleStart = () => {
    setIsActive(true);
  };

  const handleExerciseComplete = async () => {
    if (user?.id) {
      try {
        await earnPoints(user.id, currentExercise.points, 'hand_exercise', currentExercise.type);
      } catch (error) {
        console.error('Failed to award points for hand exercise:', error);
      }
    }
    onComplete();
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="space-y-6"
      >
        <Card className="p-6 space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Hand Exercise</h3>
            <p className="text-muted-foreground">{currentExercise.instruction}</p>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Time Remaining: {timeLeft}s</span>
                <span>Points: {currentExercise.points}</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>

            {!isActive && !isCompleted && (
              <Button 
                onClick={handleStart}
                className="w-full"
              >
                Start Exercise
              </Button>
            )}

            {isActive && (
              <div className="flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            )}

            {isCompleted && (
              <div className="text-center space-y-4">
                <p className="text-success font-medium">Exercise Complete!</p>
                <Button onClick={handleExerciseComplete} className="w-full">
                  Continue
                </Button>
              </div>
            )}
          </div>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
};