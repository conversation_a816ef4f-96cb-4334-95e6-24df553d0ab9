import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion'; // Assuming framer-motion is available
import { earnPoints } from '@/services/rewardsService'; // Import earnPoints
import { useAuth } from '@/contexts/AuthContext'; // Import useAuth
import { toast } from 'sonner'; // Import toast for notifications

interface EyeExerciseProps {
  onComplete: () => void;
}

// TODO: Move exercise data to database for dynamic content management and customization
const exercises = [
  { name: "Eye Rolls (Clockwise)", duration: 20, instructions: "Slowly roll your eyes in a clockwise direction. Repeat 5 times." },
  { name: "Eye Rolls (Counter-clockwise)", duration: 20, instructions: "Slowly roll your eyes in a counter-clockwise direction. Repeat 5 times." },
  { name: "Focus Near and Far", duration: 30, instructions: "Hold your finger about 6 inches from your eye. Focus on your finger for 15 seconds, then focus on something in the distance for 15 seconds." },
  { name: "Blinking Exercise", duration: 15, instructions: "Rapidly blink your eyes for 10-15 seconds to help lubricate them." },
];

const EyeExercise: React.FC<EyeExerciseProps> = ({ onComplete }) => {
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [timeLeft, setTimeLeft] = useState(exercises[0].duration);
  const [isActive, setIsActive] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const currentExercise = exercises[currentExerciseIndex];

  useEffect(() => {
    if (!isActive) {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      return;
    }

    timerRef.current = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          // Move to next exercise or complete
          if (currentExerciseIndex < exercises.length - 1) {
            setCurrentExerciseIndex((prevIndex) => prevIndex + 1);
            return exercises[currentExerciseIndex + 1].duration; // Reset timer for next exercise
          } else {
            // Exercise complete
            setIsActive(false);
            // Call the original onComplete handler and award points
            handleExerciseComplete();
            if (timerRef.current) {
              clearInterval(timerRef.current);
              timerRef.current = null;
            }
            return 0;
          }
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isActive, currentExerciseIndex, onComplete]);

  const { user } = useAuth(); // Get the current user

  const handleExerciseComplete = async () => {
    onComplete(); // Call the original onComplete handler

    if (user?.id) {
      try {
        // Award points for completing the eye exercise sequence
        const pointsToAward = 10; // Define points for completing the sequence
        await earnPoints(user.id, pointsToAward, 'exercise', 'Eye Exercise Sequence');
        toast.success(`+${pointsToAward} points earned!`, {
          description: `Completed the Eye Exercise sequence.`,
        });
      } catch (error) {
        console.error('Error awarding points for eye exercise:', error);
        // Optionally show an error toast, but don't block completion
        // toast.error('Failed to award points.');
      }
    }
  };


  const startExercise = () => {
    setCurrentExerciseIndex(0);
    setTimeLeft(exercises[0].duration);
    setIsActive(true);
  };

  const pauseResumeExercise = () => {
    setIsActive(!isActive);
  };

  const resetExercise = () => {
    setIsActive(false);
    setCurrentExerciseIndex(0);
    setTimeLeft(exercises[0].duration);
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  return (
    <div className="flex flex-col items-center justify-center p-4 min-h-[300px]">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold mb-2 tracking-tight">{currentExercise.name}</h3>
        <p className="text-muted-foreground text-sm">Exercise {currentExerciseIndex + 1} of {exercises.length}</p>
        <p className="text-lg font-medium mt-4">{formatTime(timeLeft)}</p>
      </div>

      <div className="mb-8 text-center">
        <p>{currentExercise.instructions}</p>
      </div>

       {/* Simple visual indicator (can be enhanced later) */}
       <div className="w-40 h-40 border border-border bg-background flex items-center justify-center mb-8 relative overflow-hidden" style={{borderRadius: '50%'}}>
         <motion.div
          className="absolute inset-0 bg-primary origin-center"
          initial={{ scale: 0 }}
          animate={{ scale: isActive ? 1 : 0 }}
          transition={{ duration: currentExercise.duration, ease: "linear" }}
        />
        <span className="relative z-10 text-2xl font-medium text-primary-foreground">
          {isActive ? 'Exercising' : 'Ready'}
        </span>
      </div>

      {/* Controls */}
      <div className="flex gap-4">
        {!isActive && currentExerciseIndex === 0 && (
          <Button onClick={startExercise}>Start</Button>
        )}
        {isActive && (
          <Button onClick={pauseResumeExercise} variant="outline">Pause</Button>
        )}
        {!isActive && currentExerciseIndex > 0 && currentExerciseIndex < exercises.length && (
           <Button onClick={pauseResumeExercise}>Resume</Button>
        )}
         {(currentExerciseIndex > 0 || isActive) && currentExerciseIndex < exercises.length && (
           <Button onClick={resetExercise} variant="ghost">Reset</Button>
         )}
         {currentExerciseIndex === exercises.length && (
           <Button onClick={resetExercise}>Restart</Button>
         )}
      </div>
    </div>
  );
};

export default EyeExercise;
