import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Star } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import type { Database } from '@/lib/database.types';

type Product = Database['mission_fresh']['Tables']['smokeless_products']['Row'] & {
  average_rating?: number;
  review_count?: number;
  flavor_category?: string;
  price_range?: string;
  ingredients?: string[];
  nicotine_strength_mg?: number;
};

interface ProductInfoProps {
  product: Product;
}

const ProductInfo: React.FC<ProductInfoProps> = ({ product }) => {
  const { isMobile } = useIsMobile();

  return (
    <Card className="border border-border bg-card" style={{borderRadius: '32px'}}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{product.name}</span>
          {product.average_rating !== null && product.average_rating !== undefined && (
            <span className="flex items-center text-sm">
              <Star className="h-4 w-4 fill-primary text-primary mr-1" />
              {product.average_rating.toFixed(1)} ({product.review_count || 0})
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className={`grid ${isMobile ? 'gap-4' : 'gap-6 lg:grid-cols-2'}`}>
        {product.image_url && (
          <img
            src={product.image_url}
            alt={product.name}
            className="w-full max-w-md mx-auto shadow-md" style={{borderRadius: '12px'}}
          />
        )}
        <div className="grid gap-4">
          <p className="text-muted-foreground">{product.description}</p>
          <div className="grid grid-cols-2 gap-4 mt-2">
            {/* Check for existence before rendering */}
            {product.brand && (
              <div>
                <p className="font-medium">Brand</p>
                <p className="text-muted-foreground">{product.brand}</p>
              </div>
            )}
            {product.flavor_category && (
              <div>
                <p className="font-medium">Flavor</p>
                <p className="text-muted-foreground">{product.flavor_category}</p>
              </div>
            )}
            {product.nicotine_strength_mg !== null && product.nicotine_strength_mg !== undefined && (
              <div>
                <p className="font-medium">Nicotine Strength</p>
                <p className="text-muted-foreground">{product.nicotine_strength_mg}mg</p>
              </div>
            )}
             {product.price_range && (
              <div>
                <p className="font-medium">Price Range</p>
                <p className="text-muted-foreground">{product.price_range}</p>
              </div>
            )}
          </div>
          {product.expert_notes_chemicals && (
            <div className="mt-4">
              <p className="font-medium">Expert Notes on Chemicals</p>
              <p className="text-muted-foreground">{product.expert_notes_chemicals}</p>
            </div>
          )}
          {product.expert_notes_gum_health && (
            <div className="mt-2">
              <p className="font-medium">Expert Notes on Gum Health</p>
              <p className="text-muted-foreground">{product.expert_notes_gum_health}</p>
            </div>
          )}
          {product.ingredients && Array.isArray(product.ingredients) && product.ingredients.length > 0 && (
            <div className="mt-2">
              <p className="font-medium">Ingredients</p>
              <p className="text-muted-foreground">{product.ingredients.join(', ')}</p>
            </div>
          )}
          {product.ingredients && typeof product.ingredients === 'string' && (
            <div className="mt-2">
              <p className="font-medium">Ingredients</p>
              <p className="text-muted-foreground">{product.ingredients}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductInfo;
