import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { CheckCircle, Sun } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext'; // Import useAuth
import { earnPoints } from '@/services/rewardsService'; // Import earnPoints
import { toast } from 'sonner'; // Import toast

interface CircadianResetProps {
  durationInMinutes: number;
  onComplete: () => void;
}

const CircadianReset: React.FC<CircadianResetProps> = ({ durationInMinutes, onComplete }) => {
  const totalDurationSeconds = durationInMinutes * 60;
  const [timeLeft, setTimeLeft] = useState(totalDurationSeconds);
  const [isActive, setIsActive] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const { user } = useAuth();
  const audioContextRef = useRef<AudioContext | null>(null);

  // Audio elements
  // const startSound = useRef(typeof Audio !== 'undefined' ? new Audio('/sounds/start.mp3') : null); // Replaced with Web Audio
  const completeSound = useRef(typeof Audio !== 'undefined' ? new Audio('/sounds/completion.mp3') : null); // Use existing completion sound

  useEffect(() => {
    if (typeof window !== 'undefined' && !audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    }
  }, []);

  const playTone = (frequency: number, duration: number, type: OscillatorType = 'sine') => {
    if (!audioContextRef.current) return;
    const context = audioContextRef.current;
    const oscillator = context.createOscillator();
    const gainNode = context.createGain();

    oscillator.type = type;
    oscillator.frequency.setValueAtTime(frequency, context.currentTime);
    gainNode.gain.setValueAtTime(0.1, context.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.00001, context.currentTime + duration / 1000);

    oscillator.connect(gainNode);
    gainNode.connect(context.destination);

    oscillator.start(context.currentTime);
    oscillator.stop(context.currentTime + duration / 1000);
  };

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft((prevTime) => prevTime - 1);
      }, 1000);
    } else if (timeLeft === 0 && isActive) {
      setIsActive(false);
      setIsComplete(true);
      completeSound.current?.play();
      
      // Award points on completion
      if (user?.id) {
        const pointsToAward = 15; // Specific points for this tool
        earnPoints(user.id, pointsToAward, 'exercise', 'Circadian Reset')
          .then(() => {
            toast.success(`+${pointsToAward} points for Circadian Reset!`);
          })
          .catch(err => console.error("Failed to award points for Circadian Reset", err));
      }
      onComplete();
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive, timeLeft, onComplete]);

  const toggleTimer = () => {
    if (!isActive && timeLeft === totalDurationSeconds) {
      // startSound.current?.play(); // Replaced with Web Audio
      playTone(523.25, 200); // C5 tone for start
    }
    setIsActive(!isActive);
    if (isComplete) setIsComplete(false);
  };

  const resetExercise = () => {
    setIsActive(false);
    setIsComplete(false);
    setTimeLeft(totalDurationSeconds);
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
  };

  const progress = (totalDurationSeconds - timeLeft) / totalDurationSeconds;
  const circumference = 2 * Math.PI * 54;
  const strokeDashoffset = circumference * (1 - progress);

  return (
    <div className={`flex flex-col items-center justify-center p-4 min-h-[350px] transition-all duration-300 ${isActive ? 'border-2 border-primary' : ''}`} style={isActive ? {borderRadius: '12px'} : {}}> {/* Added active border */}
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold mb-2 tracking-tight">Circadian Reset</h3>
        <p className="text-muted-foreground">
          Get {durationInMinutes} mins of natural light exposure.
        </p>
      </div>

      {/* Progress Circle & Time */}
      <div className="relative w-36 h-36 mb-6">
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 120 120">
          <circle cx="60" cy="60" r="54" fill="none" strokeWidth="8" className="stroke-border" />
          <circle
            cx="60"
            cy="60"
            r="54"
            fill="none"
            strokeWidth="8"
            className="stroke-primary transition-all duration-1000 ease-linear"
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            transform="rotate(-90 60 60)"
            strokeLinecap="round"
          />
        </svg>
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          {isComplete ? (
            <CheckCircle className="w-16 h-16 text-primary" />
          ) : (
            <div className="text-4xl font-bold">{formatTime(timeLeft)}</div>
          )}
        </div>
      </div>
      
      {/* Instructions / Completion Message */}
      <div className="mb-6 text-center">
        {isComplete ? (
          <p className="text-lg font-medium text-primary">Good job! Reset complete.</p>
        ) : isActive ? (
          <p className="text-lg font-medium">Step outside or near a window!</p>
        ) : (
          <p className="text-muted-foreground">Ready to reset your internal clock?</p>
        )}
      </div>

       {/* Detailed Instructions (collapsible maybe later) */}
       {!isActive && !isComplete && (
         <div className="text-sm text-muted-foreground text-left bg-muted p-3 border" style={{borderRadius: '8px'}}>
           <p className="font-medium mb-1">How to:</p>
           <ul className="list-disc pl-5 space-y-1">
             <li>Step outside for {durationInMinutes} minutes in natural daylight.</li>
             <li>Face the sun (safely, not staring directly) with eyes open.</li>
             <li>Best times: within 1 hour of waking and mid-afternoon.</li>
             <li>If indoor, sit near a window in bright natural light.</li>
           </ul>
         </div>
       )}

      {/* Controls */}
      <div className="flex gap-4 mt-6 pt-0">
        {!isComplete && (
          <Button onClick={toggleTimer} className="min-w-[100px]">
            {isActive ? 'Pause' : (timeLeft === totalDurationSeconds ? 'Start' : 'Resume')}
          </Button>
        )}
        <Button onClick={resetExercise} variant={isComplete ? 'default' : 'ghost'} className="min-w-[100px]">
          {isComplete ? 'Restart' : 'Reset'}
        </Button>
      </div>
    </div>
  );
};

export default CircadianReset;
