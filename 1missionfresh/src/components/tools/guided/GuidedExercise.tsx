import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Check, RotateCcw, AlertCircle, Loader2 } from 'lucide-react';
import { guidedExerciseSteps } from '@/lib/toolsData';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useAuth } from '@/contexts/AuthContext';
import { earnPoints } from '@/services/rewardsService';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';

interface GuidedExerciseProps {
  exerciseType: keyof typeof guidedExerciseSteps;
  onComplete: () => void;
}

const GuidedExercise: React.FC<GuidedExerciseProps> = ({ exerciseType, onComplete }) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [steps, setSteps] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    if (exerciseType && guidedExerciseSteps[exerciseType]) {
      setSteps(guidedExerciseSteps[exerciseType]);
      setLoading(false);
    } else {
      setError(true);
      setLoading(false);
    }
  }, [exerciseType]);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-4 min-h-[200px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error || steps.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-4 min-h-[200px]">
        <Alert variant="destructive" className="max-w-sm">
          <AlertCircle className="h-4 w-4" /> 
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Error loading exercise steps. Please try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const handleInternalComplete = async () => {
    if (user?.id) {
      try {
        const pointsToAward = 10;
        const exerciseName = guidedExerciseSteps[exerciseType]?.[0] ? exerciseType : "Guided Exercise";
        await earnPoints(user.id, pointsToAward, 'exercise', exerciseName);
        toast.success(`+${pointsToAward} points for ${exerciseName}!`);
      } catch (err) {
        console.error("Failed to award points for guided exercise", err);
      }
    }
    onComplete();
  };

  const handleNextStep = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    } else {
      handleInternalComplete();
    }
  };

  const handlePreviousStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };

  const handleRestart = () => {
    setCurrentStepIndex(0);
  };

  const isLastStep = currentStepIndex === steps.length - 1;
  const isFirstStep = currentStepIndex === 0;

  const progress = (currentStepIndex + 1) / steps.length;

  return (
    <div className="flex flex-col items-center justify-center p-4 text-center">
      <div className="w-full max-w-md mb-6">
        <div className="text-sm text-muted-foreground mb-2">Step {currentStepIndex + 1} of {steps.length}</div>
        {/* Progress Bar */}
        <div className="w-full bg-border h-2.5 mb-4" style={{borderRadius: '50px'}}>
          <div
            className="bg-primary h-2.5 transition-all duration-500 ease-in-out" style={{borderRadius: '50px'}}
            style={{ width: `${progress * 100}%` }}
          ></div>
        </div>
        {/* Step Text with Transition */}
        <div className="min-h-[60px] flex items-center justify-center">
          <AnimatePresence mode="wait">
            <motion.p
              key={currentStepIndex}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="text-lg font-semibold"
            >
              {steps[currentStepIndex]}
            </motion.p>
          </AnimatePresence>
        </div>
      </div>

      <div className="flex gap-4 mt-4">
        {!isFirstStep && (
          <Button onClick={handlePreviousStep} variant="outline">
            <ChevronLeft className="mr-2 h-4 w-4" /> Previous
          </Button>
        )}
        {isLastStep ? (
          <Button onClick={handleInternalComplete}>
            <Check className="mr-2 h-4 w-4" /> Finish Exercise
          </Button>
        ) : (
          <Button onClick={handleNextStep}>
            Next Step <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        )}
        {/* Always show Restart button */}
        <Button onClick={handleRestart} variant="ghost">
           <RotateCcw className="mr-2 h-4 w-4" /> Restart
        </Button>
      </div>
    </div>
  );
};

export default GuidedExercise;
