import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion'; // Assuming framer-motion is available for potential future animations
import { useAuth } from '@/contexts/AuthContext'; // Import useAuth
import { earnPoints } from '@/services/rewardsService'; // Import earnPoints
import { toast } from 'sonner'; // Import toast

interface QuickStretchExerciseProps {
  onComplete: () => void;
}

const stretches = [
  { name: "Reach for the Sky", duration: 15, instructions: "Stand tall, interlock your fingers, and reach your hands towards the ceiling. Feel the stretch along your sides." },
  { name: "Toe Touch", duration: 15, instructions: "Bend forward from your hips, reaching towards your toes. Keep your knees slightly bent if needed. Feel the stretch in your hamstrings." },
  { name: "Shoulder Rolls", duration: 15, instructions: "Roll your shoulders forward in a circular motion for 5 repetitions, then roll them backward for 5 repetitions." },
  { name: "Neck Stretch (Left)", duration: 15, instructions: "Gently tilt your head to the left, bringing your left ear towards your left shoulder. Hold the stretch." },
  { name: "Neck Stretch (Right)", duration: 15, instructions: "Gently tilt your head to the right, bringing your right ear towards your right shoulder. Hold the stretch." },
];

const QuickStretchExercise: React.FC<QuickStretchExerciseProps> = ({ onComplete }) => {
  const [currentStretchIndex, setCurrentStretchIndex] = useState(0);
  const [timeLeft, setTimeLeft] = useState(stretches[0].duration);
  const [isActive, setIsActive] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const { user } = useAuth(); // Get user

  const currentStretch = stretches[currentStretchIndex];

  useEffect(() => {
    if (!isActive) {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      return;
    }

    timerRef.current = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          // Move to next stretch or complete
          if (currentStretchIndex < stretches.length - 1) {
            setCurrentStretchIndex((prevIndex) => prevIndex + 1);
            return stretches[currentStretchIndex + 1].duration; // Reset timer for next stretch
          } else {
            // Exercise complete
            setIsActive(false);
            handleExerciseInternalComplete(); // Changed to internal complete
            if (timerRef.current) {
              clearInterval(timerRef.current);
              timerRef.current = null;
            }
            return 0;
          }
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isActive, currentStretchIndex, onComplete]);

  const handleExerciseInternalComplete = async () => {
    if (user?.id) {
      try {
        const pointsToAward = 5;
        await earnPoints(user.id, pointsToAward, 'exercise', 'Quick Stretch Sequence');
        toast.success(`+${pointsToAward} points for Quick Stretch!`)
      } catch (err) {
        console.error("Failed to award points for quick stretch", err);
      }
    }
    onComplete(); // Call the original onComplete passed from parent
  };

  const startExercise = () => {
    setCurrentStretchIndex(0);
    setTimeLeft(stretches[0].duration);
    setIsActive(true);
  };

  const pauseResumeExercise = () => {
    setIsActive(!isActive);
  };

  const resetExercise = () => {
    setIsActive(false);
    setCurrentStretchIndex(0);
    setTimeLeft(stretches[0].duration);
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  return (
    <div className="flex flex-col items-center justify-center p-4 min-h-[300px]">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold mb-2 tracking-tight">{currentStretch.name}</h3>
        <p className="text-muted-foreground text-sm">Stretch {currentStretchIndex + 1} of {stretches.length}</p>
        <p className="text-lg font-medium mt-4">{formatTime(timeLeft)}</p>
      </div>

      <div className="mb-8 text-center">
        <p>{currentStretch.instructions}</p>
      </div>

      {/* Simple visual indicator (can be enhanced later) */}
      <div className="w-40 h-40 border border-border bg-background flex items-center justify-center mb-8 relative overflow-hidden" style={{borderRadius: '50%'}}>
         <motion.div
          className="absolute inset-0 bg-primary origin-center"
          initial={{ scale: 0 }}
          animate={{ scale: isActive ? 1 : 0 }}
          transition={{ duration: currentStretch.duration, ease: "linear" }}
        />
        <span className="relative z-10 text-2xl font-medium text-primary-foreground">
          {isActive ? 'Stretching' : 'Ready'}
        </span>
      </div>


      {/* Controls */}
      <div className="flex gap-4">
        {!isActive && currentStretchIndex === 0 && (
          <Button onClick={startExercise}>Start</Button>
        )}
        {isActive && (
          <Button onClick={pauseResumeExercise} variant="outline">Pause</Button>
        )}
        {!isActive && currentStretchIndex > 0 && currentStretchIndex < stretches.length && (
           <Button onClick={pauseResumeExercise}>Resume</Button>
        )}
         {(currentStretchIndex > 0 || isActive) && currentStretchIndex < stretches.length && (
           <Button onClick={resetExercise} variant="ghost">Reset</Button>
         )}
         {currentStretchIndex === stretches.length && (
           <Button onClick={resetExercise}>Restart</Button>
         )}
      </div>
    </div>
  );
};

export default QuickStretchExercise;
