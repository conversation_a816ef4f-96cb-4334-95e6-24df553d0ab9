import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { PlayCircle, PauseCircle, RotateCcw, CheckCircle } from 'lucide-react';
import { useTimer } from '@/hooks/useTimer';
import { useHaptics, HapticImpact } from '@/hooks/useHaptics';
import { energyExercises } from '@/lib/toolsData';

interface EnergyExerciseProps {
  exerciseType: keyof typeof energyExercises;
  onComplete: () => void;
}

const EnergyExercise: React.FC<EnergyExerciseProps> = ({ exerciseType, onComplete }) => {
  const { impact } = useHaptics();
  const exercise = energyExercises[exerciseType];
  const [sessionComplete, setSessionComplete] = useState(false);
  
  // Extract duration in minutes from string like "5 minutes"
  const durationMinutes = parseInt(exercise.duration.split(' ')[0]);
  
  const {
    timeLeft,
    isRunning,
    progress,
    start,
    pause,
    reset
  } = useTimer(durationMinutes * 60, () => {
    setSessionComplete(true);
    impact(HapticImpact.HEAVY);
  });

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleComplete = () => {
    impact(HapticImpact.MEDIUM);
    onComplete();
  };

  return (
    <div className="flex flex-col items-center space-y-6 p-6">
      {/* Exercise Info */}
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold text-foreground">{exercise.title}</h3>
        <p className="text-muted-foreground">{exercise.description}</p>
      </div>

      {/* Timer Display */}
      <div className="text-center space-y-4">
        <motion.div 
          className="text-6xl font-mono font-bold text-primary"
          animate={{ scale: isRunning ? [1, 1.02, 1] : 1 }}
          transition={{ duration: 1, repeat: isRunning ? Infinity : 0 }}
        >
          {formatTime(timeLeft)}
        </motion.div>
        
        <Progress 
          value={progress} 
          className="h-2 w-64 bg-muted/30"
        />
        
        <p className="text-muted-foreground">
          {sessionComplete ? 'Session Complete! Great work!' : 
           isRunning ? 'Keep going, you\'re doing amazing!' : 
           'Ready to boost your energy?'}
        </p>
      </div>

      {/* Controls */}
      {!sessionComplete ? (
        <div className="flex gap-3">
          <Button
            size="lg"
            onClick={isRunning ? pause : start}
                            className="w-20 h-12 bg-primary hover:bg-secondary"
          >
            {isRunning ? <PauseCircle className="w-5 h-5" /> : <PlayCircle className="w-5 h-5" />}
          </Button>
          
          <Button
            size="lg"
            variant="outline"
            onClick={() => reset(durationMinutes * 60)}
            className="w-20 h-12"
          >
            <RotateCcw className="w-5 h-5" />
          </Button>
        </div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-4"
        >
          <CheckCircle className="w-16 h-16 text-primary mx-auto" />
          <h3 className="text-lg font-semibold text-primary">Energy Boosted!</h3>
          <p className="text-muted-foreground text-sm">
            You've completed a {durationMinutes}-minute energy session.
          </p>
          <Button onClick={handleComplete} className="mt-4">
            Complete Exercise
          </Button>
        </motion.div>
      )}
    </div>
  );
};

export default EnergyExercise;