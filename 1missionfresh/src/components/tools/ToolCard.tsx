import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from 'react-router-dom';
import { ArrowRight, CheckCircle } from 'lucide-react';

interface ToolCardProps {
  title: string;
  description: string;
  icon: React.ReactElement;
  link: string;
  features?: string[];
}

export const ToolCard: React.FC<ToolCardProps> = ({ title, description, icon, link, features }) => {
  return (
    <Link to={link} className="group block h-full no-underline">
      <Card className="flex flex-col h-full bg-card border-2 border-border hover:border-primary/50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] group-hover:border-primary" style={{borderRadius: '32px'}}>
        <CardContent className="p-6 flex flex-col items-center text-center h-full space-y-4">
          <div className="relative flex-shrink-0">
            <div className="bg-primary p-3 flex items-center justify-center w-12 h-12 shadow-sm hover:shadow-md transition-all duration-300" style={{borderRadius: '24px'}}>
              {React.cloneElement(icon, { className: "w-6 h-6 text-primary-foreground", strokeWidth: "2" })}
            </div>
          </div>
          
          <div className="flex-1 flex flex-col justify-between space-y-3">
            <CardTitle className="text-lg font-semibold text-foreground leading-tight group-hover:text-primary transition-colors duration-300">{title}</CardTitle>
            <CardDescription className="text-sm text-muted-foreground leading-relaxed font-medium group-hover:text-foreground/90 transition-colors duration-300">
              {description}
            </CardDescription>
          </div>
          
          <div className="mt-auto">
            <span className="text-primary font-semibold text-sm inline-flex items-center group-hover:text-primary transition-all duration-300 px-4 py-2 border border-primary hover:bg-primary hover:text-primary-foreground" style={{borderRadius: '16px'}}>
              Learn More
              <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" strokeWidth="2" />
            </span>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};
