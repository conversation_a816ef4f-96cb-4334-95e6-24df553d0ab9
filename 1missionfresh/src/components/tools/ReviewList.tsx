import React, { useState, useMemo } from 'react'; // Import useState and useMemo
import ReviewItem from './ReviewItem';
import ProductReviewForm from './ProductReviewForm';
import { Database } from '@/lib/database.types'; // Import Database type
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"; // Import Select components
import { Label } from "@/components/ui/label"; // Ensure Label is imported
import type { ProductReviewWithUser } from '@/services/productService'; // Cline: Import ProductReviewWithUser

// Define the type for a fetched review, including joined user data
// type SmokelessProductReview = Database['mission_fresh']['Tables']['smokeless_product_reviews']['Row'] & {
//   user: { email: string | null; name: string | null } | null;
// }; // Cline: Commented out, will use ProductReviewWithUser

interface ReviewListProps {
  reviews: ProductReviewWithUser[]; // Cline: Use ProductReviewWithUser
  productId: string;
  isSubmitting: boolean;
  onSubmitReview: (reviewData: { product_id: string; rating: number; review_text?: string }) => Promise<void>;
  onSubmitSuccess: () => Promise<void>;
}

const ReviewList: React.FC<ReviewListProps> = ({
  reviews,
  productId,
  isSubmitting,
  onSubmitReview,
  onSubmitSuccess
}) => {
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'highest' | 'lowest'>('newest'); // Declare sortBy state

  // Filter reviews to only show approved ones based on moderation_status
  const moderatedReviews = reviews.filter(review => review.moderation_status === 'approved');

  // Sort moderated reviews based on the selected option
  const sortedReviews = useMemo(() => { // Declare sortedReviews memo
    let sorted = [...moderatedReviews];
    switch (sortBy) {
      case 'oldest':
        sorted.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
        break;
      case 'highest':
        sorted.sort((a, b) => b.rating - a.rating);
        break;
      case 'lowest':
        sorted.sort((a, b) => a.rating - b.rating);
        break;
      case 'newest':
      default:
        sorted.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
    }
    return sorted;
  }, [moderatedReviews, sortBy]);


  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Reviews ({moderatedReviews.length})</h2> {/* Display count of moderated reviews */}

      <ProductReviewForm
        productId={productId}
        onSubmitSuccess={onSubmitSuccess}
        isLoading={isSubmitting}
        mutationFn={onSubmitReview}
      />

      {moderatedReviews.length > 0 && ( // Only show sort if there are moderated reviews
        <div className="flex justify-end items-center gap-2 text-sm mt-6">
          <Label htmlFor="sort-reviews">Sort by:</Label>
          <Select value={sortBy} onValueChange={(value: 'newest' | 'oldest' | 'highest' | 'lowest') => setSortBy(value)}>
            <SelectTrigger id="sort-reviews" className="w-[150px]">
              <SelectValue placeholder="Sort reviews" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="oldest">Oldest First</SelectItem>
              <SelectItem value="highest">Highest Rating</SelectItem>
              <SelectItem value="lowest">Lowest Rating</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}


      <div className="space-y-4 mt-6"> {/* Adjusted margin-top */}
        {sortedReviews.map((review) => ( // Render sorted reviews
          <ReviewItem key={review.id} review={review} />
        ))}

        {moderatedReviews.length === 0 && ( // Check moderated reviews length
          <p className="text-center text-muted-foreground">
            No reviews yet. Be the first to review this product! (Reviews appear after moderation)
          </p>
        )}
      </div>
    </div>
  );
};

export default ReviewList;
