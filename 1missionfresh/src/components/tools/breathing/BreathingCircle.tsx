import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface BreathingCircleProps {
  phase: 'inhale' | 'hold' | 'exhale' | 'rest';
  progress: number;
  className?: string;
}

export const BreathingCircle: React.FC<BreathingCircleProps> = ({
  phase,
  progress,
  className
}) => {
  const baseSize = 200;
  const maxScale = 1.5;
  
  const getScale = () => {
    switch (phase) {
      case 'inhale':
        return 1 + (progress / 100) * (maxScale - 1);
      case 'exhale':
        return maxScale - (progress / 100) * (maxScale - 1);
      case 'hold':
        return maxScale;
      case 'rest':
        return 1;
      default:
        return 1;
    }
  };

  const phaseText = {
    inhale: 'Breathe In',
    hold: 'Hold',
    exhale: 'Breathe Out',
    rest: 'Rest'
  };

  return (
    <div className={cn("relative flex items-center justify-center", className)}>
      {/* Background ripple effect */}
      <AnimatePresence>
        {(phase === 'inhale' || phase === 'hold') && (
          <motion.div
            initial={{ scale: 1, opacity: 0 }}
            animate={{ 
              scale: [1, 1.8],
              opacity: [0.2, 0]
            }}
            transition={{ 
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute bg-secondary"
            style={{ width: baseSize, height: baseSize, borderRadius: '50%' }}
          />
        )}
      </AnimatePresence>

      {/* Main breathing circle */}
      <motion.div
        animate={{
          scale: getScale(),
          opacity: phase === 'rest' ? 0.8 : 1
        }}
        transition={{ 
          type: "spring",
          stiffness: 60,
          damping: 20
        }}
        className="relative flex items-center justify-center"
      >
        <div 
          className={cn(
            "border border-border",
                          "bg-secondary",
            "shadow-sm"
          )}
          style={{ width: baseSize, height: baseSize }}
        >
          {/* Inner glow */}
          <div className="absolute inset-0 bg-secondary" style={{borderRadius: '50%'}} />
          
          {/* Content */}
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <motion.span 
              key={phase}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{
                type: "spring",
                stiffness: 200,
                damping: 20
              }}
              className="text-lg font-semibold text-primary mb-2 tracking-tight"
            >
              {phaseText[phase]}
            </motion.span>
            
            {/* Progress bar */}
            <motion.div 
              className="h-1.5 w-20 bg-secondary overflow-hidden shadow-sm" style={{borderRadius: '50px'}}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <motion.div 
                className="h-full bg-primary"
                animate={{ 
                  width: `${progress}%`,
                  transition: {
                    type: "spring",
                    stiffness: 60,
                    damping: 20
                  }
                }}
              />
            </motion.div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
