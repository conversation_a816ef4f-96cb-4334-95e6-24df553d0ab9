import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Play, Pause, RotateCcw, Square } from 'lucide-react';

interface BoxBreathingProps {
  onComplete: () => void;
  totalCycles?: number;
  cycleDuration?: number;
}

const BoxBreathing: React.FC<BoxBreathingProps> = ({
  onComplete,
  totalCycles = 5,
  cycleDuration = 4
}) => {
  const [step, setStep] = useState<'idle' | 'inhale' | 'hold1' | 'exhale' | 'hold2' | 'complete'>('idle');
  const [count, setCount] = useState(0);
  const [cycle, setCycle] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const onCompleteRef = useRef(onComplete);

  // Update the ref when onComplete changes
  useEffect(() => {
    onCompleteRef.current = onComplete;
  }, [onComplete]);

  // Initialize AudioContext safely when user starts exercise
  const initializeAudioContext = () => {
    if (typeof window !== 'undefined' && !audioContextRef.current) {
      try {
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      } catch (error) {
        console.warn('AudioContext initialization failed:', error);
        // Continue without audio - don't crash the component
      }
    }
  };
  
  const playTone = (frequency: number, duration: number, type: OscillatorType = 'sine') => {
    if (!audioContextRef.current) return;
    
    try {
      const context = audioContextRef.current;
      if (context.state === 'suspended') {
        context.resume();
      }
      
      const oscillator = context.createOscillator();
      const gainNode = context.createGain();

      oscillator.type = type;
      oscillator.frequency.setValueAtTime(frequency, context.currentTime);
      gainNode.gain.setValueAtTime(0.1, context.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.00001, context.currentTime + duration / 1000);

      oscillator.connect(gainNode);
      gainNode.connect(context.destination);

      oscillator.start(context.currentTime);
      oscillator.stop(context.currentTime + duration / 1000);
    } catch (error) {
      console.warn('Audio tone playback failed:', error);
      // Continue without audio - don't crash the component
    }
  };

  useEffect(() => {
    const cleanup = () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };

    if (!isActive) {
      cleanup();
      return;
    }

    intervalRef.current = setInterval(() => {
      setCount((prevCount) => {
        if (prevCount < cycleDuration - 1) {
          return prevCount + 1;
        } else {
          switch (step) {
            case 'inhale': setStep('hold1'); break;
            case 'hold1': setStep('exhale'); break;
            case 'exhale': setStep('hold2'); break;
            case 'hold2':
              if (cycle + 1 < totalCycles) {
                setCycle(prev => prev + 1);
                setStep('inhale');
              } else {
                setStep('complete');
                setIsActive(false);
                onCompleteRef.current();
                cleanup();
              }
              break;
          }
          return 0;
        }
      });
    }, 1000);

    return cleanup;
  }, [isActive, totalCycles, cycleDuration]); // SURGICAL FIX: Removed step, cycle from dependency array to prevent infinite recursion

  // Play sound when step changes
  useEffect(() => {
    if (!isActive || !audioContextRef.current) return;

    const toneDuration = 150;
    switch (step) {
      case 'inhale':
        playTone(440, toneDuration);
        break;
      case 'hold1':
      case 'hold2':
        playTone(330, toneDuration);
        break;
      case 'exhale':
        playTone(220, toneDuration);
        break;
      default:
        break;
    }
  }, [step, isActive]);

  const startExercise = () => {
    // Initialize audio context when user starts (user gesture required for modern browsers)
    initializeAudioContext();
    setStep('inhale');
    setCount(0);
    setCycle(0);
    setIsActive(true);
  };

  const pauseResumeExercise = () => {
    setIsActive(!isActive);
  };

  const resetExercise = () => {
    setIsActive(false);
    setStep('idle');
    setCount(0);
    setCycle(0);
  };

  // Calculate animation properties
  let scale = 1;
  let borderWidth = 2;
  let text = 'Ready to Begin';
  let instruction = 'Click Start when ready';

  if (isActive) {
    switch (step) {
      case 'inhale': 
        scale = 1.4; 
        borderWidth = 3;
        text = 'Breathe In'; 
        instruction = `${count + 1} of ${cycleDuration}`;
        break;
      case 'hold1': 
        scale = 1.4; 
        borderWidth = 3;
        text = 'Hold'; 
        instruction = `${count + 1} of ${cycleDuration}`;
        break;
      case 'exhale': 
        scale = 0.7; 
        borderWidth = 1;
        text = 'Breathe Out'; 
        instruction = `${count + 1} of ${cycleDuration}`;
        break;
      case 'hold2': 
        scale = 0.7; 
        borderWidth = 1;
        text = 'Hold'; 
        instruction = `${count + 1} of ${cycleDuration}`;
        break;
    }
  }
  
  if (step === 'complete') {
    text = 'Complete!';
    instruction = 'Well done!';
  }

  return (
    <Card className="bg-background border-2 border-border/30 shadow-sm">
      <CardContent className="p-royal-lg">
        <div className="flex flex-col items-center space-y-royal-md">
          {/* Header */}
          <div className="text-center space-y-2">
            <h3 className="text-2xl font-bold font-heading text-foreground tracking-tight">
              Box Breathing
            </h3>
            <p className="text-muted-foreground font-medium">
              Cycle {cycle + 1} of {totalCycles} • {cycleDuration}s per phase
            </p>
          </div>

          {/* Breathing Visual */}
          <div className="relative w-48 h-48 flex items-center justify-center">
            {/* Outer ring for reference */}
            <div className="absolute inset-0 border-2 border-border/20" style={{borderRadius: '48px'}} />
            
            {/* Animated breathing square */}
            <div
              className="relative border-primary bg-secondary transition-all duration-1000 ease-in-out flex items-center justify-center"
              style={{
                width: `${100 * scale}px`,
                height: `${100 * scale}px`,
                borderRadius: '32px',
                borderWidth: `${borderWidth}px`,
                transitionDuration: isActive ? `${cycleDuration}s` : '0.5s',
              }}
            >
              <Square className="h-8 w-8 text-primary" />
            </div>

            {/* Center text overlay */}
            <div className="absolute inset-0 flex flex-col items-center justify-center pointer-events-none">
              <div className="text-xl font-bold text-foreground font-heading mb-1">
                {text}
              </div>
              <div className="text-sm text-muted-foreground font-medium">
                {instruction}
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center gap-3">
            {step === 'idle' && (
              <Button onClick={startExercise} size="lg" className="font-semibold">
                <Play className="mr-2 h-5 w-5" />
                Start Exercise
              </Button>
            )}
            
            {isActive && (
              <Button onClick={pauseResumeExercise} variant="outline" size="lg">
                <Pause className="mr-2 h-5 w-5" />
                Pause
              </Button>
            )}
            
            {!isActive && step !== 'idle' && step !== 'complete' && (
              <Button onClick={pauseResumeExercise} size="lg">
                <Play className="mr-2 h-5 w-5" />
                Resume
              </Button>
            )}
            
            {(step !== 'idle' || isActive) && step !== 'complete' && (
              <Button onClick={resetExercise} variant="ghost" size="lg">
                <RotateCcw className="mr-2 h-4 w-4" />
                Reset
              </Button>
            )}
            
            {step === 'complete' && (
              <Button onClick={resetExercise} size="lg" className="font-semibold">
                <RotateCcw className="mr-2 h-5 w-5" />
                Practice Again
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BoxBreathing;
