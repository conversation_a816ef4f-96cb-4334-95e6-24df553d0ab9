import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Circle } from 'lucide-react'; // Using Circle for animation

interface BellyBreathingProps {
  onComplete: () => void;
  totalCycles?: number; // Allow setting total cycles
  inhaleDuration?: number; // Allow setting inhale duration
  exhaleDuration?: number; // Allow setting exhale duration
}

const BellyBreathing: React.FC<BellyBreathingProps> = ({
  onComplete,
  totalCycles = 5, // Default to 5 cycles
  inhaleDuration = 4, // Default inhale count
  exhaleDuration = 6 // Default exhale count
}) => {
  const [step, setStep] = useState<'idle' | 'inhale' | 'exhale' | 'complete'>('idle');
  const [count, setCount] = useState(0);
  const [cycle, setCycle] = useState(0); // Track completed cycles
  const [isActive, setIsActive] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const onCompleteRef = useRef(onComplete);

  // Update the ref when onComplete changes
  useEffect(() => {
    onCompleteRef.current = onComplete;
  }, [onComplete]);

  // Audio refs
  const inhaleSoundRef = useRef<HTMLAudioElement | null>(null);
  const exhaleSoundRef = useRef<HTMLAudioElement | null>(null);

  // Initialize audio elements safely when user starts exercise
  const initializeAudioElements = () => {
    try {
      if (!inhaleSoundRef.current) {
        inhaleSoundRef.current = new Audio('/sounds/inhale.mp3');
        inhaleSoundRef.current.onerror = () => console.warn('Inhale sound failed to load');
      }
      if (!exhaleSoundRef.current) {
        exhaleSoundRef.current = new Audio('/sounds/exhale.mp3');
        exhaleSoundRef.current.onerror = () => console.warn('Exhale sound failed to load');
      }
    } catch (error) {
      console.warn('Audio initialization failed:', error);
      // Continue without audio - don't crash the component
    }
  };

  useEffect(() => {
    const cleanup = () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      // Pause and reset audio on cleanup
      if (inhaleSoundRef.current) {
        inhaleSoundRef.current.pause();
        inhaleSoundRef.current.load();
      }
      if (exhaleSoundRef.current) {
        exhaleSoundRef.current.pause();
        exhaleSoundRef.current.load();
      }
    };

    if (!isActive) {
      cleanup();
      return;
    }

    intervalRef.current = setInterval(() => {
      setCount((prevCount) => {
        const maxCount = step === 'inhale' ? inhaleDuration : exhaleDuration;
        if (prevCount < maxCount - 1) {
          return prevCount + 1;
        } else {
          // Move to next step or cycle
          if (step === 'inhale') {
            setStep('exhale');
          } else if (step === 'exhale') {
            if (cycle + 1 < totalCycles) {
              setCycle(prev => prev + 1);
              setStep('inhale'); // Start next cycle
            } else {
              // All cycles complete
              setStep('complete');
              setIsActive(false);
              onCompleteRef.current();
              cleanup();
            }
          }
          return 0; // Reset count for the new step
        }
      });
    }, 1000); // 1 second interval

    return cleanup;
  }, [isActive, totalCycles, inhaleDuration, exhaleDuration]); // SURGICAL FIX: Removed step, cycle from dependency array to prevent infinite recursion

  // Play sound when step changes with error handling
  useEffect(() => {
    if (!isActive) return;

    const playAudioSafely = (audio: HTMLAudioElement | null) => {
      if (!audio) return;
      try {
        const playPromise = audio.play();
        if (playPromise !== undefined) {
          playPromise.catch(error => {
            console.warn('Audio playback failed:', error);
          });
        }
      } catch (error) {
        console.warn('Audio play error:', error);
      }
    };

    switch (step) {
      case 'inhale':
        playAudioSafely(inhaleSoundRef.current);
        break;
      case 'exhale':
        playAudioSafely(exhaleSoundRef.current);
        break;
      default:
        break;
    }
  }, [step, isActive]);

  const startExercise = () => {
    // Initialize audio elements when user starts (user gesture required for audio playback)
    initializeAudioElements();
    setStep('inhale');
    setCount(0);
    setCycle(0);
    setIsActive(true);
  };

  const pauseResumeExercise = () => {
    setIsActive(!isActive);
  };

  const resetExercise = () => {
    setIsActive(false);
    setStep('idle');
    setCount(0);
    setCycle(0);
  };

  // Calculate animation properties for a circle
  let scale = 1;
  let opacity = 0.6;
  let transitionDuration = 0.5; // Default quick transition
  let text = 'Ready?';

  if (isActive) {
     switch (step) {
      case 'inhale':
        scale = 1.3;
        opacity = 1;
        text = 'Inhale';
        transitionDuration = inhaleDuration;
        break;
      case 'exhale':
        scale = 0.7;
        opacity = 0.8;
        text = 'Exhale';
        transitionDuration = exhaleDuration;
        break;
    }
  }
  if (step === 'complete') text = 'Complete!';


  return (
    <div className="flex flex-col items-center justify-center p-4 min-h-[300px]">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold mb-2 tracking-tight text-foreground">Belly Breathing</h3>
        <p className="text-muted-foreground text-sm">Cycles: {cycle} / {totalCycles} | Inhale: {inhaleDuration}s, Exhale: {exhaleDuration}s</p>
      </div>

       {/* Animated Circle Visual */}
      <div className="w-40 h-40 border border-border bg-background flex items-center justify-center mb-8 relative overflow-hidden" style={{borderRadius: '50%'}}>
         <div
          className="absolute inset-0 bg-primary transition-all origin-center ease-in-out"
          style={{
            transform: `scale(${scale})`,
            opacity: opacity,
            transitionDuration: `${transitionDuration}s`,
          }}
        />
        <span className="relative z-10 text-2xl font-medium text-primary-foreground">
          {text}
        </span>
      </div>


      {/* Controls */}
      <div className="flex gap-4">
        {step === 'idle' && (
          <Button onClick={startExercise}>Start</Button>
        )}
        {isActive && (
          <Button onClick={pauseResumeExercise} variant="outline">Pause</Button>
        )}
        {!isActive && step !== 'idle' && step !== 'complete' && (
           <Button onClick={pauseResumeExercise}>Resume</Button>
        )}
         {(step !== 'idle' || isActive) && step !== 'complete' && (
           <Button onClick={resetExercise} variant="ghost">Reset</Button>
         )}
         {step === 'complete' && (
           <Button onClick={resetExercise}>Restart</Button>
         )}
      </div>
    </div>
  );
};

export default BellyBreathing;
