import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { PlayCircle, PauseCircle, RotateCcw, CheckCircle } from 'lucide-react';
import { useHaptics, HapticImpact } from '@/hooks/useHaptics';
import { focusExercises } from '@/lib/toolsData';

interface FocusExerciseProps {
  exerciseType: keyof typeof focusExercises;
  onComplete: () => void;
}

const FocusExercise: React.FC<FocusExerciseProps> = ({ exerciseType, onComplete }) => {
  const { impact } = useHaptics();
  const exercise = focusExercises[exerciseType];
  
  // FIXED: Calculate duration only once to prevent infinite loop
  const getDurationSeconds = (duration: string) => {
    const durationMinutes = parseInt(duration.split(' ')[0]);
    return durationMinutes * 60;
  };
  
  // Simple timer state - FIXED: Use function for initial state to prevent infinite loop
  const [timeLeft, setTimeLeft] = useState(() => getDurationSeconds(exercise.duration));
  const [isRunning, setIsRunning] = useState(false);
  const [sessionComplete, setSessionComplete] = useState(false);

  // CRITICAL FIX: Reset timer state when exercise type changes - FIXED INFINITE LOOP
  useEffect(() => {
    console.log('🔧 FLAW #11 FIXED: Resetting timer for exercise type:', exerciseType);
    const durationMinutes = parseInt(exercise.duration.split(' ')[0]);
    const calculatedSeconds = durationMinutes * 60;
    console.log('🔧 Setting timeLeft to:', calculatedSeconds, 'seconds');
    setTimeLeft(calculatedSeconds);
    setIsRunning(false);
    setSessionComplete(false);
  }, [exerciseType, exercise.duration]); // FIXED: Use exercise.duration instead of totalSeconds to prevent infinite loop

  // Timer effect - simple and reliable
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    
    if (isRunning) {
      interval = setInterval(() => {
        setTimeLeft((prevTime) => {
          if (prevTime <= 1) {
            setIsRunning(false);
            setSessionComplete(true);
            impact(HapticImpact.HEAVY);
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
    }
    
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isRunning, impact]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStart = () => {
    try {
      setIsRunning(true);
    } catch (error) {
      console.error('❌ Error in handleStart:', error);
    }
  };

  const handlePause = () => {
    setIsRunning(false);
  };

  const handleReset = () => {
    setIsRunning(false);
    const resetSeconds = getDurationSeconds(exercise.duration);
    setTimeLeft(resetSeconds);
    setSessionComplete(false);
  };

  const handleComplete = () => {
    impact(HapticImpact.MEDIUM);
    onComplete();
  };

  // Calculate progress
  const totalSeconds = getDurationSeconds(exercise.duration);
  const progress = totalSeconds > 0 ? ((totalSeconds - timeLeft) / totalSeconds) * 100 : 0;

  return (
    <div className="flex flex-col items-center space-y-6 p-6">
      {/* Exercise Info */}
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold text-foreground">{exercise.title}</h3>
        <p className="text-muted-foreground">{exercise.description}</p>
      </div>

      {/* Timer Display */}
      <div className="text-center space-y-4">
        <motion.div 
          className="text-6xl font-mono font-bold text-primary"
          animate={{ scale: isRunning ? [1, 1.02, 1] : 1 }}
          transition={{ duration: 1, repeat: isRunning ? Infinity : 0 }}
        >
          {formatTime(timeLeft)}
        </motion.div>
        
        <Progress 
          value={progress} 
          className="h-2 w-64 bg-muted/30"
        />
        
        <p className="text-muted-foreground">
          {sessionComplete ? 'Session Complete! Great work!' : 
           isRunning ? 'Stay focused, you\'re doing great!' : 
           'Ready to begin your focus session?'}
        </p>
      </div>

      {/* Controls */}
      {!sessionComplete ? (
        <div className="flex gap-3">
          <Button
            size="lg"
            onClick={isRunning ? handlePause : handleStart}
                            className="w-20 h-12 bg-primary hover:bg-secondary"
          >
            {isRunning ? <PauseCircle className="w-5 h-5" /> : <PlayCircle className="w-5 h-5" />}
          </Button>
          
          <Button
            size="lg"
            variant="outline"
            onClick={handleReset}
            className="w-20 h-12"
          >
            <RotateCcw className="w-5 h-5" />
          </Button>
        </div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-4"
        >
          <CheckCircle className="w-16 h-16 text-primary mx-auto" />
          <h3 className="text-lg font-semibold text-primary">Excellent Focus!</h3>
          <p className="text-muted-foreground text-sm">
            You've completed a {parseInt(exercise.duration.split(' ')[0])}-minute focus session.
          </p>
          <Button onClick={handleComplete} className="mt-4">
            Complete Exercise
          </Button>
        </motion.div>
      )}
    </div>
  );
};

export default FocusExercise;