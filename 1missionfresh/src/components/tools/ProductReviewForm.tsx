import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Star, Loader2, Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface ProductReviewFormProps {
  productId: string;
  onSubmitSuccess: () => void; // Callback to refetch reviews
  isLoading: boolean;
  mutationFn: (reviewData: { product_id: string; rating: number; review_text?: string }) => Promise<void>;
}

const ProductReviewForm: React.FC<ProductReviewFormProps> = ({ productId, onSubmitSuccess, isLoading, mutationFn }) => {
  const { user } = useAuth();
  const [rating, setRating] = useState<number>(0);
  const [hoverRating, setHoverRating] = useState<number>(0);
  const [reviewText, setReviewText] = useState("");
  const [error, setError] = useState<string | null>(null);

  const handleRatingClick = (index: number) => {
    setRating(index);
    setError(null); // Clear error when rating is selected
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!user) {
      toast.error("Please log in to submit a review.");
      return;
    }

    if (rating === 0) {
      setError("Please select a star rating.");
      toast.error("Rating is required.");
      return;
    }

    try {
      await mutationFn({
        product_id: productId,
        rating: rating,
        review_text: reviewText || undefined,
      });
      // Reset form on success
      setRating(0);
      setReviewText("");
      onSubmitSuccess(); // Trigger refetch in parent
    } catch (err) {
      // Error handling is done in the mutation hook, but we can set local error state if needed
      setError(err instanceof Error ? err.message : "An unknown error occurred.");
    }
  };

  return (
    <Card className="border border-border bg-card" style={{borderRadius: '32px'}}>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2 text-foreground"> {/* Removed /95 opacity */}
          Write a Review
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info size={16} className="text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p>Your review will be visible after moderation. We typically approve reviews within 24 hours.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardTitle>
        {!user && <CardDescription className="text-destructive">You must be logged in to submit a review.</CardDescription>} {/* Removed /80 opacity */}
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="rating" className="mb-2 block text-foreground">Your Rating*</Label> {/* Removed /90 opacity */}
            <div className="flex items-center gap-1">
              {[1, 2, 3, 4, 5].map((index) => (
                <Star
                  key={index}
                  className={cn(
                    "h-6 w-6 cursor-pointer transition-colors",
                    (hoverRating || rating) >= index
                      ? "text-primary fill-primary" // Solid fill for selected/hovered
                      : "text-[hsl(var(--border))] hover:text-primary hover:fill-primary" // Use border color for unselected, solid primary for hover
                  )}
                  onClick={() => handleRatingClick(index)}
                  onMouseEnter={() => setHoverRating(index)}
                  onMouseLeave={() => setHoverRating(0)}
                  aria-label={`Rate ${index} stars`}
                />
              ))}
            </div>
            {error && <p className="text-sm text-destructive mt-1">{error}</p>}
          </div>
          <div>
            <Label htmlFor="review-text" className="text-foreground">Your Review (Optional)</Label> {/* Removed /90 opacity */}
            <Textarea
              id="review-text"
              placeholder="Share your thoughts on this product..."
              value={reviewText}
              onChange={(e) => setReviewText(e.target.value)}
              rows={4}
              disabled={!user || isLoading}
              className="placeholder:text-[hsl(var(--muted-foreground-faint))]" // Use HSL for faint placeholder
            />
          </div>
        </CardContent>
        <CardFooter className="flex flex-col items-stretch gap-2">
          <Button type="submit" disabled={!user || rating === 0 || isLoading} className="w-full">
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin text-primary" /> {/* Removed /90 opacity */}
                Submitting...
              </>
            ) : (
              "Submit Review"
            )}
          </Button>
          <p className="text-xs text-muted-foreground text-center mt-2">
            Reviews are moderated to ensure quality and helpfulness.
          </p>
        </CardFooter>
      </form>
    </Card>
  );
};

export default ProductReviewForm;
