import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/contexts/AuthContext';
import { earnPoints } from '@/services/rewardsService';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2 } from 'lucide-react';

interface MentalChallengesProps {
  onComplete: () => void;
}

// Mental challenges data
const challenges = [
  {
    type: 'math',
    question: 'Count backwards from 100 by 7s',
    duration: 30,
    points: 5,
  },
  {
    type: 'word',
    question: 'Name as many animals as you can that start with the letter "B"',
    duration: 30,
    points: 5,
  },
  {
    type: 'memory',
    question: 'Remember and repeat this sequence: 7, 2, 8, 3, 9, 4',
    duration: 20,
    points: 5,
  },
  {
    type: 'visualization',
    question: 'Visualize and describe your favorite place in detail',
    duration: 30,
    points: 5,
  },
  {
    type: 'spelling',
    question: 'Spell these words backwards: SMOKE, HEALTH, BREATH',
    duration: 25,
    points: 5,
  }
];

export const MentalChallenges: React.FC<MentalChallengesProps> = ({ onComplete }) => {
  const { user } = useAuth();
  const [currentChallenge, setCurrentChallenge] = useState(challenges[Math.floor(Math.random() * challenges.length)]);
  const [timeLeft, setTimeLeft] = useState(currentChallenge.duration);
  const [isActive, setIsActive] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(prev => {
          const newTime = prev - 1;
          setProgress((currentChallenge.duration - newTime) / currentChallenge.duration * 100);
          return newTime;
        });
      }, 1000);
    } else if (timeLeft === 0 && isActive) {
      setIsActive(false);
      setIsCompleted(true);
      handleChallengeComplete();
    }

    return () => clearInterval(interval);
  }, [isActive, timeLeft]);

  const handleStart = () => {
    setIsActive(true);
  };

  const handleChallengeComplete = async () => {
    if (user?.id) {
      try {
        await earnPoints(user.id, currentChallenge.points, 'mental_challenge', currentChallenge.type);
      } catch (error) {
        console.error('Failed to award points for mental challenge:', error);
      }
    }
    onComplete();
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="space-y-6"
      >
        <Card className="p-6 space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Mental Challenge</h3>
            <p className="text-muted-foreground">{currentChallenge.question}</p>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Time Remaining: {timeLeft}s</span>
                <span>Points: {currentChallenge.points}</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>

            {!isActive && !isCompleted && (
              <Button 
                onClick={handleStart}
                className="w-full"
              >
                Start Challenge
              </Button>
            )}

            {isActive && (
              <div className="flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            )}

            {isCompleted && (
              <div className="text-center space-y-4">
                <p className="text-success font-medium">Challenge Complete!</p>
                <Button onClick={handleChallengeComplete} className="w-full">
                  Continue
                </Button>
              </div>
            )}
          </div>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
};