import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Clock, Star, Play } from "lucide-react";
import { cn } from "@/lib/utils";

export interface ToolExerciseProps {
  title: string;
  description: string;
  duration: string;
  difficulty: "easy" | "moderate" | "challenging";
  tags?: string[];
  onStart?: (event: React.MouseEvent) => void;
  className?: string;
  popular?: boolean;
  exerciseKey?: string;
  exerciseTitle?: string;
}

const ToolExerciseCard = ({
  title,
  description,
  duration,
  difficulty,
  tags = [],
  onStart,
  className,
  popular = false,
  exerciseKey,
  exerciseTitle,
}: ToolExerciseProps) => {
  
  // Consistent button styling for all exercises - <PERSON> approved uniformity
  const buttonClasses = React.useMemo(() => {
    return "w-full bg-primary hover:bg-primary-muted text-primary-foreground shadow-sm hover:shadow-lg hover-scale transition-all duration-300 font-medium";
  }, []);
  
  return (
    <Card 
      data-testid={`tool-exercise-card-${title}`}
      className={cn(
        "transition-all duration-200 hover:shadow-lg border border-border bg-background hover:border-primary/20 shadow-sm flex flex-col",
        "min-h-[320px] max-h-[360px]", // Reasonable card height - not wall-sized
        className
      )}
    >
      <CardHeader className="p-4 pb-2 flex-shrink-0">
        <div className="flex justify-between items-start mb-1">
          <CardTitle className="text-base font-bold text-foreground leading-tight tracking-tight line-clamp-2">{title}</CardTitle>
          {popular && (
            <div className="bg-secondary text-primary flex items-center gap-1 px-2 py-1 text-xs font-medium flex-shrink-0 shadow-sm" style={{borderRadius: '16px'}}>
              <Star className="h-4 w-4 fill-current" />
              <span>Popular</span>
            </div>
          )}
        </div>
        {/* FIXED FLAW #37: Consistent card content spacing with line clamping for visual harmony */}
        <CardDescription className="text-sm text-muted-foreground leading-relaxed line-clamp-3">{description}</CardDescription>
      </CardHeader>
      <CardContent className="p-4 pt-1 flex-grow flex flex-col">
        <div className="flex items-center gap-2 text-sm mb-4">
          <div className="flex items-center gap-1 text-muted-foreground">
            <div className="flex items-center justify-center bg-primary h-6 w-6" style={{borderRadius: '50%'}}>
              <Clock className="text-primary-foreground h-4 w-4" strokeWidth={2.5} />
            </div>
            <span className="font-medium text-foreground">{duration}</span>
          </div>
          <div className="text-muted-foreground">
            <span className="px-2 py-1 text-xs font-medium capitalize bg-secondary text-primary border border-primary shadow-sm" style={{borderRadius: '50px'}}>
              {difficulty}
            </span>
          </div>
        </div>

        <div className="flex flex-wrap gap-1 flex-grow content-start min-h-[2.5rem]">
          {tags.slice(0, 3).map((tag) => (
            <span
              key={tag}
              className="px-2 py-1 bg-secondary text-muted-foreground text-xs font-medium capitalize border border-border shadow-sm" style={{borderRadius: '50px'}}
            >
              {tag}
            </span>
          ))}
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-1 flex-shrink-0">
        {/* FIXED FLAW #120: REAL FUNCTIONAL BUTTON - DESTROYED MOCKUP EMERGENCY BUTTON */}
        <Button
          onClick={onStart}
          className="w-full bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-2 h-auto font-medium text-sm transition-all duration-300 hover:shadow-lg shadow-sm" style={{borderRadius: '16px'}}
        >
          <Play className="h-4 w-4 mr-2" strokeWidth={2.5} />
          Start Exercise
        </Button>
      </CardFooter>
    </Card>
  );
};

// EMERGENCY: Temporarily remove React.memo to isolate infinite loop
export default ToolExerciseCard;
