import { cn } from "@/lib/utils"

function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("animate-pulse bg-secondary/80 relative overflow-hidden", className)}
      style={{borderRadius: '16px'}}
      {...props}
    >
      <div className="absolute inset-0 -translate-x-full animate-shimmer bg-white/10" />
    </div>
  )
}

export { Skeleton }
