import { Toaster } from "sonner";

export function ToastProvider() {
  return (
    <Toaster
      position="top-center"
      toastOptions={{
        style: {
          background: "hsl(var(--background))",
          color: "hsl(var(--foreground))",
          border: "1px solid hsl(var(--border)/50)",
          borderRadius: "var(--radius)"
        },
        classNames: {
          toast: "group-[.toaster]:rounded-[var(--radius)] group-[.toaster]:p-4 group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border group-[.toaster]:border-border/60 group-[.toaster]:shadow-md group-[.toaster]:relative group-[.toaster]:overflow-hidden",
          success: "bg-primary text-primary-foreground border border-primary",
          error: "bg-destructive text-destructive-foreground border border-destructive/60",
          warning: "bg-warning text-warning-foreground border border-warning/60",
          info: "bg-info text-info-foreground border border-info/60",
          description: "group-[.toast]:text-muted-foreground group-[.toast]:opacity-70",
          actionButton: "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground group-[.toast]:hover:bg-primary-hover hover:scale-[1.01]",
          cancelButton: "group-[.toast]:bg-secondary group-[.toast]:text-secondary-foreground group-[.toast]:hover:bg-secondary-hover group-[.toast]:border group-[.toast]:border-secondary/90 hover:scale-[1.01]",
        },
      }}
      closeButton
      richColors
      expand={true}
      duration={4000}
      visibleToasts={3}
      pauseWhenPageIsHidden={true}
    />
  );
}
