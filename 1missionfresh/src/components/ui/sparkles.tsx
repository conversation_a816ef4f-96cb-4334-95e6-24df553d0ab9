"use client";
import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";

interface SparklesProps {
  id?: string;
  className?: string;
  background?: string;
  particleColor?: string;
  minSize?: number;
  maxSize?: number;
  particleDensity?: number;
  speed?: number;
}

export const SparklesCore: React.FC<SparklesProps> = ({
  id = "tsparticles",
  className,
  background = "transparent",
  minSize = 0.4,
  maxSize = 1,
  particleDensity = 1200,
  particleColor = "#FFFFFF",
}) => {
  const [sparkles, setSparkles] = useState<any[]>([]);
  const [mousePos, setMousePos] = useState({ x: -1, y: -1 });

  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      setMousePos({ x: event.clientX, y: event.clientY });
    };

    window.addEventListener("mousemove", handleMouseMove);

    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
    };
  }, []);

  useEffect(() => {
    const canvas = document.getElementById(id) as HTMLCanvasElement;
    if (!canvas) return;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    resizeCanvas();
    window.addEventListener("resize", resizeCanvas);

    const initSparkles = () => {
      const newSparkles = [];
      for (let i = 0; i < particleDensity; i++) {
        newSparkles.push(createSparkle(canvas));
      }
      setSparkles(newSparkles);
    };
    initSparkles();

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      sparkles.forEach((sparkle) => {
        sparkle.x += sparkle.vx;
        sparkle.y += sparkle.vy;

        if (sparkle.x < 0 || sparkle.x > canvas.width) sparkle.vx *= -1;
        if (sparkle.y < 0 || sparkle.y > canvas.height) sparkle.vy *= -1;

        drawSparkle(ctx, sparkle);
      });
      requestAnimationFrame(animate);
    };
    animate();

    return () => {
      window.removeEventListener("resize", resizeCanvas);
    };
  }, [id, particleDensity, particleColor, minSize, maxSize]);

  const createSparkle = (canvas: HTMLCanvasElement) => {
    return {
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      vx: Math.random() * 0.4 - 0.2,
      vy: Math.random() * 0.4 - 0.2,
      radius: Math.random() * (maxSize - minSize) + minSize,
      alpha: Math.random() * 0.5 + 0.5,
    };
  };

  const drawSparkle = (ctx: CanvasRenderingContext2D, sparkle: any) => {
    ctx.beginPath();
    ctx.arc(sparkle.x, sparkle.y, sparkle.radius, 0, 2 * Math.PI, false);
    ctx.fillStyle = particleColor;
    ctx.globalAlpha = sparkle.alpha;
    ctx.fill();
  };

  return (
    <div className={cn("relative w-full h-full", className)}>
      <canvas
        id={id}
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          pointerEvents: "none",
          background: background,
        }}
      />
    </div>
  );
};
