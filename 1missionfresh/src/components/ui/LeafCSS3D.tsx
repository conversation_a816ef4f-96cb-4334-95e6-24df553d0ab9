import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface LeafCSS3DProps {
  className?: string;
  color?: string;
  size?: number;
}

// Create a sophisticated 3D leaf using CSS and SVG
const LeafCSS3D: React.FC<LeafCSS3DProps> = ({ 
  className = '', 
  color = 'currentColor',
  size = 24 
}) => {
  return (
    <div 
      className={cn("relative", className)}
      style={{ 
        width: size, 
        height: size,
        perspective: '200px',
        transformStyle: 'preserve-3d'
      }}
    >
      {/* Main leaf body with natural 3D breeze animation */}
      <motion.div
        className="absolute inset-0"
        animate={{
          rotateZ: [0, 1.5, -1, 1.5, 0],    // Very gentle sway
          rotateY: [0, 2, -1.5, 2, 0],      // Subtle 3D turning
          rotateX: [0, 0.5, -0.3, 0.5, 0],  // Barely noticeable tilt
        }}
        transition={{
          duration: 12,  // Slow, peaceful timing
          ease: "easeInOut",
          repeat: Infinity,
          repeatType: "reverse",
        }}
        style={{
          transformOrigin: "50% 90%",  // Anchor at leaf stem
          transformStyle: "preserve-3d",
        }}
      >
        {/* No shadow - clean outline only like React icon */}

        {/* Main leaf - clean and stable */}
        <motion.div
          className="relative"
          animate={{
            scale: [1, 1.01, 1],  // Very subtle breathing to keep root stable
          }}
          transition={{
            duration: 6,
            ease: "easeInOut",
            repeat: Infinity,
            repeatType: "reverse",
          }}
        >
          <svg
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            {/* No gradients needed - clean Lucide style */}
            
            {/* Main leaf shape - skeletonized like React icon */}
            <path
              d="M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z"
              fill="none"
              stroke={color}
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            
            {/* Leaf vein - exact Lucide styling */}
            <motion.path
              d="M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12"
              stroke={color}
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              fill="none"
              animate={{
                strokeOpacity: [1, 0.8, 1, 0.9, 1],
              }}
              transition={{
                duration: 8,
                ease: "easeInOut",
                repeat: Infinity,
                repeatType: "reverse",
              }}
            />
          </svg>
        </motion.div>

        {/* Clean Lucide style - no particles needed */}
      </motion.div>
    </div>
  );
};

export default LeafCSS3D;
