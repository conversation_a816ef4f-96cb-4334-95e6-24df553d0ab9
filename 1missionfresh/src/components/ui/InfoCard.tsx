import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface InfoCardProps {
  icon: React.ElementType;
  title: string;
  description: string;
  href?: string;
  link?: string;
  variant?: 'subtle' | 'solid';
  index: number;
  className?: string;
  iconClassName?: string;
}

export function InfoCard({ icon: Icon, title, description, href, link, variant = 'subtle', index, className, iconClassName }: InfoCardProps) {
  const linkTo = href || link;

  const cardContent = (
    <Card 
      className={cn(
        'flex flex-col w-full h-full bg-card border border-border group hover:border-primary/30 hover:shadow-md transition-all duration-200',
        linkTo && 'cursor-pointer',
        className
      )}
    >
      <CardContent className="flex flex-col items-center text-center h-full p-6 space-y-4">
        <div className="flex-shrink-0">
          <div 
            className="flex items-center justify-center transition-all duration-200"
            style={{
              width: '48px',
              height: '48px',
              borderRadius: '16px',
              backgroundColor: 'hsl(var(--primary))',
              border: 'none'
            }}
          >
            <Icon
              className="w-5 h-5 text-primary-foreground"
              strokeWidth={2}
            />
          </div>
        </div>
        
        <div className="flex-1 flex flex-col justify-between space-y-3">
          <h3 className="text-lg font-semibold text-foreground leading-tight group-hover:text-primary transition-colors duration-200">
            {title}
          </h3>
          <p className="text-muted-foreground leading-relaxed text-base flex-1">
            {description}
          </p>
        </div>
      </CardContent>
    </Card>
  );

  const motionWrapper = (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="h-full"
    >
      {linkTo ? (
        <Link to={linkTo} className="group flex focus:outline-none focus-visible:outline-none w-full">
          {cardContent}
        </Link>
      ) : (
        <div className="flex w-full">{cardContent}</div>
      )}
    </motion.div>
  );

  return motionWrapper;
}
