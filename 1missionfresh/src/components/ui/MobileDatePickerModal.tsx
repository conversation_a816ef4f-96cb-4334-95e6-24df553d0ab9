import React, { useState, useEffect } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';

interface MobileDatePickerModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate: Date | undefined;
  onSelectDate: (date: Date | undefined) => void;
  title?: string;
}

export const MobileDatePickerModal: React.FC<MobileDatePickerModalProps> = ({
  isOpen,
  onClose,
  selectedDate,
  onSelectDate,
  title = 'Select Date',
}) => {
  const [date, setDate] = useState<Date | undefined>(selectedDate);

  // Update local state if selectedDate prop changes
  useEffect(() => {
    setDate(selectedDate);
  }, [selectedDate]);

  const handleConfirm = () => {
    onSelectDate(date);
    onClose();
  };

  const handleCancel = () => {
    // Reset local state to the original selected date on cancel
    setDate(selectedDate);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleCancel()}>
      <DialogContent className="sm:max-w-lg w-full h-full flex flex-col">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <div className="flex-grow overflow-y-auto p-4 flex justify-center items-start">
           <Calendar
            mode="single"
            selected={date}
            onSelect={setDate}
            initialFocus
          />
        </div>

        <DialogFooter className="flex flex-col sm:flex-row sm:justify-end gap-2 p-4">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleConfirm} disabled={!date}>
            Confirm
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
