import * as React from "react"

import { cn } from "@/lib/utils"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-secondary disabled:border-border disabled:text-muted-foreground font-refined tracking-elegant shadow-sm focus-visible:shadow-lg hover:border-border hover:shadow-sm hover:-translate-y-0.5 focus-visible:-translate-y-0.5 interactive-elegant transition-all duration-300 hover:scale-[1.002] focus-visible:scale-[1.005] active:scale-[0.998]",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }
