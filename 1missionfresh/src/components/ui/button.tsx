import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground hover:bg-primary-hover active:bg-primary-active" + " " + "rounded-[1.5rem]",
        primary:
          "bg-primary text-primary-foreground hover:bg-primary-hover active:bg-primary-active" + " " + "rounded-[1.5rem]",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive-hover active:bg-destructive-active" + " " + "rounded-[1.5rem]",
        outline:
          "border border-border bg-background hover:bg-muted hover:text-primary hover:border-primary" + " " + "rounded-[1.5rem]",
        secondary:
          "bg-muted text-muted-foreground border border-border hover:bg-primary/5 hover:text-primary hover:border-primary" + " " + "rounded-[1.5rem]",
        ghost: 
          "text-primary hover:bg-muted hover:text-primary-hover" + " " + "rounded-[1.5rem]",
        link: 
          "text-primary hover:text-primary-hover" + " " + "rounded-[1.5rem]",
      },
      size: {
        default: "h-10 px-4",
        sm: "h-9 px-3",
        lg: "h-11 px-6",
        xl: "h-12 px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
