import React from 'react';
import { <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>lt<PERSON>, Bar, ResponsiveContainer } from 'recharts';

// Define a basic type for the data objects
type ChartDataItem = {
  [key: string]: any; // Allow any value for now to simplify type checking
};

interface BarChartProps {
  data: Array<ChartDataItem>; // Use the basic type
  index: string; // The data key to use for the XAxis
  categories: string[]; // The data keys for the bars
  colors?: string[];
  valueFormatter?: (value: number) => string;
  height?: number | string;
}

export function BarChart({
  data,
  index,
  categories,
  colors = ['hsl(var(--primary))'],
  valueFormatter = (value) => `${value}`,
  height = 300
}: BarChartProps) {
  return (
    <ResponsiveContainer width="100%" height={height}>
      <RechartsBarChart data={data} margin={{ top: 10, right: 10, left: 0, bottom: 20 }}>
        <XAxis
          dataKey={index}
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: 'hsl(var(--border))/50' }}
          axisLine={{ stroke: 'hsl(var(--border))/50' }}
        />
        <YAxis
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: 'hsl(var(--border))/50' }}
          axisLine={{ stroke: 'hsl(var(--border))/50' }}
          tickFormatter={valueFormatter}
        />
        <Tooltip
          formatter={(value: number) => [valueFormatter(value), '']}
          labelFormatter={(label) => `${label}`}
          contentStyle={{
            backgroundColor: 'hsl(var(--background))/85',
            border: '1px solid hsl(var(--border))/30',
            borderRadius: 'var(--radius)',
            padding: '0.75rem',
            boxShadow: 'var(--shadow-sm)' as any,
            backdropFilter: 'none',
          }}
        />
        {categories.map((category, index) => (
          <Bar
            key={category}
            dataKey={category}
            fill={colors[index % colors.length]}
            radius={[4, 4, 0, 0]}
            className="transition-all duration-300 ease-out hover:opacity-80"
          />
        ))}
      </RechartsBarChart>
    </ResponsiveContainer>
  );
}
