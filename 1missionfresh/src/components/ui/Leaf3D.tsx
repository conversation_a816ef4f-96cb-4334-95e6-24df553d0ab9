import React, { useRef, useMemo, Suspense } from 'react';
import { Leaf } from 'lucide-react';

// Lazy load Three.js components to avoid breaking if not installed
let Canvas: any;
let useFrame: any;
let Shape: any;
let ExtrudeGeometry: any;
let THREE: any;

try {
  const fiber = require('@react-three/fiber');
  const three = require('three');
  Canvas = fiber.Canvas;
  useFrame = fiber.useFrame;
  Shape = three.Shape;
  ExtrudeGeometry = three.ExtrudeGeometry;
  THREE = three;
} catch (error) {
  console.log('Three.js not yet available, using fallback');
}

// Create the exact shape of the Lucide React leaf icon as a 3D geometry
const createLeafShape = () => {
  const shape = new Shape();
  
  // Start from the stem (bottom center)
  shape.moveTo(0, -1);
  
  // Create the leaf outline that matches <PERSON><PERSON>'s leaf icon
  // Left side of the leaf
  shape.bezierCurveTo(-0.3, -0.8, -0.8, -0.4, -1, 0.2);
  shape.bezierCurveTo(-0.9, 0.6, -0.6, 0.9, -0.2, 1);
  
  // Top of the leaf (pointed tip)
  shape.bezierCurveTo(-0.1, 1.1, 0.1, 1.1, 0.2, 1);
  
  // Right side of the leaf
  shape.bezierCurveTo(0.6, 0.9, 0.9, 0.6, 1, 0.2);
  shape.bezierCurveTo(0.8, -0.4, 0.3, -0.8, 0, -1);
  
  return shape;
};

// Create leaf vein lines as a separate geometry
const createLeafVeins = () => {
  const veins = new THREE.BufferGeometry();
  const points = [];
  
  // Main central vein
  for (let i = 0; i <= 20; i++) {
    const t = i / 20;
    const y = -1 + t * 2;
    points.push(0, y, 0.01);
  }
  
  // Side veins - left
  for (let i = 0; i <= 10; i++) {
    const t = i / 10;
    const startY = -0.5 + t * 1.2;
    const endX = -0.3 - t * 0.4;
    const endY = startY + 0.2;
    
    points.push(0, startY, 0.01);
    points.push(endX, endY, 0.01);
  }
  
  // Side veins - right
  for (let i = 0; i <= 10; i++) {
    const t = i / 10;
    const startY = -0.5 + t * 1.2;
    const endX = 0.3 + t * 0.4;
    const endY = startY + 0.2;
    
    points.push(0, startY, 0.01);
    points.push(endX, endY, 0.01);
  }
  
  veins.setAttribute('position', new THREE.Float32BufferAttribute(points, 3));
  return veins;
};

interface Leaf3DModelProps {
  color?: string;
  scale?: number;
}

const Leaf3DModel: React.FC<Leaf3DModelProps> = ({ color = 'hsl(160, 84.2%, 39.4%)', scale = 1 }) => {
  const leafRef = useRef<THREE.Mesh>(null);
  const veinsRef = useRef<THREE.LineSegments>(null);
  
  // Create the 3D leaf geometry
  const leafGeometry = useMemo(() => {
    const shape = createLeafShape();
    const extrudeSettings = {
      depth: 0.05,
      bevelEnabled: true,
      bevelSegments: 2,
      steps: 2,
      bevelSize: 0.02,
      bevelThickness: 0.02,
    };
    return new ExtrudeGeometry(shape, extrudeSettings);
  }, []);
  
  const veinsGeometry = useMemo(() => createLeafVeins(), []);
  
  // Sophisticated natural animation using multiple wave functions
  useFrame((state) => {
    if (leafRef.current && veinsRef.current) {
      const time = state.clock.elapsedTime;
      
      // Create complex, natural motion using multiple sine waves
      const primarySway = Math.sin(time * 0.8) * 0.15;
      const secondarySway = Math.sin(time * 1.3 + 1.2) * 0.08;
      const tertiarySway = Math.sin(time * 2.1 + 2.5) * 0.03;
      
      const totalRotationZ = primarySway + secondarySway + tertiarySway;
      
      // Add subtle Y rotation for 3D effect
      const rotationY = Math.sin(time * 0.6 + 1) * 0.1 + Math.sin(time * 1.8) * 0.05;
      
      // Add very subtle X rotation for depth
      const rotationX = Math.sin(time * 0.4 + 3) * 0.05;
      
      // Apply rotations to both leaf and veins
      leafRef.current.rotation.z = totalRotationZ;
      leafRef.current.rotation.y = rotationY;
      leafRef.current.rotation.x = rotationX;
      
      veinsRef.current.rotation.z = totalRotationZ;
      veinsRef.current.rotation.y = rotationY;
      veinsRef.current.rotation.x = rotationX;
      
      // Add subtle breathing effect to the scale
      const breathe = 1 + Math.sin(time * 0.5) * 0.02;
      leafRef.current.scale.setScalar(scale * breathe);
      veinsRef.current.scale.setScalar(scale * breathe);
    }
  });
  
  return (
    <group>
      {/* Main leaf body */}
      <mesh ref={leafRef} geometry={leafGeometry} position={[0, 0, 0]}>
        <meshPhongMaterial 
          color={color}
          shininess={30}
          transparent
          opacity={0.9}
        />
      </mesh>
      
      {/* Leaf veins */}
      <lineSegments ref={veinsRef} geometry={veinsGeometry}>
        <lineBasicMaterial color={color} opacity={0.6} transparent />
      </lineSegments>
    </group>
  );
};

interface Leaf3DProps {
  className?: string;
  color?: string;
  size?: number;
}

const Leaf3D: React.FC<Leaf3DProps> = ({ 
  className = '', 
  color = 'hsl(160, 84.2%, 39.4%)',
  size = 24 
}) => {
  // Fallback to regular Lucide leaf if Three.js is not available
  if (!Canvas || !useFrame || !THREE) {
    return (
      <Leaf 
        className={className}
        style={{ 
          width: size, 
          height: size,
          color: color,
          strokeWidth: 'var(--icon-stroke-thick)'
        }} 
      />
    );
  }
  
  return (
    <div 
      className={className}
      style={{ 
        width: size, 
        height: size,
        display: 'inline-block'
      }}
    >
      <Suspense fallback={
        <Leaf 
          style={{ 
            width: size, 
            height: size,
            color: color,
            strokeWidth: 'var(--icon-stroke-thick)'
          }} 
        />
      }>
        <Canvas
          camera={{ 
            position: [0, 0, 3], 
            fov: 50 
          }}
          style={{ 
            width: '100%', 
            height: '100%' 
          }}
        >
          {/* Lighting setup for realistic 3D appearance */}
          <ambientLight intensity={0.4} />
          <directionalLight 
            position={[2, 2, 5]} 
            intensity={0.8}
            castShadow
          />
          <pointLight 
            position={[-2, -2, 2]} 
            intensity={0.3}
            color="#ffffff"
          />
          
          {/* The 3D leaf model */}
          <Leaf3DModel color={color} scale={0.8} />
        </Canvas>
      </Suspense>
    </div>
  );
};

export default Leaf3D;
