
import { useState, useEffect } from 'react';

// Augment the global Window interface to include Capacitor
declare global {
  interface Window {
    Capacitor?: {
      Plugins?: {
        Haptics?: { // Define more specific types if known
          impact: (options: { style: 'LIGHT' | 'MEDIUM' | 'HEAVY' }) => Promise<void>;
          notification: () => Promise<void>;
          vibrate: (options?: { duration?: number }) => Promise<void>;
        };
        Health?: { // Define more specific types for Health plugin
          queryAggregated: (options: { startDate: string; endDate: string; dataType: string }) => Promise<{ value: number }>;
          requestAuthorization: (options: { permissions: string[] }) => Promise<{ authorized: boolean }>;
          isAuthorized: (options: { permissions: string[] }) => Promise<{ authorized: boolean }>;
        };
        App?: object;     // Define more specific types if known
        [key: string]: object; // Allow other plugins
      };
      isNativePlatform?: () => boolean; // Example method, adjust if needed
      getPlatform?: () => string;      // Example method, adjust if needed
    };
  }
}


export function useIsMobile(breakpoint = 768) {
  const [isMobile, setIsMobile] = useState(false);
  const [isNativeApp, setIsNativeApp] = useState(false);

  useEffect(() => {
    // Check if we're running in a mobile browser (based on screen size)
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < breakpoint);
    };
    
    // Check if we're running as a native app
    const checkIfNative = () => {
      // Check for Capacitor using the augmented Window type
      const hasCapacitor = typeof window.Capacitor !== 'undefined';
      
      // Check for Capacitor plugins using the augmented Window type
      const hasCapacitorPlugins = !!window.Capacitor?.Plugins?.Haptics ||
        !!window.Capacitor?.Plugins?.App;
      
      // Check user agent for mobile platform indicators
      const userAgent = navigator.userAgent.toLowerCase();
      const hasMobilePlatformIndicators = 
        userAgent.includes('capacitor') ||
        userAgent.includes('android') || 
        userAgent.includes('ios');
      
      // We're in a native app if we have Capacitor and either plugins or mobile platform indicators
      setIsNativeApp(hasCapacitor && (hasCapacitorPlugins || hasMobilePlatformIndicators));
    };
    
    // Initial checks
    checkScreenSize();
    checkIfNative();
    
    // Add event listener for resizing
    window.addEventListener('resize', checkScreenSize);
    
    // Clean up
    return () => window.removeEventListener('resize', checkScreenSize);
  }, [breakpoint]);

  return { 
    isMobile, 
    isNativeApp, 
    isMobileOrNative: isMobile || isNativeApp 
  };
}
