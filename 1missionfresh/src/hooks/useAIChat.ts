import { useState, useCallback, useRef } from 'react';
import { getAICoachResponse } from '@/services/aiService';

export interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  suggestions?: string[];
}

export interface UseAIChatReturn {
  messages: Message[];
  inputValue: string;
  isTyping: boolean;
  showChat: boolean;
  handleSendMessage: (message: string) => Promise<void>;
  handleStartChat: (event?: React.MouseEvent) => Promise<void>;
  setInputValue: (value: string) => void;
}

export function useAIChat(): UseAIChatReturn {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showChat, setShowChat] = useState(false);
  
  // CRITICAL: Add execution flags to prevent infinite loops
  const isExecutingRef = useRef(false);
  const lastExecutionRef = useRef(0);

  const handleSendMessage = useCallback(async (message: string) => {
    if (!message.trim()) return;
    
    const userMessage: Message = {
      id: Date.now().toString(),
      content: message,
      sender: 'user',
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    try {
      // Use the working AI service with Gemini API integration
      const conversationHistory = messages.map(m => ({
        role: m.sender === 'user' ? 'user' : 'assistant',
        content: m.content
      }));

      const aiResponseText = await getAICoachResponse(message, conversationHistory);

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: aiResponseText || "I'm here to help with your quit smoking journey! How can I support you today?",
        sender: 'ai',
        timestamp: new Date(),
        suggestions: [
          "How do I handle cravings?",
          "What's the timeline for quitting?",
          "Which quit method is best for me?"
        ]
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error calling AI coach:', error);
      // Provide helpful fallback response
      const fallbackMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "I'm here to support your quit smoking journey! I can help with cravings, withdrawal symptoms, motivation, and more. What would you like to talk about?",
        sender: 'ai',
        timestamp: new Date(),
        suggestions: [
          "How do I handle cravings?",
          "What withdrawal symptoms should I expect?",
          "Tell me about quit methods"
        ]
      };
      setMessages(prev => [...prev, fallbackMessage]);
    } finally {
      setIsTyping(false);
    }
  }, []);

  const handleStartChat = useCallback(async (event?: React.MouseEvent) => {
    // ULTRA-CRITICAL: Bulletproof loop prevention
    const now = Date.now();
    if (isExecutingRef.current || (now - lastExecutionRef.current < 2000)) {
      console.log('🚨 LOOP PREVENTION: handleStartChat blocked - too frequent calls');
      return;
    }
    
    // Prevent default and stop propagation immediately
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    
    // Set execution flags IMMEDIATELY to prevent any possibility of re-entry
    isExecutingRef.current = true;
    lastExecutionRef.current = now;
    
    console.log('🎯 DEBUG: handleStartChat called safely - execution flags set');
    
    try {
      setShowChat(true);
      console.log('🎯 DEBUG: showChat set to true');
      
      if (messages.length === 0) {
        setIsTyping(true);
        console.log('🎯 DEBUG: Creating welcome message safely...');
        
        // Create welcome message with proper safeguards
        const fallbackMessage: Message = {
          id: 'welcome-' + Date.now(),
          content: "Hi there! I'm Fresh Assistant, your 24/7 wellness coach. I'm here to help you on your journey to a smoke-free life. How can I support you today?",
          sender: 'ai',
          timestamp: new Date(),
          suggestions: [
            "How do I handle cravings?",
            "What's the timeline for quitting?",
            "Which quit method is best for me?",
            "Tell me about withdrawal symptoms"
          ]
        };
        
        setTimeout(() => {
          setMessages([fallbackMessage]);
          setIsTyping(false);
          console.log('🎯 DEBUG: Welcome message created safely');
        }, 800);
      }
    } catch (error) {
      console.error('💥 Error in handleStartChat:', error);
      setIsTyping(false);
    } finally {
      // Reset execution flag after extended delay to prevent rapid re-execution
      setTimeout(() => {
        isExecutingRef.current = false;
        console.log('🎯 DEBUG: handleStartChat execution flag reset');
      }, 3000);
    }
  }, [setMessages, setIsTyping, setShowChat]);

  return {
    messages,
    inputValue,
    isTyping,
    showChat,
    handleSendMessage,
    handleStartChat,
    setInputValue
  };
}
