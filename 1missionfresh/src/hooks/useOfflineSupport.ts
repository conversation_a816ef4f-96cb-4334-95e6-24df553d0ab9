import { useEffect, useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import {
  saveNicotineLog,
  saveDailyCheckIn,
  saveCraving,
  saveJournalEntry,
  NicotineLogInsert,
  DailyCheckInInsert,
  CravingLogInsert,
  JournalEntryInsert,
} from '@/services/logService';

// Re-define offline types based on Insert types for simplicity and correctness
export type OfflineLogEntry = 
  | { id: string, type: 'nicotineDailyLog', data: NicotineLogInsert[] } // Note: saveNicotineLog expects an array
  | { id: string, type: 'dailyCheckIn', data: DailyCheckInInsert }
  | { id: string, type: 'craving', data: CravingLogInsert }
  | { id: string, type: 'journal', data: JournalEntryInsert };

const OFFLINE_LOGS_STORAGE_KEY = 'offlineLogs';

export const useOfflineSupport = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [offlineLogs, setOfflineLogs] = useState<OfflineLogEntry[]>([]);
  const [isProcessingOfflineLogs, setIsProcessingOfflineLogs] = useState(false);

  // Load offline logs from local storage on mount
  useEffect(() => {
    const storedLogs = localStorage.getItem(OFFLINE_LOGS_STORAGE_KEY);
    if (storedLogs) {
      try {
        setOfflineLogs(JSON.parse(storedLogs));
      } catch (error) {
        console.error("Failed to parse offline logs from storage:", error);
        localStorage.removeItem(OFFLINE_LOGS_STORAGE_KEY); // Clear invalid data
      }
    }
  }, []);

  // Save offline logs to local storage whenever they change
  useEffect(() => {
    localStorage.setItem(OFFLINE_LOGS_STORAGE_KEY, JSON.stringify(offlineLogs));
  }, [offlineLogs]);

  // Handle online/offline status changes
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const processOfflineLogs = useCallback(async () => {
    if (!user || offlineLogs.length === 0) return;

    setIsProcessingOfflineLogs(true);
    console.log(`Attempting to process ${offlineLogs.length} offline log entries.`);

    let currentOfflineLogs = [...offlineLogs]; // Create a mutable copy
    const failedLogs: OfflineLogEntry[] = [];
    let processedCount = 0;

    while (currentOfflineLogs.length > 0) {
        const log = currentOfflineLogs.shift(); // Process one log at a time
        if (!log) continue;

        let success = false;
        try {
            switch (log.type) {
                case 'nicotineDailyLog':
                    await saveNicotineLog(log.data);
                    success = true;
                    break;
                case 'dailyCheckIn':
                    await saveDailyCheckIn(log.data);
                    success = true;
                    break;
                case 'craving':
                    await saveCraving(log.data);
                    success = true;
                    break;
                case 'journal':
                    await saveJournalEntry(log.data);
                    success = true;
                    break;
                default:
                    console.warn(`Unknown offline log type: ${(log as any).type}`);
                    failedLogs.push(log); // Keep unknown types
                    break;
            }
        } catch (error) {
            console.error(`Failed to process offline log entry of type ${log.type}:`, error);
            failedLogs.push(log); // Add failed log back to retry later
        }

        if (success) {
            processedCount++;
        }
    }


    setOfflineLogs(failedLogs); // Update state with remaining failed logs

    if (processedCount > 0) {
      toast({
        title: "Synced",
        description: `${processedCount} offline entries synced successfully.`,
      });
    }

    if (failedLogs.length > 0 && processedCount > 0) { 
       toast({
        title: "Sync Partially Failed",
        description: `${failedLogs.length} offline entries failed to sync. They will be retried later.`,
        variant: "destructive",
      });
    } else if (failedLogs.length > 0 && processedCount === 0) {
        toast({
        title: "Sync Failed",
        description: `All ${failedLogs.length} offline entries failed to sync. They will be retried later.`,
        variant: "destructive",
      });
    }


    setIsProcessingOfflineLogs(false);
  }, [offlineLogs, user, toast]);

  // Process offline logs when online and user is authenticated
  useEffect(() => {
    if (isOnline && user && offlineLogs.length > 0 && !isProcessingOfflineLogs) {
      processOfflineLogs();
    }
  }, [isOnline, user, offlineLogs, isProcessingOfflineLogs, processOfflineLogs]);

  const addOfflineLog = useCallback((logEntry: Omit<OfflineLogEntry, 'id'>) => {
    const logWithId = { ...logEntry, id: `offline_${Date.now()}_${Math.random()}` } as OfflineLogEntry;
    setOfflineLogs(prevLogs => [...prevLogs, logWithId]);
    toast({
      title: "Offline",
      description: "Your entry has been saved offline and will sync when you are back online.",
    });
  }, [toast]);

  return {
    isOnline,
    offlineLogs,
    addOfflineLog,
    processOfflineLogs,
    isProcessingOfflineLogs,
  };
};
