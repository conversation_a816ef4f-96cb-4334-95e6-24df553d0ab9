
import { useCallback } from 'react';

// Define TypeScript interfaces for Capacitor Haptics plugin
interface HapticsImpactOptions {
  style: HapticImpact;
}

interface HapticsVibrateOptions {
  duration: number;
}

interface HapticsPlugin {
  impact(options: HapticsImpactOptions): Promise<void>;
  notification(): Promise<void>;
  vibrate(options: HapticsVibrateOptions): Promise<void>;
}

// Extend the global Window interface to include Capacitor plugins
declare global {
  interface Window {
    Capacitor?: {
      Plugins?: {
        Haptics?: HapticsPlugin;
      };
    };
  }
}

// Enum for haptic impact style, consistent with Capacitor
export enum HapticImpact {
  LIGHT = 'LIGHT',
  MEDIUM = 'MEDIUM',
  HEAVY = 'HEAVY',
}

/**
 * A hook for providing haptic feedback.
 * This version is safe to use in any environment, it will only
 * attempt to use haptics if the Capacitor plugin is available.
 */
export function useHaptics() {
  const isHapticsAvailable = typeof window !== 'undefined' && !!window.Capacitor?.Plugins?.Haptics;

  const impact = useCallback(
    async (style: HapticImpact = HapticImpact.MEDIUM) => {
      if (isHapticsAvailable) {
        try {
          await window.Capacitor.Plugins.Haptics.impact({ style });
        } catch (error) {
          console.error('Haptics impact failed:', error);
        }
      }
    },
    [isHapticsAvailable]
  );

  const notification = useCallback(async () => {
    if (isHapticsAvailable) {
      try {
        await window.Capacitor.Plugins.Haptics.notification();
      } catch (error) {
        console.error('Haptics notification failed:', error);
      }
    }
  }, [isHapticsAvailable]);

  const vibrate = useCallback(
    async (duration: number = 300) => {
      if (isHapticsAvailable) {
        try {
          await window.Capacitor.Plugins.Haptics.vibrate({ duration });
        } catch (error) {
          console.error('Haptics vibrate failed:', error);
        }
      }
    },
    [isHapticsAvailable]
  );

  return {
    impact,
    notification,
    vibrate,
  };
}

