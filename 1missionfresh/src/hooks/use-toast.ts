
// This is a wrapper around sonner to maintain backward compatibility
// with any code that might be using the older useToast hook
import { toast as sonnerToast, type ExternalToast } from "sonner"; // Import ExternalToast type

type ToastProps = {
  title?: string;
  description?: string;
  action?: React.ReactNode;
  variant?: "default" | "destructive" | "success" | "warning" | "info" | "error"; // Add error type to variant
};

// Simple toast function that maps our parameters to sonner
function toast({ title, description, action, variant }: ToastProps) {
  // Map variant to the appropriate sonner function
  if (variant === "destructive" || variant === "error") {
    return sonnerToast.error(title || "", {
      description,
      action,
    });
  } else if (variant === "success") {
    return sonnerToast.success(title || "", {
      description,
      action,
    });
  } else if (variant === "warning") {
    return sonnerToast.warning(title || "", {
      description,
      action,
    });
  } else if (variant === "info") {
    return sonnerToast.info(title || "", {
      description,
      action,
    });
  } else {
    // Default toast
    return sonnerToast(title || "", {
      description,
      action,
    });
  }
}

// Mock the old useToast hook API with sonner's functions
function useToast() {
  return {
    toast,
    dismiss: sonnerToast.dismiss,
    // Use ExternalToast for the options type
    error: (title: string, options?: ExternalToast) => sonnerToast.error(title, options),
    success: (title: string, options?: ExternalToast) => sonnerToast.success(title, options),
    info: (title: string, options?: ExternalToast) => sonnerToast.info(title, options),
    warning: (title: string, options?: ExternalToast) => sonnerToast.warning(title, options),
  };
}

// Export both the toast function and the useToast hook
export { useToast, toast };
