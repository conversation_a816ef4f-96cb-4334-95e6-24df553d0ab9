import React, { createContext, useContext, useState, ReactNode } from 'react';

// Type definition for our sidebar context
type SidebarContextType = {
  isOpen: boolean;
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
};

// Default context values
const defaultContext: SidebarContextType = {
  isOpen: false,
  toggleSidebar: () => {},
  setSidebarOpen: () => {}
};

// Create the context
const SidebarContext = createContext<SidebarContextType>(defaultContext);

// Provider component that wraps app and makes sidebar context available
export const SidebarProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const toggleSidebar = () => setIsOpen(prev => !prev);
  const setSidebarOpen = (open: boolean) => setIsOpen(open);

  return (
    <SidebarContext.Provider value={{ isOpen, toggleSidebar, setSidebarOpen }}>
      {children}
    </SidebarContext.Provider>
  );
};

// Custom hook for accessing the sidebar context
export const useSidebarToggle = () => useContext(SidebarContext);
