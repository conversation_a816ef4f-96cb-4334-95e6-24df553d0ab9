import "dotenv/config";
// This is a test script to be run with ts-node
import { saveUserGoal } from './services/goalService.ts';
import { supabase } from './lib/supabase.ts';

const runTest = async () => {
  console.log('Starting goal creation test...');

  // Manually create a user object for testing purposes
  // In a real scenario, this would come from the auth context
  const testUser = {
    id: '068215cb-d0d2-48c7-91c4-a311d76489f9', // This needs to be a valid user ID from your db
  };

  if (!testUser.id) {
    console.error('Test user ID is not set. Please add a valid user UUID.');
    return;
  }

  const newGoal = {
    user_id: testUser.id,
    goal_type: 'afresh' as const,
    method: 'Cold Turkey' as const,
    quit_date: new Date().toISOString(),
    motivation: 'Test goal created directly via script.',
    product_type: 'Cigarettes',
    method_details: {},
    daily_step_goal: 5000,
  };

  try {
    console.log('Attempting to save goal:', newGoal);
    const savedGoal = await saveUserGoal(newGoal, testUser.id);
    console.log('Successfully saved goal:', savedGoal);

    // Verify directly from Supabase
    console.log('Verifying creation with Supabase...');
    const { data, error } = await supabase
      .from('user_goals')
      .select('*')
      .eq('id', savedGoal.id)
      .single();

    if (error) {
      throw new Error(`Supabase verification failed: ${error.message}`);
    }

    if (data) {
      console.log('SUCCESS: Goal found in database:', data);
    } else {
      throw new Error('Verification failed. Goal not found in database after creation.');
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
};

runTest(); 