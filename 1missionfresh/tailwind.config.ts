import type { Config } from "tailwindcss";
import plugin from "tailwindcss/plugin";
import tailwindcssAnimate from "tailwindcss-animate";

export default {
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "1.5rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
          hover: 'hsl(var(--primary-hover))',
          active: "hsl(var(--primary-active))",
          subtle: "hsl(var(--primary-subtle))",
          muted: "hsl(var(--primary-muted))",
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
          hover: "hsl(var(--secondary-hover))",
          active: "hsl(var(--secondary-active))",
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
          hover: "hsl(var(--destructive-hover))",
          active: "hsl(var(--destructive-active))",
        },
        success: {
          DEFAULT: 'hsl(var(--success))',
          foreground: 'hsl(var(--success-foreground))',
        },
        warning: {
          DEFAULT: 'hsl(var(--warning))',
          foreground: 'hsl(var(--warning-foreground))',
        },
        info: {
          DEFAULT: 'hsl(var(--info))',
          foreground: 'hsl(var(--info-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
          subtle: "hsl(var(--muted-subtle))",
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
          hover: "hsl(var(--accent-hover))",
          active: "hsl(var(--accent-active))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))',
        },
        green: {
          50: "hsl(var(--primary-subtle))",
          100: "hsl(var(--primary-muted))",
          200: "hsl(var(--primary-muted))",
          300: "hsl(var(--primary-muted))",
          400: "hsl(var(--primary-hover))",
          500: "hsl(var(--primary))",
          600: "hsl(var(--primary-hover))",
          700: "hsl(var(--primary-active))",
          800: "hsl(var(--primary-active))",
          900: "hsl(var(--primary-active))",
        },
        gray: {
          50: "hsl(var(--secondary))",
          100: "hsl(var(--secondary-hover))",
          200: "hsl(var(--secondary-active))",
          300: "hsl(var(--muted))",
          400: "hsl(var(--muted-foreground))",
          500: "hsl(var(--foreground)/0.65)",
          600: "hsl(var(--foreground)/0.8)",
          700: "hsl(var(--foreground))",
          800: "hsl(var(--foreground))",
          900: "hsl(var(--foreground))",
        },
      },
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', "Segoe UI", "Roboto", "Helvetica Neue", "Arial", "Noto Sans", "sans-serif", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"],
        heading: ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif'],
        refined: ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif'],
      },
      // Spacing uses standard Tailwind classes for clean, consistent design
      // No custom spacing needed - Tailwind's default spacing scale is perfect
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 0.125rem)",
        sm: "calc(var(--radius) - 0.25rem)",
        xl: "calc(var(--radius) + 0.125rem)",
        '2xl': "calc(var(--radius) + 0.25rem)",
        '3xl': "calc(var(--radius) + 0.5rem)",
      },
      boxShadow: {
        'elegant': '0 4px 12px -2px hsl(160 20% 55% / 0.1), 0 4px 8px -4px hsl(160 20% 55% / 0.08)',
      },
      textShadow: {
        'sm': '0 1px 2px var(--current-solid-shadow-color-subtle)',
        'DEFAULT': '0 2px 4px var(--current-solid-shadow-color-subtle)',
        'lg': '0 4px 8px var(--current-solid-shadow-color-subtle)',
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "fade-in": {
          "0%": { opacity: "0", transform: "translateY(4px) scale(0.99)" },
          "100%": { opacity: "1", transform: "translateY(0) scale(1)" },
        },
        "fade-in-up": {
          "0%": { opacity: "0", transform: "translateY(12px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        "scale-in": {
          "0%": { opacity: "0", transform: "scale(0.96)" },
          "100%": { opacity: "1", transform: "scale(1)" },
        },
        "slide-in-right": {
          "0%": { transform: "translateX(100%)", opacity: "0" },
          "100%": { transform: "translateX(0)", opacity: "1" },
        },
        "slide-in-left": {
          "0%": { transform: "translateX(-100%)", opacity: "0" },
          "100%": { transform: "translateX(0)", opacity: "1" },
        },
        "gentle-float": {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(-6px)" },
        },
        "pulse-gentle": {
          "0%, 100%": { transform: "scale(1)" },
          "50%": { transform: "scale(1.03)" },
        },
        "shimmer": {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(100%)" },
        },
        "breathe": {
          "0%, 100%": { transform: "scale(1)" },
          "50%": { transform: "scale(1.05)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.25s ease-out",
        "accordion-up": "accordion-up 0.25s ease-out",
        "fade-in": "fade-in 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
        "fade-in-up": "fade-in-up 0.5s cubic-bezier(0.22, 1, 0.36, 1)",
        "scale-in": "scale-in 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
        "slide-in": "slide-in-right 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
        "slide-in-left": "slide-in-left 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
        "gentle-float": "gentle-float 8s ease-in-out infinite",
        "pulse-gentle": "pulse-gentle 6s ease-in-out infinite",
        "shimmer": "shimmer 2s ease-in-out infinite",
        "breathe": "breathe 4s ease-in-out infinite",
      },
      transitionTimingFunction: {
        'elegant': 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'smooth': 'cubic-bezier(0.22, 1, 0.36, 1)',
      },
      letterSpacing: {
        'elegant': '-0.01em',
        'refined': '-0.005em',
      },
    },
  },
  plugins: [
    tailwindcssAnimate,
    plugin(function({ addUtilities, theme }) {
      const newUtilities = {
        '.tracking-elegant': {
          letterSpacing: '-0.01em',
        },
        '.tracking-refined': {
          letterSpacing: '-0.005em',
        },
        '.ease-elegant': {
          transitionTimingFunction: theme('transitionTimingFunction.elegant'),
        },
        '.ease-smooth': {
          transitionTimingFunction: theme('transitionTimingFunction.smooth'),
        },
      }
      addUtilities(newUtilities)
    }),
  ],
} satisfies Config;
