const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  await page.setViewport({ width: 1200, height: 800 });
  
  // Login first
  await page.goto('http://localhost:5001/auth', { waitUntil: 'domcontentloaded', timeout: 60000 });
  
  const dashboardLink = await page.$('a[href="/app/dashboard"]');
  if (!dashboardLink) {
    await page.waitForSelector('input[id="email"]', { timeout: 10000 });
    await page.type('input[id="email"]', '<EMAIL>');
    await page.type('input[id="password"]', 'J4913836j');
    await page.click('button[type="submit"]');
    await page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 60000 });
  }
  
  // Capture key pages
  const pages = [
    { url: 'http://localhost:5001/app/goals', name: 'goals_current' },
    { url: 'http://localhost:5001/app/tools/breathing', name: 'breathing_tools_current' },
    { url: 'http://localhost:5001/app/progress', name: 'progress_current' },
    { url: 'http://localhost:5001/app/community', name: 'community_current' },
    { url: 'http://localhost:5001/app/settings', name: 'settings_current' }
  ];
  
  for (const pageInfo of pages) {
    console.log(`Capturing ${pageInfo.name}...`);
    await page.goto(pageInfo.url, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await page.screenshot({ path: `${pageInfo.name}.png` });
  }
  
  console.log('All screenshots captured');
  await browser.close();
})();
