# Quick Installation Guide

## ✅ Extension is Ready!

Your Cursor Auto-Continue extension is now compiled and ready to install for personal use.

## Installation Options

### Option 1: Development Mode (Recommended for testing)

1. **Open VS Code/Cursor**
2. **Open this extension folder**: 
   - File → Open Folder → Select `vscode-extension-auto-continue`
3. **Press F5** - This will launch a new "Extension Development Host" window
4. **The extension is now active** in that new window

### Option 2: Install as VSIX Package

1. **Install VSCE** (VS Code Extension packager):
   ```bash
   npm install -g vsce
   ```

2. **Package the extension**:
   ```bash
   vsce package
   ```

3. **Install the generated .vsix file**:
   ```bash
   code --install-extension cursor-auto-continue-0.0.1.vsix
   ```

### Option 3: Manual Installation (Symlink)

1. **Find your VS Code extensions directory**:
   - **macOS**: `~/.vscode/extensions/`
   - **Windows**: `%USERPROFILE%\.vscode\extensions\`
   - **Linux**: `~/.vscode/extensions/`

2. **Create a symlink**:
   ```bash
   ln -s /path/to/vscode-extension-auto-continue ~/.vscode/extensions/cursor-auto-continue
   ```

3. **Restart VS Code/Cursor**

## ⚙️ Settings & Configuration

### Access Settings:
1. **Open Settings**: `Ctrl/Cmd + ,` (comma)
2. **Search**: "cursor auto continue"
3. **Configure all options** in the UI

### Available Settings:

| Setting | Default | Description |
|---------|---------|-------------|
| **Enabled** | `true` | ✅ **Auto-starts when Cursor opens!** |
| **Polling Interval** | `500ms` | How often to check for triggers |
| **Show Notifications** | `false` | Show popup when triggers activate |
| **Custom Triggers** | `{}` | Add your own trigger → response pairs |
| **Enabled Triggers** | All `true` | Enable/disable specific categories |

### Quick Toggle Commands:
- **Command Palette**: `Ctrl/Cmd + Shift + P`
- **"Cursor Auto Continue: Toggle"** - Enable/disable instantly
- **"Cursor Auto Continue: Continue"** - Manual trigger

### Custom Triggers Example:
```json
{
  "Let me pause here": "Yes, continue please",
  "Should we proceed": "Absolutely, proceed",
  "Your custom phrase": "Your custom response"
}
```

## How to Test

1. **Extension auto-starts** when you open Cursor! 🚀
2. **Type any trigger phrase**:
   - "I've reached my tool call limit of 25"
   - "Would you like me to continue"
   - "I need to pause execution"
3. **Watch it automatically add responses**
4. **Check notification** (if enabled in settings)

## Triggers Supported

### Tool Call Limits:
- ✅ "I've reached my tool call limit of 25" → "continue"
- ✅ "I've hit the limit" → "continue"

### Pause Scenarios:
- ✅ "I need to pause execution" → "continue"
- ✅ "I'll need to pause" → "continue"

### Continue Questions:
- ✅ "Would you like me to continue" → "Yes, please continue"
- ✅ "Should I continue with" → "Yes, continue"
- ✅ "Shall I continue" → "Yes, continue"

### Length Constraints:
- ✅ "Due to length constraints" → "continue"

### Plus Your Custom Triggers! 🎯

---

🎉 **You're all set!** The extension will now automatically help you continue AI conversations without manual intervention.