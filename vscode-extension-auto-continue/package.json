{"name": "cursor-auto-continue", "displayName": "Cursor Auto Continue", "description": "Automatically continues AI conversations when they hit tool call limits or pause execution", "version": "0.0.1", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./extension.js", "contributes": {"commands": [{"command": "cursor-auto-continue.continue", "title": "Continue", "category": "Cursor Auto Continue"}, {"command": "cursor-auto-continue.toggle", "title": "Toggle Auto Continue", "category": "Cursor Auto Continue"}], "configuration": {"title": "Cursor Auto Continue", "properties": {"cursorAutoContinue.enabled": {"type": "boolean", "default": true, "description": "Enable/disable the auto-continue functionality"}, "cursorAutoContinue.pollingInterval": {"type": "number", "default": 500, "minimum": 100, "maximum": 5000, "description": "How often to check for triggers (in milliseconds)"}, "cursorAutoContinue.showNotifications": {"type": "boolean", "default": false, "description": "Show notification when auto-continue triggers"}, "cursorAutoContinue.customTriggers": {"type": "object", "default": {}, "description": "Custom trigger phrases and their responses (JSON object)", "patternProperties": {".*": {"type": "string"}}}, "cursorAutoContinue.enabledTriggers": {"type": "object", "default": {"toolCallLimit": true, "pauseExecution": true, "proceedRemaining": true, "wouldLikeToContinue": true, "lengthConstraints": true, "hitLimit": true}, "description": "Enable/disable specific trigger categories", "properties": {"toolCallLimit": {"type": "boolean", "description": "I've reached my tool call limit"}, "pauseExecution": {"type": "boolean", "description": "I need to pause execution"}, "proceedRemaining": {"type": "boolean", "description": "To proceed with the remaining"}, "wouldLikeToContinue": {"type": "boolean", "description": "Would you like me to continue"}, "lengthConstraints": {"type": "boolean", "description": "Due to length constraints"}, "hitLimit": {"type": "boolean", "description": "I've hit the limit"}}}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/vscode": "^1.60.0", "@types/node": "^16.11.7", "typescript": "^4.5.5"}}