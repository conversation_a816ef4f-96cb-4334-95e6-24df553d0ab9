"use strict";
// Auto-Continue Extension for VS Code & Cursor
// Watches for AI tool call limits and automatically continues conversations
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
exports.__esModule = true;
exports.deactivate = exports.activate = void 0;
var vscode = require("vscode");
function activate(context) {
    console.log('Cursor Auto-Continue extension is now active');
    // Store the last known text to detect changes
    var lastText = '';
    var interval = null;
    // Get configuration
    function getConfig() {
        return vscode.workspace.getConfiguration('cursorAutoContinue');
    }
    // Default trigger responses organized by category
    var defaultTriggerCategories = {
        toolCallLimit: {
            "I've reached my tool call limit of 25": "continue",
            "I've hit the limit": "continue",
            "I've reached the limit": "continue",
            "By default, we stop the agent after 25 tool calls": "resume the conversation",
            "You can resume the conversation": "continue",
            "we stop the agent after 25 tool calls": "continue"
        },
        pauseExecution: {
            "I need to pause execution": "continue",
            "I'll need to pause": "continue",
            "I need to stop here due to": "continue"
        },
        proceedRemaining: {
            "To proceed with the remaining": "continue",
            "Let me continue with": "continue"
        },
        wouldLikeToContinue: {
            "Would you like me to continue": "Yes, please continue",
            "Should I continue with": "Yes, continue",
            "Shall I continue": "Yes, continue",
            "Let me know if you'd like me to continue": "Yes, continue",
            "I can continue if you'd like": "Yes, continue",
            "Would you like me to proceed": "Yes, please proceed"
        },
        lengthConstraints: {
            "Due to length constraints": "continue"
        },
        hitLimit: {
            "I've hit the limit": "continue"
        }
    };
    // Build active trigger responses based on configuration
    function buildTriggerResponses() {
        var config = getConfig();
        var enabledTriggers = config.get('enabledTriggers');
        var customTriggers = config.get('customTriggers');
        var triggers = {};
        // Add enabled default triggers
        for (var _i = 0, _a = Object.entries(enabledTriggers); _i < _a.length; _i++) {
            var _b = _a[_i], category = _b[0], isEnabled_1 = _b[1];
            if (isEnabled_1 && defaultTriggerCategories[category]) {
                triggers = __assign(__assign({}, triggers), defaultTriggerCategories[category]);
            }
        }
        // Add custom triggers
        triggers = __assign(__assign({}, triggers), customTriggers);
        return triggers;
    }
    // Create a function that checks the editor content
    function checkForTriggers() {
        var config = getConfig();
        var isEnabled = config.get('enabled');
        if (!isEnabled)
            return;
        var editor = vscode.window.activeTextEditor;
        if (!editor)
            return;
        var document = editor.document;
        var text = document.getText();
        // Only process if text has changed
        if (text === lastText)
            return;
        lastText = text;
        // Get current trigger responses
        var triggerResponses = buildTriggerResponses();
        var showNotifications = config.get('showNotifications');
        // Check for each trigger (check in reverse order to catch the most recent ones first)
        var triggers = Object.keys(triggerResponses).reverse();
        var _loop_1 = function (trigger) {
            var triggerIndex = text.lastIndexOf(trigger);
            if (triggerIndex !== -1) {
                // Check if this trigger hasn't been processed yet by looking for the response
                var responseText_1 = triggerResponses[trigger];
                var textAfterTrigger = text.substring(triggerIndex + trigger.length);
                // Only respond if the response isn't already there
                if (!textAfterTrigger.trim().startsWith(responseText_1.trim())) {
                    // Find the end of the current line after the trigger
                    var triggerPos = document.positionAt(triggerIndex + trigger.length);
                    var lineEnd_1 = document.lineAt(triggerPos.line).range.end;
                    // Insert the response on a new line
                    editor.edit(function (editBuilder) {
                        editBuilder.insert(lineEnd_1, '\n' + responseText_1);
                    }).then(function () {
                        // Move cursor to the end of the inserted text
                        var newPosition = new vscode.Position(lineEnd_1.line + 1, responseText_1.length);
                        editor.selection = new vscode.Selection(newPosition, newPosition);
                        // Show notification if enabled
                        if (showNotifications) {
                            vscode.window.showInformationMessage("Auto-continued: \"".concat(trigger, "\" \u2192 \"").concat(responseText_1, "\""));
                        }
                    });
                    return "break";
                }
            }
        };
        for (var _i = 0, triggers_1 = triggers; _i < triggers_1.length; _i++) {
            var trigger = triggers_1[_i];
            var state_1 = _loop_1(trigger);
            if (state_1 === "break")
                break;
        }
    }
    // Start the monitoring
    function startMonitoring() {
        if (interval) {
            clearInterval(interval);
        }
        var config = getConfig();
        var pollingInterval = config.get('pollingInterval');
        interval = setInterval(checkForTriggers, pollingInterval);
    }
    // Initialize monitoring
    startMonitoring();
    // Listen for configuration changes
    var configChangeDisposable = vscode.workspace.onDidChangeConfiguration(function (event) {
        if (event.affectsConfiguration('cursorAutoContinue')) {
            // Restart monitoring with new settings
            startMonitoring();
        }
    });
    // Register command to manually trigger continuation
    var continueDisposable = vscode.commands.registerCommand('cursor-auto-continue.continue', function () {
        var editor = vscode.window.activeTextEditor;
        if (editor) {
            var position_1 = editor.selection.active;
            editor.edit(function (editBuilder) {
                editBuilder.insert(position_1, 'continue');
            });
        }
    });
    // Register command to toggle the extension
    var toggleDisposable = vscode.commands.registerCommand('cursor-auto-continue.toggle', function () {
        var config = getConfig();
        var currentEnabled = config.get('enabled');
        config.update('enabled', !currentEnabled, vscode.ConfigurationTarget.Global).then(function () {
            var status = !currentEnabled ? 'enabled' : 'disabled';
            vscode.window.showInformationMessage("Cursor Auto Continue ".concat(status));
        });
    });
    // Clean up when the extension is deactivated
    context.subscriptions.push({
        dispose: function () {
            if (interval) {
                clearInterval(interval);
            }
        }
    });
    context.subscriptions.push(configChangeDisposable);
    context.subscriptions.push(continueDisposable);
    context.subscriptions.push(toggleDisposable);
    // Show activation message
    var config = getConfig();
    var isEnabled = config.get('enabled');
    var status = isEnabled ? 'enabled and monitoring' : 'disabled';
    vscode.window.showInformationMessage("Cursor Auto Continue is ".concat(status));
}
exports.activate = activate;
function deactivate() {
    console.log('Cursor Auto-Continue extension is now deactivated');
}
exports.deactivate = deactivate;
