# 🚀 Cursor Auto-Continue Settings Guide

## ✅ YES! It Auto-Starts When You Open Cursor!

The extension is configured with `"onStartupFinished"` activation, which means it **automatically starts monitoring** as soon as Cursor/VS Code finishes loading. No manual activation needed!

## 🎛️ How to Access Settings

### Method 1: Settings UI (Recommended)
1. **Open Cursor/VS Code**
2. **Press `Ctrl/Cmd + ,`** (comma key)
3. **Search for**: `cursor auto continue`
4. **Configure all options** in the visual interface

### Method 2: Command Palette
1. **Press `Ctrl/Cmd + Shift + P`**
2. **Type**: `Preferences: Open Settings`
3. **Search**: `cursor auto continue`

### Method 3: Settings JSON (Advanced)
1. **Press `Ctrl/Cmd + Shift + P`**
2. **Type**: `Preferences: Open User Settings (JSON)`
3. **Add your config** (see examples below)

## ⚙️ All Available Settings

### 🔛 Enable/Disable Extension
```json
"cursorAutoContinue.enabled": true
```
- **Default**: `true` (auto-enabled when Cursor starts)
- **Description**: Master on/off switch
- **Quick Toggle**: Command Palette → "Cursor Auto Continue: Toggle"

### ⏱️ Polling Interval (Speed)
```json
"cursorAutoContinue.pollingInterval": 500
```
- **Default**: `500ms` (half second)
- **Range**: 100ms - 5000ms
- **Lower = Faster response** (but uses more CPU)
- **Higher = Slower response** (but uses less CPU)

### 🔔 Show Notifications
```json
"cursorAutoContinue.showNotifications": false
```
- **Default**: `false` (silent operation)
- **When enabled**: Shows popup each time it auto-continues
- **Example**: "Auto-continued: 'I've reached my tool call limit' → 'continue'"

### 🎯 Custom Triggers (Your Own!)
```json
"cursorAutoContinue.customTriggers": {
  "Let me pause here": "Yes, continue please",
  "Should we proceed": "Absolutely, proceed",
  "I need to think": "Please continue thinking",
  "Wait for input": "No need to wait, continue"
}
```
- **Format**: `"trigger phrase": "your response"`
- **Use Cases**: 
  - Your specific AI prompts
  - Different AI assistants
  - Custom workflow phrases

### 🏷️ Enable/Disable Categories
```json
"cursorAutoContinue.enabledTriggers": {
  "toolCallLimit": true,
  "pauseExecution": true,
  "proceedRemaining": true,
  "wouldLikeToContinue": true,
  "lengthConstraints": true,
  "hitLimit": true
}
```

**Categories Explained**:
- **toolCallLimit**: "I've reached my tool call limit of 25"
- **pauseExecution**: "I need to pause execution"
- **proceedRemaining**: "To proceed with the remaining"
- **wouldLikeToContinue**: "Would you like me to continue"
- **lengthConstraints**: "Due to length constraints"
- **hitLimit**: "I've hit the limit"

## 💡 Recommended Settings for Different Use Cases

### 🏃‍♂️ Speed Demon (Ultra Fast)
```json
{
  "cursorAutoContinue.enabled": true,
  "cursorAutoContinue.pollingInterval": 100,
  "cursorAutoContinue.showNotifications": false
}
```

### 🔔 I Want to Know Everything
```json
{
  "cursorAutoContinue.enabled": true,
  "cursorAutoContinue.pollingInterval": 500,
  "cursorAutoContinue.showNotifications": true
}
```

### 🎯 Only Tool Call Limits
```json
{
  "cursorAutoContinue.enabled": true,
  "cursorAutoContinue.enabledTriggers": {
    "toolCallLimit": true,
    "pauseExecution": false,
    "proceedRemaining": false,
    "wouldLikeToContinue": false,
    "lengthConstraints": false,
    "hitLimit": true
  }
}
```

### 🌟 Kitchen Sink (Everything!)
```json
{
  "cursorAutoContinue.enabled": true,
  "cursorAutoContinue.pollingInterval": 300,
  "cursorAutoContinue.showNotifications": true,
  "cursorAutoContinue.customTriggers": {
    "Let me pause": "Continue, please",
    "Should I proceed": "Yes, proceed",
    "Need permission": "Permission granted, continue"
  }
}
```

## 🚨 Quick Commands

| Command | What It Does |
|---------|--------------|
| `Cursor Auto Continue: Toggle` | Enable/disable instantly |
| `Cursor Auto Continue: Continue` | Manually insert "continue" |

## 🔧 Troubleshooting

### Extension Not Working?
1. **Check if enabled**: Settings → Search "cursor auto continue" → Enabled = ✅
2. **Try toggle**: Command Palette → "Cursor Auto Continue: Toggle" (twice)
3. **Check notifications**: Enable notifications to see if it's detecting triggers

### Too Slow/Fast?
- **Too slow**: Lower `pollingInterval` (try 200ms)
- **Too fast/CPU heavy**: Raise `pollingInterval` (try 1000ms)

### Missing Triggers?
- **Add custom ones**: Use `customTriggers` setting
- **Check categories**: Make sure relevant categories are enabled in `enabledTriggers`

---

🎉 **Perfect!** Your extension will now automatically start when you open Cursor and continue your AI conversations seamlessly!