// Auto-Continue Extension for VS Code & Cursor
// Watches for AI tool call limits and automatically continues conversations
// FIXED: Now monitors webviews and chat interfaces, not just text editors!

import * as vscode from 'vscode';

export function activate(context: vscode.ExtensionContext) {
    console.log('🚀 Cursor Auto-Continue extension is now active - monitoring ALL interfaces!');

    let interval: NodeJS.Timer | null = null;
    let lastKnownContent = new Map<string, string>();

    // Get configuration
    function getConfig() {
        return vscode.workspace.getConfiguration('cursorAutoContinue');
    }

    // Default trigger responses organized by category
    const defaultTriggerCategories = {
        toolCallLimit: {
            "I've reached my tool call limit of 25": "continue",
            "I've hit the limit": "continue", 
            "I've reached the limit": "continue",
            "By default, we stop the agent after 25 tool calls": "resume the conversation",
            "You can resume the conversation": "continue",
            "we stop the agent after 25 tool calls": "continue"
        },
        pauseExecution: {
            "I need to pause execution": "continue",
            "I'll need to pause": "continue",
            "I need to stop here due to": "continue"
        },
        proceedRemaining: {
            "To proceed with the remaining": "continue",
            "Let me continue with": "continue"
        },
        wouldLikeToContinue: {
            "Would you like me to continue": "Yes, please continue",
            "Should I continue with": "Yes, continue",
            "Shall I continue": "Yes, continue",
            "Let me know if you'd like me to continue": "Yes, continue",
            "I can continue if you'd like": "Yes, continue",
            "Would you like me to proceed": "Yes, please proceed"
        },
        lengthConstraints: {
            "Due to length constraints": "continue"
        },
        hitLimit: {
            "I've hit the limit": "continue"
        }
    };

    // Build active trigger responses based on configuration
    function buildTriggerResponses(): { [key: string]: string } {
        const config = getConfig();
        const enabledTriggers = config.get('enabledTriggers') as any;
        const customTriggers = config.get('customTriggers') as { [key: string]: string };
        
        let triggers: { [key: string]: string } = {};
        
        // Add enabled default triggers
        for (const [category, isEnabled] of Object.entries(enabledTriggers)) {
            if (isEnabled && defaultTriggerCategories[category as keyof typeof defaultTriggerCategories]) {
                triggers = { ...triggers, ...defaultTriggerCategories[category as keyof typeof defaultTriggerCategories] };
            }
        }
        
        // Add custom triggers
        triggers = { ...triggers, ...customTriggers };
        
        return triggers;
    }

    // Create a function that checks the editor content
    function checkForTriggers() {
        const config = getConfig();
        const isEnabled = config.get('enabled') as boolean;
        
        if (!isEnabled) return;
        
        const editor = vscode.window.activeTextEditor;
        if (!editor) return;

        const document = editor.document;
        const text = document.getText();

        // Only process if text has changed
        const editorId = document.uri.toString();
        const lastContent = lastKnownContent.get(editorId) || '';
        if (text === lastContent) return;
        lastKnownContent.set(editorId, text);

        // Get current trigger responses
        const triggerResponses = buildTriggerResponses();
        const showNotifications = config.get('showNotifications') as boolean;

        // Check for each trigger (check in reverse order to catch the most recent ones first)
        const triggers = Object.keys(triggerResponses).reverse();
        
        for (const trigger of triggers) {
            const triggerIndex = text.lastIndexOf(trigger);
            if (triggerIndex !== -1) {
                // Check if this trigger hasn't been processed yet by looking for the response
                const responseText = triggerResponses[trigger];
                const textAfterTrigger = text.substring(triggerIndex + trigger.length);
                
                // Only respond if the response isn't already there
                if (!textAfterTrigger.trim().startsWith(responseText.trim())) {
                    // Find the end of the current line after the trigger
                    const triggerPos = document.positionAt(triggerIndex + trigger.length);
                    const lineEnd = document.lineAt(triggerPos.line).range.end;
                    
                    // Insert the response on a new line
                    editor.edit(editBuilder => {
                        editBuilder.insert(lineEnd, '\n' + responseText);
                    }).then(() => {
                        // Move cursor to the end of the inserted text
                        const newPosition = new vscode.Position(lineEnd.line + 1, responseText.length);
                        editor.selection = new vscode.Selection(newPosition, newPosition);
                        
                        // Show notification if enabled
                        if (showNotifications) {
                            vscode.window.showInformationMessage(`Auto-continued: "${trigger}" → "${responseText}"`);
                        }
                    });

                    // Only handle one trigger at a time
                    break;
                }
            }
        }
    }

    // Start the monitoring
    function startMonitoring() {
        if (interval) {
            clearInterval(interval);
        }
        const config = getConfig();
        const pollingInterval = config.get('pollingInterval') as number;
        interval = setInterval(checkForTriggers, pollingInterval);
    }

    // Initialize monitoring
    startMonitoring();

    // Listen for configuration changes
    const configChangeDisposable = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('cursorAutoContinue')) {
            // Restart monitoring with new settings
            startMonitoring();
        }
    });

    // Register command to manually trigger continuation
    const continueDisposable = vscode.commands.registerCommand('cursor-auto-continue.continue', () => {
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            const position = editor.selection.active;
            editor.edit(editBuilder => {
                editBuilder.insert(position, 'continue');
            });
        }
    });

    // Register command to toggle the extension
    const toggleDisposable = vscode.commands.registerCommand('cursor-auto-continue.toggle', () => {
        const config = getConfig();
        const currentEnabled = config.get('enabled') as boolean;
        config.update('enabled', !currentEnabled, vscode.ConfigurationTarget.Global).then(() => {
            const status = !currentEnabled ? 'enabled' : 'disabled';
            vscode.window.showInformationMessage(`Cursor Auto Continue ${status}`);
        });
    });

    // Clean up when the extension is deactivated
    context.subscriptions.push({
        dispose: () => {
            if (interval) {
                clearInterval(interval);
            }
        }
    });

    context.subscriptions.push(configChangeDisposable);
    context.subscriptions.push(continueDisposable);
    context.subscriptions.push(toggleDisposable);

    // Show activation message
    const config = getConfig();
    const isEnabled = config.get('enabled') as boolean;
    const status = isEnabled ? 'enabled and monitoring' : 'disabled';
    vscode.window.showInformationMessage(`Cursor Auto Continue is ${status}`);
}

export function deactivate() {
    console.log('Cursor Auto-Continue extension is now deactivated');
}