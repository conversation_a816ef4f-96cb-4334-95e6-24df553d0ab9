import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};
import { GoogleGenerativeAI } from 'npm:@google/generative-ai';

// GEMINI_API_KEY will be checked inside the handler
// const GEMINI_API_KEY = Deno.env.get('GEMINI_API_KEY') || ''; 
// if (!GEMINI_API_KEY) {
//   console.error("GEMINI_API_KEY is not set in environment variables.");
// }
    // Defer client initialization to inside the request handler after key check
    // const genAI = new GoogleGenerativeAI(GEMINI_API_KEY); 
    // const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

    serve(async (req: Request) => {
      if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const GEMINI_API_KEY = Deno.env.get('GEMINI_API_KEY');
    if (!GEMINI_API_KEY) {
      console.warn("GEMINI_API_KEY is not set. Returning a placeholder AI insight for MVP demo.");
      // Define a simple structure for the placeholder, matching what ContextualAnalysis might expect for this field
      const placeholderAnalysis = { 
        progressHighlight: "You're doing great! Keep focusing on your goals and remember your motivations. Every step forward, no matter how small, is progress."
      };
      return new Response(JSON.stringify({ suggestion: JSON.stringify(placeholderAnalysis) }), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

    const { prompt } = await req.json();

    if (!prompt) {
      return new Response(JSON.stringify({ error: 'Prompt is required' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }
    
    // Generate content
    const result = await model.generateContent(prompt);
    const response = result.response;
    let text = response.text();
    // Clean the text: remove markdown code block fences if present
    if (text.startsWith("```json\n")) {
      text = text.substring(7); // Length of "```json\n"
    }
    if (text.endsWith("\n```")) {
      text = text.substring(0, text.length - 4); // Length of "\n```"
    }
    // It's also possible it might just be ``` at start and end without json and newline
    if (text.startsWith("```")) {
      text = text.substring(3);
    }
    if (text.endsWith("```")) {
      text = text.substring(0, text.length - 3);
    }
    text = text.trim(); // Trim any leading/trailing whitespace just in case

    return new Response(JSON.stringify({ suggestion: text }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error processing request in get-dashboard-ai-insights:', error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred in AI Edge Function";
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
