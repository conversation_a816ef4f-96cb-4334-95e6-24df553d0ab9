/// <reference types="https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts" />
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

console.log('Delete user function booting up...')

serve(async (req) => {
  // 1. Check method and authorization
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method Not Allowed' }), { status: 405, headers: { 'Content-Type': 'application/json' } })
  }

  const authHeader = req.headers.get('Authorization')
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({ error: 'Missing or invalid authorization token' }), { status: 401, headers: { 'Content-Type': 'application/json' } })
  }

  try {
    // 2. Create Supabase clients
    // Client to validate user token
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: authHeader } } }
    )
    
    // Get user from token
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser()
    if (userError || !user) {
      console.error('User fetch error:', userError)
      return new Response(JSON.stringify({ error: 'Invalid token or user not found' }), { status: 401, headers: { 'Content-Type': 'application/json' } })
    }

    const userIdToDelete = user.id
    console.log(`Attempting to delete user: ${userIdToDelete}`)

    // 3. Create Admin Client (requires service_role key)
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // 4. Delete associated data (add more tables as needed)
    // --- List compiled from MCP list_tables and cross-referenced with function --- 
    const tablesToDeleteFrom = [
      'activity_points_log',          // user_id
      'claimed_rewards',              // user_id
      'community_posts',              // user_id (FK to profiles.id)
      'conversation_messages',        // sender_id (FK to auth.users.id)
      'conversation_participants',    // user_id
      'craving_logs',                 // user_id
      'custom_products',              // user_id
      'goal_milestones',              // user_id
      'health_metrics',               // user_id
      'journal_entries',              // user_id
      'medication_reminders',         // user_id
      'meditation_sessions',          // user_id
      'nicotine_logs',                // user_id
      'nicotine_product_costs',       // user_id
      'notification_preferences',     // user_id (PK)
      'notifications',                // user_id
      'post_likes',                   // user_id
      'push_tokens',                  // user_id
      'relapses',                     // user_id
      'smokeless_product_reviews',    // user_id (FK to profiles.id)
      'step_rewards',                 // user_id
      'success_stories',              // user_id (FK to profiles.id)
      'success_story_comments',       // user_id (FK to profiles.id)
      'success_story_likes',          // user_id
      'testimonials',                 // user_id
      'triggers',                     // user_id (for custom triggers)
      'coping_strategies',            // user_id (for custom strategies)
      'user_achievements',            // user_id
      'user_badges',                  // user_id
      'user_gamification_stats',      // user_id (PK)
      'user_goals',                   // user_id (Unique)
      'user_learning_progress',       // user_id
      'user_logins',                  // user_id (FK to profiles.id)
      'user_preferences',             // user_id (Unique)
      'user_trigger_strategy_map',    // user_id
      // Profiles MUST be last among these simple loop deletions
      'profiles',                     // id (PK, is user_id from auth)
    ]

    // Define column names to use for deletion for specific tables
    // Default is 'user_id'. Override if the column linking to the user is named differently.
    const userLinkColumnOverrides: { [key: string]: string } = {
      'profiles': 'id', // profiles.id is the user_id from auth.users
      'conversation_messages': 'sender_id',
      // For tables where user_id is the primary key and also the FK to auth.users
      'notification_preferences': 'user_id',
      'user_gamification_stats': 'user_id',
    };
    
    // Tables requiring special handling (e.g., multiple user foreign keys)
    // These will be handled separately after the main loop.
    const specialHandlingTables = {
      support_requests: ['sender_id', 'receiver_id'],
      support_connections: ['user1_id', 'user2_id'],
    };

    for (const table of tablesToDeleteFrom) {
      const columnToDeleteOn = userLinkColumnOverrides[table] || 'user_id';
      
      console.log(`Deleting from ${table} in mission_fresh for user ${userIdToDelete} using column ${columnToDeleteOn}...`)
      const { error: deleteError } = await supabaseAdmin
        .schema('mission_fresh')
        .from(table)
        .delete()
        .eq(columnToDeleteOn, userIdToDelete)
      
      if (deleteError) {
        // Log error but continue trying other deletes if possible
        console.error(`Error deleting from ${table}:`, deleteError.message)
        // Optionally, you might want to return an error immediately depending on importance
        // return new Response(JSON.stringify({ error: `Failed to delete data from ${table}: ${deleteError.message}` }), { status: 500, headers: { 'Content-Type': 'application/json' } })
      }
    }

    // Handle tables with special logic (e.g., multiple user FKs)
    console.log("Handling tables with special deletion logic...");

    // Support Requests
    if (specialHandlingTables.support_requests) {
      const srTable = 'support_requests';
      const srColumns = specialHandlingTables.support_requests;
      console.log(`Deleting from ${srTable} where ${srColumns[0]} OR ${srColumns[1]} is ${userIdToDelete}...`);
      const { error: srDeleteError } = await supabaseAdmin
        .schema('mission_fresh')
        .from(srTable)
        .delete()
        .or(`${srColumns[0]}.eq.${userIdToDelete},${srColumns[1]}.eq.${userIdToDelete}`);
      if (srDeleteError) {
        console.error(`Error deleting from ${srTable}:`, srDeleteError.message);
      }
    }

    // Support Connections
    if (specialHandlingTables.support_connections) {
      const scTable = 'support_connections';
      const scColumns = specialHandlingTables.support_connections;
      console.log(`Deleting from ${scTable} where ${scColumns[0]} OR ${scColumns[1]} is ${userIdToDelete}...`);
      const { error: scDeleteError } = await supabaseAdmin
        .schema('mission_fresh')
        .from(scTable)
        .delete()
        .or(`${scColumns[0]}.eq.${userIdToDelete},${scColumns[1]}.eq.${userIdToDelete}`);
      if (scDeleteError) {
        console.error(`Error deleting from ${scTable}:`, scDeleteError.message);
      }
    }
    
    // Consider if empty conversations should be deleted after participants are removed.
    // For now, `conversation_participants` is handled in the main loop, which is sufficient for removing user association.

    // 5. Delete user from auth (MUST BE LAST DATA OPERATION)
    console.log(`Deleting user ${userIdToDelete} from auth...`)
    const { error: adminDeleteError } = await supabaseAdmin.auth.admin.deleteUser(userIdToDelete)
    if (adminDeleteError) {
      console.error('Auth user deletion error:', adminDeleteError.message)
      throw new Error(`Failed to delete auth user: ${adminDeleteError.message}`)
    }

    console.log(`Successfully deleted user: ${userIdToDelete}`)

    // 6. Return success response
    return new Response(JSON.stringify({ message: 'User deleted successfully' }), { status: 200, headers: { 'Content-Type': 'application/json' } })

  } catch (error) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('Unhandled error in delete-user function:', message, error)
    return new Response(JSON.stringify({ error: message }), { status: 500, headers: { 'Content-Type': 'application/json' } })
  }
})

/* 
Setup Notes:
1. Deploy this function to Supabase Edge Functions: `supabase functions deploy delete-user`
2. Ensure environment variables are set in Supabase dashboard:
   - SUPABASE_URL
   - SUPABASE_ANON_KEY
   - SUPABASE_SERVICE_ROLE_KEY
3. Update RLS policies if needed, although admin client bypasses RLS.
4. Add any other tables with user_id foreign keys to `tablesToDeleteFrom`.
5. Ensure the 'profiles' table uses the auth user id as its primary key ('id') or adjust the .eq() condition.
*/
