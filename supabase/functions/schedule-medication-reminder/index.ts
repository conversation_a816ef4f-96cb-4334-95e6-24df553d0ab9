import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.43.2";

// Assume a generic push notification service endpoint and API key
// In a real application, these would be stored securely (e.g., in Supabase secrets)
const PUSH_NOTIFICATION_SERVICE_URL = Deno.env.get("PUSH_NOTIFICATION_SERVICE_URL");
const PUSH_NOTIFICATION_SERVICE_API_KEY = Deno.env.get("PUSH_NOTIFICATION_SERVICE_API_KEY");

serve(async (req) => {
  const supabaseClient = createClient(
    Deno.env.get("SUPABASE_URL") ?? "",
    Deno.env.get("SUPABASE_ANON_KEY") ?? "",
    {
      global: {
        headers: { Authorization: req.headers.get("Authorization")! },
      },
    },
  );

  try {
    const payload = await req.json();
    console.log("Received payload:", payload);

    // Check if the payload is from a medication_reminders insert or update
    if (payload.type === "INSERT" || payload.type === "UPDATE") {
      const reminder = payload.new; // The new or updated reminder record

      if (!reminder || !reminder.user_id || !reminder.time || !reminder.medication_type) {
        console.error("Invalid reminder data in payload");
        return new Response(JSON.stringify({ error: "Invalid reminder data" }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        });
      }

      // Fetch the user's push token(s) from the push_tokens table
      const { data: pushTokens, error: pushTokenError } = await supabaseClient
        .schema("mission_fresh")
        .from("push_tokens")
        .select("token")
        .eq("user_id", reminder.user_id);

      if (pushTokenError) {
        console.error("Error fetching push tokens:", pushTokenError);
        return new Response(JSON.stringify({ error: "Error fetching push tokens" }), {
          status: 500,
          headers: { "Content-Type": "application/json" },
        });
      }

      if (!pushTokens || pushTokens.length === 0) {
        console.log(`No push tokens found for user ${reminder.user_id}. Skipping notification scheduling.`);
        return new Response(JSON.stringify({ message: "No push tokens found for user" }), {
          status: 200,
          headers: { "Content-Type": "application/json" },
        });
      }

      // Schedule notification for each token (assuming a service that handles scheduling)
      const notificationTitle = "Medication Reminder";
      const notificationBody = `Time to take your ${reminder.medication_type}!`;
      const scheduledTime = reminder.time; // Assuming time is in HH:MM:SS format

      for (const tokenData of pushTokens) {
        const deviceToken = tokenData.token;
        console.log(`Attempting to schedule notification for token: ${deviceToken} at ${scheduledTime}`);

        // Call the push notification service API to schedule the notification
        // This is a placeholder and needs to be replaced with actual API call logic
        try {
          const response = await fetch(PUSH_NOTIFICATION_SERVICE_URL!, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${PUSH_NOTIFICATION_SERVICE_API_KEY}`,
            },
            body: JSON.stringify({
              token: deviceToken,
              title: notificationTitle,
              body: notificationBody,
              schedule: { time: scheduledTime, repeat: "daily" }, // Assuming service supports daily scheduling
              data: { reminderId: reminder.id, userId: reminder.user_id },
            }),
          });

          if (!response.ok) {
            const errorBody = await response.text();
            console.error(`Failed to schedule notification for token ${deviceToken}: ${response.status} - ${errorBody}`);
          } else {
            console.log(`Successfully requested notification scheduling for token ${deviceToken}`);
          }
        } catch (apiError) {
          console.error(`Error calling push notification service for token ${deviceToken}:`, apiError);
        }
      }

      return new Response(JSON.stringify({ message: "Notification scheduling requests sent" }), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });

    } else if (payload.type === "DELETE") {
        const oldReminder = payload.old; // The deleted reminder record

        if (!oldReminder || !oldReminder.id || !oldReminder.user_id) {
            console.error("Invalid old reminder data in payload for delete");
            return new Response(JSON.stringify({ error: "Invalid old reminder data" }), {
              status: 400,
              headers: { "Content-Type": "application/json" },
            });
        }

        // Fetch the user's push token(s) from the push_tokens table
        const { data: pushTokens, error: pushTokenError } = await supabaseClient
            .schema("mission_fresh")
            .from("push_tokens")
            .select("token")
            .eq("user_id", oldReminder.user_id);

        if (pushTokenError) {
            console.error("Error fetching push tokens for deletion:", pushTokenError);
             // Continue with cancellation attempts even if fetching tokens fails
        }

        if (pushTokens && pushTokens.length > 0) {
             // Cancel notification for each token (assuming a service that handles cancellation)
            const notificationIdToCancel = `medication-reminder-${oldReminder.id}`; // Must match the ID used for scheduling

            for (const tokenData of pushTokens) {
                const deviceToken = tokenData.token;
                console.log(`Attempting to cancel notification for token: ${deviceToken} with ID: ${notificationIdToCancel}`);

                // Call the push notification service API to cancel the notification
                // This is a placeholder and needs to be replaced with actual API call logic
                try {
                    // Assuming the service has a cancellation endpoint and requires the notification ID and token
                    const response = await fetch(`${PUSH_NOTIFICATION_SERVICE_URL}/cancel`, {
                        method: "POST", // Or DELETE, depending on the service API
                        headers: {
                            "Content-Type": "application/json",
                            "Authorization": `Bearer ${PUSH_NOTIFICATION_SERVICE_API_KEY}`,
                        },
                        body: JSON.stringify({
                            token: deviceToken,
                            notificationId: notificationIdToCancel,
                        }),
                    });

                    if (!response.ok) {
                        const errorBody = await response.text();
                        console.error(`Failed to cancel notification for token ${deviceToken} with ID ${notificationIdToCancel}: ${response.status} - ${errorBody}`);
                    } else {
                        console.log(`Successfully requested notification cancellation for token ${deviceToken} with ID ${notificationIdToCancel}`);
                    }
                } catch (apiError) {
                    console.error(`Error calling push notification service for cancellation for token ${deviceToken}:`, apiError);
                }
            }
        } else {
            console.log(`No push tokens found for user ${oldReminder.user_id} during deletion. No notifications to cancel.`);
        }


        return new Response(JSON.stringify({ message: "Notification cancellation requests sent" }), {
            status: 200,
            headers: { "Content-Type": "application/json" },
        });
    }


    return new Response(JSON.stringify({ message: "Unhandled event type" }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });

  } catch (error) {
    console.error("Error processing Edge Function request:", error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
});
