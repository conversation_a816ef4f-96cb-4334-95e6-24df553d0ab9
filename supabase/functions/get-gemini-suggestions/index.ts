import { serve } from 'jsr:@std/http/server';
import { GoogleGenerativeAI } from 'https://esm.sh/@google/generative-ai@0.24.0';

const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: CORS_HEADERS });
  }

  try {
    const body = await req.json();
    const prompt = body.prompt;
    const max_tokens = body.max_tokens !== undefined ? Number(body.max_tokens) : 500;
    const temperature = body.temperature !== undefined ? Number(body.temperature) : 0.7;

    if (!prompt) {
      return new Response(JSON.stringify({ error: 'Prompt is required' }), {
        headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    // Get API key from environment
    const apiKey = Deno.env.get('GEMINI_API_KEY');
    
    if (!apiKey) {
      console.error('GEMINI_API_KEY environment variable not set');
      return new Response(JSON.stringify({ error: 'AI service not configured' }), {
        headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' },
        status: 503,
      });
    }

    console.log(`Received prompt: ${prompt}, max_tokens: ${max_tokens}, temperature: ${temperature}`);

    // Initialize Gemini AI
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: 'gemini-pro' });

    // Generate content
    const result = await model.generateContent({
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      generationConfig: {
        maxOutputTokens: max_tokens,
        temperature: temperature,
      },
    });

    const response = await result.response;
    const suggestion = response.text();
    
    if (!suggestion) {
      console.error('Gemini API returned empty response');
      return new Response(JSON.stringify({ error: 'AI service returned empty response' }), {
        headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' },
        status: 503,
      });
    }

    console.log('Generated response:', suggestion);

    return new Response(JSON.stringify({ suggestion }), {
      headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' },
      status: 200,
    });
  } catch (e: unknown) {
    console.error('Error processing request:', e);
    let errorMessage = 'Failed to get suggestion from AI';
    
    if (e instanceof Error) {
      errorMessage = e.message;
      console.error(`Error: ${e.name} - ${e.message}`, e.stack);
    } else {
      errorMessage = 'An unexpected error occurred. Please check the function logs.';
      try {
        errorMessage = `Unexpected error: ${JSON.stringify(e)}`;
      } catch (_) {
        errorMessage = 'An unexpected and non-serializable error occurred.';
      }
    }
    
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});

console.log('Gemini AI Edge Function started - ready to serve real AI responses.');
