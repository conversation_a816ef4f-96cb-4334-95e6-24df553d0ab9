import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.5";
import webpush from 'npm:web-push';

// Configure web-push with your VAPID keys
const VAPID_MAILTO = Deno.env.get('VAPID_MAILTO');
const VAPID_PUBLIC_KEY = Deno.env.get('VAPID_PUBLIC_KEY');
const VAPID_PRIVATE_KEY = Deno.env.get('VAPID_PRIVATE_KEY');

if (!VAPID_MAILTO || !VAPID_PUBLIC_KEY || !VAPID_PRIVATE_KEY) {
 console.error('VAPID keys are not set in environment variables. Push notifications will fail.');
 // Optionally throw an error to prevent the function from starting if keys are critical for boot.
} else {
 webpush.setVapidDetails(
   VAPID_MAILTO,
   VAPID_PUBLIC_KEY,
   VAPID_PRIVATE_KEY
 );
}

serve(async (req) => {
  const { user_id, title, body, url } = await req.json();

  if (!user_id || !title || !body) {
    return new Response(JSON.stringify({ error: 'Missing user_id, title, or body' }), {
      headers: { 'Content-Type': 'application/json' },
      status: 400,
    });
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    {
      global: {
        headers: { Authorization: `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}` },
      },
    }
  );

  // Fetch push tokens for the user
  const { data: pushTokens, error } = await supabaseClient
    .schema('mission_fresh')
    .from('push_tokens')
    .select('token')
    .eq('user_id', user_id);

  if (error) {
    console.error('Error fetching push tokens:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch push tokens' }), {
      headers: { 'Content-Type': 'application/json' },
      status: 500,
    });
  }

  if (!pushTokens || pushTokens.length === 0) {
    return new Response(JSON.stringify({ message: 'No push tokens found for user' }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200,
    });
  }

  const notificationPayload = {
    title: title,
    body: body,
    url: url,
  };

  // Send notification to each token
  const sendPromises = pushTokens.map(async (tokenEntry) => {
    try {
      // tokenEntry will have 'token' and 'platform' columns
      const platform = tokenEntry.platform || 'web'; // Default to web if platform is null/undefined
      const tokenValue = tokenEntry.token;

      console.log(`Processing token for platform: ${platform}`);

      if (platform === 'web') {
        const subscription = JSON.parse(tokenValue); // Expects tokenValue to be a JSON string for web
        await webpush.sendNotification(subscription, JSON.stringify(notificationPayload));
        console.log('Web push notification sent to a subscription.');
      } else if (platform === 'ios' || platform === 'android') {
        // TODO: Implement sending to FCM/APNs using the raw device token (tokenValue)
        // This will require platform-specific libraries and configurations (e.g., FCM server key)
        // For now, we'll just log it.
        console.log(`TODO: Send ${platform.toUpperCase()} push to token: ${tokenValue.substring(0, 20)}... with payload:`, notificationPayload);
        // Example (conceptual - requires actual FCM/APNs setup):
        // if (platform === 'android') {
        //   await sendFcmNotification(tokenValue, notificationPayload);
        // } else if (platform === 'ios') {
        //   await sendApnsNotification(tokenValue, notificationPayload);
        // }
      } else {
        console.warn(`Unsupported push token platform: ${platform} for token: ${tokenValue}`);
      }
    } catch (sendError) {
      console.error('Error sending notification to subscription:', tokenEntry, sendError);
      // TODO: Handle expired subscriptions (e.g., remove from database)
    }
  });

  await Promise.all(sendPromises);

  return new Response(JSON.stringify({ message: 'Push notifications sent' }), {
    headers: { 'Content-Type': 'application/json' },
    status: 200,
  });
});
