-- Create the public.badges table
CREATE TABLE public.badges (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    description text,
    icon_name text,
    points integer
);

-- Enable Row Level Security for public.badges
ALTER TABLE public.badges ENABLE ROW LEVEL SECURITY;

-- Allow public read access to badges
CREATE POLICY "Enable read access for all users" ON public.badges
FOR SELECT USING (true);

-- Alter user_badges to reference badges table
ALTER TABLE public.user_badges
DROP COLUMN badge_id;

ALTER TABLE public.user_badges
ADD COLUMN badge_uuid uuid REFERENCES public.badges(id);

-- Populate the badges table with initial data
INSERT INTO public.badges (id, name, description, icon_name, points) VALUES
('c4a3e2b1-b723-4b38-93e3-29e5a8a628a3', 'First 24 Hours!', 'You made it through the first day nicotine-free.', 'Award', 10),
('f8b3e5a3-9b5a-4e1a-8e4a-3e2c1d0b5e4a', '72 Hours Strong', '<PERSON><PERSON> is likely out of your system.', 'Award', 20),
('a2b4e6a7-3c1d-4f2a-9b5a-3e2c1d0b5e4a', 'One Week Afresh', 'Completed your first full week!', 'Award', 50),
('b3c5d7a8-4e2a-4f3a-8b4a-3e2c1d0b5e4a', 'Two Week Milestone', 'Consistency is building.', 'Award', 75),
('c4d6e8a9-5f3a-4e4a-9a3a-3e2c1d0b5e4a', 'One Month Afresh!', 'A major milestone achieved!', 'Award', 100),
('d5e7f9ab-6f4a-4d5a-8b2a-3e2c1d0b5e4a', 'Three Months Strong', 'Cravings are likely much less frequent.', 'Award', 150),
('e6f8a0bc-7f5a-4c6a-9a1a-3e2c1d0b5e4a', 'Half Year Afresh!', 'Incredible progress!', 'Award', 200),
('f7a9b1cd-8f6a-4b7a-8a0a-3e2c1d0b5e4a', 'One Year Afresh!', 'Congratulations on a full year!', 'Award', 500),
('a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6', 'Goal Setter', 'You''ve set your first goal!', 'Target', 10),
('b2c3d4e5-f6a7-b8c9-d0e1-f2a3b4c5d6e7', 'First Log Entry', 'You started tracking your journey.', 'CheckCircle', 5),
('c3d4e5f6-a7b8-c9d0-e1f2-a3b4c5d6e7f8', 'Consistent Logger', 'Logged entries for 7 different days.', 'CheckCircle', 25),
('d4e5f6a7-b8c9-d0e1-f2a3-b4c5d6e7f8a9', 'Dedicated Tracker', 'Logged entries for 30 different days.', 'CheckCircle', 75),
('af32f214-edba-4990-a665-4f5b69d19801', 'Craving Conqueror', 'Successfully used a coping mechanism during a craving.', 'ShieldCheck', 15);

-- Enable Row Level Security for public.user_badges
ALTER TABLE public.user_badges ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to insert into user_badges
CREATE POLICY "Allow authenticated users to insert user_badges" ON public.user_badges
FOR INSERT TO authenticated WITH CHECK (auth.uid() = user_id);

-- Allow public read access for user_badges
CREATE POLICY "Enable read access for all users on user_badges" ON public.user_badges
FOR SELECT USING (true);
