-- Disable RLS on user_goals to clear existing policy effects if any during this transaction block
ALTER TABLE mission_fresh.user_goals DISABLE ROW LEVEL SECURITY;

-- Re-enable RLS. New policies will apply after this.
ALTER TABLE mission_fresh.user_goals ENABLE ROW LEVEL SECURITY;

-- Drop common existing policies to avoid conflicts. Add more if specific old policies are known.
DROP POLICY IF EXISTS "Authenticated users can manage their own goals" ON mission_fresh.user_goals;
DROP POLICY IF EXISTS "Allow read access for authenticated users on user_goals" ON mission_fresh.user_goals;
DROP POLICY IF EXISTS "Users can update their own goals" ON mission_fresh.user_goals;
DROP POLICY IF EXISTS "Users can select their own goals" ON mission_fresh.user_goals;
DROP POLICY IF EXISTS "Users can insert their own goals" ON mission_fresh.user_goals;
DROP POLICY IF EXISTS "Users can delete their own goals" ON mission_fresh.user_goals;


-- Create a new, very permissive policy for authenticated users on user_goals
-- This allows any authenticated user to perform ALL operations (SELECT, INSERT, UPDATE, DELETE) on ANY row.
CREATE POLICY "TEMP_MVP_ALLOW_ALL_FOR_AUTHENTICATED_ON_USER_GOALS"
ON mission_fresh.user_goals
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

-- Note: This policy removes per-user data isolation for the user_goals table for authenticated users.
-- This is based on the instruction to make it behave "like MySQL" for the MVP.
