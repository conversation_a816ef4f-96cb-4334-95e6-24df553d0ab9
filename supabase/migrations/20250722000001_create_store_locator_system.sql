-- Create comprehensive store locator system for NRT products
-- This creates the physical store directory with ratings, pricing, and location data

-- 1. Physical stores table - comprehensive directory of physical locations
CREATE TABLE nrt_physical_stores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    chain_name TEXT, -- e.g., 'CVS', 'Walgreens', 'Independent'
    store_type TEXT NOT NULL CHECK (store_type IN ('pharmacy', 'grocery', 'convenience', 'medical', 'specialty', 'other')),
    
    -- Location information
    address TEXT NOT NULL,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    zip_code TEXT NOT NULL,
    country TEXT NOT NULL DEFAULT 'USA',
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    
    -- Contact information
    phone TEXT,
    email TEXT,
    website_url TEXT,
    
    -- Operating hours (JSON format for flexibility)
    operating_hours JSONB, -- {"monday": "9:00-21:00", "tuesday": "9:00-21:00", etc.}
    
    -- Store features
    accepts_insurance BOOLEAN DEFAULT false,
    prescription_counter BOOLEAN DEFAULT false,
    pharmacist_consultation BOOLEAN DEFAULT false,
    drive_through BOOLEAN DEFAULT false,
    parking_available BOOLEAN DEFAULT true,
    wheelchair_accessible BOOLEAN DEFAULT false,
    
    -- Calculated fields
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    
    is_active BOOLEAN DEFAULT true,
    verified BOOLEAN DEFAULT false, -- verified by admin
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Store product availability and pricing
CREATE TABLE nrt_store_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    store_id UUID REFERENCES nrt_physical_stores(id) ON DELETE CASCADE NOT NULL,
    product_id UUID REFERENCES nrt_products(id) ON DELETE CASCADE NOT NULL,
    
    -- Pricing information
    price DECIMAL(10,2),
    currency TEXT DEFAULT 'USD',
    on_sale BOOLEAN DEFAULT false,
    sale_price DECIMAL(10,2),
    discount_percentage INTEGER,
    
    -- Availability
    in_stock BOOLEAN DEFAULT true,
    stock_level TEXT CHECK (stock_level IN ('high', 'medium', 'low', 'out_of_stock')),
    last_restocked DATE,
    
    -- Special notes
    behind_counter BOOLEAN DEFAULT false, -- requires asking pharmacist
    prescription_required BOOLEAN DEFAULT false,
    age_verification_required BOOLEAN DEFAULT false,
    
    -- Price verification
    price_verified_at TIMESTAMP WITH TIME ZONE,
    price_verified_by UUID REFERENCES auth.users(id),
    
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_store_product UNIQUE (store_id, product_id)
);

-- 3. Store reviews and ratings
CREATE TABLE nrt_store_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    store_id UUID REFERENCES nrt_physical_stores(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Rating components (1-5 stars)
    overall_rating INTEGER CHECK (overall_rating >= 1 AND overall_rating <= 5) NOT NULL,
    product_selection_rating INTEGER CHECK (product_selection_rating >= 1 AND product_selection_rating <= 5),
    staff_helpfulness_rating INTEGER CHECK (staff_helpfulness_rating >= 1 AND staff_helpfulness_rating <= 5),
    pricing_rating INTEGER CHECK (pricing_rating >= 1 AND pricing_rating <= 5),
    cleanliness_rating INTEGER CHECK (cleanliness_rating >= 1 AND cleanliness_rating <= 5),
    location_convenience_rating INTEGER CHECK (location_convenience_rating >= 1 AND location_convenience_rating <= 5),
    
    -- Review content
    title TEXT NOT NULL,
    review_text TEXT NOT NULL,
    
    -- Visit context
    visit_date DATE,
    products_purchased TEXT[], -- array of product names purchased
    total_spent DECIMAL(10,2),
    wait_time_minutes INTEGER,
    would_recommend BOOLEAN DEFAULT true,
    
    -- Engagement metrics
    helpful_votes INTEGER DEFAULT 0,
    unhelpful_votes INTEGER DEFAULT 0,
    
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_user_store_review UNIQUE (user_id, store_id)
);

-- 4. Store review votes
CREATE TABLE nrt_store_review_votes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    review_id UUID REFERENCES nrt_store_reviews(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    is_helpful BOOLEAN NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_user_store_review_vote UNIQUE (user_id, review_id)
);

-- 5. User location preferences for personalized store recommendations
CREATE TABLE nrt_user_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL, -- e.g., 'Home', 'Work', 'Mom's House'
    address TEXT NOT NULL,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    zip_code TEXT NOT NULL,
    country TEXT NOT NULL DEFAULT 'USA',
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    
    is_primary BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. User favorite stores
CREATE TABLE nrt_user_favorite_stores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    store_id UUID REFERENCES nrt_physical_stores(id) ON DELETE CASCADE NOT NULL,
    notes TEXT, -- personal notes about this store
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_user_favorite_store UNIQUE (user_id, store_id)
);

-- 7. Price tracking and alerts
CREATE TABLE nrt_price_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    product_id UUID REFERENCES nrt_products(id) ON DELETE CASCADE NOT NULL,
    target_price DECIMAL(10,2) NOT NULL,
    notification_radius_miles INTEGER DEFAULT 10, -- alert for stores within X miles
    user_location_id UUID REFERENCES nrt_user_locations(id),
    
    is_active BOOLEAN DEFAULT true,
    last_checked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_user_product_alert UNIQUE (user_id, product_id)
);

-- Create spatial index for location-based queries
CREATE INDEX idx_nrt_physical_stores_location ON nrt_physical_stores USING GIST (
    ll_to_earth(latitude, longitude)
);

-- Create other performance indexes
CREATE INDEX idx_nrt_physical_stores_city_state ON nrt_physical_stores(city, state);
CREATE INDEX idx_nrt_physical_stores_zip ON nrt_physical_stores(zip_code);
CREATE INDEX idx_nrt_physical_stores_chain ON nrt_physical_stores(chain_name);
CREATE INDEX idx_nrt_physical_stores_type ON nrt_physical_stores(store_type);
CREATE INDEX idx_nrt_physical_stores_rating ON nrt_physical_stores(average_rating DESC);
CREATE INDEX idx_nrt_physical_stores_active ON nrt_physical_stores(is_active);

CREATE INDEX idx_nrt_store_products_store ON nrt_store_products(store_id);
CREATE INDEX idx_nrt_store_products_product ON nrt_store_products(product_id);
CREATE INDEX idx_nrt_store_products_price ON nrt_store_products(price);
CREATE INDEX idx_nrt_store_products_stock ON nrt_store_products(in_stock);

CREATE INDEX idx_nrt_store_reviews_store ON nrt_store_reviews(store_id);
CREATE INDEX idx_nrt_store_reviews_user ON nrt_store_reviews(user_id);
CREATE INDEX idx_nrt_store_reviews_rating ON nrt_store_reviews(overall_rating DESC);
CREATE INDEX idx_nrt_store_reviews_date ON nrt_store_reviews(visit_date DESC);

CREATE INDEX idx_nrt_user_locations_user ON nrt_user_locations(user_id);
CREATE INDEX idx_nrt_user_locations_primary ON nrt_user_locations(user_id, is_primary);

CREATE INDEX idx_nrt_user_favorite_stores_user ON nrt_user_favorite_stores(user_id);
CREATE INDEX idx_nrt_user_favorite_stores_store ON nrt_user_favorite_stores(store_id);

CREATE INDEX idx_nrt_price_alerts_user ON nrt_price_alerts(user_id);
CREATE INDEX idx_nrt_price_alerts_product ON nrt_price_alerts(product_id);
CREATE INDEX idx_nrt_price_alerts_active ON nrt_price_alerts(is_active);

-- Enable Row Level Security
ALTER TABLE nrt_physical_stores ENABLE ROW LEVEL SECURITY;
ALTER TABLE nrt_store_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE nrt_store_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE nrt_store_review_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE nrt_user_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE nrt_user_favorite_stores ENABLE ROW LEVEL SECURITY;
ALTER TABLE nrt_price_alerts ENABLE ROW LEVEL SECURITY;

-- RLS Policies for physical stores
CREATE POLICY "Allow public read access to active stores" ON nrt_physical_stores
    FOR SELECT USING (is_active = true);

-- RLS Policies for store products
CREATE POLICY "Allow public read access to active store products" ON nrt_store_products
    FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to verify prices" ON nrt_store_products
    FOR UPDATE USING (auth.uid() = price_verified_by);

-- RLS Policies for store reviews
CREATE POLICY "Allow public read access to active store reviews" ON nrt_store_reviews
    FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to create store reviews" ON nrt_store_reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users to update their own store reviews" ON nrt_store_reviews
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Allow users to delete their own store reviews" ON nrt_store_reviews
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for store review votes
CREATE POLICY "Allow public read access to store review votes" ON nrt_store_review_votes
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated users to vote on store reviews" ON nrt_store_review_votes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users to update their own store review votes" ON nrt_store_review_votes
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Allow users to delete their own store review votes" ON nrt_store_review_votes
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for user locations
CREATE POLICY "Allow users to manage their own locations" ON nrt_user_locations
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for user favorite stores
CREATE POLICY "Allow users to manage their own favorite stores" ON nrt_user_favorite_stores
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for price alerts
CREATE POLICY "Allow users to manage their own price alerts" ON nrt_price_alerts
    FOR ALL USING (auth.uid() = user_id);

-- Create functions for distance calculations
CREATE OR REPLACE FUNCTION calculate_distance_miles(
    lat1 DECIMAL, lon1 DECIMAL, lat2 DECIMAL, lon2 DECIMAL
) RETURNS DECIMAL AS $$
BEGIN
    RETURN earth_distance(ll_to_earth(lat1, lon1), ll_to_earth(lat2, lon2)) * 0.000621371;
END;
$$ LANGUAGE plpgsql;

-- Create function to update store ratings when reviews are added/updated
CREATE OR REPLACE FUNCTION update_store_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE nrt_physical_stores 
    SET 
        average_rating = (
            SELECT ROUND(AVG(overall_rating), 2) 
            FROM nrt_store_reviews 
            WHERE store_id = COALESCE(NEW.store_id, OLD.store_id) 
            AND is_active = true
        ),
        total_reviews = (
            SELECT COUNT(*) 
            FROM nrt_store_reviews 
            WHERE store_id = COALESCE(NEW.store_id, OLD.store_id) 
            AND is_active = true
        ),
        updated_at = NOW()
    WHERE id = COALESCE(NEW.store_id, OLD.store_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update store ratings
CREATE TRIGGER trigger_update_store_rating_on_insert
    AFTER INSERT ON nrt_store_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_store_rating();

CREATE TRIGGER trigger_update_store_rating_on_update
    AFTER UPDATE ON nrt_store_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_store_rating();

CREATE TRIGGER trigger_update_store_rating_on_delete
    AFTER DELETE ON nrt_store_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_store_rating();
