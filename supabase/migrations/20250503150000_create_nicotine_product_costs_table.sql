-- Create the nicotine_product_costs table
CREATE TABLE public.nicotine_product_costs (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    product_type text NOT NULL,
    cost_per_unit numeric NOT NULL,
    unit_name text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,

    CONSTRAINT unique_user_product_type UNIQUE (user_id, product_type)
);

-- Enable Row Level Security (RLS) for the nicotine_product_costs table
ALTER TABLE public.nicotine_product_costs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for the nicotine_product_costs table
CREATE POLICY "Authenticated users can view their nicotine product costs" ON public.nicotine_product_costs
FOR SELECT USING (
  auth.uid() = user_id
);

CREATE POLICY "Authenticated users can insert their nicotine product costs" ON public.nicotine_product_costs
FOR INSERT WITH CHECK (
  auth.uid() = user_id
);

CREATE POLICY "Authenticated users can update their nicotine product costs" ON public.nicotine_product_costs
FOR UPDATE USING (
  auth.uid() = user_id
);

CREATE POLICY "Authenticated users can delete their nicotine product costs" ON public.nicotine_product_costs
FOR DELETE USING (
  auth.uid() = user_id
);
