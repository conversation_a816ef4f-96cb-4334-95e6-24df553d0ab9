-- Create quit methods table to replace hardcoded data
CREATE TABLE quit_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    best_for TEXT[] NOT NULL DEFAULT '{}',
    considerations TEXT[] NOT NULL DEFAULT '{}',
    success_rate TEXT NOT NULL,
    timeframe TEXT NOT NULL,
    difficulty TEXT NOT NULL CHECK (difficulty IN ('Easy', 'Moderate', 'Hard', 'Very Hard')),
    cta_text TEXT,
    cta_link TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert the real quit methods data
INSERT INTO quit_methods (title, description, best_for, considerations, success_rate, timeframe, difficulty, cta_text, cta_link, sort_order) VALUES
('Cold Turkey', 'Stopping nicotine use abruptly without any aids or gradual reduction.', 
 ARRAY['Highly motivated individuals', 'Those preferring an all-or-nothing approach', 'Users with strong support systems'],
 ARRAY['Intense withdrawal symptoms likely', 'Higher relapse risk for many', 'Requires significant mental preparation'],
 '~3-5% (unaided)', 'Immediate', 'Very Hard', NULL, NULL, 1),

('Gradual Reduction', 'Slowly decreasing the amount or frequency of nicotine use over time until quitting completely.',
 ARRAY['Those who prefer a slower transition', 'Users who want to minimize withdrawal', 'Individuals who can track intake carefully'],
 ARRAY['Requires discipline and tracking', 'Can prolong the quitting process', 'May still experience cravings'],
 '~5-10%', 'Weeks to Months', 'Hard', NULL, NULL, 2),

('Tapering Schedule', 'Following a structured plan to reduce nicotine intake over a set period, often involving specific steps and timelines.',
 ARRAY['Individuals who prefer structure', 'Those who want a clear roadmap', 'Can be combined with NRT'],
 ARRAY['Requires adherence to the schedule', 'May not be flexible enough for some', 'Still involves managing withdrawal'],
 '~10-15% (with structure)', '4-12 Weeks', 'Moderate', NULL, NULL, 3),

('NRT Assisted', 'Using Nicotine Replacement Therapy (NRT) products like patches, gum, or lozenges to manage withdrawal symptoms while quitting.',
 ARRAY['Most nicotine users', 'Those experiencing strong withdrawal', 'Individuals seeking to double quit chances'],
 ARRAY['Still involves nicotine initially', 'Potential cost of products', 'Requires correct usage'],
 '~15-25% (can be higher with counseling)', '8-12+ Weeks', 'Moderate', 'Explore NRT Options', '/tools/nrt-guide', 4),

('Harm Reduction', 'Reducing health risks by switching from combustible tobacco to significantly less harmful nicotine products (e.g., verified smokeless alternatives).',
 ARRAY['Those unable/unwilling to quit nicotine entirely', 'Users seeking to reduce major health risks', 'As a potential bridge to full cessation'],
 ARRAY['Still involves nicotine dependence', 'Long-term effects of some alternatives vary', 'Requires careful product selection'],
 'Varies (focus is risk reduction)', 'Ongoing / Variable', 'Easy', 'Explore Smokeless Products', '/tools/smokeless-directory', 5),

('Behavioral Support & Counseling', 'Utilizing therapy, support groups, or coaching to address psychological aspects of addiction and develop coping strategies.',
 ARRAY['Everyone, often combined with other methods', 'Those needing accountability and emotional support', 'Individuals with co-occurring mental health conditions'],
 ARRAY['May involve cost or time commitment', 'Finding the right therapist/group is key', 'Effectiveness varies by individual'],
 'Increases success of other methods significantly', 'Ongoing', 'Moderate', NULL, NULL, 6);

-- Enable Row Level Security
ALTER TABLE quit_methods ENABLE ROW LEVEL SECURITY;

-- Create policy to allow public read access (since this is educational content)
CREATE POLICY "Allow public read access to quit methods" ON quit_methods
    FOR SELECT USING (is_active = true);

-- Create policy to allow authenticated users to read all methods
CREATE POLICY "Allow authenticated read access to all quit methods" ON quit_methods
    FOR SELECT USING (auth.role() = 'authenticated');

-- Add index for performance
CREATE INDEX idx_quit_methods_sort_order ON quit_methods(sort_order);
CREATE INDEX idx_quit_methods_active ON quit_methods(is_active);
