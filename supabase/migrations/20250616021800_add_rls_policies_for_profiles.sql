-- Enable RLS on the profiles table if it's not already enabled.
ALTER TABLE mission_fresh.profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to ensure a clean slate.
DROP POLICY IF EXISTS "Users can create their own profile." ON mission_fresh.profiles;
DROP POLICY IF EXISTS "Users can read their own profile." ON mission_fresh.profiles;
DROP POLICY IF EXISTS "Users can update their own profile." ON mission_fresh.profiles;

-- Create a new policy for inserting profiles.
-- This allows any authenticated user to insert a row into the profiles table,
-- but only if the 'id' of the new row matches their own user ID from auth.uid().
CREATE POLICY "Users can create their own profile."
ON mission_fresh.profiles FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = id);

-- Create a new policy for selecting profiles.
-- This allows an authenticated user to select their own profile.
CREATE POLICY "Users can read their own profile."
ON mission_fresh.profiles FOR SELECT
TO authenticated
USING (auth.uid() = id);

-- Create a new policy for updating profiles.
-- This allows an authenticated user to update their own profile.
CREATE POLICY "Users can update their own profile."
ON mission_fresh.profiles FOR UPDATE
TO authenticated
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);
