-- Create the medication_reminders table
CREATE TABLE public.medication_reminders (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    medication_name text NOT NULL,
    dosage text,
    frequency text NOT NULL,
    start_date date NOT NULL,
    end_date date,
    reminder_time time with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Enable Row Level Security (RLS) for the medication_reminders table
ALTER TABLE public.medication_reminders ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for the medication_reminders table
CREATE POLICY "Authenticated users can view their medication reminders" ON public.medication_reminders
FOR SELECT USING (
  user_id = auth.uid()
);

CREATE POLICY "Authenticated users can insert their medication reminders" ON public.medication_reminders
FOR INSERT WITH CHECK (
  user_id = auth.uid()
);

CREATE POLICY "Authenticated users can update their medication reminders" ON public.medication_reminders
FOR UPDATE USING (
  user_id = auth.uid()
);

CREATE POLICY "Authenticated users can delete their medication reminders" ON public.medication_reminders
FOR DELETE USING (
  user_id = auth.uid()
);
