CREATE TABLE public.notification_preferences (
    user_id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE
);

ALTER TABLE public.notification_preferences ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can view their own notification preferences."
ON public.notification_preferences FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Authenticated users can update their own notification preferences."
ON public.notification_preferences FOR UPDATE
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Authenticated users can delete their own notification preferences."
ON public.notification_preferences FOR DELETE
TO authenticated
USING (auth.uid() = user_id);

-- The insert policy is handled by the trigger, no need for a separate policy here.
