-- Create a table to store push notification tokens
CREATE TABLE push_tokens (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users (id) ON DELETE CASCADE,
  token TEXT NOT NULL UNIQUE,
  platform TEXT, -- e.g., 'ios', 'android', 'web'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add an index on user_id for faster lookups
CREATE INDEX idx_push_tokens_user_id ON push_tokens (user_id);

-- Enable Row Level Security (RLS)
ALTER TABLE push_tokens ENABLE ROW LEVEL SECURITY;

-- Policies for RLS
CREATE POLICY "Authenticated users can view their own push tokens" ON push_tokens
FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Authenticated users can insert their own push tokens" ON push_tokens
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Authenticated users can update their own push tokens" ON push_tokens
FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Authenticated users can delete their own push tokens" ON push_tokens
FOR DELETE USING (auth.uid() = user_id);
