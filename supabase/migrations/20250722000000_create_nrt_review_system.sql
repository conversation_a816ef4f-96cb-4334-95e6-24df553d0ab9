-- Create comprehensive NRT review and rating system tables
-- This creates the "ratebeer for NRT" functionality

-- 1. Products table - comprehensive catalog of all NRT products, quit gears, and nicotine products
CREATE TABLE nrt_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('nrt', 'quit_gear', 'nicotine_product')),
    subcategory TEXT NOT NULL, -- e.g., 'patch', 'gum', 'e-cigarette', 'pouch'
    brand TEXT NOT NULL,
    manufacturer TEXT,
    description TEXT,
    nicotine_strength TEXT[], -- e.g., ['2mg', '4mg', '8mg']
    form_factor TEXT, -- e.g., 'patch', 'gum', 'lozenge', 'spray'
    flavors TEXT[], -- available flavors
    fda_approved BOOLEAN DEFAULT false,
    prescription_required BOOLEAN DEFAULT false,
    image_url TEXT,
    official_website TEXT,
    
    -- Calculated fields (updated by triggers)
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    price_range_min DECIMAL(10,2),
    price_range_max DECIMAL(10,2),
    
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Product reviews table - user reviews and ratings
CREATE TABLE nrt_product_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES nrt_products(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Rating components (1-5 stars)
    overall_rating INTEGER CHECK (overall_rating >= 1 AND overall_rating <= 5) NOT NULL,
    effectiveness_rating INTEGER CHECK (effectiveness_rating >= 1 AND effectiveness_rating <= 5),
    ease_of_use_rating INTEGER CHECK (ease_of_use_rating >= 1 AND ease_of_use_rating <= 5),
    taste_rating INTEGER CHECK (taste_rating >= 1 AND taste_rating <= 5),
    value_rating INTEGER CHECK (value_rating >= 1 AND value_rating <= 5),
    
    -- Review content
    title TEXT NOT NULL,
    review_text TEXT NOT NULL,
    pros TEXT[], -- array of pros
    cons TEXT[], -- array of cons
    
    -- Usage context
    usage_duration TEXT, -- e.g., '2 weeks', '3 months'
    quit_success BOOLEAN, -- did they successfully quit using this product
    would_recommend BOOLEAN DEFAULT true,
    verified_purchase BOOLEAN DEFAULT false,
    
    -- Engagement metrics
    helpful_votes INTEGER DEFAULT 0,
    unhelpful_votes INTEGER DEFAULT 0,
    
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_user_product_review UNIQUE (user_id, product_id)
);

-- 3. Review helpfulness votes
CREATE TABLE nrt_review_votes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    review_id UUID REFERENCES nrt_product_reviews(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    is_helpful BOOLEAN NOT NULL, -- true = helpful, false = unhelpful
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_user_review_vote UNIQUE (user_id, review_id)
);

-- 4. Online vendors table - comprehensive vendor directory
CREATE TABLE nrt_vendors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('online', 'physical', 'both')),
    website_url TEXT,
    description TEXT,
    logo_url TEXT,
    
    -- Contact information
    phone TEXT,
    email TEXT,
    customer_service_hours TEXT,
    
    -- Shipping information (for online vendors)
    shipping_countries TEXT[], -- countries they ship to
    free_shipping_threshold DECIMAL(10,2),
    standard_shipping_cost DECIMAL(10,2),
    expedited_shipping_cost DECIMAL(10,2),
    
    -- Business details
    established_year INTEGER,
    business_license TEXT,
    accepts_insurance BOOLEAN DEFAULT false,
    prescription_fulfillment BOOLEAN DEFAULT false,
    
    -- Calculated fields
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Vendor reviews table
CREATE TABLE nrt_vendor_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_id UUID REFERENCES nrt_vendors(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Rating components (1-5 stars)
    overall_rating INTEGER CHECK (overall_rating >= 1 AND overall_rating <= 5) NOT NULL,
    product_quality_rating INTEGER CHECK (product_quality_rating >= 1 AND product_quality_rating <= 5),
    shipping_speed_rating INTEGER CHECK (shipping_speed_rating >= 1 AND shipping_speed_rating <= 5),
    customer_service_rating INTEGER CHECK (customer_service_rating >= 1 AND customer_service_rating <= 5),
    value_rating INTEGER CHECK (value_rating >= 1 AND value_rating <= 5),
    
    -- Review content
    title TEXT NOT NULL,
    review_text TEXT NOT NULL,
    
    -- Order context
    order_total DECIMAL(10,2),
    shipping_time_days INTEGER,
    would_recommend BOOLEAN DEFAULT true,
    verified_purchase BOOLEAN DEFAULT false,
    
    -- Engagement metrics
    helpful_votes INTEGER DEFAULT 0,
    unhelpful_votes INTEGER DEFAULT 0,
    
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_user_vendor_review UNIQUE (user_id, vendor_id)
);

-- 6. Vendor product inventory - what products each vendor sells
CREATE TABLE nrt_vendor_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_id UUID REFERENCES nrt_vendors(id) ON DELETE CASCADE NOT NULL,
    product_id UUID REFERENCES nrt_products(id) ON DELETE CASCADE NOT NULL,
    
    -- Pricing and availability
    price DECIMAL(10,2),
    currency TEXT DEFAULT 'USD',
    in_stock BOOLEAN DEFAULT true,
    stock_quantity INTEGER,
    
    -- Delivery information
    standard_delivery_days INTEGER,
    expedited_delivery_days INTEGER,
    same_day_delivery BOOLEAN DEFAULT false,
    
    -- Special offers
    on_sale BOOLEAN DEFAULT false,
    sale_price DECIMAL(10,2),
    discount_percentage INTEGER,
    
    product_url TEXT, -- direct link to product on vendor site
    
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_vendor_product UNIQUE (vendor_id, product_id)
);

-- Create indexes for optimal performance
CREATE INDEX idx_nrt_products_category ON nrt_products(category);
CREATE INDEX idx_nrt_products_subcategory ON nrt_products(subcategory);
CREATE INDEX idx_nrt_products_brand ON nrt_products(brand);
CREATE INDEX idx_nrt_products_rating ON nrt_products(average_rating DESC);
CREATE INDEX idx_nrt_products_active ON nrt_products(is_active);

CREATE INDEX idx_nrt_product_reviews_product ON nrt_product_reviews(product_id);
CREATE INDEX idx_nrt_product_reviews_user ON nrt_product_reviews(user_id);
CREATE INDEX idx_nrt_product_reviews_rating ON nrt_product_reviews(overall_rating DESC);
CREATE INDEX idx_nrt_product_reviews_created ON nrt_product_reviews(created_at DESC);

CREATE INDEX idx_nrt_vendors_type ON nrt_vendors(type);
CREATE INDEX idx_nrt_vendors_rating ON nrt_vendors(average_rating DESC);
CREATE INDEX idx_nrt_vendors_active ON nrt_vendors(is_active);

CREATE INDEX idx_nrt_vendor_reviews_vendor ON nrt_vendor_reviews(vendor_id);
CREATE INDEX idx_nrt_vendor_reviews_user ON nrt_vendor_reviews(user_id);
CREATE INDEX idx_nrt_vendor_reviews_rating ON nrt_vendor_reviews(overall_rating DESC);

CREATE INDEX idx_nrt_vendor_products_vendor ON nrt_vendor_products(vendor_id);
CREATE INDEX idx_nrt_vendor_products_product ON nrt_vendor_products(product_id);
CREATE INDEX idx_nrt_vendor_products_price ON nrt_vendor_products(price);
CREATE INDEX idx_nrt_vendor_products_stock ON nrt_vendor_products(in_stock);

-- Enable Row Level Security
ALTER TABLE nrt_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE nrt_product_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE nrt_review_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE nrt_vendors ENABLE ROW LEVEL SECURITY;
ALTER TABLE nrt_vendor_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE nrt_vendor_products ENABLE ROW LEVEL SECURITY;

-- RLS Policies for products (public read, admin write)
CREATE POLICY "Allow public read access to active products" ON nrt_products
    FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to read all products" ON nrt_products
    FOR SELECT USING (auth.role() = 'authenticated');

-- RLS Policies for product reviews
CREATE POLICY "Allow public read access to active product reviews" ON nrt_product_reviews
    FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to create product reviews" ON nrt_product_reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users to update their own product reviews" ON nrt_product_reviews
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Allow users to delete their own product reviews" ON nrt_product_reviews
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for review votes
CREATE POLICY "Allow public read access to review votes" ON nrt_review_votes
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated users to vote on reviews" ON nrt_review_votes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users to update their own votes" ON nrt_review_votes
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Allow users to delete their own votes" ON nrt_review_votes
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for vendors
CREATE POLICY "Allow public read access to active vendors" ON nrt_vendors
    FOR SELECT USING (is_active = true);

-- RLS Policies for vendor reviews
CREATE POLICY "Allow public read access to active vendor reviews" ON nrt_vendor_reviews
    FOR SELECT USING (is_active = true);

CREATE POLICY "Allow authenticated users to create vendor reviews" ON nrt_vendor_reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users to update their own vendor reviews" ON nrt_vendor_reviews
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Allow users to delete their own vendor reviews" ON nrt_vendor_reviews
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for vendor products
CREATE POLICY "Allow public read access to active vendor products" ON nrt_vendor_products
    FOR SELECT USING (is_active = true);
