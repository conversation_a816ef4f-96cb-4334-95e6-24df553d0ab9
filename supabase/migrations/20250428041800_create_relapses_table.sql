CREATE TABLE public.relapses (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    relapse_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    trigger TEXT,
    situation TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

ALTER TABLE public.relapses ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can insert their own relapses"
ON public.relapses
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own relapses"
ON public.relapses
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

COMMENT ON TABLE public.relapses IS 'Stores records of user-reported relapses.';
COMMENT ON COLUMN public.relapses.relapse_at IS 'Timestamp when the relapse occurred.';
COMMENT ON COLUMN public.relapses.trigger IS 'Optional user-provided trigger for the relapse.';
COMMENT ON COLUMN public.relapses.situation IS 'Optional user-provided description of the situation.';
COMMENT ON COLUMN public.relapses.notes IS 'Optional free-form notes about the relapse.';
