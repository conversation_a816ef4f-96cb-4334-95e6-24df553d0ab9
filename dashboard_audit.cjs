const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: {width: 1920, height: 1080}
  });
  
  const page = await browser.newPage();
  await page.setViewport({width: 1920, height: 1080});
  
  console.log('📊 DASHBOARD COMPREHENSIVE AUDIT');
  
  // Navigate to dashboard (need to login first)
  await page.goto('http://localhost:5001/auth');
  await page.waitForSelector('form', {timeout: 10000});
  
  // Login with test credentials
  await page.fill('input[type="email"]', '<EMAIL>');
  await page.fill('input[type="password"]', 'J4913836j');
  
  const submitButton = await page.$('button[type="submit"]');
  await submitButton?.click();
  
  // Wait for dashboard to load
  await page.waitForNavigation({timeout: 15000});
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  const currentUrl = page.url();
  console.log('📍 Current URL after login:', currentUrl);
  
  // Check if we're on dashboard
  if (currentUrl.includes('/app/dashboard') || currentUrl.includes('/dashboard')) {
    console.log('✅ Successfully reached dashboard');
    
    // Audit dashboard elements
    const widgets = await page.$$('[class*="card"], [class*="widget"], .bg-card');
    console.log('📱 Dashboard widgets/cards found:', widgets.length);
    
    const buttons = await page.$$('button');
    console.log('🔘 Dashboard buttons found:', buttons.length);
    
    const charts = await page.$$('svg, canvas, [class*="chart"]');
    console.log('📈 Charts/visualizations found:', charts.length);
    
    // Check for loading states
    const loaders = await page.$$('[class*="loading"], .spinner, [class*="skeleton"]');
    console.log('⏳ Loading elements found:', loaders.length);
    
    // Check for error states
    const errors = await page.$$('[class*="error"], .text-destructive, .text-red');
    console.log('❌ Error elements found:', errors.length);
    
    // Test navigation menu
    const navItems = await page.$$('nav a, [role="navigation"] a');
    console.log('🧭 Navigation items found:', navItems.length);
    
    // Check headings consistency
    const headings = await page.$$eval('h1, h2, h3, h4, h5, h6', elements => {
      return elements.map(el => ({
        tag: el.tagName,
        text: el.textContent?.substring(0, 30),
        fontSize: window.getComputedStyle(el).fontSize
      }));
    });
    
    console.log('\n📝 DASHBOARD HEADINGS:');
    headings.forEach((h, i) => {
      console.log(`  ${h.tag}: ${h.fontSize} - "${h.text}"`);
    });
    
    // Take screenshot
    await page.screenshot({path: 'dashboard_audit.png', fullPage: true});
    console.log('\n📸 Dashboard audit screenshot saved');
    
  } else {
    console.log('❌ Failed to reach dashboard, current URL:', currentUrl);
    await page.screenshot({path: 'dashboard_login_failed.png'});
  }
  
  await browser.close();
})();
