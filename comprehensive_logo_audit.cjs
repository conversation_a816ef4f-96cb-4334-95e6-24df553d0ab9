const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: {width: 1920, height: 1080}
  });
  
  const page = await browser.newPage();
  await page.setViewport({width: 1920, height: 1080});
  
  await page.goto('http://localhost:5001');
  await page.waitForSelector('body', {timeout: 10000});
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  console.log('🔍 COMPREHENSIVE LOGO AUDIT');
  
  // Check for Leaf icon specifically
  const leafIcon = await page.$('svg[class*="lucide-leaf"], .lucide-leaf');
  console.log('🍃 Leaf icon found:', leafIcon !== null);
  
  // Check for Mission Fresh text
  const brandText = await page.$('text=Mission Fresh');
  console.log('📝 "Mission Fresh" text found:', brandText !== null);
  
  // Check NavbarBrand link
  const navbarBrand = await page.$('a[href="/"]');
  console.log('🔗 Brand link found:', navbarBrand !== null);
  
  // Get all SVG elements and analyze
  const svgElements = await page.$$eval('svg', svgs => {
    return svgs.map(svg => ({
      className: svg.className.baseVal,
      parent: svg.parentElement?.tagName,
      visible: window.getComputedStyle(svg).display !== 'none'
    }));
  });
  
  console.log('🎨 SVG Analysis:');
  svgElements.forEach((svg, index) => {
    if (svg.className.includes('lucide')) {
      console.log(`  SVG ${index}: ${svg.className}, Parent: ${svg.parent}, Visible: ${svg.visible}`);
    }
  });
  
  // Take screenshot of just the header area
  const header = await page.$('header');
  if (header) {
    await header.screenshot({path: 'header_logo_audit.png'});
    console.log('📸 Header screenshot saved');
  }
  
  // Check logo contrast and visibility
  const logoElement = await page.$('a[href="/"] svg');
  if (logoElement) {
    const logoStyles = await logoElement.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        color: styles.color,
        fill: styles.fill,
        width: styles.width,
        height: styles.height,
        display: styles.display,
        opacity: styles.opacity
      };
    });
    console.log('🎨 Logo styles:', logoStyles);
  }
  
  await page.screenshot({path: 'full_page_logo_audit.png', fullPage: true});
  console.log('📸 Full page screenshot saved');
  
  await browser.close();
})();
