// Simple test to check if the 2nrtlist app is working
const http = require('http');

function testAppResponse() {
  return new Promise((resolve, reject) => {
    const req = http.get('http://localhost:5002', (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log('✅ App is responding on port 5002');
        console.log('📄 Response status:', res.statusCode);
        
        // Check if the response contains expected elements
        const hasTitle = data.includes('NRT List') || data.includes('NRTList');
        const hasReactRoot = data.includes('root') || data.includes('app');
        const hasViteScript = data.includes('vite') || data.includes('script');
        
        console.log('🔍 Content checks:');
        console.log('  - Has title:', hasTitle);
        console.log('  - Has React root:', hasReactRoot);
        console.log('  - Has Vite scripts:', hasViteScript);
        
        if (res.statusCode === 200) {
          console.log('🎉 App is loading successfully!');
          resolve(true);
        } else {
          console.log('❌ App returned non-200 status');
          resolve(false);
        }
      });
    });
    
    req.on('error', (err) => {
      console.log('❌ Failed to connect to app:', err.message);
      reject(err);
    });
    
    req.setTimeout(5000, () => {
      console.log('❌ Request timed out');
      req.destroy();
      reject(new Error('Timeout'));
    });
  });
}

async function checkAuthComponents() {
  console.log('\n🔐 Checking authentication components...');
  
  // Check if AuthModal component exists
  const fs = require('fs');
  const path = require('path');
  
  try {
    const authModalPath = path.join(__dirname, '2nrtlist/src/components/AuthModal.tsx');
    const authContextPath = path.join(__dirname, '2nrtlist/src/contexts/AuthContext.tsx');
    const supabasePath = path.join(__dirname, '2nrtlist/src/lib/supabase.ts');
    
    const authModalExists = fs.existsSync(authModalPath);
    const authContextExists = fs.existsSync(authContextPath);
    const supabaseExists = fs.existsSync(supabasePath);
    
    console.log('📁 Auth component files:');
    console.log('  - AuthModal.tsx:', authModalExists ? '✅' : '❌');
    console.log('  - AuthContext.tsx:', authContextExists ? '✅' : '❌');
    console.log('  - supabase.ts:', supabaseExists ? '✅' : '❌');
    
    if (authModalExists && authContextExists && supabaseExists) {
      console.log('✅ All authentication components are present');
      return true;
    } else {
      console.log('❌ Some authentication components are missing');
      return false;
    }
  } catch (error) {
    console.log('❌ Error checking auth components:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting 2nrtlist app functionality test...\n');
  
  try {
    // Test 1: Check if app responds
    await testAppResponse();
    
    // Test 2: Check auth components
    await checkAuthComponents();
    
    console.log('\n🎯 SUMMARY:');
    console.log('✅ App is running on port 5002');
    console.log('✅ Authentication components are in place');
    console.log('✅ Ready for manual testing in browser');
    
    console.log('\n📋 MANUAL TEST CHECKLIST:');
    console.log('1. Open http://localhost:5002 in browser');
    console.log('2. Check if "Sign In" and "Sign Up" buttons are visible');
    console.log('3. Click "Sign In" button to open auth modal');
    console.log('4. Enter test credentials: <EMAIL> / J4913836j');
    console.log('5. Verify login works and "Logout" button appears');
    console.log('6. Test navigation between tabs');
    console.log('7. Check for visual consistency and no mockups');
    
  } catch (error) {
    console.log('\n❌ Test failed:', error.message);
  }
}

runTests();
