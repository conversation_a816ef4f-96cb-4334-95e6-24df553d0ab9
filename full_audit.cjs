const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // Track issues
  const issues = [];
  
  page.on('console', msg => {
    if (msg.type() === 'error') {
      issues.push({ type: 'error', text: msg.text() });
    }
  });
  
  await page.setViewport({ width: 1200, height: 800 });
  
  // Login
  await page.goto('http://localhost:5001/auth');
  await page.type('input[id="email"]', '<EMAIL>');
  await page.type('input[id="password"]', 'J4913836j');
  await page.click('button[type="submit"]');
  await page.waitForNavigation();
  
  // Check pages
  const pages = [
    '/app/dashboard',
    '/app/goals',
    '/app/progress',
    '/app/community',
    '/app/tools/breathing'
  ];
  
  for (const url of pages) {
    console.log(`Checking ${url}...`);
    await page.goto(`http://localhost:5001${url}`);
    await page.waitForTimeout(2000);
    const name = url.split('/').pop();
    await page.screenshot({ path: `audit_${name}.png`, fullPage: true });
  }
  
  console.log(`\nErrors found: ${issues.length}`);
  issues.forEach(i => console.log(i));
  
  await browser.close();
})();
