const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // Login first
  await page.goto('http://localhost:5001/auth');
  await page.type('input[id="email"]', '<EMAIL>');
  await page.type('input[id="password"]', 'J4913836j');
  await page.click('button[type="submit"]');
  await page.waitForNavigation();
  
  const pages = [
    { url: '/app/dashboard', name: 'Dashboard' },
    { url: '/app/goals', name: 'Goals' },
    { url: '/app/progress', name: 'Progress' },
    { url: '/app/community', name: 'Community' },
    { url: '/app/tools/breathing', name: 'Breathing Tools' },
    { url: '/app/tools/cravings', name: 'Craving Tools' },
    { url: '/app/settings', name: '<PERSON><PERSON><PERSON>' },
    { url: '/app/journal', name: 'Journal' },
    { url: '/app/log', name: 'Log Entry' }
  ];
  
  console.log('🔍 COMPREHENSIVE PAGE AUDIT BEGINS!\n');
  
  for (const pageInfo of pages) {
    console.log(`\n=== ${pageInfo.name.toUpperCase()} AUDIT ===`);
    
    try {
      await page.goto(`http://localhost:5001${pageInfo.url}`, { 
        waitUntil: 'networkidle2',
        timeout: 15000
      });
      
      const bodyText = await page.evaluate(() => document.body.innerText);
      const contentLength = bodyText.length;
      
      // Screenshot
      const filename = `audit_${pageInfo.name.toLowerCase().replace(/ /g, '_')}.png`;
      await page.screenshot({ path: filename, fullPage: true });
      
      // Analysis
      console.log(`📊 Content: ${contentLength} chars`);
      console.log(`📸 Screenshot: ${filename}`);
      
      // Check for issues
      const issues = [];
      if (contentLength < 200) issues.push('Minimal content');
      if (bodyText.includes('Loading')) issues.push('Contains loading text');
      if (bodyText.includes('Error')) issues.push('Contains error text');
      if (bodyText.includes('404')) issues.push('404 error');
      
      if (issues.length > 0) {
        console.log(`❌ Issues: ${issues.join(', ')}`);
      } else {
        console.log(`✅ No major issues detected`);
      }
      
    } catch (error) {
      console.log(`❌ Failed to load: ${error.message}`);
    }
  }
  
  console.log('\n🎯 AUDIT COMPLETE!');
  await browser.close();
})();
