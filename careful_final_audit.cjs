const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // Login
  await page.goto('http://localhost:5001/auth');
  await page.type('input[id="email"]', '<EMAIL>');
  await page.type('input[id="password"]', 'J4913836j');
  await page.click('button[type="submit"]');
  await page.waitForNavigation();
  
  const pages = [
    { url: '/app/dashboard', name: 'Dashboard' },
    { url: '/app/goals', name: 'Goals' },
    { url: '/app/progress', name: 'Progress' },
    { url: '/app/community', name: 'Community' },
    { url: '/app/log', name: 'Log Entry' }
  ];
  
  console.log('�� CAREFUL FINAL AUDIT - LONGER WAIT TIMES\n');
  
  for (const pageInfo of pages) {
    console.log(`Checking ${pageInfo.name}...`);
    await page.goto(`http://localhost:5001${pageInfo.url}`);
    
    // Wait longer for data loading
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    const bodyText = await page.evaluate(() => document.body.innerText);
    const contentLength = bodyText.length;
    const hasLoading = bodyText.includes('Loading');
    
    console.log(`${pageInfo.name}:`);
    console.log(`  📊 Content: ${contentLength} chars`);
    console.log(`  ${hasLoading ? '❌' : '✅'} Loading text: ${hasLoading ? 'PRESENT' : 'CLEAN'}`);
    console.log(`  ${contentLength > 400 ? '✅' : '❌'} Content: ${contentLength > 400 ? 'RICH' : 'MINIMAL'}`);
    
    if (contentLength < 200) {
      console.log(`  🔍 DEBUG - First 200 chars: ${bodyText.substring(0, 200)}`);
    }
    console.log('');
  }
  
  console.log('🏆 FINAL STEVE JOBS VERDICT!');
  await browser.close();
})();
