const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: {width: 1920, height: 1080}
  });
  
  const page = await browser.newPage();
  await page.setViewport({width: 1920, height: 1080});
  
  await page.goto('http://localhost:5001');
  await page.waitForSelector('body', {timeout: 10000});
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  console.log('�� VERIFYING HEADING CONSISTENCY FIXES...');
  
  const headings = await page.$$eval('h1, h2, h3, h4, h5, h6', elements => {
    return elements.map(el => ({
      tag: el.tagName,
      text: el.textContent?.substring(0, 30),
      fontSize: window.getComputedStyle(el).fontSize,
      fontWeight: window.getComputedStyle(el).fontWeight
    }));
  });
  
  console.log('\nH2 SIZES (should all be 60px now):');
  headings.filter(h => h.tag === 'H2').forEach((h, i) => {
    console.log(`  H2 ${i+1}: ${h.fontSize} - "${h.text}"`);
  });
  
  console.log('\nH3 SIZES (should all be 18px now):');
  headings.filter(h => h.tag === 'H3').forEach((h, i) => {
    console.log(`  H3 ${i+1}: ${h.fontSize} - "${h.text}"`);
  });
  
  // Check if fixes worked
  const h2Sizes = headings.filter(h => h.tag === 'H2').map(h => h.fontSize);
  const h3Sizes = headings.filter(h => h.tag === 'H3').map(h => h.fontSize);
  
  const uniqueH2Sizes = [...new Set(h2Sizes)];
  const uniqueH3Sizes = [...new Set(h3Sizes)];
  
  console.log(`\n✅ CONSISTENCY CHECK:`);
  console.log(`H2 unique sizes: ${uniqueH2Sizes.length} (should be 1)`);
  console.log(`H3 unique sizes: ${uniqueH3Sizes.length} (should be minimal)`);
  
  if (uniqueH2Sizes.length === 1) {
    console.log('🎉 H2 CONSISTENCY ACHIEVED!');
  } else {
    console.log('❌ H2 still has inconsistencies');
  }
  
  await page.screenshot({path: 'homepage_consistency_verified.png', fullPage: true});
  console.log('\n📸 Verification screenshot saved');
  
  await browser.close();
})();
