import * as vscode from 'vscode';

export function activate(context: vscode.ExtensionContext) {
    console.log('🚀 Our Cursor Auto Continue - Simple Copy & Execute');
    
    // The EXACT proven script from the 229-star GitHub repo
    const PROVEN_CURSOR_AUTO_RESUME_SCRIPT = `
// Our Cursor Auto Continue - Based on 229-star GitHub repo
// https://github.com/thelastbackspace/cursor-auto-resume
(function() {
    console.log('%c[Our Auto-Continue] 🚀 ACTIVATED!', 'color: #00FF00; font-weight: bold; font-size: 14px;');
    
    let isProcessing = false;
    const COOLDOWN_TIME = 3000; // 3 seconds cooldown
    let lastProcessTime = 0;
    let clickCount = 0;
    
    // Enhanced function to find resume links
    function findResumeLinks() {
        const links = [];
        
        // Enhanced text search - most reliable method
        const allElements = document.querySelectorAll('*');
        for (const element of allElements) {
            const text = element.textContent || '';
            const lowerText = text.toLowerCase();
            
            // More aggressive resume detection
            if (lowerText.includes('resume the conversation') || 
                lowerText.includes('resume conversation') ||
                lowerText.includes('continue the conversation') ||
                (lowerText.includes('resume') && lowerText.includes('conversation'))) {
                
                // Check if element or parent is clickable
                let clickableElement = element;
                for (let i = 0; i < 3; i++) {
                    const style = window.getComputedStyle(clickableElement);
                    if (style.cursor === 'pointer' || 
                        clickableElement.onclick || 
                        clickableElement.getAttribute('data-link') ||
                        clickableElement.tagName === 'A' ||
                        clickableElement.tagName === 'BUTTON' ||
                        clickableElement.getAttribute('role') === 'button') {
                        links.push(clickableElement);
                        break;
                    }
                    if (clickableElement.parentElement) {
                        clickableElement = clickableElement.parentElement;
                    } else {
                        break;
                    }
                }
            }
        }
        
        return [...new Set(links)]; // Remove duplicates
    }
    
    // Enhanced click function with multiple methods
    function clickResumeLink() {
        if (isProcessing) {
            console.log('[Our Auto-Continue] ⏳ Still processing, skipping...');
            return;
        }
        
        const now = Date.now();
        if (now - lastProcessTime < COOLDOWN_TIME) {
            console.log('[Our Auto-Continue] 🕐 Cooldown active, waiting...');
            return;
        }
        
        const resumeLinks = findResumeLinks();
        
        if (resumeLinks.length > 0) {
            isProcessing = true;
            lastProcessTime = now;
            clickCount++;
            
            console.log('%c[Our Auto-Continue] 🎯 Found ' + resumeLinks.length + ' resume link(s)! Auto-clicking in 1 second... (Click #' + clickCount + ')', 'color: #FF9800; font-weight: bold;');
            
            setTimeout(() => {
                try {
                    const link = resumeLinks[0];
                    
                    // Multiple click strategies
                    link.click();
                    
                    const mouseEvents = ['mousedown', 'mouseup', 'click'];
                    mouseEvents.forEach(eventType => {
                        const event = new MouseEvent(eventType, {
                            view: window,
                            bubbles: true,
                            cancelable: true
                        });
                        link.dispatchEvent(event);
                    });
                    
                    console.log('%c[Our Auto-Continue] ✅ Successfully clicked! (Click #' + clickCount + ')', 'color: #4CAF50; font-weight: bold;');
                    
                } catch (error) {
                    console.error('[Our Auto-Continue] ❌ Error clicking resume link:', error);
                } finally {
                    setTimeout(() => {
                        isProcessing = false;
                    }, 1000);
                }
            }, 1000);
        }
    }
    
    // Enhanced rate limit detection
    function checkForRateLimit() {
        const bodyText = document.body ? document.body.textContent.toLowerCase() : '';
        
        const triggers = [
            'default stop the agent',
            'we default stop the agent',
            'resume the conversation',
            'you can resume the conversation',
            'hit rate limit',
            'tool call limit',
            'reached the tool call limit',
            'continue the conversation',
            'agent stopped',
            'limit reached'
        ];
        
        const hasRateLimit = triggers.some(trigger => bodyText.includes(trigger));
        
        if (hasRateLimit) {
            console.log('%c[Our Auto-Continue] 🚨 Rate limit detected! Auto-clicking resume link...', 'color: #F44336; font-weight: bold;');
            setTimeout(clickResumeLink, 500);
        }
    }
    
    // Enhanced mutation observer
    const observer = new MutationObserver((mutations) => {
        let shouldCheck = false;
        
        for (const mutation of mutations) {
            if (mutation.type === 'childList') {
                for (const node of mutation.addedNodes) {
                    if (node.nodeType === Node.TEXT_NODE || node.nodeType === Node.ELEMENT_NODE) {
                        const text = (node.textContent || '').toLowerCase();
                        if (text.includes('stop') || 
                            text.includes('resume') || 
                            text.includes('limit') ||
                            text.includes('continue')) {
                            shouldCheck = true;
                            break;
                        }
                    }
                }
            }
            if (shouldCheck) break;
        }
        
        if (shouldCheck) {
            setTimeout(checkForRateLimit, 200);
        }
    });
    
    // Start observing
    function startObserving() {
        if (document.body) {
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                characterData: true
            });
            console.log('[Our Auto-Continue] 👀 Enhanced monitoring started');
        } else {
            const bodyWatcher = new MutationObserver(() => {
                if (document.body) {
                    startObserving();
                    bodyWatcher.disconnect();
                }
            });
            bodyWatcher.observe(document, { childList: true, subtree: true });
        }
    }
    
    startObserving();
    
    setTimeout(checkForRateLimit, 1000);
    setInterval(checkForRateLimit, 5000);
    
    window.addEventListener('scroll', () => {
        setTimeout(checkForRateLimit, 300);
    }, { passive: true });
    
    window.addEventListener('focus', () => {
        setTimeout(checkForRateLimit, 500);
    });
    
    console.log('%c[Our Auto-Continue] 🎉 READY TO AUTO-CLICK!', 'color: #4CAF50; font-weight: bold; font-size: 14px;');
})();
`;

    // Status bar item
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.text = '$(play) COPY & EXECUTE';
    statusBarItem.tooltip = 'Our Cursor Auto Continue: Click to copy & execute script!';
    statusBarItem.command = 'our-cursor-auto-continue.copy-execute';
    statusBarItem.show();

    // Copy & Execute function
    async function copyAndExecute() {
        try {
            // Copy to clipboard
            await vscode.env.clipboard.writeText(PROVEN_CURSOR_AUTO_RESUME_SCRIPT);
            
            // Update status bar to show success
            statusBarItem.text = '$(check) COPIED!';
            statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.prominentBackground');
            
            // Show success notification on the right (non-modal)
            vscode.window.showInformationMessage(
                '✅ Success! Script copied to clipboard. Paste in Cursor DevTools Console (F12 → Console → Paste → Enter)',
                { modal: false }
            );
            
            // Reset status bar after 3 seconds
            setTimeout(() => {
                statusBarItem.text = '$(play) COPY & EXECUTE';
                statusBarItem.backgroundColor = undefined;
            }, 3000);
            
        } catch (error) {
            vscode.window.showErrorMessage(`❌ Failed to copy script: ${error}`);
        }
    }

    // Register command
    const copyExecuteCommand = vscode.commands.registerCommand('our-cursor-auto-continue.copy-execute', copyAndExecute);

    // Auto-activate on startup after 3 seconds
    setTimeout(() => {
        console.log('🚀 Auto-activating Our Cursor Auto Continue...');
        copyAndExecute();
    }, 3000);

    context.subscriptions.push(statusBarItem, copyExecuteCommand);
    
    console.log('✅ Our Cursor Auto Continue Extension activated! Simple copy & execute mode ready.');
}

export function deactivate() {
    console.log('Our Cursor Auto Continue Extension deactivated');
}