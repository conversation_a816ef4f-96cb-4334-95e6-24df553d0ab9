# Cursor Marketplace Submission Email

Below is a template email for submitting the Auto Continue extension to the Cursor team.

Subject: New Cursor Extension Submission: Auto Continue

---

Dear Cursor Team,

I'm excited to submit a new extension for the Cursor Marketplace called "Auto Continue." This extension enhances the Claude AI experience by automatically continuing conversations when <PERSON> hits the tool call limit.

## Extension Details
- **Name**: Auto Continue
- **Description**: Automatically responds with 'continue' when <PERSON> hits the tool call limit
- **GitHub Repository**: https://github.com/risa-labs-inc/cursor-auto-continue
- **Version**: 0.1.0
- **Publisher**: Risa Labs

## Key Features
- **Automatic Continuation**: Detects when Claude <PERSON> reaches tool call limits and automatically continues the conversation
- **Visual Indicator**: Shows an "AUTO" indicator in the status bar and chat window for clear visibility
- **Theme-Aware UI**: Adapts to light and dark themes for seamless integration with Cursor
- **Configurable Timing**: Users can adjust the wait time before auto-continuing
- **Modal Information**: Provides a clean modal interface when clicked for additional information

## Installation
The VSIX package is attached to this email and is also available on our GitHub releases page. We've also published the extension to the VS Code Marketplace under the publisher "risalabs".

## Why Cursor Users Will Find This Valuable
Cursor users frequently work with Claude AI for extended sessions, and the tool call limit can interrupt their workflow. This extension eliminates that interruption, providing a smoother, more productive experience. Based on user feedback, this is one of the most commonly requested features.

## Testing
We've thoroughly tested this extension across macOS, Windows, and Linux environments with both light and dark themes. All DOM detection methods have been verified to work with the latest version of Cursor.

Please let me know if you need any additional information or if there are any adjustments needed to make this extension suitable for the Cursor Marketplace.

Thank you for your consideration,

[Your Name]
Risa Labs
[Your Contact Information]

---

Note: Remember to attach the .vsix file (located at `cursor-auto-continue-0.1.0.vsix`) when sending this email. 