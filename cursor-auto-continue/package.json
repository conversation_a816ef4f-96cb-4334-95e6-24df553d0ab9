{"name": "our-cursor-auto-continue", "displayName": "Our Cursor Auto Continue", "description": "FULLY AUTOMATIC - Auto-clicks 'resume conversation' links in Cursor when rate limits hit. No manual intervention required!", "version": "1.1.0", "publisher": "ourcursor", "icon": "icon.png", "repository": {"type": "git", "url": "https://github.com/risa-labs-inc/cursor-auto-continue"}, "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "keywords": ["cursor", "auto-continue", "automation", "ai", "conversation", "resume", "fully-automatic", "rate-limit", "claude"], "galleryBanner": {"color": "#2b78d4", "theme": "dark"}, "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "cursor-auto-continue.enable", "title": "Enable Cursor Auto Continue"}, {"command": "cursor-auto-continue.disable", "title": "Disable Cursor Auto Continue"}, {"command": "cursor-auto-continue.forceResume", "title": "Force Resume Conversation (Emergency)"}, {"command": "cursor-auto-continue.configure", "title": "Configure Auto Continue"}], "configuration": {"title": "Auto Continue", "properties": {"autoContinue.enabled": {"type": "boolean", "default": true, "description": "Enable/disable auto continue functionality"}, "autoContinue.waitTimeMs": {"type": "number", "default": 2000, "description": "Wait time in milliseconds before auto-continuing"}, "autoContinue.selectors": {"type": "object", "default": {"editorSelectors": [".aislash-editor-input[contenteditable=\"true\"]", "[role=\"textbox\"]", "textarea"], "buttonSelectors": [".composer-button-area .anysphere-button", "button[type=\"submit\"]", "button.send"], "limitIndicatorSelectors": [".markdown-section", ".anysphere-markdown-container-root", "section", ".markdown-link"]}, "description": "CSS selectors for detecting UI elements (advanced use only)"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "lint": "eslint src --ext ts", "package": "vsce package"}, "devDependencies": {"@types/node": "^16.11.7", "@types/vscode": "^1.60.0", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "eslint": "^8.26.0", "typescript": "^4.8.4", "vsce": "^2.15.0"}, "author": "Our Cursor Team", "license": "MIT"}