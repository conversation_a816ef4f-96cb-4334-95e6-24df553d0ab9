<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4286f4"/>
      <stop offset="100%" stop-color="#373277"/>
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#bg-gradient)" stroke="#ffffff" stroke-width="2"/>
  
  <!-- Auto-continue symbol -->
  <g fill="#ffffff">
    <!-- Main circular arrow -->
    <path d="M64,28 C83.882,28 100,44.118 100,64 C100,77.234 92.642,88.772 81.564,94.846 L89.882,103.164 C103.458,95.094 113,80.472 113,64 C113,37.49 91.51,16 64,16 C36.49,16 15,37.49 15,64 C15,80.472 24.542,95.094 38.118,103.164 L46.436,94.846 C35.358,88.772 28,77.234 28,64 C28,44.118 44.118,28 64,28 Z"/>
    
    <!-- Center play button -->
    <polygon points="48,48 80,64 48,80"/>
  </g>
</svg> 