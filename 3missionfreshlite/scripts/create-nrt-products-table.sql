-- Create nrt_products table for storing NRT product information
CREATE TABLE IF NOT EXISTS public.nrt_products (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL, -- patch, gum, lozenge, prescription
    category VARCHAR(100) NOT NULL,
    brand VARCHAR(100),
    strength VARCHAR(50),
    description TEXT,
    price_range VARCHAR(50),
    effectiveness_rating DECIMAL(3,1) DEFAULT 0,
    user_rating DECIMAL(3,1) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    pros TEXT[], -- Array of pros
    cons TEXT[], -- Array of cons
    best_for TEXT,
    duration VARCHAR(100),
    success_rate INTEGER DEFAULT 0, -- Percentage
    where_to_buy TEXT[], -- Array of retailers
    active_ingredient VARCHAR(100),
    side_effects TEXT[], -- Array of side effects
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_nrt_products_type ON public.nrt_products(type);
CREATE INDEX IF NOT EXISTS idx_nrt_products_category ON public.nrt_products(category);
CREATE INDEX IF NOT EXISTS idx_nrt_products_is_active ON public.nrt_products(is_active);
CREATE INDEX IF NOT EXISTS idx_nrt_products_effectiveness ON public.nrt_products(effectiveness_rating DESC);

-- Enable Row Level Security
ALTER TABLE public.nrt_products ENABLE ROW LEVEL SECURITY;

-- Create policy to allow public read access
CREATE POLICY IF NOT EXISTS "Allow public read access" ON public.nrt_products
    FOR SELECT USING (true);

-- Grant permissions
GRANT SELECT ON public.nrt_products TO anon;
GRANT SELECT ON public.nrt_products TO authenticated;
