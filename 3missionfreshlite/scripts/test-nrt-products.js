import { createClient } from '@supabase/supabase-js';

// Test NRT products database access
console.log('=== TESTING NRT PRODUCTS DATABASE ACCESS ===');

async function testNRTProducts() {
  try {
    console.log('Testing without schema...');
    const supabaseNoSchema = createClient(
      'https://yekarqanirdkdckimpna.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'
    );
    
    const { data: nrtData, error: nrtError } = await supabaseNoSchema
      .from('nrt_products')
      .select('*')
      .limit(3);
    
    if (nrtError) {
      console.log('❌ nrt_products error:', nrtError.message);
    } else {
      console.log('✅ nrt_products accessible:', nrtData.length, 'records');
      if (nrtData.length > 0) {
        console.log('Sample record:', JSON.stringify(nrtData[0], null, 2));
      }
    }
    
    // Check alternative table names
    const tableNames = ['products', 'nrt_product', 'nicotine_products', 'replacement_products'];
    
    for (const tableName of tableNames) {
      try {
        const { data, error } = await supabaseNoSchema
          .from(tableName)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ ${tableName} error:`, error.message);
        } else {
          console.log(`✅ ${tableName} accessible:`, data.length, 'records');
        }
      } catch (err) {
        console.log(`❌ ${tableName} exception:`, err.message);
      }
    }
  } catch (err) {
    console.error('Connection error:', err.message);
  }
}

testNRTProducts();
