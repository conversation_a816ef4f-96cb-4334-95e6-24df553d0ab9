// Test script to check dashboard data access
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'

const supabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'mission_fresh'
  }
})

async function testDashboardData() {
  console.log('=== TESTING DASHBOARD DATA ACCESS ===')
  
  // Test authentication
  console.log('\n1. Testing authentication...')
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    console.log('Auth result:', { 
      hasUser: !!user, 
      userId: user?.id, 
      userEmail: user?.email,
      authError: authError?.message 
    })
    
    if (!user) {
      console.log('No authenticated user - trying to sign in with test user...')
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'J4913836j'
      })
      console.log('Sign in result:', { 
        hasUser: !!signInData.user, 
        userId: signInData.user?.id,
        signInError: signInError?.message 
      })
    }
  } catch (error) {
    console.error('Auth test failed:', error)
  }
  
  // Test user_goals table access
  console.log('\n2. Testing user_goals table...')
  try {
    // First, try to get all goals to see the structure
    const { data: allGoals, error: allGoalsError } = await supabase
      .from('user_goals')
      .select('*')
      .limit(5)
    
    console.log('All goals query result:', { 
      hasData: !!allGoals, 
      count: allGoals?.length,
      sampleData: allGoals?.[0],
      error: allGoalsError?.message 
    })
    
    // Try to get goals for specific user
    const testUserId = '068215cb-d0d2-48c7-91c4-a311d76489f9'
    const { data: userGoals, error: userGoalsError } = await supabase
      .from('user_goals')
      .select('*')
      .eq('user_id', testUserId)
    
    console.log('User goals query result:', { 
      hasData: !!userGoals, 
      count: userGoals?.length,
      data: userGoals,
      error: userGoalsError?.message 
    })
    
  } catch (error) {
    console.error('Goals test failed:', error)
  }
  
  // Test table structure
  console.log('\n3. Testing table structure...')
  try {
    const { data: tableInfo, error: tableError } = await supabase
      .rpc('get_table_columns', { table_name: 'user_goals' })
    
    console.log('Table structure:', { tableInfo, tableError: tableError?.message })
  } catch (error) {
    console.log('Table structure query not available:', error.message)
  }
}

testDashboardData().catch(console.error)
