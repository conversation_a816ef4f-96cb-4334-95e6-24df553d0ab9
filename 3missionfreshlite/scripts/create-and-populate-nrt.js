import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Create and populate NRT products table
console.log('=== CREATING AND POPULATING NRT PRODUCTS TABLE ===');

const supabase = createClient(
  'https://yekarqanirdkdckimpna.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'
);

async function createAndPopulateNRT() {
  try {
    // Step 1: Create the table using SQL
    console.log('📋 Creating nrt_products table...');
    
    const createTableSQL = `
-- Create nrt_products table for storing NRT product information
CREATE TABLE IF NOT EXISTS public.nrt_products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    category VARCHAR(100) NOT NULL,
    brand VARCHAR(100),
    strength VARCHAR(50),
    description TEXT,
    price_range VARCHAR(50),
    effectiveness_rating DECIMAL(3,1) DEFAULT 0,
    user_rating DECIMAL(3,1) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    pros TEXT[],
    cons TEXT[],
    best_for TEXT,
    duration VARCHAR(100),
    success_rate INTEGER DEFAULT 0,
    where_to_buy TEXT[],
    active_ingredient VARCHAR(100),
    side_effects TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_nrt_products_type ON public.nrt_products(type);
CREATE INDEX IF NOT EXISTS idx_nrt_products_category ON public.nrt_products(category);
CREATE INDEX IF NOT EXISTS idx_nrt_products_is_active ON public.nrt_products(is_active);

-- Enable Row Level Security
ALTER TABLE public.nrt_products ENABLE ROW LEVEL SECURITY;

-- Create policy to allow public read access
DROP POLICY IF EXISTS "Allow public read access" ON public.nrt_products;
CREATE POLICY "Allow public read access" ON public.nrt_products
    FOR SELECT USING (true);

-- Grant permissions
GRANT SELECT ON public.nrt_products TO anon;
GRANT SELECT ON public.nrt_products TO authenticated;
    `;
    
    const { error: createError } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    
    if (createError) {
      console.log('❌ Error creating table (trying direct approach):', createError.message);
    } else {
      console.log('✅ Table created successfully');
    }
    
    // Step 2: Insert the NRT products data
    console.log('💊 Adding NRT products data...');
    
    const nrtProducts = [
      {
        name: "Nicorette Clear Patch",
        type: "patch",
        category: "patch",
        brand: "Nicorette",
        strength: "21mg/24hr",
        description: "Clear, discreet nicotine patch for 24-hour craving control. Step 1 of 3-step program.",
        price_range: "$25-35",
        effectiveness_rating: 4.2,
        user_rating: 4.1,
        review_count: 156,
        pros: ["24-hour craving control", "Clear and discreet", "Proven step-down program", "Water-resistant"],
        cons: ["May cause skin irritation", "Vivid dreams possible", "Need to rotate application sites", "Takes time to take effect"],
        best_for: "Heavy smokers (20+ cigarettes/day)",
        duration: "8-12 weeks",
        success_rate: 65,
        where_to_buy: ["CVS", "Walgreens", "Amazon", "Target"],
        active_ingredient: "Nicotine",
        side_effects: ["Skin irritation", "Vivid dreams", "Sleep disturbances"],
        is_active: true
      },
      {
        name: "NicoDerm CQ Patch",
        type: "patch",
        category: "patch",
        brand: "NicoDerm CQ",
        strength: "14mg/24hr",
        description: "America's #1 selling nicotine patch. Step 2 of 3-step program for moderate smokers.",
        price_range: "$30-40",
        effectiveness_rating: 4.3,
        user_rating: 4.2,
        review_count: 203,
        pros: ["Most popular brand", "Extended-release formula", "Good adhesion", "Comprehensive support program"],
        cons: ["More expensive", "Can be visible on skin", "May cause local irritation", "Requires prescription for higher doses"],
        best_for: "Moderate smokers (10-20 cigarettes/day)",
        duration: "6-10 weeks",
        success_rate: 68,
        where_to_buy: ["Pharmacy chains", "Online retailers", "Grocery stores"],
        active_ingredient: "Nicotine",
        side_effects: ["Skin redness", "Itching", "Sleep issues"],
        is_active: true
      },
      {
        name: "Nicorette Gum Original",
        type: "gum",
        category: "gum",
        brand: "Nicorette",
        strength: "4mg",
        description: "Sugar-free nicotine gum for fast craving relief. Original flavor for immediate control.",
        price_range: "$15-25",
        effectiveness_rating: 4.0,
        user_rating: 3.8,
        review_count: 289,
        pros: ["Fast-acting relief", "Portable and discreet", "Multiple flavors available", "Control your dosing"],
        cons: ["Requires proper chewing technique", "May cause jaw fatigue", "Taste can be unpleasant", "Digestive issues possible"],
        best_for: "People who need immediate craving control",
        duration: "12 weeks",
        success_rate: 58,
        where_to_buy: ["All major pharmacies", "Convenience stores", "Online"],
        active_ingredient: "Nicotine",
        side_effects: ["Jaw soreness", "Hiccups", "Nausea", "Heartburn"],
        is_active: true
      },
      {
        name: "Nicorette Fruit Chill Gum",
        type: "gum",
        category: "gum",
        brand: "Nicorette",
        strength: "2mg",
        description: "Cool fruit flavor nicotine gum for light to moderate smokers. Refreshing taste experience.",
        price_range: "$18-28",
        effectiveness_rating: 3.9,
        user_rating: 4.0,
        review_count: 164,
        pros: ["Pleasant fruit flavor", "Cooling sensation", "Lower nicotine strength", "Good for light smokers"],
        cons: ["Lower nicotine may not satisfy heavy smokers", "Artificial taste", "More expensive per piece", "Limited availability"],
        best_for: "Light smokers (less than 10 cigarettes/day)",
        duration: "12 weeks",
        success_rate: 55,
        where_to_buy: ["CVS", "Walgreens", "Rite Aid", "Amazon"],
        active_ingredient: "Nicotine",
        side_effects: ["Mild stomach upset", "Jaw discomfort"],
        is_active: true
      },
      {
        name: "Nicorette Mini Lozenge",
        type: "lozenge",
        category: "lozenge",
        brand: "Nicorette",
        strength: "4mg",
        description: "Discreet mini lozenge for fast, effective craving relief. Dissolves completely.",
        price_range: "$20-30",
        effectiveness_rating: 4.1,
        user_rating: 4.3,
        review_count: 127,
        pros: ["Very discreet", "Fast absorption", "No chewing required", "Mint flavor"],
        cons: ["Can be expensive", "May cause throat irritation", "Limited flavor options", "Takes practice to use correctly"],
        best_for: "Professional settings, public use",
        duration: "12 weeks",
        success_rate: 62,
        where_to_buy: ["Major pharmacies", "Online retailers", "Medical centers"],
        active_ingredient: "Nicotine",
        side_effects: ["Throat irritation", "Nausea", "Hiccups"],
        is_active: true
      }
    ];
    
    // Insert products one by one to handle arrays properly
    let successCount = 0;
    for (const product of nrtProducts) {
      const { error: insertError } = await supabase
        .from('nrt_products')
        .insert([product]);
      
      if (insertError) {
        console.log(`❌ Error inserting ${product.name}:`, insertError.message);
      } else {
        console.log(`✅ Added: ${product.name}`);
        successCount++;
      }
    }
    
    console.log(`\n🎉 Successfully added ${successCount}/${nrtProducts.length} NRT products!`);
    
    // Verify the data
    const { data: verifyData, error: verifyError } = await supabase
      .from('nrt_products')
      .select('id, name, type, effectiveness_rating')
      .order('id');
    
    if (verifyError) {
      console.error('❌ Error verifying data:', verifyError.message);
    } else {
      console.log('\n📊 Database verification:');
      console.log(`Total NRT products: ${verifyData.length}`);
      verifyData.forEach(product => {
        console.log(`  ${product.id}. ${product.name} (${product.type}) - ${product.effectiveness_rating}⭐`);
      });
    }
    
  } catch (err) {
    console.error('💥 Fatal error:', err.message);
  }
}

createAndPopulateNRT();
