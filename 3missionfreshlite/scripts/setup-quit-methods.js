// Setup script to create quit_methods table and data
// HOLY RULE 0001: Real database setup to eliminate hardcoded data

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'

const supabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'mission_fresh'
  }
})

const quitMethodsData = [
  {
    name: 'Cold Turkey',
    description: 'Complete and immediate cessation of nicotine use without gradual reduction or substitutes.',
    timeframe: '1-2 weeks',
    difficulty: 'High',
    success_rate_min: 3,
    success_rate_max: 15,
    best_for: ['Highly motivated individuals', 'Those who prefer direct approach', 'Previous successful attempts'],
    considerations: ['Requires strong willpower', 'Higher relapse rates initially', 'More intense withdrawal symptoms'],
    sort_order: 1
  },
  {
    name: 'Gradual Reduction',
    description: 'Slowly reducing nicotine intake over time by decreasing frequency or amount.',
    timeframe: '4-12 weeks',
    difficulty: 'Medium',
    success_rate_min: 15,
    success_rate_max: 30,
    best_for: ['Beginners to quitting', 'Those who have tried cold turkey unsuccessfully', 'People with high stress levels'],
    considerations: ['Requires discipline to stick to schedule', 'May prolong withdrawal symptoms', 'Risk of reverting to higher usage'],
    sort_order: 2
  },
  {
    name: 'Nicotine Replacement',
    description: 'Using medically approved nicotine products to ease withdrawal while breaking behavioral habits.',
    timeframe: '8-12 weeks',
    difficulty: 'Low',
    success_rate_min: 25,
    success_rate_max: 45,
    best_for: ['Heavy smokers', 'Those with strong physical addiction', 'First-time quitters'],
    considerations: ['May maintain nicotine dependence temporarily', 'Requires proper medical guidance', 'Can be expensive'],
    sort_order: 3
  },
  {
    name: 'Prescription Medication',
    description: 'Doctor-prescribed medications like Chantix or Zyban to reduce cravings and withdrawal symptoms.',
    timeframe: '12-24 weeks',
    difficulty: 'Medium',
    success_rate_min: 30,
    success_rate_max: 50,
    best_for: ['Those with previous failed attempts', 'Heavy long-term users', 'People with medical supervision'],
    considerations: ['Requires medical consultation', 'Potential side effects', 'Not suitable for everyone'],
    sort_order: 4
  },
  {
    name: 'Behavioral Therapy',
    description: 'Working with counselors to change thought patterns and develop coping strategies.',
    timeframe: '8-16 weeks',
    difficulty: 'Medium',
    success_rate_min: 20,
    success_rate_max: 40,
    best_for: ['Those who smoke due to stress', 'People with strong psychological addiction', 'Those seeking long-term solutions'],
    considerations: ['Requires time commitment', 'May need to find the right therapist', 'Progress can be gradual'],
    sort_order: 5
  },
  {
    name: 'Support Groups',
    description: 'Joining group sessions or online communities for motivation and shared experiences.',
    timeframe: 'Ongoing',
    difficulty: 'Low',
    success_rate_min: 25,
    success_rate_max: 35,
    best_for: ['Social learners', 'Those who benefit from community', 'People seeking accountability'],
    considerations: ['Requires active participation', 'May not suit introverted individuals', 'Quality varies by group'],
    sort_order: 6
  }
]

async function setupQuitMethods() {
  try {
    console.log('Setting up quit_methods data...')
    
    // First check if data already exists
    const { data: existingData, error: checkError } = await supabase
      .from('quit_methods')
      .select('id')
      .limit(1)
    
    if (checkError) {
      console.error('Table might not exist:', checkError.message)
    }
    
    if (existingData && existingData.length > 0) {
      console.log('Quit methods data already exists')
      return
    }
    
    // Insert the data
    const { data, error } = await supabase
      .from('quit_methods')
      .insert(quitMethodsData)
      .select()
    
    if (error) {
      console.error('Error inserting quit methods:', error)
      return
    }
    
    console.log('Successfully created quit methods:', data?.length || 0, 'records')
    
  } catch (err) {
    console.error('Setup error:', err)
  }
}

setupQuitMethods()
