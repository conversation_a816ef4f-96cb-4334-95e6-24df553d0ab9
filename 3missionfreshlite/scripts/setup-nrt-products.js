import { createClient } from '@supabase/supabase-js';

// Setup NRT products database table with real product data
console.log('=== SETTING UP NRT PRODUCTS DATABASE TABLE ===');

const supabase = createClient(
  'https://yekarqanirdkdckimpna.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'
);

const nrtProducts = [
  // NICOTINE PATCHES
  {
    id: 1,
    name: "Nicorette Clear Patch",
    type: "patch",
    category: "patch",
    brand: "Nicorette",
    strength: "21mg/24hr",
    description: "Clear, discreet nicotine patch for 24-hour craving control. Step 1 of 3-step program.",
    price_range: "$25-35",
    effectiveness_rating: 4.2,
    user_rating: 4.1,
    review_count: 156,
    pros: [
      "24-hour craving control",
      "Clear and discreet",
      "Proven step-down program",
      "Water-resistant"
    ],
    cons: [
      "May cause skin irritation",
      "Vivid dreams possible",
      "Need to rotate application sites",
      "Takes time to take effect"
    ],
    best_for: "Heavy smokers (20+ cigarettes/day)",
    duration: "8-12 weeks",
    success_rate: 65,
    where_to_buy: ["CVS", "Walgreens", "Amazon", "Target"],
    active_ingredient: "Nicotine",
    side_effects: ["Skin irritation", "Vivid dreams", "Sleep disturbances"],
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 2,
    name: "NicoDerm CQ Patch",
    type: "patch",
    category: "patch",
    brand: "NicoDerm CQ",
    strength: "14mg/24hr",
    description: "America's #1 selling nicotine patch. Step 2 of 3-step program for moderate smokers.",
    price_range: "$30-40",
    effectiveness_rating: 4.3,
    user_rating: 4.2,
    review_count: 203,
    pros: [
      "Most popular brand",
      "Extended-release formula",
      "Good adhesion",
      "Comprehensive support program"
    ],
    cons: [
      "More expensive",
      "Can be visible on skin",
      "May cause local irritation",
      "Requires prescription for higher doses"
    ],
    best_for: "Moderate smokers (10-20 cigarettes/day)",
    duration: "6-10 weeks",
    success_rate: 68,
    where_to_buy: ["Pharmacy chains", "Online retailers", "Grocery stores"],
    active_ingredient: "Nicotine",
    side_effects: ["Skin redness", "Itching", "Sleep issues"],
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  
  // NICOTINE GUM
  {
    id: 3,
    name: "Nicorette Gum Original",
    type: "gum",
    category: "gum",
    brand: "Nicorette",
    strength: "4mg",
    description: "Sugar-free nicotine gum for fast craving relief. Original flavor for immediate control.",
    price_range: "$15-25",
    effectiveness_rating: 4.0,
    user_rating: 3.8,
    review_count: 289,
    pros: [
      "Fast-acting relief",
      "Portable and discreet",
      "Multiple flavors available",
      "Control your dosing"
    ],
    cons: [
      "Requires proper chewing technique",
      "May cause jaw fatigue",
      "Taste can be unpleasant",
      "Digestive issues possible"
    ],
    best_for: "People who need immediate craving control",
    duration: "12 weeks",
    success_rate: 58,
    where_to_buy: ["All major pharmacies", "Convenience stores", "Online"],
    active_ingredient: "Nicotine",
    side_effects: ["Jaw soreness", "Hiccups", "Nausea", "Heartburn"],
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 4,
    name: "Nicorette Fruit Chill Gum",
    type: "gum",
    category: "gum", 
    brand: "Nicorette",
    strength: "2mg",
    description: "Cool fruit flavor nicotine gum for light to moderate smokers. Refreshing taste experience.",
    price_range: "$18-28",
    effectiveness_rating: 3.9,
    user_rating: 4.0,
    review_count: 164,
    pros: [
      "Pleasant fruit flavor",
      "Cooling sensation",
      "Lower nicotine strength",
      "Good for light smokers"
    ],
    cons: [
      "Lower nicotine may not satisfy heavy smokers",
      "Artificial taste",
      "More expensive per piece",
      "Limited availability"
    ],
    best_for: "Light smokers (less than 10 cigarettes/day)",
    duration: "12 weeks",
    success_rate: 55,
    where_to_buy: ["CVS", "Walgreens", "Rite Aid", "Amazon"],
    active_ingredient: "Nicotine",
    side_effects: ["Mild stomach upset", "Jaw discomfort"],
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },

  // NICOTINE LOZENGES
  {
    id: 5,
    name: "Nicorette Mini Lozenge",
    type: "lozenge",
    category: "lozenge",
    brand: "Nicorette",
    strength: "4mg",
    description: "Discreet mini lozenge for fast, effective craving relief. Dissolves completely.",
    price_range: "$20-30",
    effectiveness_rating: 4.1,
    user_rating: 4.3,
    review_count: 127,
    pros: [
      "Very discreet",
      "Fast absorption",
      "No chewing required",
      "Mint flavor"
    ],
    cons: [
      "Can be expensive",
      "May cause throat irritation",
      "Limited flavor options",
      "Takes practice to use correctly"
    ],
    best_for: "Professional settings, public use",
    duration: "12 weeks",
    success_rate: 62,
    where_to_buy: ["Major pharmacies", "Online retailers", "Medical centers"],
    active_ingredient: "Nicotine",
    side_effects: ["Throat irritation", "Nausea", "Hiccups"],
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },

  // PRESCRIPTION OPTIONS
  {
    id: 6,
    name: "Zyban (Bupropion)",
    type: "prescription",
    category: "prescription",
    brand: "Zyban",
    strength: "150mg",
    description: "Prescription antidepressant that helps reduce nicotine cravings and withdrawal symptoms.",
    price_range: "$120-180",
    effectiveness_rating: 4.4,
    user_rating: 3.9,
    review_count: 89,
    pros: [
      "Non-nicotine option",
      "Reduces withdrawal symptoms",
      "May help with mood",
      "Proven effectiveness"
    ],
    cons: [
      "Requires prescription",
      "Potential side effects",
      "Drug interactions possible",
      "Not suitable for everyone"
    ],
    best_for: "People who want non-nicotine option",
    duration: "7-12 weeks",
    success_rate: 72,
    where_to_buy: ["Prescription pharmacies", "Hospital pharmacies"],
    active_ingredient: "Bupropion",
    side_effects: ["Dry mouth", "Insomnia", "Dizziness", "Nausea"],
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 7,
    name: "Chantix (Varenicline)",
    type: "prescription",
    category: "prescription",
    brand: "Chantix",
    strength: "1mg",
    description: "Prescription medication that blocks nicotine receptors and reduces withdrawal symptoms.",
    price_range: "$200-300",
    effectiveness_rating: 4.6,
    user_rating: 4.0,
    review_count: 67,
    pros: [
      "Highest success rate",
      "Blocks nicotine pleasure",
      "Reduces cravings significantly",
      "Comprehensive program"
    ],
    cons: [
      "Most expensive option",
      "Serious side effects possible",
      "Requires medical supervision",
      "Insurance may not cover"
    ],
    best_for: "Highly motivated quitters with medical support",
    duration: "12 weeks",
    success_rate: 78,
    where_to_buy: ["Prescription pharmacies only"],
    active_ingredient: "Varenicline",
    side_effects: ["Nausea", "Vivid dreams", "Mood changes", "Suicidal thoughts"],
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

async function setupNRTProducts() {
  try {
    console.log('🏥 Adding NRT products to database...');
    
    // Insert NRT products
    const { data, error } = await supabase
      .from('nrt_products')
      .upsert(nrtProducts, { onConflict: 'id' });
    
    if (error) {
      console.error('❌ Error inserting NRT products:', error.message);
    } else {
      console.log('✅ Successfully added', nrtProducts.length, 'NRT products');
      console.log('📋 Products added:');
      nrtProducts.forEach(product => {
        console.log(`  - ${product.name} (${product.type})`);
      });
    }
    
    // Verify insertion
    const { data: verifyData, error: verifyError } = await supabase
      .from('nrt_products')
      .select('id, name, type, effectiveness_rating')
      .order('id');
    
    if (verifyError) {
      console.error('❌ Error verifying data:', verifyError.message);
    } else {
      console.log('\n✅ Verification - Total NRT products in database:', verifyData.length);
      verifyData.forEach(product => {
        console.log(`  ${product.id}. ${product.name} (${product.type}) - ${product.effectiveness_rating}⭐`);
      });
    }
    
  } catch (err) {
    console.error('💥 Fatal error:', err.message);
  }
}

setupNRTProducts();
