// Check what tables exist in the mission_fresh schema
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'

const supabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'mission_fresh'
  }
})

async function checkTables() {
  console.log('Checking available tables...')
  
  // Try different potential table names
  const potentialTables = [
    'quit_methods',
    'quitting_methods',
    'methods',
    'quit_strategies',
    'nrt_products',
    'products',
    'guides',
    'quit_guides'
  ]
  
  for (const tableName of potentialTables) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1)
      
      if (!error) {
        console.log(`✅ Table "${tableName}" exists with ${data?.length || 0} records`)
        if (data && data.length > 0) {
          console.log('Sample record:', JSON.stringify(data[0], null, 2))
        }
      } else {
        console.log(`❌ Table "${tableName}" error:`, error.message)
      }
    } catch (err) {
      console.log(`❌ Table "${tableName}" failed:`, err.message)
    }
  }
}

checkTables()
