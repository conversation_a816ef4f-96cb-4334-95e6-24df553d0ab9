import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

// Add NRT products to existing products table
console.log('=== ADDING NRT PRODUCTS TO EXISTING PRODUCTS TABLE ===');

const supabase = createClient(
  'https://yekarqanirdkdckimpna.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'
);

// Use a consistent owner_id (you can replace this with a real user ID)
const OWNER_ID = '1ce2d7d6-e1ae-4f10-b9cf-7438caae64d0';

const nrtProducts = [
  {
    id: uuidv4(),
    owner_id: OWNER_ID,
    name: "Nicorette Clear Patch 21mg",
    slug: "nicorette-clear-patch-21mg",
    tagline: "Clear, discreet 24-hour nicotine patch",
    description: "Nicorette Clear Patch provides 24-hour craving control with a clear, discreet design. Step 1 of the proven 3-step program for heavy smokers.",
    website_url: null,
    logo_url: null,
    gallery_urls: null,
    pricing_model: "one-time",
    launch_date: null,
    category_id: null,
    is_coming_soon: false,
    specific_attributes: {
      brand: "Nicorette",
      price: "$25-35",
      category: "patch",
      type: "patch",
      strength: "21mg/24hr",
      rating: 4.2,
      reviews: 156,
      effectiveness: 4.2,
      success_rate: 65,
      duration: "8-12 weeks",
      pros: ["24-hour craving control", "Clear and discreet", "Proven step-down program", "Water-resistant"],
      cons: ["May cause skin irritation", "Vivid dreams possible", "Need to rotate application sites", "Takes time to take effect"],
      best_for: "Heavy smokers (20+ cigarettes/day)",
      where_to_buy: ["CVS", "Walgreens", "Amazon", "Target"],
      active_ingredient: "Nicotine",
      side_effects: ["Skin irritation", "Vivid dreams", "Sleep disturbances"]
    },
    status: "approved"
  },
  {
    id: uuidv4(),
    owner_id: OWNER_ID,
    name: "NicoDerm CQ Patch 14mg",
    slug: "nicoderm-cq-patch-14mg",
    tagline: "America's #1 selling nicotine patch",
    description: "NicoDerm CQ Patch offers extended-release nicotine delivery for moderate smokers. Step 2 of the comprehensive 3-step program.",
    website_url: null,
    logo_url: null,
    gallery_urls: null,
    pricing_model: "one-time",
    launch_date: null,
    category_id: null,
    is_coming_soon: false,
    specific_attributes: {
      brand: "NicoDerm CQ",
      price: "$30-40",
      category: "patch",
      type: "patch",
      strength: "14mg/24hr",
      rating: 4.3,
      reviews: 203,
      effectiveness: 4.3,
      success_rate: 68,
      duration: "6-10 weeks",
      pros: ["Most popular brand", "Extended-release formula", "Good adhesion", "Comprehensive support program"],
      cons: ["More expensive", "Can be visible on skin", "May cause local irritation", "Requires prescription for higher doses"],
      best_for: "Moderate smokers (10-20 cigarettes/day)",
      where_to_buy: ["Pharmacy chains", "Online retailers", "Grocery stores"],
      active_ingredient: "Nicotine",
      side_effects: ["Skin redness", "Itching", "Sleep issues"]
    },
    status: "approved"
  },
  {
    id: uuidv4(),
    owner_id: OWNER_ID,
    name: "Nicorette Fruit Chill Gum 2mg",
    slug: "nicorette-fruit-chill-gum-2mg",
    tagline: "Cool fruit flavor nicotine gum",
    description: "Nicorette Fruit Chill Gum provides fast craving relief with a refreshing fruit flavor. Perfect for light to moderate smokers.",
    website_url: null,
    logo_url: null,
    gallery_urls: null,
    pricing_model: "one-time",
    launch_date: null,
    category_id: null,
    is_coming_soon: false,
    specific_attributes: {
      brand: "Nicorette",
      price: "$18-28",
      category: "gum",
      type: "gum",
      strength: "2mg",
      rating: 4.0,
      reviews: 164,
      effectiveness: 3.9,
      success_rate: 55,
      duration: "12 weeks",
      pros: ["Pleasant fruit flavor", "Cooling sensation", "Lower nicotine strength", "Good for light smokers"],
      cons: ["Lower nicotine may not satisfy heavy smokers", "Artificial taste", "More expensive per piece", "Limited availability"],
      best_for: "Light smokers (less than 10 cigarettes/day)",
      where_to_buy: ["CVS", "Walgreens", "Rite Aid", "Amazon"],
      active_ingredient: "Nicotine",
      side_effects: ["Mild stomach upset", "Jaw discomfort"]
    },
    status: "approved"
  },
  {
    id: uuidv4(),
    owner_id: OWNER_ID,
    name: "Nicorette Mini Lozenge 4mg",
    slug: "nicorette-mini-lozenge-4mg",
    tagline: "Discreet mini lozenge for fast relief",
    description: "Nicorette Mini Lozenge offers discreet, fast-acting craving relief that dissolves completely. Perfect for professional settings.",
    website_url: null,
    logo_url: null,
    gallery_urls: null,
    pricing_model: "one-time",
    launch_date: null,
    category_id: null,
    is_coming_soon: false,
    specific_attributes: {
      brand: "Nicorette",
      price: "$20-30",
      category: "lozenge",
      type: "lozenge",
      strength: "4mg",
      rating: 4.3,
      reviews: 127,
      effectiveness: 4.1,
      success_rate: 62,
      duration: "12 weeks",
      pros: ["Very discreet", "Fast absorption", "No chewing required", "Mint flavor"],
      cons: ["Can be expensive", "May cause throat irritation", "Limited flavor options", "Takes practice to use correctly"],
      best_for: "Professional settings, public use",
      where_to_buy: ["Major pharmacies", "Online retailers", "Medical centers"],
      active_ingredient: "Nicotine",
      side_effects: ["Throat irritation", "Nausea", "Hiccups"]
    },
    status: "approved"
  }
];

async function addNRTProducts() {
  try {
    console.log('💊 Adding NRT products to products table...');
    
    let successCount = 0;
    for (const product of nrtProducts) {
      const { error: insertError } = await supabase
        .from('products')
        .insert([product]);
      
      if (insertError) {
        console.log(`❌ Error inserting ${product.name}:`, insertError.message);
      } else {
        console.log(`✅ Added: ${product.name}`);
        successCount++;
      }
    }
    
    console.log(`\n🎉 Successfully added ${successCount}/${nrtProducts.length} NRT products!`);
    
    // Verify the data by checking for NRT products
    const { data: verifyData, error: verifyError } = await supabase
      .from('products')
      .select('id, name, specific_attributes')
      .not('specific_attributes->>category', 'is', null)
      .order('created_at');
    
    if (verifyError) {
      console.error('❌ Error verifying data:', verifyError.message);
    } else {
      console.log('\n📊 Database verification:');
      console.log(`Total products with categories: ${verifyData.length}`);
      verifyData.forEach(product => {
        const attrs = product.specific_attributes;
        console.log(`  - ${product.name} (${attrs?.category || 'unknown'}) - ${attrs?.rating || 'N/A'}⭐`);
      });
    }
    
  } catch (err) {
    console.error('💥 Fatal error:', err.message);
  }
}

addNRTProducts();
