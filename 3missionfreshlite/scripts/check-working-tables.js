// Check what tables we can actually access
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'

const supabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'mission_fresh'
  }
})

async function checkWorkingTables() {
  console.log('Checking tables that work...')
  
  // Check tables we know work from the app
  const knownTables = [
    'profiles',
    'user_goals',
    'user_journal_entries',
    'user_progress',
    'mood_entries',
    'breathing_sessions',
    'focus_sessions',
    'user_rewards',
    'user_achievements',
    'community_posts',
    'community_stories'
  ]
  
  for (const tableName of knownTables) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1)
      
      if (!error) {
        console.log(`✅ Table "${tableName}" accessible with ${data?.length || 0} records`)
      } else {
        console.log(`❌ Table "${tableName}" error:`, error.code, error.message)
      }
    } catch (err) {
      console.log(`❌ Table "${tableName}" failed:`, err.message)
    }
  }
  
  // Check if there's a way to access quit methods through a different path
  console.log('\nTrying to access quit methods without schema...')
  const supabaseNoSchema = createClient(supabaseUrl, supabaseKey)
  
  try {
    const { data, error } = await supabaseNoSchema
      .from('quit_methods')
      .select('*')
      .limit(1)
    
    if (!error) {
      console.log(`✅ quit_methods accessible without schema: ${data?.length || 0} records`)
      if (data && data.length > 0) {
        console.log('Sample record:', JSON.stringify(data[0], null, 2))
      }
    } else {
      console.log(`❌ quit_methods without schema error:`, error.code, error.message)
    }
  } catch (err) {
    console.log(`❌ quit_methods without schema failed:`, err.message)
  }
}

checkWorkingTables()
