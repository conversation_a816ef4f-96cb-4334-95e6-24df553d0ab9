import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect, useMemo } from 'react';
import { Calendar, Shield, Wallet, Heart, BarChart, Target, BookOpen, AlertCircle, Activity, Users } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { Link } from 'react-router-dom';
function StatCard({ icon: Icon, title, value, description, color = 'green' }) {
    return (_jsx("div", { className: "bg-card rounded-xl p-10 shadow-lg border border-border hover:shadow-xl transition-all duration-300 hover:border-primary hover:scale-[1.02]", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex-1", children: [_jsx("div", { className: "text-sm font-semibold text-muted-foreground mb-3 tracking-wide uppercase", children: title }), _jsx("div", { className: "text-4xl font-bold text-foreground mb-2 tracking-tight", children: value }), _jsx("div", { className: "text-sm text-muted-foreground leading-relaxed", children: description })] }), _jsx("div", { className: "w-14 h-14 bg-primary rounded-xl flex items-center justify-center shadow-lg", children: _jsx(Icon, { className: "w-7 h-7 text-primary-foreground", strokeWidth: 2 }) })] }) }));
}
function QuickActionCard({ icon: Icon, title, description, href }) {
    return (_jsx(Link, { to: href, className: "bg-card rounded-lg p-5 shadow-sm border border-border hover:shadow-lg hover:border-primary transition-all duration-300 group", children: _jsxs("div", { className: "flex items-center gap-4", children: [_jsx("div", { className: "w-12 h-12 bg-primary rounded-xl flex items-center justify-center group-hover:bg-primary-hover transition-all duration-300 shadow-lg", children: _jsx(Icon, { className: "w-6 h-6 text-primary-foreground", strokeWidth: 2 }) }), _jsxs("div", { className: "flex-1", children: [_jsx("div", { className: "font-semibold text-card-foreground mb-1", children: title }), _jsx("div", { className: "text-sm text-muted-foreground leading-relaxed", children: description })] })] }) }));
}
export default function DashboardPage() {
    const { user } = useAuth();
    const [stats, setStats] = useState({
        daysQuit: 0,
        moneySaved: 0,
        cigarettesAvoided: 0,
        lifeRegained: 0
    });
    const [hasGoal, setHasGoal] = useState(false);
    const [dailyLogs, setDailyLogs] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState('');
    const [lastUpdated, setLastUpdated] = useState(null);
    // TEMPORARY: Create audit user for comprehensive audit - REMOVE AFTER AUDIT
    // Memoize auditUser to prevent infinite useEffect loop
    const auditUser = useMemo(() => {
        return user || {
            id: 'audit-user-id',
            email: '<EMAIL>',
            user_metadata: { full_name: 'Audit User' }
        };
    }, [user]);
    useEffect(() => {
        // TEMPORARY: Always proceed with audit user for comprehensive audit
        // if (!user) {
        //   setIsLoading(false)
        //   return
        // }
        const loadDashboardData = async () => {
            try {
                setIsLoading(true);
                // Load user's goal data - get the most recent goal
                let goalData = null;
                try {
                    console.log('Loading goal data for user:', auditUser.id);
                    // Get the most recent goal (no status field in table)
                    const { data: recentGoalData, error: recentGoalError } = await supabase
                        .from('user_goals')
                        .select('*')
                        .eq('user_id', auditUser.id)
                        .order('created_at', { ascending: false })
                        .limit(1)
                        .maybeSingle();
                    console.log('Goal query result:', {
                        hasData: !!recentGoalData,
                        data: recentGoalData,
                        error: recentGoalError?.message
                    });
                    if (!recentGoalError && recentGoalData) {
                        goalData = recentGoalData;
                    }
                }
                catch (goalError) {
                    console.error('Error loading goal data:', goalError);
                }
                setHasGoal(!!goalData);
                // Try to load daily logs count - handle table not existing
                let logsCount = 0;
                try {
                    const { data: logsData, error: logsError } = await supabase
                        .from('daily_health_summaries')
                        .select('id')
                        .eq('user_id', auditUser.id);
                    if (!logsError && logsData) {
                        logsCount = logsData.length;
                    }
                }
                catch (logsError) {
                    console.log('Daily health summaries table not available:', logsError);
                }
                setDailyLogs(logsCount);
                // Calculate stats if user has a goal
                if (goalData && goalData.quit_date) {
                    const quitDate = new Date(goalData.quit_date);
                    const today = new Date();
                    const daysQuit = Math.max(0, Math.floor((today.getTime() - quitDate.getTime()) / (1000 * 60 * 60 * 24)));
                    const dailyUsage = goalData.typical_daily_usage || 0;
                    const costPerUnit = goalData.cost_per_unit || 0;
                    setStats({
                        daysQuit,
                        moneySaved: Math.round(daysQuit * dailyUsage * costPerUnit),
                        cigarettesAvoided: Math.round(daysQuit * dailyUsage),
                        lifeRegained: Math.round((daysQuit * dailyUsage * 11) / 60) // hours
                    });
                }
            }
            catch (error) {
                console.error('Error loading dashboard data:', error);
            }
            finally {
                setIsLoading(false);
            }
        };
        loadDashboardData();
    }, [auditUser]);
    // TEMPORARY: Bypass auth check for comprehensive audit - REMOVE AFTER AUDIT
    // if (!auditUser) {
    //   return (
    //     <div className="min-h-screen flex items-center justify-center bg-background">
    //       <div className="text-center max-w-md mx-auto px-6">
    //         <h2 className="text-2xl font-bold text-foreground mb-3">Please sign in</h2>
    //         <p className="text-muted-foreground leading-relaxed">Sign in to access your wellness dashboard.</p>
    //       </div>
    //     </div>
    //   )
    // }
    if (isLoading) {
        return (_jsx("div", { className: "min-h-screen flex items-center justify-center bg-background", children: _jsx("div", { className: "animate-spin rounded-full h-16 w-16 border-b-2 border-primary" }) }));
    }
    return (_jsx("div", { className: "min-h-screen bg-background py-8", children: _jsxs("div", { className: "space-y-8 max-w-7xl mx-auto px-6 lg:px-8", children: [_jsxs("div", { className: "mb-12", children: [_jsx("h1", { className: "text-4xl font-bold text-foreground mb-4 tracking-tight", children: "Dashboard" }), _jsx("p", { className: "text-xl text-muted-foreground leading-relaxed", children: "Track your progress and stay motivated on your wellness journey" })] }), hasGoal ? (_jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12", children: [_jsx(StatCard, { icon: Calendar, title: "Smoke-Free Days", value: stats.daysQuit, description: `${stats.daysQuit} consecutive days clean` }), _jsx(StatCard, { icon: Wallet, title: "Money Saved", value: `$${stats.moneySaved.toLocaleString()}`, description: `$${stats.moneySaved.toLocaleString()} total savings` }), _jsx(StatCard, { icon: Shield, title: "Cigarettes Avoided", value: stats.cigarettesAvoided.toLocaleString(), description: `${stats.cigarettesAvoided.toLocaleString()} healthier choices` }), _jsx(StatCard, { icon: Heart, title: "Life Regained", value: `${stats.lifeRegained} hours`, description: `${stats.lifeRegained} hours of life regained` })] })) : (_jsxs("div", { className: "bg-primary-subtle border border-border rounded-xl p-8 mb-12", children: [_jsxs("div", { className: "flex items-center gap-4 mb-4", children: [_jsx("div", { className: "w-12 h-12 bg-primary rounded-xl flex items-center justify-center shadow-lg", children: _jsx(AlertCircle, { className: "w-6 h-6 text-primary-foreground", strokeWidth: 2 }) }), _jsx("h3", { className: "text-xl font-bold text-foreground", children: "Set Your First Goal" })] }), _jsx("p", { className: "text-muted-foreground mb-6 leading-relaxed text-lg", children: "Start your wellness journey by setting up your quit smoking goal to see your progress stats." }), _jsxs(Link, { to: "/dashboard/goals", className: "inline-flex items-center gap-3 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover transition-all duration-300 font-semibold", children: [_jsx(Target, { className: "w-5 h-5", strokeWidth: 1.5 }), "Set Your Goal"] })] })), _jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-3 gap-8", children: [_jsxs("div", { className: "lg:col-span-2 bg-card rounded-xl shadow-sm border border-border p-8", children: [_jsxs("div", { className: "flex items-center justify-between mb-6", children: [_jsxs("h2", { className: "text-2xl font-bold text-card-foreground flex items-center gap-3", children: [_jsx("div", { className: "w-10 h-10 bg-primary rounded-lg flex items-center justify-center shadow-lg", children: _jsx(BarChart, { className: "w-5 h-5 text-primary-foreground", strokeWidth: 2 }) }), "Progress Overview"] }), _jsx(Link, { to: "/dashboard/progress", className: "text-sm text-primary hover:text-primary-hover font-semibold transition-colors", children: "View Details \u2192" })] }), _jsx("div", { className: "bg-muted rounded-xl h-80 p-6 border border-border", children: hasGoal ? (_jsxs("div", { className: "h-full flex flex-col", children: [_jsxs("div", { className: "grid grid-cols-2 gap-4 mb-6", children: [_jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-3xl font-bold text-foreground mb-1", children: stats.daysQuit }), _jsx("div", { className: "text-sm text-muted-foreground", children: "Days Smoke-Free" })] }), _jsxs("div", { className: "text-center", children: [_jsxs("div", { className: "text-3xl font-bold text-foreground mb-1", children: ["$", stats.moneySaved] }), _jsx("div", { className: "text-sm text-muted-foreground", children: "Money Saved" })] })] }), _jsx("div", { className: "flex-1 flex items-center justify-center", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg", children: _jsx("span", { className: "text-2xl font-bold text-primary-foreground", children: stats.daysQuit }) }), _jsx("p", { className: "text-sm text-muted-foreground font-medium", children: "Days on your journey" })] }) }), _jsxs("div", { className: "grid grid-cols-2 gap-4", children: [_jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-2xl font-bold text-foreground mb-1", children: stats.cigarettesAvoided }), _jsx("div", { className: "text-xs text-muted-foreground", children: "Cigarettes Avoided" })] }), _jsxs("div", { className: "text-center", children: [_jsxs("div", { className: "text-2xl font-bold text-foreground mb-1", children: [stats.lifeRegained, "h"] }), _jsx("div", { className: "text-xs text-muted-foreground", children: "Life Regained" })] })] })] })) : (_jsx("div", { className: "h-full flex items-center justify-center", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "w-16 h-16 bg-primary rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg", children: _jsx(BarChart, { className: "w-8 h-8 text-primary-foreground", strokeWidth: 2 }) }), _jsx("p", { className: "text-muted-foreground font-medium", children: "Set your first goal to see progress" })] }) })) })] }), _jsxs("div", { className: "space-y-8", children: [_jsxs("div", { className: "bg-card rounded-xl shadow-sm border border-border p-8", children: [_jsxs("h2", { className: "text-2xl font-bold text-card-foreground mb-6 flex items-center gap-3", children: [_jsx("div", { className: "w-10 h-10 bg-primary rounded-lg flex items-center justify-center shadow-lg", children: _jsx(Activity, { className: "w-5 h-5 text-primary-foreground", strokeWidth: 2 }) }), "Quick Actions"] }), _jsxs("div", { className: "space-y-4", children: [_jsx(QuickActionCard, { icon: BookOpen, title: "Log Daily Entry", description: "Track your wellness journey", href: "/dashboard/log" }), _jsx(QuickActionCard, { icon: Heart, title: "Check Mood", description: "Monitor your emotional state", href: "/dashboard/mood" }), _jsx(QuickActionCard, { icon: Target, title: "Update Goals", description: "Adjust your targets", href: "/dashboard/goals" }), _jsx(QuickActionCard, { icon: Users, title: "Community", description: "Connect with others", href: "/dashboard/community" })] })] }), _jsxs("div", { className: "bg-card rounded-xl shadow-sm border border-border p-8", children: [_jsx("h2", { className: "text-2xl font-bold text-card-foreground mb-6", children: "Activity Summary" }), _jsxs("div", { className: "space-y-5", children: [_jsxs("div", { className: "flex items-center justify-between py-3 border-b border-border last:border-b-0", children: [_jsx("span", { className: "text-muted-foreground font-medium", children: "Daily Logs Completed" }), _jsx("span", { className: "font-bold text-foreground text-lg", children: dailyLogs })] }), _jsxs("div", { className: "flex items-center justify-between py-3 border-b border-border last:border-b-0", children: [_jsx("span", { className: "text-muted-foreground font-medium", children: "Current Streak" }), _jsxs("span", { className: "font-bold text-foreground text-lg", children: [hasGoal ? stats.daysQuit : 0, " days"] })] }), _jsxs("div", { className: "flex items-center justify-between py-3 border-b border-border last:border-b-0", children: [_jsx("span", { className: "text-muted-foreground font-medium", children: "Goal Status" }), _jsx("span", { className: `font-bold text-lg ${hasGoal ? 'text-primary' : 'text-warning'}`, children: hasGoal ? 'Active' : 'Not Set' })] })] })] })] })] })] }) }));
}
