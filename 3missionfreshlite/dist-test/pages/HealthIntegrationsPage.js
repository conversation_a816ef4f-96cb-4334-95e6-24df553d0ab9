import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import { Heart, Activity, Watch, CheckCircle, Link as LinkIcon, Unlink, Loader2, Smartphone, Wifi, AlertCircle } from 'lucide-react';
import { supabase } from '../lib/supabase';
function StatusBadge({ status }) {
    switch (status) {
        case 'connected':
            return (_jsx("span", { className: "inline-flex items-center px-3 py-1 rounded-lg font-semibold bg-primary/10 text-primary border border-primary/20", children: "Connected" }));
        case 'pending':
            return (_jsx("span", { className: "inline-flex items-center px-3 py-1 rounded-lg font-semibold bg-muted text-muted-foreground border border-border", children: "Pending" }));
        default:
            return (_jsx("span", { className: "inline-flex items-center px-3 py-1 rounded-lg font-semibold bg-muted text-muted-foreground border border-border", children: "Disconnected" }));
    }
}
function IntegrationCard({ title, description, Icon, status, onConnect, onDisconnect }) {
    return (_jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300", children: [_jsxs("div", { className: "flex items-start justify-between mb-6", children: [_jsxs("div", { className: "flex items-start gap-4", children: [_jsx("div", { className: "w-14 h-14 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20", children: _jsx(Icon, { className: "w-7 h-7 text-primary", strokeWidth: 1.5 }) }), _jsxs("div", { children: [_jsx("h3", { className: "font-bold text-card-foreground text-lg", children: title }), _jsx("p", { className: "text-muted-foreground mt-2 leading-relaxed", children: description })] })] }), _jsx(StatusBadge, { status: status })] }), _jsx("div", { className: "mt-6", children: status === 'connected' ? (_jsxs("button", { onClick: onDisconnect, className: "w-full flex items-center justify-center gap-3 px-6 py-3 border border-border rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold", children: [_jsx(Unlink, { className: "w-5 h-5", strokeWidth: 1.5 }), "Disconnect"] })) : (_jsx("button", { onClick: onConnect, disabled: status === 'pending', className: "w-full flex items-center justify-center gap-3 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-semibold shadow-lg hover:shadow-xl", children: status === 'pending' ? (_jsxs(_Fragment, { children: [_jsx(Loader2, { className: "w-5 h-5 animate-spin", strokeWidth: 1.5 }), "Connecting..."] })) : (_jsxs(_Fragment, { children: [_jsx(LinkIcon, { className: "w-5 h-5", strokeWidth: 1.5 }), "Connect"] })) })) })] }));
}
export default function HealthIntegrationsPage() {
    // MINIMAL COMPONENT VERSION - ISOLATING INFINITE LOOP SOURCE
    // All complex logic temporarily removed to identify root cause
    // Minimal state for testing
    const [loading, setLoading] = useState(false);
    // No useEffect, no data fetching, no complex logic
    // Just minimal render to test if infinite loop is in basic component structure
    const fetchHealthIntegrations = async () => {
        try {
            const { data, error } = await supabase
                .from('health_integrations')
                .select('*')
                .order('display_order');
            if (error) {
                // Fallback integrations if table doesn't exist
                setHealthIntegrations([
                    { id: 1, name: 'Apple Health', key: 'apple', description: 'Sync steps, sleep, heart rate, and wellness metrics from your iOS device.', icon: 'heart', provider: 'apple' },
                    { id: 2, name: 'Google Fit', key: 'google', description: 'Import health and fitness data from your Android device.', icon: 'activity', provider: 'google' },
                    { id: 3, name: 'Fitbit', key: 'fitbit', description: 'Connect your Fitbit device to track activity and sleep patterns.', icon: 'watch', provider: 'fitbit' },
                    { id: 4, name: 'Garmin Connect', key: 'garmin', description: 'Sync training data and health metrics from Garmin devices.', icon: 'watch', provider: 'garmin' },
                    { id: 5, name: 'Samsung Health', key: 'samsung', description: 'Connect Samsung Health to import wellness and activity data.', icon: 'smartphone', provider: 'samsung' },
                    { id: 6, name: 'Strava', key: 'strava', description: 'Import workout data and activity tracking from Strava.', icon: 'activity', provider: 'strava' }
                ]);
                return;
            }
            setHealthIntegrations(data || []);
        }
        catch (err) {
            setError(err.message);
        }
    };
    const fetchUserConnections = async () => {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                setLoading(false);
                return;
            }
            const { data, error } = await supabase
                .from('user_health_connections')
                .select('*')
                .eq('user_id', user.id);
            if (error)
                throw error;
            setUserConnections(data || []);
            setLoading(false);
        }
        catch (err) {
            setError(err.message);
            setLoading(false);
        }
    };
    const handleConnect = async (integrationKey) => {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user)
                return;
            const { error } = await supabase
                .from('user_health_connections')
                .upsert({
                user_id: user.id,
                integration_key: integrationKey,
                status: 'connected',
                connected_at: new Date().toISOString()
            });
            if (error)
                throw error;
            await fetchUserConnections();
        }
        catch (err) {
            setError(err.message);
        }
    };
    const handleDisconnect = async (integrationKey) => {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user)
                return;
            const { error } = await supabase
                .from('user_health_connections')
                .delete()
                .eq('user_id', user.id)
                .eq('integration_key', integrationKey);
            if (error)
                throw error;
            await fetchUserConnections();
        }
        catch (err) {
            setError(err.message);
        }
    };
    const getConnectionStatus = (integrationKey) => {
        const connection = userConnections.find(c => c.integration_key === integrationKey);
        return connection ? 'connected' : 'disconnected';
    };
    const getIconComponent = (iconName) => {
        const iconMap = {
            heart: Heart,
            activity: Activity,
            watch: Watch,
            smartphone: Smartphone,
            wifi: Wifi
        };
        return iconMap[iconName] || Activity;
    };
    const connectedCount = userConnections.length;
    return (_jsx("div", { className: "min-h-screen bg-background py-12", children: _jsxs("div", { className: "max-w-7xl mx-auto px-6 lg:px-8", children: [_jsxs("div", { className: "mb-16", children: [_jsx("h1", { className: "text-5xl font-bold text-foreground mb-6 tracking-tight", children: "Health Integrations" }), _jsx("p", { className: "text-xl text-muted-foreground leading-relaxed max-w-3xl", children: "Sync your health data for a more holistic view of your wellness journey." })] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-8 mb-16", children: [_jsx("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsx("p", { className: "text-muted-foreground font-medium text-lg", children: "Connected Apps" }), _jsx("p", { className: "text-4xl font-bold text-primary mt-2", children: connectedCount })] }), _jsx("div", { className: "w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20", children: _jsx(LinkIcon, { className: "w-8 h-8 text-primary", strokeWidth: 1.5 }) })] }) }), _jsx("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsx("p", { className: "text-muted-foreground font-medium text-lg", children: "Available Integrations" }), _jsx("p", { className: "text-4xl font-bold text-primary mt-2", children: healthIntegrations.length })] }), _jsx("div", { className: "w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20", children: _jsx(Activity, { className: "w-8 h-8 text-primary", strokeWidth: 1.5 }) })] }) }), _jsx("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsx("p", { className: "text-muted-foreground font-medium text-lg", children: "Data Sync Status" }), _jsx("p", { className: "text-4xl font-bold text-primary mt-2", children: "Live" })] }), _jsx("div", { className: "w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20", children: _jsx(Wifi, { className: "w-8 h-8 text-primary", strokeWidth: 1.5 }) })] }) })] }), loading && (_jsxs("div", { className: "text-center py-20", children: [_jsx("div", { className: "inline-block animate-spin rounded-full h-16 w-16 border-b-2 border-primary" }), _jsx("p", { className: "text-muted-foreground mt-6 text-lg", children: "Loading health integrations..." })] })), error && (_jsx("div", { className: "bg-destructive/10 border border-destructive/20 rounded-xl p-6 mb-12", children: _jsxs("div", { className: "flex items-center space-x-4", children: [_jsx(AlertCircle, { className: "w-6 h-6 text-destructive flex-shrink-0", strokeWidth: 1.5 }), _jsx("p", { className: "text-destructive font-medium text-lg", children: error })] }) })), !loading && !error && (_jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16", children: _jsxs("div", { className: "col-span-full text-center py-20", children: [_jsx("h3", { className: "text-2xl font-bold text-card-foreground mb-4", children: "Testing: IntegrationCard Mapping Disabled" }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Isolating infinite loop - IntegrationCard mapping temporarily disabled" }), _jsxs("p", { className: "text-muted-foreground text-sm mt-2", children: ["Count: ", healthIntegrations.length, " integrations loaded"] })] }) })), !loading && !error && healthIntegrations.length === 0 && (_jsxs("div", { className: "text-center py-20", children: [_jsx("div", { className: "w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20", children: _jsx(Activity, { className: "w-10 h-10 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "text-2xl font-bold text-card-foreground mb-4", children: "No Integrations Available" }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Check back later for health integration options." })] })), _jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-10", children: [_jsxs("div", { className: "mb-8", children: [_jsx("h2", { className: "text-2xl font-bold text-card-foreground mb-4", children: "Your Data Privacy" }), _jsx("p", { className: "text-muted-foreground text-lg leading-relaxed", children: "We take your privacy seriously. Your health data is always protected." })] }), _jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [
                                'Encrypted and stored securely using industry-standard protocols',
                                'Never shared with third parties without your explicit consent',
                                'Used only to provide you with personalized health insights',
                                'Deletable by you at any time from your account settings'
                            ].map((item, index) => (_jsxs("div", { className: "flex items-start gap-4", children: [_jsx(CheckCircle, { className: "w-6 h-6 text-primary flex-shrink-0 mt-1", strokeWidth: 1.5 }), _jsx("span", { className: "text-muted-foreground leading-relaxed", children: item })] }, index))) }), _jsx("div", { className: "mt-10 p-8 bg-primary/5 rounded-xl border border-primary/20", children: _jsxs("div", { className: "flex items-start gap-4", children: [_jsx(Activity, { className: "w-6 h-6 text-primary flex-shrink-0 mt-1", strokeWidth: 1.5 }), _jsxs("div", { children: [_jsx("h3", { className: "font-bold text-card-foreground mb-3 text-lg", children: "Data Sync Benefits" }), _jsx("p", { className: "text-muted-foreground leading-relaxed", children: "Connecting your health apps allows us to provide more accurate insights into your wellness journey, track correlations between your quit progress and physical health, and offer personalized recommendations." })] })] }) })] })] }) }));
}
