import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Compass, ArrowRight, CheckCircle, Clock, Users, Brain, Heart, Target, AlertCircle, Play, Bookmark, TrendingUp, Search, Filter } from 'lucide-react';
export default function QuitMethodsPage() {
    const [selectedMethod, setSelectedMethod] = useState('overview');
    const [methods, setMethods] = useState([]);
    const [filteredMethods, setFilteredMethods] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedDifficulty, setSelectedDifficulty] = useState('all');
    // Fetch real quit methods from Supabase database - RULE 0001: ZERO hardcoding
    useEffect(() => {
        async function fetchQuitMethods() {
            try {
                setLoading(true);
                // Create Supabase client without schema restriction to access quit_methods table
                const { createClient } = await import('@supabase/supabase-js');
                const supabaseNoSchema = createClient('https://yekarqanirdkdckimpna.supabase.co', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc');
                // Using correct quit_methods table with proper connection
                const { data, error } = await supabaseNoSchema
                    .from('quit_methods')
                    .select('*')
                    .eq('is_active', true)
                    .order('sort_order', { ascending: true })
                    .order('name', { ascending: true });
                if (error)
                    throw error;
                // Use real quit methods data directly - RULE 0001: real data only
                const quitMethodsData = data?.map(method => ({
                    id: method.id.toString(),
                    name: method.name,
                    duration: method.timeframe || 'Varies',
                    difficulty: method.difficulty || 'Medium',
                    successRate: method.success_rate_min && method.success_rate_max
                        ? `${method.success_rate_min}-${method.success_rate_max}%`
                        : 'Varies',
                    icon: getMethodIcon(method.name),
                    description: method.description,
                    pros: method.best_for || [],
                    cons: method.considerations || [],
                    whoItsFor: method.best_for ? method.best_for.join(', ') : 'General users',
                    tips: method.considerations || []
                })) || [];
                setMethods(quitMethodsData);
                setFilteredMethods(quitMethodsData);
            }
            catch (err) {
                console.error('Error fetching quit methods:', err);
                setError('Failed to load quit methods');
            }
            finally {
                setLoading(false);
            }
        }
        fetchQuitMethods();
    }, []);
    // Filter methods based on search term and difficulty
    useEffect(() => {
        let filtered = methods;
        // Filter by search term
        if (searchTerm) {
            filtered = filtered.filter(method => method.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                method.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                method.whoItsFor.toLowerCase().includes(searchTerm.toLowerCase()));
        }
        // Filter by difficulty
        if (selectedDifficulty !== 'all') {
            filtered = filtered.filter(method => method.difficulty === selectedDifficulty);
        }
        setFilteredMethods(filtered);
    }, [methods, searchTerm, selectedDifficulty]);
    // Get unique difficulties for filter
    const difficulties = ['all', ...Array.from(new Set(methods.map(m => m.difficulty).filter(Boolean)))];
    // Get icon based on method type - RULE 0001: logic only, no hardcoded data
    const getMethodIcon = (methodName) => {
        const name = methodName?.toLowerCase() || '';
        if (name.includes('cold') || name.includes('turkey'))
            return Target;
        if (name.includes('gradual') || name.includes('tapering'))
            return Clock;
        if (name.includes('therapy') || name.includes('counseling'))
            return Heart;
        if (name.includes('mindful') || name.includes('meditation'))
            return Brain;
        if (name.includes('support') || name.includes('group'))
            return Users;
        return Compass;
    };
    // RULE 0001: All hardcoded methods array COMPLETELY REMOVED - using only real database data
    const selectedMethodData = methods.find(m => m.id === selectedMethod);
    const getDifficultyColor = (difficulty) => {
        switch (difficulty) {
            case 'Easy': return 'bg-primary-subtle text-primary border border-border';
            case 'Easy-Medium': return 'bg-muted text-muted-foreground border border-border';
            case 'Medium': return 'bg-accent text-accent-foreground border border-border';
            case 'Hard': return 'bg-destructive-subtle text-destructive border border-border';
            default: return 'bg-muted text-muted-foreground border border-border';
        }
    };
    return (_jsxs("div", { className: "min-h-screen bg-background", children: [_jsx("section", { className: "bg-background py-24", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6 lg:px-8 text-center", children: [_jsx("div", { className: "w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg", children: _jsx(Compass, { className: "w-10 h-10 text-primary-foreground", strokeWidth: 2 }) }), _jsx("h1", { className: "text-6xl font-bold text-foreground mb-8 tracking-tight", children: "Quit Methods" }), _jsx("p", { className: "text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto", children: "Evidence-based strategies and step-by-step guides for successful smoking cessation and long-term wellness" })] }) }), _jsx("section", { className: "bg-card border-b border-border py-12", children: _jsxs("div", { className: "max-w-7xl mx-auto px-6 lg:px-8", children: [_jsxs("div", { className: "flex flex-col md:flex-row gap-6 items-center", children: [_jsx("div", { className: "flex-1", children: _jsxs("div", { className: "relative", children: [_jsx(Search, { className: "absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" }), _jsx("input", { type: "text", placeholder: "Search quit methods...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value), className: "w-full pl-12 pr-4 py-3 border border-border rounded-lg bg-input text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring" })] }) }), _jsx("div", { className: "md:w-64", children: _jsxs("div", { className: "relative", children: [_jsx(Filter, { className: "absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" }), _jsx("select", { value: selectedDifficulty, onChange: (e) => setSelectedDifficulty(e.target.value), className: "w-full pl-12 pr-4 py-3 border border-border rounded-lg bg-input text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring appearance-none", children: difficulties.map(difficulty => (_jsx("option", { value: difficulty, children: difficulty === 'all' ? 'All Difficulties' : difficulty }, difficulty))) })] }) })] }), _jsx("div", { className: "mt-6 text-center", children: _jsxs("p", { className: "text-muted-foreground", children: ["Showing ", filteredMethods.length, " of ", methods.length, " methods"] }) })] }) }), _jsx("section", { className: "bg-background border-b border-border py-16", children: _jsxs("div", { className: "max-w-7xl mx-auto px-6 lg:px-8", children: [_jsx("h2", { className: "text-4xl font-bold text-foreground mb-12 text-center", children: "Choose Your Quit Method" }), loading ? (_jsx("div", { className: "grid md:grid-cols-2 lg:grid-cols-3 gap-8", children: [...Array(6)].map((_, i) => (_jsxs("div", { className: "p-8 rounded-xl border border-border bg-card", children: [_jsxs("div", { className: "flex items-center space-x-4 mb-4", children: [_jsx("div", { className: "w-8 h-8 bg-muted rounded animate-pulse" }), _jsx("div", { className: "h-6 bg-muted rounded animate-pulse w-32" })] }), _jsx("div", { className: "h-4 bg-muted rounded animate-pulse mb-2 w-24" }), _jsx("div", { className: "h-4 bg-muted rounded animate-pulse w-28" })] }, i))) })) : error ? (_jsx("div", { className: "bg-destructive/10 border border-destructive/20 rounded-xl p-6 mb-12", children: _jsxs("div", { className: "flex items-center space-x-4", children: [_jsx(AlertCircle, { className: "w-6 h-6 text-destructive flex-shrink-0", strokeWidth: 1.5 }), _jsx("p", { className: "text-destructive font-medium text-lg", children: error })] }) })) : (_jsx("div", { className: "grid md:grid-cols-2 lg:grid-cols-3 gap-8", children: filteredMethods.map((method) => {
                                const IconComponent = method.icon;
                                return (_jsxs("div", { className: "bg-card p-8 rounded-2xl shadow-lg border border-border hover:shadow-2xl transition-all duration-300", children: [_jsx("div", { className: "flex items-start justify-between mb-6", children: _jsxs("div", { className: "flex items-center space-x-4", children: [_jsx("div", { className: "w-16 h-16 bg-primary rounded-xl flex items-center justify-center shadow-lg", children: _jsx(IconComponent, { className: "w-8 h-8 text-primary-foreground", strokeWidth: 2 }) }), _jsxs("div", { children: [_jsx("h3", { className: "font-bold text-card-foreground text-xl leading-tight", children: method.name }), _jsxs("div", { className: "flex items-center space-x-3 mt-2", children: [_jsx("span", { className: `inline-block px-3 py-1 rounded-lg text-sm font-semibold ${getDifficultyColor(method.difficulty)}`, children: method.difficulty }), method.successRate && (_jsxs("div", { className: "flex items-center space-x-2 text-muted-foreground", children: [_jsx(TrendingUp, { className: "w-4 h-4", strokeWidth: 1.5 }), _jsx("span", { className: "font-medium", children: method.successRate })] }))] })] })] }) }), _jsxs("div", { className: "mb-6", children: [_jsx("p", { className: "text-muted-foreground mb-6 line-clamp-3 leading-relaxed", children: method.description }), _jsxs("div", { className: "space-y-3", children: [_jsxs("div", { className: "flex justify-between", children: [_jsx("span", { className: "text-muted-foreground font-medium", children: "Duration:" }), _jsx("span", { className: "font-semibold text-card-foreground", children: method.duration })] }), method.whoItsFor && (_jsxs("div", { className: "flex justify-between", children: [_jsx("span", { className: "text-muted-foreground font-medium", children: "Best for:" }), _jsx("span", { className: "font-semibold text-card-foreground text-right", children: method.whoItsFor })] }))] })] }), _jsxs("div", { className: "border-t border-gray-100 pt-4", children: [_jsxs("div", { className: "flex space-x-2 mb-3", children: [_jsxs("button", { onClick: () => setSelectedMethod(method.id), className: "flex-1 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary-hover transition-colors flex items-center justify-center space-x-2", children: [_jsx(Play, { className: "w-4 h-4" }), _jsx("span", { children: "Start Method" })] }), _jsx("button", { className: "px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors", children: _jsx(Bookmark, { className: "w-4 h-4" }) })] }), _jsxs("button", { onClick: () => setSelectedMethod(method.id), className: "w-full text-sm text-green-600 hover:text-green-700 transition-colors flex items-center justify-center space-x-1", children: [_jsx("span", { children: "View Details" }), _jsx(ArrowRight, { className: "w-3 h-3" })] })] })] }, method.id));
                            }) })), filteredMethods.length === 0 && !loading && !error && methods.length > 0 && (_jsxs("div", { className: "text-center py-20", children: [_jsx("div", { className: "w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg", children: _jsx(Search, { className: "w-10 h-10 text-primary-foreground", strokeWidth: 2 }) }), _jsx("h3", { className: "text-2xl font-bold text-card-foreground mb-4", children: "No Methods Found" }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Try adjusting your search or filter criteria." })] })), !loading && !error && methods.length === 0 && (_jsxs("div", { className: "text-center py-12", children: [_jsx("div", { className: "w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg", children: _jsx(Compass, { className: "w-10 h-10 text-primary-foreground", strokeWidth: 2 }) }), _jsx("h3", { className: "text-2xl font-bold text-card-foreground mb-4", children: "No Methods Available" }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Check back later for quit method options." })] }))] }) }), methods.length > 0 && (_jsx("section", { className: "bg-white border-b border-gray-200 py-6", children: _jsx("div", { className: "max-w-4xl mx-auto px-6", children: _jsxs("div", { className: "flex flex-wrap gap-4 justify-center", children: [_jsx("button", { onClick: () => setSelectedMethod('overview'), className: `px-6 py-3 rounded-lg font-medium transition-colors ${selectedMethod === 'overview'
                                    ? 'bg-primary text-primary-foreground'
                                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`, children: "Overview" }), methods.map((method) => (_jsx("button", { onClick: () => setSelectedMethod(method.id), className: `px-6 py-3 rounded-lg font-medium transition-colors ${selectedMethod === method.id
                                    ? 'bg-primary text-primary-foreground'
                                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`, children: method.name }, method.id)))] }) }) })), selectedMethodData && (_jsx("section", { className: "py-12", children: _jsx("div", { className: "max-w-6xl mx-auto px-6", children: _jsxs("div", { className: "bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden", children: [_jsx("div", { className: "bg-primary-subtle p-6 border-b border-gray-100", children: _jsxs("div", { className: "flex items-center space-x-4", children: [_jsx("div", { className: "w-12 h-12 bg-primary-muted rounded-lg flex items-center justify-center", children: _jsx(selectedMethodData.icon, { className: "w-6 h-6 text-green-600" }) }), _jsxs("div", { children: [_jsx("h2", { className: "text-2xl font-bold text-gray-900", children: selectedMethodData.name }), _jsx("p", { className: "text-gray-700", children: selectedMethodData.description })] })] }) }), _jsxs("div", { className: "p-6", children: [_jsxs("div", { className: "grid lg:grid-cols-2 gap-8", children: [_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { children: [_jsxs("h3", { className: "text-lg font-semibold text-gray-900 mb-4 flex items-center", children: [_jsx(CheckCircle, { className: "w-5 h-5 text-green-500 mr-2" }), "Advantages"] }), _jsx("ul", { className: "space-y-2", children: selectedMethodData.pros.map((pro, index) => (_jsxs("li", { className: "flex items-start space-x-2", children: [_jsx(CheckCircle, { className: "w-4 h-4 text-green-500 mt-1 flex-shrink-0" }), _jsx("span", { className: "text-gray-700", children: pro })] }, index))) })] }), _jsxs("div", { children: [_jsxs("h3", { className: "text-lg font-semibold text-gray-900 mb-4 flex items-center", children: [_jsx(Target, { className: "w-5 h-5 text-orange-500 mr-2" }), "Considerations"] }), _jsx("ul", { className: "space-y-2", children: selectedMethodData.cons.map((con, index) => (_jsxs("li", { className: "flex items-start space-x-2", children: [_jsx(Target, { className: "w-4 h-4 text-orange-500 mt-1 flex-shrink-0" }), _jsx("span", { className: "text-gray-700", children: con })] }, index))) })] })] }), _jsxs("div", { className: "space-y-6", children: [_jsxs("div", { children: [_jsxs("h3", { className: "text-lg font-semibold text-gray-900 mb-4 flex items-center", children: [_jsx(Users, { className: "w-5 h-5 text-blue-500 mr-2" }), "Who It's For"] }), _jsx("p", { className: "text-gray-700 bg-blue-50 p-4 rounded-lg", children: selectedMethodData.whoItsFor })] }), _jsxs("div", { children: [_jsxs("h3", { className: "text-lg font-semibold text-gray-900 mb-4 flex items-center", children: [_jsx(Brain, { className: "w-5 h-5 text-purple-500 mr-2" }), "Tips for Success"] }), _jsx("ul", { className: "space-y-2", children: selectedMethodData.tips.map((tip, index) => (_jsxs("li", { className: "flex items-start space-x-2", children: [_jsx("div", { className: "w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0", children: _jsx("span", { className: "text-purple-600 text-xs font-medium", children: index + 1 }) }), _jsx("span", { className: "text-gray-700", children: tip })] }, index))) })] })] })] }), _jsx("div", { className: "mt-8 pt-6 border-t border-gray-100", children: _jsxs("div", { className: "grid grid-cols-3 gap-4 text-center", children: [_jsxs("div", { className: "p-4 bg-gray-50 rounded-lg", children: [_jsx("div", { className: "text-2xl font-bold text-gray-900", children: selectedMethodData.duration }), _jsx("div", { className: "text-sm text-gray-600", children: "Duration" })] }), _jsxs("div", { className: "p-4 bg-gray-50 rounded-lg", children: [_jsx("div", { className: "text-2xl font-bold text-gray-900", children: selectedMethodData.successRate }), _jsx("div", { className: "text-sm text-gray-600", children: "Success Rate" })] }), _jsxs("div", { className: "p-4 bg-gray-50 rounded-lg", children: [_jsx("div", { className: `inline-block px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(selectedMethodData.difficulty)}`, children: selectedMethodData.difficulty }), _jsx("div", { className: "text-sm text-gray-600 mt-1", children: "Difficulty" })] })] }) })] })] }) }) })), selectedMethod === 'overview' && (_jsx("section", { className: "py-12", children: _jsx("div", { className: "max-w-4xl mx-auto px-6", children: _jsxs("div", { className: "bg-white p-8 rounded-xl shadow-sm border border-gray-100", children: [_jsx("h2", { className: "text-2xl font-bold text-gray-900 mb-6", children: "Choosing the Right Method for You" }), _jsxs("div", { className: "space-y-6", children: [_jsx("p", { className: "text-gray-700 leading-relaxed", children: "There's no one-size-fits-all approach to quitting nicotine. The best method depends on your personal habits, support system, health status, and preferences. Research shows that combining multiple approaches often increases success rates." }), _jsxs("div", { className: "grid md:grid-cols-2 gap-6", children: [_jsxs("div", { className: "bg-blue-50 p-6 rounded-lg", children: [_jsx("h3", { className: "font-semibold text-blue-900 mb-3", children: "Consider These Factors:" }), _jsxs("ul", { className: "space-y-2 text-blue-800", children: [_jsx("li", { children: "\u2022 How long you've been using nicotine" }), _jsx("li", { children: "\u2022 Your current usage level" }), _jsx("li", { children: "\u2022 Previous quit attempts" }), _jsx("li", { children: "\u2022 Available support system" }), _jsx("li", { children: "\u2022 Health conditions" }), _jsx("li", { children: "\u2022 Personal preferences" })] })] }), _jsxs("div", { className: "bg-primary-subtle p-6 rounded-lg", children: [_jsx("h3", { className: "font-semibold text-green-900 mb-3", children: "Combination Approaches:" }), _jsxs("ul", { className: "space-y-2 text-green-800", children: [_jsx("li", { children: "\u2022 NRT + Behavioral therapy" }), _jsx("li", { children: "\u2022 Support groups + Gradual reduction" }), _jsx("li", { children: "\u2022 Professional counseling + Medication" }), _jsx("li", { children: "\u2022 Mindfulness + Community support" }), _jsx("li", { children: "\u2022 Digital tools + Peer accountability" })] })] })] })] })] }) }) })), _jsx("section", { className: "bg-gray-100 py-16", children: _jsxs("div", { className: "max-w-4xl mx-auto px-6 text-center", children: [_jsx("h2", { className: "text-3xl font-bold text-gray-900 mb-4", children: "Ready to Start Your Journey?" }), _jsx("p", { className: "text-xl text-gray-600 mb-8", children: "Get personalized support and track your progress with Mission Fresh's comprehensive wellness tools." }), _jsxs("div", { className: "flex flex-col sm:flex-row justify-center gap-4", children: [_jsxs(Link, { to: "/tools/nrt-guide", className: "btn-secondary inline-flex items-center justify-center px-8 py-3 rounded-lg text-lg font-medium transition-colors", children: [_jsx(Heart, { className: "w-5 h-5 mr-2" }), "NRT Guide", _jsx(ArrowRight, { className: "w-5 h-5 ml-2" })] }), _jsxs(Link, { to: "/auth?mode=signup", className: "btn-primary inline-flex items-center justify-center px-8 py-3 rounded-lg text-lg font-medium transition-colors", children: ["Get Started", _jsx(ArrowRight, { className: "w-5 h-5 ml-2" })] })] })] }) })] }));
}
