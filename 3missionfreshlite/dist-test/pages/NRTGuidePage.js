import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Tablet, ArrowRight, Heart, CheckCircle, AlertCircle, Info, ShoppingCart, Star, ExternalLink } from 'lucide-react';
import { supabase } from '../lib/supabase';
export default function NRTGuidePage() {
    const [activeTab, setActiveTab] = useState('overview');
    const [nrtProducts, setNrtProducts] = useState([]);
    const [nrtVendors, setNrtVendors] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    // Fetch real NRT products and vendors from database
    useEffect(() => {
        async function fetchNRTData() {
            try {
                setLoading(true);
                // Fetch real NRT products
                const { data: productsData, error: productsError } = await supabase
                    .from('nrt_products')
                    .select('*')
                    .order('category', { ascending: true })
                    .order('effectiveness_rating', { ascending: false });
                if (productsError)
                    throw productsError;
                // Fetch real NRT vendors for benefits/considerations data
                const { data: vendorsData, error: vendorsError } = await supabase
                    .from('nrt_vendors')
                    .select('features, specialties, description')
                    .not('features', 'is', null)
                    .not('specialties', 'is', null);
                if (vendorsError)
                    throw vendorsError;
                setNrtProducts(productsData || []);
                setNrtVendors(vendorsData || []);
            }
            catch (err) {
                console.error('Error fetching NRT data:', err);
                setError('Failed to load NRT data');
            }
            finally {
                setLoading(false);
            }
        }
        fetchNRTData();
    }, []);
    // Generate tabs dynamically from real database categories
    const tabs = [
        { id: 'overview', label: 'Overview' },
        ...Array.from(new Set(nrtProducts.map(p => p.category))).map(category => ({
            id: category,
            label: category.charAt(0).toUpperCase() + category.slice(1)
        }))
    ];
    // Extract real benefits from vendor specialties and features
    const benefits = React.useMemo(() => {
        const allSpecialties = nrtVendors.flatMap(vendor => vendor.specialties || []);
        const allFeatures = nrtVendors.flatMap(vendor => vendor.features || []);
        const uniqueBenefits = [...new Set([...allSpecialties, ...allFeatures])];
        return uniqueBenefits.filter(benefit => benefit && benefit.length > 0);
    }, [nrtVendors]);
    // Extract real considerations from vendor descriptions
    const considerations = React.useMemo(() => {
        const descriptions = nrtVendors
            .map(vendor => vendor.description)
            .filter(desc => desc && desc.length > 0);
        return descriptions.slice(0, 5); // Take first 5 real descriptions as considerations
    }, [nrtVendors]);
    // Get products for current active tab
    const getProductsForTab = () => {
        if (activeTab === 'overview')
            return [];
        return nrtProducts.filter(p => p.category === activeTab);
    };
    return (_jsxs("div", { className: "min-h-screen bg-background", children: [_jsx("section", { className: "bg-background py-24", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6 lg:px-8 text-center", children: [_jsx("div", { className: "w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg", children: _jsx(Tablet, { className: "w-10 h-10 text-primary-foreground", strokeWidth: 2 }) }), _jsx("h1", { className: "text-6xl font-bold text-foreground mb-4 tracking-tight", children: "Nicotine Replacement Therapy" }), _jsx("h2", { className: "text-6xl font-bold text-foreground mb-8 tracking-tight", children: "Guide" }), _jsx("p", { className: "text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto", children: "Understanding your NRT options to find the right solution for your wellness journey" })] }) }), _jsx("section", { className: "bg-card border-b border-border", children: _jsx("div", { className: "max-w-6xl mx-auto px-6", children: _jsx("nav", { className: "flex space-x-8 overflow-x-auto", children: tabs.map((tab) => (_jsx("button", { onClick: () => setActiveTab(tab.id), className: `py-6 px-4 border-b-2 font-semibold text-base whitespace-nowrap transition-all duration-300 ${activeTab === tab.id
                                ? 'border-primary text-primary'
                                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'}`, children: tab.label }, tab.id))) }) }) }), _jsx("section", { className: "py-12", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6", children: [activeTab === 'overview' && (_jsxs("div", { className: "space-y-12", children: [_jsxs("div", { children: [_jsx("h3", { className: "text-3xl font-bold text-foreground mb-8 tracking-tight", children: "What is NRT?" }), _jsxs("div", { className: "bg-card p-8 rounded-xl shadow-lg border border-border", children: [_jsx("p", { className: "text-card-foreground leading-relaxed mb-6 text-lg", children: "Nicotine Replacement Therapy (NRT) is a medically-approved way to take nicotine by means other than tobacco. It can help reduce unpleasant withdrawal effects such as mood swings, cravings and anxiety that may occur when you stop smoking or using nicotine products." }), _jsx("p", { className: "text-card-foreground leading-relaxed text-lg", children: "NRT is available as skin patches, chewing gum, inhalers, tablets, oral strips, lozenges, nasal and mouth spray. These products provide low, controlled doses of nicotine without the tar, carbon monoxide and other poisonous chemicals present in tobacco smoke." })] })] }), _jsxs("div", { children: [_jsx("h3", { className: "text-2xl font-bold text-foreground mb-6", children: "Benefits of NRT" }), _jsx("div", { className: "grid md:grid-cols-2 gap-4", children: benefits.map((benefit, index) => (_jsxs("div", { className: "flex items-start space-x-3 bg-card p-4 rounded-lg shadow-sm border border-border", children: [_jsx(CheckCircle, { className: "w-5 h-5 text-primary mt-0.5 flex-shrink-0" }), _jsx("span", { className: "text-card-foreground", children: benefit })] }, index))) })] }), _jsxs("div", { children: [_jsx("h3", { className: "text-2xl font-bold text-foreground mb-6", children: "Considerations" }), _jsx("div", { className: "grid md:grid-cols-2 gap-4", children: considerations.map((consideration, index) => (_jsxs("div", { className: "flex items-start space-x-3 bg-card p-4 rounded-lg shadow-sm border border-border", children: [_jsx(Info, { className: "w-5 h-5 text-primary mt-0.5 flex-shrink-0" }), _jsx("span", { className: "text-card-foreground", children: consideration })] }, index))) })] })] })), activeTab !== 'overview' && (_jsx("div", { className: "space-y-6", children: loading ? (_jsxs("div", { className: "bg-card p-8 rounded-xl shadow-sm border border-border text-center", children: [_jsx("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4" }), _jsx("p", { className: "text-muted-foreground", children: "Loading NRT products..." })] })) : error ? (_jsxs("div", { className: "bg-card p-8 rounded-xl shadow-sm border border-border text-center", children: [_jsx(AlertCircle, { className: "w-12 h-12 text-destructive mx-auto mb-4" }), _jsx("p", { className: "text-destructive mb-2", children: "Error loading products" }), _jsx("p", { className: "text-muted-foreground", children: error })] })) : (_jsxs("div", { className: "grid gap-6", children: [getProductsForTab().map((product) => (_jsxs("div", { className: "bg-card p-8 rounded-xl shadow-sm border border-border", children: [_jsxs("div", { className: "flex items-start justify-between mb-4", children: [_jsxs("div", { children: [_jsx("h3", { className: "text-2xl font-bold text-card-foreground mb-2", children: product.name }), _jsxs("div", { className: "flex items-center space-x-4 mb-2", children: [product.brand && (_jsx("span", { className: "bg-primary-subtle text-primary text-sm font-medium px-3 py-1 rounded-full", children: product.brand })), product.strength && (_jsx("span", { className: "bg-primary-subtle text-primary text-sm font-medium px-3 py-1 rounded-full", children: product.strength })), product.price_range && (_jsx("span", { className: "bg-primary-subtle text-primary text-sm font-medium px-3 py-1 rounded-full", children: product.price_range }))] })] }), product.effectiveness_rating && (_jsxs("div", { className: "text-right", children: [_jsx("div", { className: "flex items-center space-x-1", children: [...Array(5)].map((_, i) => (_jsx("span", { className: `text-lg ${i < Math.floor(product.effectiveness_rating)
                                                                        ? 'text-primary'
                                                                        : 'text-muted-foreground'}`, children: "\u2605" }, i))) }), _jsxs("p", { className: "text-sm text-muted-foreground mt-1", children: [product.effectiveness_rating, "/5.0 effectiveness"] })] }))] }), _jsx("p", { className: "text-card-foreground leading-relaxed mb-6", children: product.description }), product.duration_hours && (_jsx("div", { className: "mb-6", children: _jsxs("p", { className: "text-sm text-muted-foreground", children: [_jsx("strong", { children: "Duration:" }), " ", product.duration_hours, " hours per dose"] }) })), _jsxs("div", { className: "grid md:grid-cols-2 gap-6", children: [product.pros && product.pros.length > 0 && (_jsxs("div", { children: [_jsx("h4", { className: "font-semibold text-gray-900 mb-3", children: "Advantages:" }), _jsx("ul", { className: "space-y-2", children: product.pros.map((pro, index) => (_jsxs("li", { className: "flex items-start space-x-2", children: [_jsx(CheckCircle, { className: "w-4 h-4 text-green-500 mt-1 flex-shrink-0" }), _jsx("span", { className: "text-gray-700", children: pro })] }, index))) })] })), product.cons && product.cons.length > 0 && (_jsxs("div", { children: [_jsx("h4", { className: "font-semibold text-gray-900 mb-3", children: "Considerations:" }), _jsx("ul", { className: "space-y-2", children: product.cons.map((con, index) => (_jsxs("li", { className: "flex items-start space-x-2", children: [_jsx(AlertCircle, { className: "w-4 h-4 text-orange-500 mt-1 flex-shrink-0" }), _jsx("span", { className: "text-gray-700", children: con })] }, index))) })] }))] }), product.usage_instructions && (_jsxs("div", { className: "mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4", children: [_jsx("h4", { className: "font-semibold text-blue-900 mb-2", children: "Usage Instructions:" }), _jsx("p", { className: "text-blue-800", children: product.usage_instructions })] })), product.side_effects && product.side_effects.length > 0 && (_jsxs("div", { className: "mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4", children: [_jsx("h4", { className: "font-semibold text-yellow-900 mb-2", children: "Possible Side Effects:" }), _jsx("div", { className: "flex flex-wrap gap-2", children: product.side_effects.map((effect, index) => (_jsx("span", { className: "bg-yellow-100 text-yellow-800 text-sm px-2 py-1 rounded", children: effect }, index))) })] }))] }, product.id))), getProductsForTab().length === 0 && (_jsxs("div", { className: "bg-white p-8 rounded-xl shadow-sm border border-gray-100 text-center", children: [_jsx(Info, { className: "w-12 h-12 text-gray-400 mx-auto mb-4" }), _jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-2", children: "No products found" }), _jsx("p", { className: "text-gray-600", children: "No NRT products available for this category yet." })] }))] })) })), activeTab !== 'overview' && (_jsxs("div", { className: "space-y-8", children: [_jsxs("div", { className: "bg-white p-8 rounded-xl shadow-sm border border-gray-100", children: [_jsxs("h3", { className: "text-2xl font-bold text-gray-900 mb-6", children: [activeTab === 'lozenges' && 'Nicotine Lozenges', activeTab === 'inhalers' && 'Nicotine Inhalers', activeTab === 'sprays' && 'Nicotine Sprays', activeTab === 'gum' && 'Nicotine Gum', activeTab === 'patches' && 'Nicotine Patches'] }), _jsxs("p", { className: "text-gray-700 leading-relaxed mb-4", children: [activeTab === 'lozenges' && 'Nicotine lozenges dissolve slowly in your mouth to provide controlled nicotine release. They\'re available in different strengths and flavors.', activeTab === 'inhalers' && 'Nicotine inhalers provide nicotine vapor that is absorbed through the mouth and throat. They mimic the hand-to-mouth action of smoking.', activeTab === 'sprays' && 'Nicotine sprays deliver nicotine quickly through the mouth or nose. They provide fast relief but require careful dosing.', activeTab === 'gum' && 'Nicotine gum provides nicotine through chewing and helps control withdrawal symptoms. Available in various strengths and flavors.', activeTab === 'patches' && 'Nicotine patches provide steady nicotine delivery through the skin over 16-24 hours. They offer consistent craving control.'] }), _jsx("div", { className: "bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6", children: _jsxs("div", { className: "flex items-start space-x-3", children: [_jsx(Info, { className: "w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" }), _jsx("p", { className: "text-blue-800", children: "Consult with a healthcare provider to determine the best NRT option for your specific needs and medical history." })] }) })] }), getProductsForTab().length > 0 && (_jsxs("div", { children: [_jsx("h4", { className: "text-xl font-bold text-gray-900 mb-6", children: "Recommended Products" }), _jsx("div", { className: "grid md:grid-cols-2 lg:grid-cols-3 gap-6", children: getProductsForTab().map((product) => (_jsxs("div", { className: "bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow", children: [_jsxs("div", { className: "flex items-start justify-between mb-4", children: [_jsx("h5", { className: "font-bold text-gray-900 text-lg leading-tight", children: product.name }), product.effectiveness_rating && (_jsxs("div", { className: "flex items-center space-x-1 bg-primary-subtle px-2 py-1 rounded-full", children: [_jsx(Star, { className: "w-3 h-3 text-green-600 fill-current" }), _jsxs("span", { className: "text-sm font-medium text-green-700", children: [product.effectiveness_rating, "/5"] })] }))] }), _jsxs("div", { className: "space-y-3 mb-6", children: [product.strength && (_jsxs("div", { className: "flex justify-between", children: [_jsx("span", { className: "text-gray-600", children: "Strength:" }), _jsx("span", { className: "font-medium text-gray-900", children: product.strength })] })), product.duration && (_jsxs("div", { className: "flex justify-between", children: [_jsx("span", { className: "text-gray-600", children: "Duration:" }), _jsx("span", { className: "font-medium text-gray-900", children: product.duration })] })), product.manufacturer && (_jsxs("div", { className: "flex justify-between", children: [_jsx("span", { className: "text-gray-600", children: "Brand:" }), _jsx("span", { className: "font-medium text-gray-900", children: product.manufacturer })] }))] }), _jsxs("div", { className: "border-t border-gray-100 pt-4", children: [product.price && (_jsxs("div", { className: "flex items-center justify-between mb-4", children: [_jsxs("span", { className: "text-2xl font-bold text-green-600", children: ["$", product.price] }), product.price_per_unit && (_jsx("span", { className: "text-sm text-gray-500", children: product.price_per_unit }))] })), _jsxs("div", { className: "flex space-x-2", children: [product.buy_link && (_jsxs("a", { href: product.buy_link, target: "_blank", rel: "noopener noreferrer", className: "flex-1 btn-primary px-4 py-2 rounded-lg font-medium text-center transition-colors flex items-center justify-center space-x-2", children: [_jsx(ShoppingCart, { className: "w-4 h-4" }), _jsx("span", { children: "Buy Now" }), _jsx(ExternalLink, { className: "w-3 h-3" })] })), product.compare_link && (_jsx("a", { href: product.compare_link, target: "_blank", rel: "noopener noreferrer", className: "px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors", children: "Compare" }))] }), product.affiliate_note && (_jsx("p", { className: "text-xs text-gray-500 mt-2", children: product.affiliate_note }))] })] }, product.id))) })] })), getProductsForTab().length === 0 && (_jsxs("div", { className: "bg-gray-50 p-8 rounded-xl border border-gray-200 text-center", children: [_jsx(Tablet, { className: "w-12 h-12 text-gray-400 mx-auto mb-4" }), _jsx("h4", { className: "text-lg font-medium text-gray-900 mb-2", children: "Products Coming Soon" }), _jsxs("p", { className: "text-gray-600", children: ["We're working on adding more ", activeTab, " options to help you on your wellness journey."] })] }))] }))] }) }), _jsx("section", { className: "bg-primary text-primary-foreground py-16", children: _jsxs("div", { className: "max-w-4xl mx-auto px-6 text-center", children: [_jsx("h2", { className: "text-3xl font-bold text-primary-foreground mb-4", children: "Ready for Personalized Support?" }), _jsx("p", { className: "text-xl text-primary-foreground mb-8", children: "Mission Fresh offers comprehensive tools to track your progress, manage cravings, and boost your well-being during your wellness journey." }), _jsxs("div", { className: "flex flex-col sm:flex-row justify-center gap-4", children: [_jsxs(Link, { to: "/tools/smokeless-directory", className: "inline-flex items-center justify-center border-2 border-primary-foreground text-primary-foreground px-8 py-3 rounded-lg text-lg font-medium hover:bg-primary-foreground hover:text-primary transition-colors", children: [_jsx(Heart, { className: "w-5 h-5 mr-2" }), "Explore Alternatives", _jsx(ArrowRight, { className: "w-5 h-5 ml-2" })] }), _jsxs(Link, { to: "/auth?mode=signup", className: "inline-flex items-center justify-center bg-secondary text-secondary-foreground px-8 py-3 rounded-lg text-lg font-medium hover:bg-secondary-hover transition-colors border border-border shadow-lg", children: ["Get Started", _jsx(ArrowRight, { className: "w-5 h-5 ml-2" })] })] })] }) })] }));
}
