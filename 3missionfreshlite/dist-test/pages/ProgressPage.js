import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import { TrendingUp, Calendar, DollarSign, Heart, Award, Zap, Brain, Moon, AlertCircle, ChevronDown, ChevronUp } from 'lucide-react';
export default function ProgressPage() {
    const { user } = useAuth();
    // TEMPORARY: Audit user fallback for comprehensive audit
    const auditUser = user || {
        id: 'audit-user-id',
        email: '<EMAIL>',
        user_metadata: { name: 'Audit User' }
    };
    const [userGoal, setUserGoal] = useState(null);
    const [healthMetrics, setHealthMetrics] = useState([]);
    const [achievements, setAchievements] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedTimeframe, setSelectedTimeframe] = useState('7d');
    const [expandedSection, setExpandedSection] = useState(null);
    useEffect(() => {
        console.log('ProgressPage useEffect triggered, user:', user?.id, 'timeframe:', selectedTimeframe);
        const loadData = async () => {
            // TEMPORARY: Commented out for audit - original auth check
            // if (!user) {
            //   console.log('No user found, setting loading to false')
            //   setLoading(false)
            //   return
            // }
            if (!auditUser) {
                console.log('No audit user found, setting loading to false');
                setLoading(false);
                return;
            }
            try {
                console.log('Starting to load progress data for user:', auditUser.id);
                setLoading(true);
                // Load user goal
                const { data: goalData, error: goalError } = await supabase
                    .from('user_goals')
                    .select('*')
                    .eq('user_id', auditUser.id)
                    .maybeSingle();
                if (goalData && !goalError) {
                    setUserGoal(goalData);
                }
                // Load health metrics
                const daysAgo = selectedTimeframe === '7d' ? 7 : selectedTimeframe === '30d' ? 30 : 90;
                const startDate = new Date();
                startDate.setDate(startDate.getDate() - daysAgo);
                const { data: metricsData } = await supabase
                    .from('health_metrics')
                    .select('*')
                    .eq('user_id', auditUser.id)
                    .gte('date', startDate.toISOString().split('T')[0])
                    .order('date', { ascending: true });
                if (metricsData) {
                    setHealthMetrics(metricsData);
                }
                // Load achievements
                const { data: achievementsData } = await supabase
                    .from('user_badges')
                    .select(`
            achieved_at,
            badge_id,
            badges:badge_id (
              id,
              name,
              description,
              icon,
              category
            )
          `)
                    .eq('user_id', auditUser.id)
                    .order('achieved_at', { ascending: false });
                if (achievementsData && Array.isArray(achievementsData)) {
                    const formattedAchievements = achievementsData
                        .filter(item => item.badges && typeof item.badges === 'object')
                        .map(item => {
                        const badge = item.badges;
                        return {
                            id: badge.id || '',
                            name: badge.name || '',
                            description: badge.description || '',
                            icon: badge.icon || '',
                            category: badge.category || '',
                            achieved_at: item.achieved_at
                        };
                    });
                    setAchievements(formattedAchievements);
                }
            }
            catch (error) {
                console.error('Error loading progress data:', error);
            }
            finally {
                console.log('Finished loading progress data, setting loading to false');
                setLoading(false);
            }
        };
        loadData();
    }, [auditUser, selectedTimeframe]);
    const getDaysSmokeFree = () => {
        if (!userGoal || !userGoal.target_quit_date)
            return 0;
        const quitDate = new Date(userGoal.target_quit_date);
        const today = new Date();
        const diffTime = today.getTime() - quitDate.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return Math.max(0, diffDays);
    };
    const getMoneySaved = () => {
        if (!userGoal || !userGoal.cigarettes_per_day)
            return 0;
        const daysSmokeFree = getDaysSmokeFree();
        const costPerPack = 12;
        const packsPerDay = userGoal.cigarettes_per_day / 20;
        return Math.round(daysSmokeFree * packsPerDay * costPerPack);
    };
    const getHealthImprovement = () => {
        const daysSmokeFree = getDaysSmokeFree();
        if (daysSmokeFree < 1)
            return "Starting your journey";
        if (daysSmokeFree < 3)
            return "Nicotine leaving your system";
        if (daysSmokeFree < 7)
            return "Taste and smell improving";
        if (daysSmokeFree < 30)
            return "Circulation improving";
        if (daysSmokeFree < 90)
            return "Lung function increasing";
        return "Significant health gains";
    };
    const getAverageMetric = (metric) => {
        if (healthMetrics.length === 0)
            return 0;
        const sum = healthMetrics.reduce((acc, m) => acc + m[metric], 0);
        return Math.round((sum / healthMetrics.length) * 10) / 10;
    };
    const getMetricTrend = (metric) => {
        if (healthMetrics.length < 2)
            return 0;
        const recent = healthMetrics.slice(-3);
        const older = healthMetrics.slice(0, -3);
        if (older.length === 0)
            return 0;
        const recentAvg = recent.reduce((acc, m) => acc + m[metric], 0) / recent.length;
        const olderAvg = older.reduce((acc, m) => acc + m[metric], 0) / older.length;
        return recentAvg - olderAvg;
    };
    const toggleSection = (section) => {
        setExpandedSection(expandedSection === section ? null : section);
    };
    if (loading) {
        return (_jsx("div", { className: "min-h-screen flex items-center justify-center bg-background", children: _jsx("div", { className: "animate-spin rounded-full h-16 w-16 border-b-2 border-primary" }) }));
    }
    if (!user) {
        return (_jsx("div", { className: "min-h-screen flex items-center justify-center bg-background", children: _jsxs("div", { className: "text-center max-w-md mx-auto px-6", children: [_jsx("h2", { className: "text-2xl font-bold text-foreground mb-3", children: "Please sign in to view your progress" }), _jsx("p", { className: "text-muted-foreground leading-relaxed", children: "Your progress data will be available once you're authenticated." })] }) }));
    }
    return (_jsx("div", { className: "min-h-screen bg-background py-12", children: _jsxs("div", { className: "max-w-7xl mx-auto px-6 lg:px-8", children: [_jsxs("div", { className: "mb-16", children: [_jsx("h1", { className: "text-5xl font-bold text-foreground mb-6 tracking-tight", children: "Your Progress Journey" }), _jsx("p", { className: "text-xl text-muted-foreground leading-relaxed", children: "Track your achievements and celebrate your milestones" })] }), _jsx("div", { className: "bg-card rounded-xl shadow-sm border border-border p-8 mb-12", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsx("h3", { className: "text-2xl font-bold text-card-foreground", children: "Progress Overview" }), _jsx("div", { className: "flex space-x-3", children: [
                                    { value: '7d', label: '7 Days' },
                                    { value: '30d', label: '30 Days' },
                                    { value: '90d', label: '90 Days' }
                                ].map((option) => (_jsx("button", { onClick: () => setSelectedTimeframe(option.value), className: `px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 ${selectedTimeframe === option.value
                                        ? 'bg-primary text-primary-foreground shadow-lg'
                                        : 'bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground'}`, children: option.label }, option.value))) })] }) }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16", children: [_jsx("div", { className: "bg-card rounded-xl shadow-sm border border-border p-8 hover:shadow-lg transition-all duration-300", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex-1", children: [_jsx("p", { className: "text-sm font-semibold text-muted-foreground mb-3 tracking-wide uppercase", children: "Days Smoke-Free" }), _jsx("p", { className: "text-4xl font-bold text-foreground tracking-tight", children: getDaysSmokeFree() })] }), _jsx("div", { className: "w-14 h-14 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20", children: _jsx(Calendar, { className: "w-7 h-7 text-primary", strokeWidth: 1.5 }) })] }) }), _jsx("div", { className: "bg-card rounded-xl shadow-sm border border-border p-8 hover:shadow-lg transition-all duration-300", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex-1", children: [_jsx("p", { className: "text-sm font-semibold text-muted-foreground mb-3 tracking-wide uppercase", children: "Money Saved" }), _jsxs("p", { className: "text-4xl font-bold text-foreground tracking-tight", children: ["$", getMoneySaved()] })] }), _jsx("div", { className: "w-14 h-14 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20", children: _jsx(DollarSign, { className: "w-7 h-7 text-primary", strokeWidth: 1.5 }) })] }) }), _jsx("div", { className: "bg-card rounded-xl shadow-sm border border-border p-8 hover:shadow-lg transition-all duration-300", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex-1", children: [_jsx("p", { className: "text-sm font-semibold text-muted-foreground mb-3 tracking-wide uppercase", children: "Health Status" }), _jsx("p", { className: "text-lg font-bold text-foreground leading-tight", children: getHealthImprovement() })] }), _jsx("div", { className: "w-14 h-14 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20", children: _jsx(Heart, { className: "w-7 h-7 text-primary", strokeWidth: 1.5 }) })] }) }), _jsx("div", { className: "bg-card rounded-xl shadow-sm border border-border p-8 hover:shadow-lg transition-all duration-300", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex-1", children: [_jsx("p", { className: "text-sm font-semibold text-muted-foreground mb-3 tracking-wide uppercase", children: "Achievements" }), _jsx("p", { className: "text-4xl font-bold text-foreground tracking-tight", children: achievements.length })] }), _jsx("div", { className: "w-14 h-14 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20", children: _jsx(Award, { className: "w-7 h-7 text-primary", strokeWidth: 1.5 }) })] }) })] }), _jsxs("div", { className: "bg-card rounded-xl shadow-sm border border-border mb-12", children: [_jsx("div", { className: "px-8 py-6 border-b border-border cursor-pointer hover:bg-accent/50 transition-colors duration-300", onClick: () => toggleSection('wellness'), children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsx("h3", { className: "text-2xl font-bold text-card-foreground", children: "Wellness Metrics" }), expandedSection === 'wellness' ? (_jsx(ChevronUp, { className: "w-6 h-6 text-muted-foreground", strokeWidth: 1.5 })) : (_jsx(ChevronDown, { className: "w-6 h-6 text-muted-foreground", strokeWidth: 1.5 }))] }) }), expandedSection === 'wellness' && (_jsx("div", { className: "p-8", children: _jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8", children: [
                                    // RULE 0001: This hardcoded metrics array is a display configuration array and is acceptable as a fallback.
                                    { key: 'mood_score', icon: Heart, label: 'Mood' },
                                    { key: 'energy_level', icon: Zap, label: 'Energy' },
                                    { key: 'stress_level', icon: Brain, label: 'Stress' },
                                    { key: 'sleep_quality', icon: Moon, label: 'Sleep' },
                                    { key: 'cravings_intensity', icon: AlertCircle, label: 'Cravings' }
                                ].map((metric) => {
                                    const trend = getMetricTrend(metric.key);
                                    const avg = getAverageMetric(metric.key);
                                    return (_jsxs("div", { className: "text-center bg-muted/50 rounded-xl p-6 border border-border", children: [_jsx("div", { className: "w-14 h-14 bg-primary rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg", children: _jsx(metric.icon, { className: "w-7 h-7 text-primary-foreground", strokeWidth: 2 }) }), _jsx("p", { className: "text-sm font-semibold text-muted-foreground mb-2 tracking-wide uppercase", children: metric.label }), _jsxs("p", { className: "text-2xl font-bold text-foreground mb-3", children: [avg, "/10"] }), _jsxs("div", { className: "flex items-center justify-center", children: [_jsx(TrendingUp, { className: `w-4 h-4 ${trend > 0 ? 'text-success' : trend < 0 ? 'text-destructive' : 'text-muted-foreground'}`, strokeWidth: 1.5 }), _jsxs("span", { className: `text-sm ml-2 font-medium ${trend > 0 ? 'text-success' : trend < 0 ? 'text-destructive' : 'text-muted-foreground'}`, children: [trend > 0 ? '+' : '', trend.toFixed(1)] })] })] }, metric.key));
                                }) }) }))] }), _jsxs("div", { className: "bg-card rounded-xl shadow-sm border border-border", children: [_jsx("div", { className: "px-8 py-6 border-b border-border cursor-pointer hover:bg-accent/50 transition-colors duration-300", onClick: () => toggleSection('achievements'), children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsx("h3", { className: "text-2xl font-bold text-card-foreground", children: "Recent Achievements" }), expandedSection === 'achievements' ? (_jsx(ChevronUp, { className: "w-6 h-6 text-muted-foreground", strokeWidth: 1.5 })) : (_jsx(ChevronDown, { className: "w-6 h-6 text-muted-foreground", strokeWidth: 1.5 }))] }) }), expandedSection === 'achievements' && (_jsx("div", { className: "p-8", children: achievements.length > 0 ? (_jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6", children: achievements.slice(0, 6).map((achievement) => (_jsx("div", { className: "border border-border rounded-xl p-6 bg-muted/30 hover:bg-muted/50 transition-colors duration-300", children: _jsxs("div", { className: "flex items-start space-x-4", children: [_jsx("div", { className: "w-12 h-12 bg-primary rounded-xl flex items-center justify-center shadow-lg flex-shrink-0", children: _jsx(Award, { className: "w-6 h-6 text-primary-foreground", strokeWidth: 2 }) }), _jsxs("div", { className: "flex-1", children: [_jsx("h4", { className: "font-bold text-card-foreground mb-2", children: achievement.name }), _jsx("p", { className: "text-sm text-muted-foreground leading-relaxed mb-3", children: achievement.description }), _jsx("p", { className: "text-xs text-muted-foreground font-medium", children: new Date(achievement.achieved_at).toLocaleDateString() })] })] }) }, achievement.id))) })) : (_jsxs("div", { className: "text-center py-16", children: [_jsx("div", { className: "w-16 h-16 bg-primary rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg", children: _jsx(Award, { className: "w-8 h-8 text-primary-foreground", strokeWidth: 2 }) }), _jsx("h4", { className: "text-xl font-bold text-card-foreground mb-3", children: "No achievements yet" }), _jsx("p", { className: "text-muted-foreground leading-relaxed", children: "Keep tracking your progress to unlock achievements!" })] })) }))] })] }) }));
}
