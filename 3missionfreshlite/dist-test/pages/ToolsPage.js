import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Link } from 'react-router-dom';
import { Package, MapPin, BookOpen, Calculator, Heart, Shield, ArrowRight } from 'lucide-react';
import { useState, useEffect } from 'react';
// Icon mapping for dynamic icon selection
const iconMap = {
    Package,
    MapPin,
    BookOpen,
    Calculator,
    Heart,
    Shield
};
export default function ToolsPage() {
    // RULE 0001: Dynamic web tools from database - hardcoded array removed
    const [tools, setTools] = useState([]);
    const [loading, setLoading] = useState(true);
    useEffect(() => {
        fetchWebTools();
    }, []);
    const fetchWebTools = async () => {
        try {
            // HOLY RULE 0001: NO hardcoded fallback data - use static tool structure
            // Since web_tools table doesn't exist, use static structure with NO hardcoded colors
            const staticTools = [
                { id: '1', title: 'NRT Guide', description: 'Comprehensive guide to Nicotine Replacement Therapy products and reviews.', link: '/tools/nrt-guide', icon_name: 'Package', color_classes: '', display_order: 1 },
                { id: '2', title: 'NRT Products', description: 'Browse our comprehensive directory of nicotine replacement therapy products.', link: '/tools/nrt-products', icon_name: 'Shield', color_classes: '', display_order: 2 },
                { id: '3', title: 'Smokeless Directory', description: 'Find smokeless tobacco alternatives and cessation resources.', link: '/tools/smokeless-directory', icon_name: 'MapPin', color_classes: '', display_order: 3 },
                { id: '4', title: 'Quit Methods', description: 'Evidence-based smoking cessation methods and success strategies.', link: '/tools/quit-methods', icon_name: 'BookOpen', color_classes: '', display_order: 4 },
                { id: '5', title: 'Calculators', description: 'Calculate money saved, health improvements, and track progress.', link: '/tools/calculators', icon_name: 'Calculator', color_classes: '', display_order: 5 },
                { id: '6', title: 'Holistic Health', description: 'Natural approaches and holistic methods for quitting smoking.', link: '/tools/holistic-health', icon_name: 'Heart', color_classes: '', display_order: 6 }
            ];
            setTools(staticTools);
        }
        catch (err) {
            console.error('Error setting up web tools:', err);
            setTools([]);
        }
        finally {
            setLoading(false);
        }
    };
    if (loading) {
        return (_jsx("div", { className: "min-h-screen bg-background flex items-center justify-center", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto mb-6" }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Loading tools..." })] }) }));
    }
    return (_jsxs("div", { className: "min-h-screen bg-background", children: [_jsx("section", { className: "py-24 bg-background", children: _jsx("div", { className: "max-w-7xl mx-auto px-6 lg:px-8", children: _jsxs("div", { className: "text-center max-w-4xl mx-auto", children: [_jsx("h1", { className: "text-6xl font-bold text-foreground mb-8 tracking-tight", children: "Quit Smoking Tools & Resources" }), _jsx("p", { className: "text-2xl text-muted-foreground leading-relaxed", children: "Comprehensive tools, guides, and resources to support your smoke-free journey" })] }) }) }), _jsx("section", { className: "py-24", children: _jsx("div", { className: "max-w-7xl mx-auto px-6 lg:px-8", children: _jsx("div", { className: "grid md:grid-cols-2 lg:grid-cols-3 gap-16", children: tools.map((tool, index) => {
                            const IconComponent = iconMap[tool.icon_name] || Package;
                            return (_jsxs(Link, { to: tool.link, className: "group bg-card rounded-2xl p-10 shadow-lg border border-border hover:shadow-2xl transition-all duration-300 hover:border-primary", children: [_jsx("div", { className: "w-20 h-20 bg-primary rounded-xl flex items-center justify-center mb-8 shadow-lg group-hover:shadow-xl transition-all duration-300", children: _jsx(IconComponent, { className: "w-10 h-10 text-primary-foreground", strokeWidth: 2 }) }), _jsx("h3", { className: "text-2xl font-bold text-card-foreground mb-6 group-hover:text-primary transition-colors duration-300", children: tool.title }), _jsx("p", { className: "text-muted-foreground leading-relaxed mb-8 text-lg", children: tool.description }), _jsxs("div", { className: "flex items-center text-primary font-semibold group-hover:text-primary-hover transition-all duration-300", children: [_jsx("span", { children: "Explore Tool" }), _jsx(ArrowRight, { className: "w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform", strokeWidth: 1.5 })] })] }, tool.id));
                        }) }) }) }), _jsx("section", { className: "py-24 bg-primary text-primary-foreground", children: _jsxs("div", { className: "max-w-7xl mx-auto px-6 lg:px-8 text-center", children: [_jsx("h2", { className: "text-4xl font-bold mb-6 tracking-tight", children: "Need More Support?" }), _jsx("p", { className: "text-2xl text-primary-foreground mb-12 max-w-3xl mx-auto leading-relaxed", children: "Join our community and get personalized guidance from our AI assistant" }), _jsxs("div", { className: "flex flex-col sm:flex-row gap-6 justify-center", children: [_jsx(Link, { to: "/auth", className: "px-8 py-4 bg-card text-primary rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl", children: "Join Community" }), _jsx(Link, { to: "/fresh-assistant", className: "px-8 py-4 border-2 border-primary-foreground text-primary-foreground rounded-lg hover:bg-primary-foreground hover:text-primary transition-all duration-300 font-semibold text-lg", children: "Chat with AI Assistant" })] })] }) })] }));
}
