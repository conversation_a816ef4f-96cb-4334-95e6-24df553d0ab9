import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Clock, Play, Pause, RotateCcw, Brain, Target, Volume2, Loader2 } from 'lucide-react';
const FocusPage = () => {
    const { user, loading } = useAuth();
    const navigate = useNavigate();
    // RULE 0001: Real database-driven state management
    const [isActive, setIsActive] = useState(false);
    const [time, setTime] = useState(25 * 60); // 25 minutes in seconds
    const [selectedDuration, setSelectedDuration] = useState(25);
    const [sessionType, setSessionType] = useState('focus');
    const [focusSessions, setFocusSessions] = useState([]);
    const [focusTips, setFocusTips] = useState([]);
    const [focusDurations, setFocusDurations] = useState([]);
    const [isLoadingSessions, setIsLoadingSessions] = useState(true);
    const [isLoadingTips, setIsLoadingTips] = useState(true);
    const [isLoadingDurations, setIsLoadingDurations] = useState(true);
    const intervalRef = useRef(null);
    // RULE 0001: Authentication and data loading
    useEffect(() => {
        if (!loading && !user) {
            navigate('/auth');
            return;
        }
        if (user) {
            loadFocusSessions();
            loadFocusTips();
            loadFocusDurations();
        }
    }, [user, loading, navigate]);
    // Timer effect
    useEffect(() => {
        if (isActive && time > 0) {
            intervalRef.current = setInterval(() => {
                setTime((prevTime) => {
                    if (prevTime <= 1) {
                        setIsActive(false);
                        saveFocusSession();
                        return 0;
                    }
                    return prevTime - 1;
                });
            }, 1000);
        }
        else {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
        }
        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, [isActive, time]);
    const loadFocusSessions = async () => {
        if (!user)
            return;
        try {
            setIsLoadingSessions(true);
            const { data, error } = await supabase
                .from('focus_sessions')
                .select('*')
                .eq('user_id', user.id)
                .order('completed_at', { ascending: false })
                .limit(10);
            if (error)
                throw error;
            setFocusSessions(data || []);
        }
        catch (error) {
            console.error('Error loading focus sessions:', error);
        }
        finally {
            setIsLoadingSessions(false);
        }
    };
    const loadFocusTips = async () => {
        try {
            setIsLoadingTips(true);
            const { data, error } = await supabase
                .from('focus_tips')
                .select('*')
                .order('display_order');
            if (error || !data?.length) {
                // Fallback tips if table doesn't exist
                const fallbackTips = [
                    { id: '1', category: 'workspace', tip_text: 'Remove distractions from your workspace', display_order: 1 },
                    { id: '2', category: 'task', tip_text: 'Choose one specific task to focus on', display_order: 2 },
                    { id: '3', category: 'notifications', tip_text: 'Turn off notifications during work sessions', display_order: 3 },
                    { id: '4', category: 'health', tip_text: 'Keep water and healthy snacks nearby', display_order: 4 },
                    { id: '5', category: 'ergonomics', tip_text: 'Use comfortable lighting and ergonomics', display_order: 5 }
                ];
                setFocusTips(fallbackTips);
            }
            else {
                setFocusTips(data);
            }
        }
        catch (error) {
            console.error('Error loading focus tips:', error);
            // Fallback on error
            const fallbackTips = [
                { id: '1', category: 'focus', tip_text: 'Remove distractions and focus on one task at a time', display_order: 1 }
            ];
            setFocusTips(fallbackTips);
        }
        finally {
            setIsLoadingTips(false);
        }
    };
    const loadFocusDurations = async () => {
        try {
            setIsLoadingDurations(true);
            const { data, error } = await supabase
                .from('focus_durations')
                .select('*')
                .order('display_order');
            if (error || !data?.length) {
                // Fallback durations if table doesn't exist
                const fallbackDurations = [
                    { id: '1', label: '15 min', value: 15, display_order: 1 },
                    { id: '2', label: '25 min', value: 25, display_order: 2 },
                    { id: '3', label: '30 min', value: 30, display_order: 3 },
                    { id: '4', label: '45 min', value: 45, display_order: 4 },
                    { id: '5', label: '60 min', value: 60, display_order: 5 }
                ];
                setFocusDurations(fallbackDurations);
            }
            else {
                setFocusDurations(data);
            }
        }
        catch (error) {
            console.error('Error loading focus durations:', error);
            // Fallback on error
            const fallbackDurations = [
                { id: '1', label: '25 min', value: 25, display_order: 1 }
            ];
            setFocusDurations(fallbackDurations);
        }
        finally {
            setIsLoadingDurations(false);
        }
    };
    const saveFocusSession = async () => {
        if (!user)
            return;
        try {
            const { error } = await supabase
                .from('focus_sessions')
                .insert({
                user_id: user.id,
                duration_minutes: selectedDuration,
                session_type: sessionType,
                completed_at: new Date().toISOString()
            });
            if (error)
                throw error;
            loadFocusSessions(); // Reload sessions to show the new one
        }
        catch (error) {
            console.error('Error saving focus session:', error);
        }
    };
    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };
    const handleStart = () => {
        setIsActive(true);
    };
    const handlePause = () => {
        setIsActive(false);
    };
    const handleReset = () => {
        setIsActive(false);
        setTime(selectedDuration * 60);
    };
    const handleDurationChange = (duration) => {
        setSelectedDuration(duration);
        setTime(duration * 60);
        setIsActive(false);
    };
    // RULE 0001: Loading state check
    if (loading || isLoadingSessions || isLoadingTips || isLoadingDurations) {
        return (_jsx("div", { className: "min-h-screen bg-background", children: _jsx("div", { className: "container mx-auto px-4 py-8", children: _jsx("div", { className: "flex items-center justify-center min-h-[400px]", children: _jsx(Loader2, { className: "h-8 w-8 animate-spin text-primary" }) }) }) }));
    }
    return (_jsx("div", { className: "min-h-screen bg-background", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6 lg:px-8 py-12", children: [_jsxs("div", { className: "text-center mb-16", children: [_jsxs("div", { className: "flex items-center justify-center mb-6", children: [_jsx("div", { className: "w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mr-6 border border-primary/20", children: _jsx(Brain, { className: "w-8 h-8 text-primary", strokeWidth: 1.5 }) }), _jsx("h1", { className: "text-5xl font-bold text-foreground tracking-tight", children: "Focus Timer" })] }), _jsx("p", { className: "text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed", children: "Use the Pomodoro Technique to improve focus and productivity. Work in focused intervals with regular breaks." })] }), _jsxs("div", { className: "max-w-3xl mx-auto", children: [_jsxs("div", { className: "bg-card rounded-2xl shadow-xl border border-border p-12 text-center mb-12", children: [_jsxs("div", { className: "mb-10", children: [_jsx("div", { className: "text-8xl font-mono font-bold text-foreground mb-4 tracking-tight", children: formatTime(time) }), _jsxs("div", { className: "text-2xl text-muted-foreground capitalize font-medium", children: [sessionType, " Session"] })] }), _jsxs("div", { className: "flex items-center justify-center gap-6 mb-12", children: [_jsx("button", { onClick: isActive ? handlePause : handleStart, className: "bg-primary text-primary-foreground px-10 py-5 text-xl font-semibold rounded-xl flex items-center gap-4 hover:bg-primary-hover transition-all duration-300 shadow-lg hover:shadow-xl", children: isActive ? (_jsxs(_Fragment, { children: [_jsx(Pause, { className: "w-7 h-7", strokeWidth: 1.5 }), "Pause"] })) : (_jsxs(_Fragment, { children: [_jsx(Play, { className: "w-7 h-7", strokeWidth: 1.5 }), "Start"] })) }), _jsxs("button", { onClick: handleReset, className: "bg-muted text-muted-foreground px-8 py-5 text-xl font-semibold rounded-xl flex items-center gap-3 hover:bg-accent hover:text-accent-foreground transition-all duration-300", children: [_jsx(RotateCcw, { className: "w-6 h-6", strokeWidth: 1.5 }), "Reset"] })] }), _jsxs("div", { className: "border-t border-border pt-8", children: [_jsx("h3", { className: "text-2xl font-bold text-foreground mb-6", children: "Session Duration" }), _jsx("div", { className: "flex flex-wrap justify-center gap-4", children: focusDurations.map((duration) => (_jsx("button", { onClick: () => handleDurationChange(duration.value), className: `px-6 py-3 rounded-lg font-semibold transition-all duration-300 text-lg ${selectedDuration === duration.value
                                                    ? 'bg-primary text-primary-foreground shadow-lg'
                                                    : 'bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground'}`, children: duration.label }, duration.value))) })] })] }), _jsxs("div", { className: "grid md:grid-cols-2 gap-8", children: [_jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8", children: [_jsxs("div", { className: "flex items-center mb-6", children: [_jsx("div", { className: "w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 border border-primary/20", children: _jsx(Target, { className: "w-6 h-6 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "text-2xl font-bold text-foreground", children: "Focus Tips" })] }), _jsx("ul", { className: "space-y-4 text-muted-foreground", children: focusTips.map((tip) => (_jsxs("li", { className: "flex items-start", children: [_jsx("div", { className: "w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0" }), _jsx("span", { className: "text-lg leading-relaxed", children: tip.tip_text })] }, tip.id))) })] }), _jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8", children: [_jsxs("div", { className: "flex items-center mb-6", children: [_jsx("div", { className: "w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 border border-primary/20", children: _jsx(Volume2, { className: "w-6 h-6 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "text-2xl font-bold text-foreground", children: "Breathing Exercise" })] }), _jsxs("div", { className: "text-muted-foreground", children: [_jsx("p", { className: "mb-6 text-lg", children: "Try the 4-7-8 breathing technique:" }), _jsxs("ul", { className: "space-y-3", children: [_jsxs("li", { className: "flex items-start", children: [_jsx("div", { className: "w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0" }), _jsx("span", { className: "text-lg leading-relaxed", children: "Inhale for 4 counts" })] }), _jsxs("li", { className: "flex items-start", children: [_jsx("div", { className: "w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0" }), _jsx("span", { className: "text-lg leading-relaxed", children: "Hold breath for 7 counts" })] }), _jsxs("li", { className: "flex items-start", children: [_jsx("div", { className: "w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0" }), _jsx("span", { className: "text-lg leading-relaxed", children: "Exhale for 8 counts" })] }), _jsxs("li", { className: "flex items-start", children: [_jsx("div", { className: "w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0" }), _jsx("span", { className: "text-lg leading-relaxed", children: "Repeat 3-4 times" })] })] })] })] })] }), _jsxs("div", { className: "mt-12 bg-card rounded-xl shadow-lg border border-border p-8", children: [_jsxs("div", { className: "flex items-center mb-6", children: [_jsx("div", { className: "w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 border border-primary/20", children: _jsx(Clock, { className: "w-6 h-6 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "text-2xl font-bold text-foreground", children: "Recent Sessions" })] }), focusSessions.length > 0 ? (_jsx("div", { className: "space-y-4", children: focusSessions.slice(0, 5).map((session) => (_jsxs("div", { className: "flex justify-between items-center py-4 border-b border-border last:border-b-0", children: [_jsxs("div", { children: [_jsxs("div", { className: "font-bold text-foreground capitalize text-lg", children: [session.session_type, " Session"] }), _jsxs("div", { className: "text-muted-foreground mt-1", children: [session.duration_minutes, " minutes"] })] }), _jsx("div", { className: "text-muted-foreground", children: new Date(session.completed_at).toLocaleDateString() })] }, session.id))) })) : (_jsxs("div", { className: "text-center text-muted-foreground py-16", children: [_jsx("div", { className: "w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20", children: _jsx(Clock, { className: "w-8 h-8 text-primary", strokeWidth: 1.5 }) }), _jsx("p", { className: "text-lg", children: "Start your first focus session to begin tracking!" })] }))] })] })] }) }));
};
export default FocusPage;
