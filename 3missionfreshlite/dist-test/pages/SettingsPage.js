import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Settings, User, Shield, Download, Trash2, Save, Check } from 'lucide-react';
export default function SettingsPage() {
    const { user, loading, signOut } = useAuth();
    const navigate = useNavigate();
    const [settings, setSettings] = useState(null);
    const [profile, setProfile] = useState(null);
    const [isLoadingSettings, setIsLoadingSettings] = useState(true);
    const [isLoadingProfile, setIsLoadingProfile] = useState(true);
    const [activeTab, setActiveTab] = useState('profile');
    const [isSaving, setIsSaving] = useState(false);
    const [saveMessage, setSaveMessage] = useState('');
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    // Profile form state
    const [displayName, setDisplayName] = useState('');
    const [bio, setBio] = useState('');
    const [phone, setPhone] = useState('');
    const [emergencyContact, setEmergencyContact] = useState('');
    // Settings form state
    const [notificationsEnabled, setNotificationsEnabled] = useState(true);
    const [emailReminders, setEmailReminders] = useState(true);
    const [privacyMode, setPrivacyMode] = useState(false);
    const [dataSharing, setDataSharing] = useState(false);
    const [theme, setTheme] = useState('light');
    const [language, setLanguage] = useState('en');
    const [timezone, setTimezone] = useState('UTC');
    useEffect(() => {
        if (!loading && !user) {
            navigate('/auth');
            return;
        }
        if (user) {
            loadSettings();
            loadProfile();
        }
    }, [user, loading, navigate]);
    const loadSettings = async () => {
        if (!user)
            return;
        try {
            setIsLoadingSettings(true);
            const { data, error } = await supabase
                .from('user_settings')
                .select('*')
                .eq('user_id', user.id)
                .single();
            if (error && error.code !== 'PGRST116')
                throw error;
            if (data) {
                setSettings(data);
                setNotificationsEnabled(data.notifications_enabled);
                setEmailReminders(data.email_reminders);
                setPrivacyMode(data.privacy_mode);
                setDataSharing(data.data_sharing);
                setTheme(data.theme);
                setLanguage(data.language);
                setTimezone(data.timezone);
            }
        }
        catch (error) {
            console.error('Error loading settings:', error);
        }
        finally {
            setIsLoadingSettings(false);
        }
    };
    const loadProfile = async () => {
        if (!user)
            return;
        try {
            setIsLoadingProfile(true);
            const { data, error } = await supabase
                .from('user_profiles')
                .select('*')
                .eq('user_id', user.id)
                .single();
            if (error && error.code !== 'PGRST116')
                throw error;
            if (data) {
                setProfile(data);
                setDisplayName(data.display_name || '');
                setBio(data.bio || '');
                setPhone(data.phone || '');
                setEmergencyContact(data.emergency_contact || '');
            }
        }
        catch (error) {
            console.error('Error loading profile:', error);
        }
        finally {
            setIsLoadingProfile(false);
        }
    };
    const saveProfile = async () => {
        if (!user)
            return;
        try {
            setIsSaving(true);
            const { data, error } = await supabase
                .from('user_profiles')
                .upsert({
                user_id: user.id,
                display_name: displayName,
                bio: bio,
                phone: phone,
                emergency_contact: emergencyContact,
                updated_at: new Date().toISOString()
            })
                .select()
                .single();
            if (error)
                throw error;
            setProfile(data);
            setSaveMessage('Profile updated successfully!');
            setTimeout(() => setSaveMessage(''), 3000);
        }
        catch (error) {
            console.error('Error saving profile:', error);
            setSaveMessage('Error saving profile. Please try again.');
        }
        finally {
            setIsSaving(false);
        }
    };
    const saveSettings = async () => {
        if (!user)
            return;
        try {
            setIsSaving(true);
            const { data, error } = await supabase
                .from('user_settings')
                .upsert({
                user_id: user.id,
                notifications_enabled: notificationsEnabled,
                email_reminders: emailReminders,
                privacy_mode: privacyMode,
                data_sharing: dataSharing,
                theme: theme,
                language: language,
                timezone: timezone,
                updated_at: new Date().toISOString()
            })
                .select()
                .single();
            if (error)
                throw error;
            setSettings(data);
            setSaveMessage('Settings updated successfully!');
            setTimeout(() => setSaveMessage(''), 3000);
        }
        catch (error) {
            console.error('Error saving settings:', error);
            setSaveMessage('Error saving settings. Please try again.');
        }
        finally {
            setIsSaving(false);
        }
    };
    const exportData = async () => {
        if (!user)
            return;
        try {
            // Export user data
            const { data: profileData } = await supabase
                .from('user_profiles')
                .select('*')
                .eq('user_id', user.id);
            const { data: settingsData } = await supabase
                .from('user_settings')
                .select('*')
                .eq('user_id', user.id);
            const { data: healthData } = await supabase
                .from('health_metrics')
                .select('*')
                .eq('user_id', user.id);
            const exportData = {
                profile: profileData,
                settings: settingsData,
                health_metrics: healthData,
                exported_at: new Date().toISOString()
            };
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `mission-fresh-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            setSaveMessage('Data exported successfully!');
            setTimeout(() => setSaveMessage(''), 3000);
        }
        catch (error) {
            console.error('Error exporting data:', error);
            setSaveMessage('Error exporting data. Please try again.');
        }
    };
    const deleteAccount = async () => {
        if (!user)
            return;
        try {
            // In a real app, this would trigger a server-side deletion process
            // For now, just sign out and show a message
            await signOut();
            navigate('/');
            alert('Account deletion initiated. Please contact support to complete the process.');
        }
        catch (error) {
            console.error('Error deleting account:', error);
            setSaveMessage('Error initiating account deletion. Please contact support.');
        }
    };
    if (loading) {
        return (_jsx("div", { className: "min-h-screen bg-background flex items-center justify-center", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto" }), _jsx("p", { className: "mt-6 text-muted-foreground text-lg", children: "Loading settings..." })] }) }));
    }
    return (_jsx("div", { className: "min-h-screen bg-background", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6 lg:px-8 py-12", children: [_jsxs("div", { className: "mb-16", children: [_jsxs("h1", { className: "text-5xl font-bold text-foreground flex items-center mb-6 tracking-tight", children: [_jsx("div", { className: "w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mr-6 border border-primary/20", children: _jsx(Settings, { className: "h-8 w-8 text-primary", strokeWidth: 1.5 }) }), "Settings"] }), _jsx("p", { className: "text-xl text-muted-foreground leading-relaxed", children: "Manage your account, preferences, and privacy settings." })] }), saveMessage && (_jsxs("div", { className: `mb-8 p-6 rounded-xl flex items-center shadow-lg border ${saveMessage.includes('Error')
                        ? 'bg-destructive/10 text-destructive border-destructive/20'
                        : 'bg-primary/10 text-primary border-primary/20'}`, children: [_jsx(Check, { className: "mr-3 h-6 w-6", strokeWidth: 1.5 }), _jsx("span", { className: "text-lg font-medium", children: saveMessage })] })), _jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border", children: [_jsx("div", { className: "border-b border-border", children: _jsx("nav", { className: "flex space-x-12 px-8", children: [
                                    { id: 'profile', label: 'Profile', icon: User },
                                    { id: 'preferences', label: 'Preferences', icon: Settings },
                                    { id: 'privacy', label: 'Privacy', icon: Shield },
                                    { id: 'account', label: 'Account', icon: Trash2 }
                                ].map((tab) => {
                                    const Icon = tab.icon;
                                    return (_jsxs("button", { onClick: () => setActiveTab(tab.id), className: `py-6 px-2 border-b-2 font-semibold text-lg flex items-center transition-all duration-300 ${activeTab === tab.id
                                            ? 'border-primary text-primary'
                                            : 'border-transparent text-muted-foreground hover:text-foreground'}`, children: [_jsx(Icon, { className: "mr-3 h-5 w-5", strokeWidth: 1.5 }), tab.label] }, tab.id));
                                }) }) }), _jsxs("div", { className: "p-10", children: [activeTab === 'profile' && (_jsxs("div", { className: "space-y-8", children: [_jsx("h3", { className: "text-2xl font-bold text-card-foreground", children: "Profile Information" }), _jsxs("div", { className: "grid grid-cols-1 gap-8 sm:grid-cols-2", children: [_jsxs("div", { children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground mb-3", children: "Display Name" }), _jsx("input", { type: "text", value: displayName, onChange: (e) => setDisplayName(e.target.value), className: "w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg", placeholder: "How should we address you?" })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground mb-3", children: "Phone Number" }), _jsx("input", { type: "tel", value: phone, onChange: (e) => setPhone(e.target.value), className: "w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg", placeholder: "Your phone number" })] })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground mb-3", children: "Bio" }), _jsx("textarea", { value: bio, onChange: (e) => setBio(e.target.value), rows: 4, className: "w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg leading-relaxed resize-none", placeholder: "Tell us a bit about yourself..." })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground mb-3", children: "Emergency Contact" }), _jsx("input", { type: "text", value: emergencyContact, onChange: (e) => setEmergencyContact(e.target.value), className: "w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg", placeholder: "Name and phone number" })] }), _jsxs("button", { onClick: saveProfile, disabled: isSaving, className: "bg-primary text-primary-foreground px-8 py-4 rounded-lg hover:bg-primary-hover disabled:opacity-50 transition-all duration-300 flex items-center font-semibold text-lg shadow-lg hover:shadow-xl", children: [_jsx(Save, { className: "mr-3 h-5 w-5", strokeWidth: 1.5 }), isSaving ? 'Saving...' : 'Save Profile'] })] })), activeTab === 'preferences' && (_jsxs("div", { className: "space-y-8", children: [_jsx("h3", { className: "text-2xl font-bold text-card-foreground", children: "Preferences" }), _jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex items-center justify-between p-6 border border-border rounded-xl", children: [_jsxs("div", { children: [_jsx("div", { className: "font-bold text-card-foreground text-lg", children: "Notifications" }), _jsx("div", { className: "text-muted-foreground", children: "Receive app notifications" })] }), _jsx("button", { onClick: () => setNotificationsEnabled(!notificationsEnabled), className: `relative inline-flex h-8 w-14 items-center rounded-full transition-all duration-300 ${notificationsEnabled ? 'bg-primary' : 'bg-muted'}`, children: _jsx("span", { className: `inline-block h-6 w-6 transform rounded-full bg-white transition-transform shadow-lg ${notificationsEnabled ? 'translate-x-7' : 'translate-x-1'}` }) })] }), _jsxs("div", { className: "flex items-center justify-between p-6 border border-border rounded-xl", children: [_jsxs("div", { children: [_jsx("div", { className: "font-bold text-card-foreground text-lg", children: "Email Reminders" }), _jsx("div", { className: "text-muted-foreground", children: "Get reminders via email" })] }), _jsx("button", { onClick: () => setEmailReminders(!emailReminders), className: `relative inline-flex h-8 w-14 items-center rounded-full transition-all duration-300 ${emailReminders ? 'bg-primary' : 'bg-muted'}`, children: _jsx("span", { className: `inline-block h-6 w-6 transform rounded-full bg-white transition-transform shadow-lg ${emailReminders ? 'translate-x-7' : 'translate-x-1'}` }) })] })] }), _jsxs("div", { className: "grid grid-cols-1 gap-8 sm:grid-cols-2", children: [_jsxs("div", { children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground mb-3", children: "Theme" }), _jsxs("select", { value: theme, onChange: (e) => setTheme(e.target.value), className: "w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg", children: [_jsx("option", { value: "light", children: "Light" }), _jsx("option", { value: "dark", children: "Dark" }), _jsx("option", { value: "auto", children: "Auto" })] })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground mb-3", children: "Language" }), _jsxs("select", { value: language, onChange: (e) => setLanguage(e.target.value), className: "w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg", children: [_jsx("option", { value: "en", children: "English" }), _jsx("option", { value: "es", children: "Spanish" }), _jsx("option", { value: "fr", children: "French" }), _jsx("option", { value: "de", children: "German" })] })] })] }), _jsxs("button", { onClick: saveSettings, disabled: isSaving, className: "bg-primary text-primary-foreground px-8 py-4 rounded-lg hover:bg-primary-hover disabled:opacity-50 transition-all duration-300 flex items-center font-semibold text-lg shadow-lg hover:shadow-xl", children: [_jsx(Save, { className: "mr-3 h-5 w-5", strokeWidth: 1.5 }), isSaving ? 'Saving...' : 'Save Preferences'] })] })), activeTab === 'privacy' && (_jsxs("div", { className: "space-y-6", children: [_jsx("h3", { className: "text-xl font-bold text-foreground tracking-tight", children: "Privacy & Data" }), _jsxs("div", { className: "space-y-4", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsx("div", { className: "font-medium text-gray-900", children: "Privacy Mode" }), _jsx("div", { className: "text-sm text-gray-600", children: "Hide your activity from other users" })] }), _jsx("button", { onClick: () => setPrivacyMode(!privacyMode), className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${privacyMode ? 'bg-primary' : 'bg-gray-200'}`, children: _jsx("span", { className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${privacyMode ? 'translate-x-6' : 'translate-x-1'}` }) })] }), _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsx("div", { className: "font-medium text-gray-900", children: "Data Sharing" }), _jsx("div", { className: "text-sm text-gray-600", children: "Share anonymized data for research" })] }), _jsx("button", { onClick: () => setDataSharing(!dataSharing), className: `relative inline-flex h-6 w-11 items-centers rounded-full transition-colors ${dataSharing ? 'bg-primary' : 'bg-gray-200'}`, children: _jsx("span", { className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${dataSharing ? 'translate-x-6' : 'translate-x-1'}` }) })] })] }), _jsxs("div", { className: "border-t border-gray-200 pt-6", children: [_jsx("h4", { className: "font-medium text-gray-900 mb-4", children: "Data Management" }), _jsx("div", { className: "space-y-3", children: _jsxs("button", { onClick: exportData, className: "w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors", children: [_jsx(Download, { className: "mr-2 h-4 w-4" }), "Export My Data"] }) })] }), _jsxs("button", { onClick: saveSettings, disabled: isSaving, className: "btn-primary px-6 py-3 rounded-lg disabled:opacity-50 transition-colors flex items-center", children: [_jsx(Save, { className: "mr-2 h-4 w-4" }), isSaving ? 'Saving...' : 'Save Privacy Settings'] })] })), activeTab === 'account' && (_jsxs("div", { className: "space-y-6", children: [_jsx("h3", { className: "text-lg font-medium text-gray-900", children: "Account Management" }), _jsx("div", { className: "bg-yellow-50 border border-yellow-200 rounded-lg p-4", children: _jsxs("div", { className: "flex", children: [_jsx("div", { className: "flex-shrink-0", children: _jsx(Settings, { className: "h-5 w-5 text-yellow-600" }) }), _jsxs("div", { className: "ml-3", children: [_jsx("h4", { className: "text-sm font-medium text-yellow-800", children: "Account Information" }), _jsxs("div", { className: "mt-2 text-sm text-yellow-700", children: [_jsxs("p", { children: ["Email: ", user?.email] }), _jsxs("p", { children: ["Member since: ", user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'] })] })] })] }) }), _jsxs("div", { className: "border-t border-gray-200 pt-6", children: [_jsx("h4", { className: "font-medium text-gray-900 mb-4 text-red-600", children: "Danger Zone" }), _jsx("div", { className: "bg-red-50 border border-red-200 rounded-lg p-4", children: _jsxs("div", { className: "flex", children: [_jsx("div", { className: "flex-shrink-0", children: _jsx(Trash2, { className: "h-5 w-5 text-red-600" }) }), _jsxs("div", { className: "ml-3 flex-1", children: [_jsx("h4", { className: "text-sm font-medium text-red-800", children: "Delete Account" }), _jsx("p", { className: "mt-1 text-sm text-red-700", children: "Permanently delete your account and all associated data. This action cannot be undone." }), _jsx("div", { className: "mt-4", children: !showDeleteConfirm ? (_jsx("button", { onClick: () => setShowDeleteConfirm(true), className: "bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm", children: "Delete Account" })) : (_jsxs("div", { className: "space-y-3", children: [_jsx("p", { className: "text-sm font-medium text-red-800", children: "Are you absolutely sure? This action cannot be undone." }), _jsxs("div", { className: "flex space-x-3", children: [_jsx("button", { onClick: deleteAccount, className: "bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm", children: "Yes, Delete My Account" }), _jsx("button", { onClick: () => setShowDeleteConfirm(false), className: "bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-sm", children: "Cancel" })] })] })) })] })] }) })] })] }))] })] })] }) }));
}
