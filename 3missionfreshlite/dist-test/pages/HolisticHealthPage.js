import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { SunMoon, ArrowRight, Heart, Brain, Leaf, Moon, Sun, Wind, Zap, Clock } from 'lucide-react';
import { supabase } from '../lib/supabase';
export default function HolisticHealthPage() {
    const [activeCategory, setActiveCategory] = useState('overview');
    const [timerActive, setTimerActive] = useState(false);
    const [timerSeconds, setTimerSeconds] = useState(0);
    const [selectedExercise, setSelectedExercise] = useState(null);
    // RULE 0001: Real Supabase database integration for wellness content
    const [categories, setCategories] = useState([]);
    const [wellnessTips, setWellnessTips] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    useEffect(() => {
        fetchWellnessCategories();
        fetchWellnessTips();
    }, []);
    const fetchWellnessCategories = async () => {
        try {
            const { data, error } = await supabase
                .from('wellness_categories')
                .select('*')
                .order('display_order');
            if (error)
                throw error;
            setCategories(data || []);
        }
        catch (err) {
            setError(err.message);
        }
    };
    const fetchWellnessTips = async () => {
        try {
            const { data, error } = await supabase
                .from('wellness_tips')
                .select('*')
                .order('category_id, display_order');
            if (error)
                throw error;
            setWellnessTips(data || []);
            setLoading(false);
        }
        catch (err) {
            setError(err.message);
            setLoading(false);
        }
    };
    // RULE 0001: Real wellness content from database - categories dynamically loaded
    const getWellnessTipsByCategory = (categoryName) => {
        return wellnessTips.filter(tip => tip.category?.toLowerCase().includes(categoryName.toLowerCase()) ||
            tip.title?.toLowerCase().includes(categoryName.toLowerCase()) ||
            tip.type?.toLowerCase().includes(categoryName.toLowerCase()));
    };
    const energyTips = getWellnessTipsByCategory('energy');
    const focusTips = getWellnessTipsByCategory('focus');
    const sleepTips = getWellnessTipsByCategory('sleep');
    const breathingExercises = getWellnessTipsByCategory('breathing');
    const mindfulnessPractices = getWellnessTipsByCategory('mindfulness');
    const exerciseProtocols = getWellnessTipsByCategory('exercise');
    const breathingTechniques = getWellnessTipsByCategory('breathing');
    const cravingManagementTools = getWellnessTipsByCategory('craving');
    const nutritionSupport = getWellnessTipsByCategory('nutrition');
    // Timer functionality for guided exercises
    useEffect(() => {
        let interval = null;
        if (timerActive) {
            interval = setInterval(() => {
                setTimerSeconds(seconds => seconds + 1);
            }, 1000);
        }
        else if (!timerActive && timerSeconds !== 0) {
            clearInterval(interval);
        }
        return () => clearInterval(interval);
    }, [timerActive, timerSeconds]);
    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };
    const startTimer = (exercise) => {
        setSelectedExercise(exercise);
        setTimerSeconds(0);
        setTimerActive(true);
    };
    const pauseTimer = () => {
        setTimerActive(false);
    };
    const resetTimer = () => {
        setTimerActive(false);
        setTimerSeconds(0);
        setSelectedExercise(null);
    };
    const getCurrentContent = () => {
        switch (activeCategory) {
            case 'cravings':
                return cravingManagementTools.map(tip => ({
                    title: tip.title,
                    description: tip.description || tip.content,
                    duration: tip.duration || '5-10 minutes',
                    steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
                }));
            case 'nutrition':
                return nutritionSupport.map(tip => ({
                    title: tip.title,
                    description: tip.description || tip.content,
                    duration: tip.duration || 'Daily',
                    steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
                }));
            case 'exercise':
                return exerciseProtocols.map(tip => ({
                    title: tip.title,
                    description: tip.description || tip.content,
                    duration: tip.duration || '15-30 minutes',
                    steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
                }));
            case 'breathing':
                return breathingTechniques.map(tip => ({
                    title: tip.title,
                    description: tip.description || tip.content,
                    duration: tip.duration || '3-5 minutes',
                    steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
                }));
            case 'mindfulness':
                return mindfulnessPractices.map(tip => ({
                    title: tip.title,
                    description: tip.description || tip.content,
                    duration: tip.duration || '10-20 minutes',
                    steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
                }));
            case 'sleep':
                return sleepTips.map(tip => ({
                    title: tip.title,
                    description: tip.description || tip.content,
                    duration: tip.duration || 'Evening routine',
                    steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
                }));
            case 'energy':
                return energyTips.map(tip => ({
                    title: tip.title,
                    description: tip.description || tip.content,
                    duration: tip.duration || '5-15 minutes',
                    steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
                }));
            case 'focus':
                return focusTips.map(tip => ({
                    title: tip.title,
                    description: tip.description || tip.content,
                    duration: tip.duration || '10-30 minutes',
                    steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
                }));
            default:
                return [];
        }
    };
    return (_jsxs("div", { className: "min-h-screen bg-background", children: [_jsx("section", { className: "bg-background py-24", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6 lg:px-8 text-center", children: [_jsx("div", { className: "w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg", children: _jsx(SunMoon, { className: "w-10 h-10 text-primary-foreground", strokeWidth: 2 }) }), _jsx("h1", { className: "text-6xl font-bold text-foreground mb-8 tracking-tight", children: "Holistic Health" }), _jsx("p", { className: "text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto", children: "Integrative wellness approaches and mindfulness techniques to support your journey" })] }) }), _jsx("section", { className: "bg-card border-b border-border py-12", children: _jsx("div", { className: "max-w-6xl mx-auto px-6", children: _jsx("div", { className: "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4", children: categories.map((category) => {
                            const IconComponent = category.icon;
                            return (_jsxs("button", { onClick: () => setActiveCategory(category.id), className: `p-6 rounded-xl border-2 transition-all duration-300 text-center shadow-lg hover:shadow-xl ${activeCategory === category.id
                                    ? 'border-primary bg-primary/10'
                                    : 'border-border hover:border-primary/50 bg-card'}`, children: [_jsx(IconComponent, { className: `w-10 h-10 mx-auto mb-3 ${activeCategory === category.id ? 'text-primary' : 'text-muted-foreground'}`, strokeWidth: 1.5 }), _jsx("h3", { className: `font-semibold ${activeCategory === category.id ? 'text-primary' : 'text-card-foreground'}`, children: category.label })] }, category.id));
                        }) }) }) }), _jsx("section", { className: "py-12", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6", children: [activeCategory === 'overview' && (_jsxs("div", { className: "space-y-8", children: [_jsxs("div", { className: "bg-white p-8 rounded-xl shadow-sm border border-gray-100", children: [_jsx("h2", { className: "text-2xl font-bold text-gray-900 mb-6", children: "Holistic Wellness Approach" }), _jsxs("div", { className: "grid md:grid-cols-2 gap-8", children: [_jsxs("div", { children: [_jsx("p", { className: "text-gray-700 leading-relaxed mb-6", children: "Holistic health considers the whole person - mind, body, and spirit - in the quest for optimal health and wellness. Rather than focusing on illness or specific body parts, holistic health practitioners view health and wellness as a state of the whole person." }), _jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-4", children: "Core Principles:" }), _jsxs("ul", { className: "space-y-2", children: [_jsxs("li", { className: "flex items-start space-x-2", children: [_jsx(Heart, { className: "w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" }), _jsx("span", { className: "text-gray-700", children: "Mind-body connection" })] }), _jsxs("li", { className: "flex items-start space-x-2", children: [_jsx(Leaf, { className: "w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" }), _jsx("span", { className: "text-gray-700", children: "Natural healing processes" })] }), _jsxs("li", { className: "flex items-start space-x-2", children: [_jsx(Brain, { className: "w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" }), _jsx("span", { className: "text-gray-700", children: "Prevention over treatment" })] }), _jsxs("li", { className: "flex items-start space-x-2", children: [_jsx(SunMoon, { className: "w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0" }), _jsx("span", { className: "text-gray-700", children: "Balance and harmony" })] })] })] }), _jsxs("div", { className: "bg-gradient-to-br from-green-50 to-blue-50 p-6 rounded-lg", children: [_jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-4", children: "Benefits for Your Journey:" }), _jsxs("ul", { className: "space-y-3", children: [_jsxs("li", { className: "flex items-start space-x-2", children: [_jsx(Clock, { className: "w-4 h-4 text-green-600 mt-1 flex-shrink-0" }), _jsx("span", { className: "text-gray-700", children: "Reduces stress and anxiety" })] }), _jsxs("li", { className: "flex items-start space-x-2", children: [_jsx(Zap, { className: "w-4 h-4 text-green-600 mt-1 flex-shrink-0" }), _jsx("span", { className: "text-gray-700", children: "Improves energy levels" })] }), _jsxs("li", { className: "flex items-start space-x-2", children: [_jsx(Brain, { className: "w-4 h-4 text-green-600 mt-1 flex-shrink-0" }), _jsx("span", { className: "text-gray-700", children: "Enhances mental clarity" })] }), _jsxs("li", { className: "flex items-start space-x-2", children: [_jsx(Heart, { className: "w-4 h-4 text-green-600 mt-1 flex-shrink-0" }), _jsx("span", { className: "text-gray-700", children: "Supports emotional balance" })] })] })] })] })] }), _jsxs("div", { className: "grid md:grid-cols-3 gap-6", children: [_jsxs("div", { className: "bg-white p-6 rounded-xl shadow-sm border border-gray-100 text-center", children: [_jsx(Moon, { className: "w-12 h-12 text-blue-500 mx-auto mb-4" }), _jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-2", children: "Sleep Quality" }), _jsx("p", { className: "text-gray-600 text-sm", children: "Optimize your sleep for better recovery and energy" })] }), _jsxs("div", { className: "bg-white p-6 rounded-xl shadow-sm border border-gray-100 text-center", children: [_jsx(Sun, { className: "w-12 h-12 text-yellow-500 mx-auto mb-4" }), _jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-2", children: "Energy Levels" }), _jsx("p", { className: "text-gray-600 text-sm", children: "Natural ways to boost and maintain energy" })] }), _jsxs("div", { className: "bg-white p-6 rounded-xl shadow-sm border border-gray-100 text-center", children: [_jsx(Brain, { className: "w-12 h-12 text-purple-500 mx-auto mb-4" }), _jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-2", children: "Mental Focus" }), _jsx("p", { className: "text-gray-600 text-sm", children: "Techniques to improve concentration and clarity" })] })] })] })), activeCategory !== 'overview' && (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "text-center mb-8", children: [_jsxs("h2", { className: "text-2xl font-bold text-gray-900 mb-2", children: [categories.find(c => c.id === activeCategory)?.label, " Techniques"] }), _jsxs("p", { className: "text-gray-600", children: ["Evidence-based practices to improve your ", categories.find(c => c.id === activeCategory)?.label.toLowerCase()] })] }), _jsx("div", { className: "grid lg:grid-cols-2 gap-6", children: getCurrentContent().map((technique, index) => (_jsxs("div", { className: "bg-white p-6 rounded-xl shadow-sm border border-gray-100", children: [_jsxs("div", { className: "flex items-start justify-between mb-4", children: [_jsx("h3", { className: "text-lg font-semibold text-gray-900", children: technique.title }), _jsx("span", { className: "bg-primary-muted text-primary-text text-xs font-medium px-2 py-1 rounded", children: technique.duration })] }), _jsx("p", { className: "text-gray-600 mb-4", children: technique.description }), _jsxs("div", { children: [_jsx("h4", { className: "font-medium text-gray-900 mb-2", children: "Steps:" }), _jsx("ol", { className: "space-y-2", children: technique.steps.map((step, stepIndex) => (_jsxs("li", { className: "flex items-start space-x-2", children: [_jsx("span", { className: "bg-primary-muted text-primary-text text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center mt-0.5 flex-shrink-0", children: stepIndex + 1 }), _jsx("span", { className: "text-gray-700 text-sm", children: step })] }, stepIndex))) })] })] }, index))) })] }))] }) }), _jsx("section", { className: "bg-gray-100 py-16", children: _jsxs("div", { className: "max-w-4xl mx-auto px-6 text-center", children: [_jsx("h2", { className: "text-3xl font-bold text-gray-900 mb-4", children: "Ready to Embrace Holistic Wellness?" }), _jsx("p", { className: "text-xl text-gray-600 mb-8", children: "Join Mission Fresh for personalized wellness tracking, expert guidance, and a supportive community." }), _jsxs("div", { className: "flex flex-col sm:flex-row justify-center gap-4", children: [_jsxs(Link, { to: "/tools/quit-methods", className: "inline-flex items-center justify-center border-2 border-primary text-primary px-8 py-3 rounded-lg text-lg font-medium hover:bg-primary hover:text-primary-foreground transition-colors", children: [_jsx(Wind, { className: "w-5 h-5 mr-2" }), "Quit Methods", _jsx(ArrowRight, { className: "w-5 h-5 ml-2" })] }), _jsxs(Link, { to: "/auth?mode=signup", className: "btn-primary inline-flex items-center justify-center px-8 py-3 rounded-lg text-lg font-medium transition-colors", children: ["Get Started", _jsx(ArrowRight, { className: "w-5 h-5 ml-2" })] })] })] }) })] }));
}
