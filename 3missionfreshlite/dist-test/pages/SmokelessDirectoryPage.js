import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Store, ArrowRight, Star, Filter, Search, Heart, ShoppingCart, AlertCircle, ExternalLink, DollarSign } from 'lucide-react';
import { supabase } from '../lib/supabase';
export default function SmokelessDirectoryPage() {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [selectedRating, setSelectedRating] = useState('all');
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    // Fetch real products from Supabase database - RULE 0001: ZERO hardcoding
    useEffect(() => {
        async function fetchProducts() {
            try {
                setLoading(true);
                const { data, error } = await supabase
                    .from('smokeless_products')
                    .select('*')
                    .order('user_rating_avg', { ascending: false })
                    .order('name', { ascending: true });
                if (error)
                    throw error;
                setProducts(data || []);
            }
            catch (err) {
                console.error('Error fetching smokeless products:', err);
                setError('Failed to load products');
            }
            finally {
                setLoading(false);
            }
        }
        fetchProducts();
    }, []);
    // Generate categories dynamically from real database products - RULE 0001: ZERO hardcoding
    const categories = [
        { id: 'all', label: 'All Categories' },
        ...Array.from(new Set(products.map(p => p.category))).map(category => ({
            id: category,
            label: category.charAt(0).toUpperCase() + category.slice(1)
        }))
    ];
    // Filter products using real database data - RULE 0001: ZERO hardcoding
    const filteredProducts = products.filter(product => {
        const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            product.description.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
        const matchesRating = selectedRating === 'all' ||
            (selectedRating === '4+' && product.user_rating_avg >= 4) ||
            (selectedRating === '4.5+' && product.user_rating_avg >= 4.5);
        return matchesSearch && matchesCategory && matchesRating;
    });
    const renderStars = (rating) => {
        return Array.from({ length: 5 }, (_, i) => (_jsx(Star, { className: `w-5 h-5 ${i < Math.floor(rating) ? 'text-primary fill-current' : 'text-muted-foreground'}`, strokeWidth: 1.5 }, i)));
    };
    return (_jsxs("div", { className: "min-h-screen bg-background", children: [_jsx("section", { className: "bg-background py-24", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6 lg:px-8 text-center", children: [_jsx("div", { className: "w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg", children: _jsx(Store, { className: "w-10 h-10 text-primary-foreground", strokeWidth: 2 }) }), _jsx("h1", { className: "text-6xl font-bold text-foreground mb-8 tracking-tight", children: "Smokeless Directory" }), _jsx("p", { className: "text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto", children: "Comprehensive, curated directory of smokeless alternatives with expert reviews and recommendations" })] }) }), _jsx("section", { className: "bg-card border-b border-border py-12", children: _jsx("div", { className: "max-w-7xl mx-auto px-6 lg:px-8", children: _jsxs("div", { className: "flex flex-col lg:flex-row gap-6 items-center", children: [_jsxs("div", { className: "relative flex-1 max-w-lg", children: [_jsx(Search, { className: "absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-6 h-6", strokeWidth: 1.5 }), _jsx("input", { type: "text", placeholder: "Search products...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value), className: "w-full pl-12 pr-4 py-4 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg" })] }), _jsxs("div", { className: "flex items-center space-x-3", children: [_jsx(Filter, { className: "w-6 h-6 text-muted-foreground", strokeWidth: 1.5 }), _jsx("select", { value: selectedCategory, onChange: (e) => setSelectedCategory(e.target.value), className: "border border-border rounded-lg px-4 py-4 focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg font-medium", children: categories.map((category) => (_jsx("option", { value: category.id, children: category.label }, category.id))) })] }), _jsxs("div", { className: "flex items-center space-x-3", children: [_jsx(Star, { className: "w-6 h-6 text-muted-foreground", strokeWidth: 1.5 }), _jsxs("select", { value: selectedRating, onChange: (e) => setSelectedRating(e.target.value), className: "border border-border rounded-lg px-4 py-4 focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg font-medium", children: [_jsx("option", { value: "all", children: "All Ratings" }), _jsx("option", { value: "4+", children: "4+ Stars" }), _jsx("option", { value: "4.5+", children: "4.5+ Stars" })] })] })] }) }) }), _jsx("section", { className: "py-12", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6", children: [_jsxs("div", { className: "mb-8", children: [_jsxs("h2", { className: "text-3xl font-bold text-foreground tracking-tight", children: [filteredProducts.length, " Products Found"] }), _jsx("p", { className: "text-muted-foreground text-lg leading-relaxed", children: "Expertly curated smokeless alternatives with verified reviews" })] }), loading ? (_jsx("div", { className: "grid md:grid-cols-2 lg:grid-cols-3 gap-6", children: [...Array(6)].map((_, i) => (_jsxs("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden", children: [_jsx("div", { className: "h-48 bg-gray-200 animate-pulse" }), _jsxs("div", { className: "p-6", children: [_jsx("div", { className: "h-4 bg-gray-200 rounded animate-pulse mb-2" }), _jsx("div", { className: "h-3 bg-gray-200 rounded animate-pulse mb-4 w-2/3" }), _jsx("div", { className: "h-12 bg-gray-200 rounded animate-pulse" })] })] }, i))) })) : error ? (_jsxs("div", { className: "bg-white p-8 rounded-xl shadow-sm border border-gray-100 text-center", children: [_jsx(AlertCircle, { className: "w-12 h-12 text-red-500 mx-auto mb-4" }), _jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-2", children: "Error loading products" }), _jsx("p", { className: "text-gray-600", children: error })] })) : (_jsxs("div", { className: "grid md:grid-cols-2 lg:grid-cols-3 gap-6", children: [filteredProducts.map((product) => (_jsxs("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow", children: [_jsx("div", { className: "h-48 bg-gray-100 flex items-center justify-center", children: _jsx(Store, { className: "w-12 h-12 text-gray-400" }) }), _jsxs("div", { className: "p-6", children: [_jsxs("div", { className: "flex items-start justify-between mb-2", children: [_jsx("h3", { className: "text-lg font-semibold text-gray-900", children: product.name }), _jsx("span", { className: "text-lg font-bold text-green-600", children: product.price_range || 'Price varies' })] }), product.brand && (_jsx("div", { className: "mb-2", children: _jsx("span", { className: "inline-block bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded", children: product.brand }) })), product.effectiveness_rating && (_jsxs("div", { className: "flex items-center space-x-2 mb-2", children: [_jsxs("div", { className: "flex items-center", children: [renderStars(product.user_rating_avg || 0), _jsxs("span", { className: "ml-1 text-sm text-gray-600", children: ["(", product.user_rating_count || 0, ")"] })] }), _jsxs("span", { className: "text-sm text-gray-600", children: [product.effectiveness_rating, "/5.0 effectiveness"] })] })), product.nicotine_strengths && Object.keys(product.nicotine_strengths).length > 0 && (_jsx("div", { className: "mb-3", children: _jsx("div", { className: "flex flex-wrap gap-1", children: Object.entries(product.nicotine_strengths).slice(0, 3).map(([strength, available], index) => (available && (_jsx("span", { className: `inline-block px-2 py-1 rounded text-xs font-medium ${strength.includes('high') || strength.includes('strong') ? 'bg-red-100 text-red-800' :
                                                                strength.includes('medium') ? 'bg-yellow-100 text-yellow-800' :
                                                                    strength.includes('low') || strength.includes('mild') ? 'bg-accent text-primary' :
                                                                        'bg-gray-100 text-gray-800'}`, children: strength }, index)))) }) })), _jsx("p", { className: "text-gray-600 text-sm mb-4 line-clamp-2", children: product.description }), product.tags && product.tags.length > 0 && (_jsx("div", { className: "mb-4", children: _jsxs("div", { className: "flex flex-wrap gap-1", children: [product.tags.slice(0, 3).map((tag, index) => (_jsx("span", { className: "inline-block bg-primary-subtle text-primary text-xs px-2 py-1 rounded", children: tag }, index))), product.tags.length > 3 && (_jsxs("span", { className: "inline-block bg-gray-50 text-gray-600 text-xs px-2 py-1 rounded", children: ["+", product.tags.length - 3, " more"] }))] }) })), _jsxs("div", { className: "border-t border-gray-100 pt-4 mt-4", children: [product.price && (_jsxs("div", { className: "flex items-center justify-between mb-4", children: [_jsxs("div", { className: "flex items-center space-x-2", children: [_jsx(DollarSign, { className: "w-4 h-4 text-green-600" }), _jsxs("span", { className: "text-2xl font-bold text-green-600", children: ["$", product.price] })] }), product.price_per_unit && (_jsx("span", { className: "text-sm text-gray-500", children: product.price_per_unit }))] })), (product.vendor_name || product.store_name) && (_jsxs("div", { className: "mb-3", children: [_jsx("span", { className: "text-sm text-gray-600", children: "Available at: " }), _jsx("span", { className: "text-sm font-medium text-gray-900", children: product.vendor_name || product.store_name })] })), _jsxs("div", { className: "flex space-x-2", children: [product.buy_link ? (_jsxs("a", { href: product.buy_link, target: "_blank", rel: "noopener noreferrer", className: "flex-1 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary-hover transition-colors flex items-center justify-center space-x-2", children: [_jsx(ShoppingCart, { className: "w-4 h-4" }), _jsx("span", { children: "Buy Now" }), _jsx(ExternalLink, { className: "w-3 h-3" })] })) : (_jsxs("button", { className: "flex-1 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary-hover transition-colors flex items-center justify-center space-x-2", children: [_jsx(ShoppingCart, { className: "w-4 h-4" }), _jsx("span", { children: "View Details" })] })), product.compare_link && (_jsx("a", { href: product.compare_link, target: "_blank", rel: "noopener noreferrer", className: "px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors", children: "Compare" })), _jsx("button", { className: "p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors", children: _jsx(Heart, { className: "w-4 h-4 text-gray-600" }) })] }), (product.affiliate_note || product.buy_link) && (_jsx("p", { className: "text-xs text-gray-500 mt-3", children: product.affiliate_note || 'We may earn a commission from purchases made through affiliate links.' }))] })] })] }, product.id))), filteredProducts.length === 0 && (_jsxs("div", { className: "text-center py-12 col-span-full", children: [_jsx(Store, { className: "w-16 h-16 text-gray-300 mx-auto mb-4" }), _jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-2", children: "No products found" }), _jsx("p", { className: "text-gray-600", children: "Try adjusting your search or filter criteria" })] }))] }))] }) }), _jsx("section", { className: "bg-gray-100 py-16", children: _jsxs("div", { className: "max-w-4xl mx-auto px-6 text-center", children: [_jsx("h2", { className: "text-3xl font-bold text-gray-900 mb-4", children: "Need Personalized Recommendations?" }), _jsx("p", { className: "text-xl text-gray-600 mb-8", children: "Get expert guidance and track your journey with Mission Fresh's comprehensive wellness tools." }), _jsxs("div", { className: "flex flex-col sm:flex-row justify-center gap-4", children: [_jsxs(Link, { to: "/tools/nrt-guide", className: "inline-flex items-center justify-center border-2 border-primary text-primary px-8 py-3 rounded-lg text-lg font-medium hover:bg-primary hover:text-primary-foreground transition-colors", children: [_jsx(Heart, { className: "w-5 h-5 mr-2" }), "NRT Guide", _jsx(ArrowRight, { className: "w-5 h-5 ml-2" })] }), _jsxs(Link, { to: "/auth?mode=signup", className: "inline-flex items-center justify-center bg-primary text-primary-foreground px-8 py-3 rounded-lg text-lg font-medium hover:bg-primary-hover transition-colors", children: ["Get Started", _jsx(ArrowRight, { className: "w-5 h-5 ml-2" })] })] })] }) })] }));
}
