import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Wind, Play, Pause, RotateCcw, ArrowLeft, Clock, Target, Heart } from 'lucide-react';
import { supabase } from '../lib/supabase';
function BreathingSession({ exercise, onComplete, onBack }) {
    const [isActive, setIsActive] = useState(false);
    const [timeLeft, setTimeLeft] = useState(exercise.duration);
    const [currentPhase, setCurrentPhase] = useState('inhale');
    const [phaseTimeLeft, setPhaseTimeLeft] = useState(exercise.inhale_time);
    const [cycleCount, setCycleCount] = useState(0);
    useEffect(() => {
        let interval;
        if (isActive && timeLeft > 0) {
            interval = setInterval(() => {
                setTimeLeft(time => time - 1);
                setPhaseTimeLeft(time => {
                    if (time <= 1) {
                        // Move to next phase
                        if (currentPhase === 'inhale') {
                            setCurrentPhase('hold');
                            return exercise.hold_time;
                        }
                        else if (currentPhase === 'hold') {
                            setCurrentPhase('exhale');
                            return exercise.exhale_time;
                        }
                        else {
                            setCurrentPhase('inhale');
                            setCycleCount(count => count + 1);
                            return exercise.inhale_time;
                        }
                    }
                    return time - 1;
                });
            }, 1000);
        }
        else if (timeLeft === 0) {
            setIsActive(false);
            onComplete();
        }
        return () => clearInterval(interval);
    }, [isActive, timeLeft, currentPhase, exercise, onComplete]);
    const toggleSession = () => {
        setIsActive(!isActive);
    };
    const resetSession = () => {
        setIsActive(false);
        setTimeLeft(exercise.duration);
        setCurrentPhase('inhale');
        setPhaseTimeLeft(exercise.inhale_time);
        setCycleCount(0);
    };
    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };
    const getPhaseInstruction = () => {
        switch (currentPhase) {
            case 'inhale':
                return 'Breathe In';
            case 'hold':
                return 'Hold';
            case 'exhale':
                return 'Breathe Out';
        }
    };
    const getPhaseColor = () => {
        switch (currentPhase) {
            case 'inhale':
                return 'bg-primary';
            case 'hold':
                return 'bg-accent';
            case 'exhale':
                return 'bg-primary/80';
        }
    };
    return (_jsxs("div", { className: "space-y-8", children: [_jsx("div", { className: "flex items-center gap-6", children: _jsxs("button", { onClick: onBack, className: "flex items-center gap-3 px-6 py-3 text-muted-foreground hover:text-foreground transition-all duration-300 font-semibold", children: [_jsx(ArrowLeft, { className: "w-5 h-5", strokeWidth: 1.5 }), "Back to Exercises"] }) }), _jsxs("div", { className: "text-center space-y-6", children: [_jsx("h1", { className: "text-5xl font-bold text-foreground tracking-tight", children: exercise.title }), _jsx("p", { className: "text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed", children: exercise.description }), _jsxs("div", { className: "flex justify-center gap-8 text-muted-foreground", children: [_jsxs("div", { className: "flex items-center gap-2", children: [_jsx(Clock, { className: "w-5 h-5", strokeWidth: 1.5 }), _jsx("span", { className: "font-medium", children: formatTime(exercise.duration) })] }), _jsxs("div", { className: "flex items-center gap-2", children: [_jsx(Target, { className: "w-5 h-5", strokeWidth: 1.5 }), _jsx("span", { className: "font-medium", children: exercise.difficulty })] }), _jsxs("div", { className: "flex items-center gap-2", children: [_jsx(Heart, { className: "w-5 h-5", strokeWidth: 1.5 }), _jsxs("span", { className: "font-medium", children: ["Cycle ", cycleCount + 1] })] })] })] }), _jsxs("div", { className: "flex flex-col items-center space-y-8", children: [_jsxs("div", { className: "relative", children: [_jsx("div", { className: `w-80 h-80 rounded-full flex items-center justify-center transition-all duration-1000 shadow-2xl ${getPhaseColor()}`, children: _jsxs("div", { className: "w-64 h-64 rounded-full bg-card bg-opacity-95 flex flex-col items-center justify-center border border-border", children: [_jsx("div", { className: "text-3xl font-bold text-card-foreground mb-4", children: getPhaseInstruction() }), _jsx("div", { className: "text-6xl font-mono text-primary", children: phaseTimeLeft })] }) }), _jsx("div", { className: "absolute -inset-4", children: _jsxs("svg", { className: "w-full h-full transform -rotate-90", viewBox: "0 0 100 100", children: [_jsx("circle", { cx: "50", cy: "50", r: "48", stroke: "rgba(0,0,0,0.1)", strokeWidth: "2", fill: "none" }), _jsx("circle", { cx: "50", cy: "50", r: "48", stroke: "hsl(var(--primary))", strokeWidth: "3", fill: "none", strokeDasharray: `${((phaseTimeLeft / (currentPhase === 'inhale' ? exercise.inhaleTime : currentPhase === 'hold' ? exercise.holdTime : exercise.exhaleTime)) * 301.59)} 301.59`, className: "transition-all duration-1000" })] }) })] }), _jsxs("div", { className: "flex items-center gap-6", children: [_jsx("button", { onClick: toggleSession, className: `w-20 h-20 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl ${isActive
                                    ? 'bg-destructive hover:bg-destructive-hover text-destructive-foreground'
                                    : 'bg-primary hover:bg-primary-hover text-primary-foreground'}`, children: isActive ? _jsx(Pause, { className: "w-8 h-8", strokeWidth: 1.5 }) : _jsx(Play, { className: "w-8 h-8", strokeWidth: 1.5 }) }), _jsx("button", { onClick: resetSession, className: "w-20 h-20 rounded-full bg-accent hover:bg-accent-hover text-accent-foreground flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl", children: _jsx(RotateCcw, { className: "w-8 h-8", strokeWidth: 1.5 }) })] }), _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-xl text-muted-foreground font-semibold", children: "Time Remaining" }), _jsx("div", { className: "text-5xl font-mono font-bold text-foreground", children: formatTime(timeLeft) })] })] }), _jsxs("div", { className: "max-w-md mx-auto", children: [_jsx("h3", { className: "text-xl font-bold text-foreground mb-4 text-center tracking-tight", children: "Benefits" }), _jsx("ul", { className: "space-y-3", children: exercise.benefits.map((benefit, index) => (_jsxs("li", { className: "flex items-center gap-3 text-card-foreground", children: [_jsx("div", { className: "w-2 h-2 bg-primary rounded-full" }), benefit] }, index))) })] })] }));
}
export default function BreathingToolsPage() {
    const [currentExercise, setCurrentExercise] = useState(null);
    const [breathingExercises, setBreathingExercises] = useState([]);
    const [loading, setLoading] = useState(true);
    useEffect(() => {
        fetchBreathingExercises();
    }, []);
    const fetchBreathingExercises = async () => {
        try {
            const { data, error } = await supabase
                .from('breathing_exercises')
                .select('*')
                .order('difficulty', { ascending: true });
            if (error) {
                console.error('Error fetching breathing exercises:', error);
                // Fallback to basic structure if database fails
                setBreathingExercises([
                    { id: '1', title: '4-7-8 Breathing', description: 'A calming technique that reduces anxiety and promotes relaxation.', duration: 240, inhale_time: 4, hold_time: 7, exhale_time: 8, difficulty: 'Beginner', benefits: ['Reduces anxiety', 'Improves sleep', 'Calms nervous system'] },
                    { id: '2', title: 'Deep Belly Breathing', description: 'Focus on slow, deep breaths to activate the parasympathetic nervous system.', duration: 300, inhale_time: 6, hold_time: 2, exhale_time: 6, difficulty: 'Beginner', benefits: ['Reduces stress', 'Lowers blood pressure', 'Improves focus'] },
                    { id: '3', title: 'Box Breathing', description: 'A square breathing pattern used by Navy SEALs for focus and calm.', duration: 360, inhale_time: 4, hold_time: 4, exhale_time: 4, difficulty: 'Intermediate', benefits: ['Enhances focus', 'Builds resilience', 'Manages stress'] },
                    { id: '4', title: 'Triangle Breathing', description: 'Three-part breathing rhythm for balanced energy and clarity.', duration: 180, inhale_time: 4, hold_time: 4, exhale_time: 4, difficulty: 'Intermediate', benefits: ['Improves concentration', 'Balances energy', 'Reduces tension'] }
                ]);
            }
            else {
                setBreathingExercises(data || []);
            }
        }
        catch (err) {
            console.error('Error fetching breathing exercises:', err);
            setBreathingExercises([]);
        }
        finally {
            setLoading(false);
        }
    };
    const startExercise = (exercise) => {
        setCurrentExercise(exercise);
    };
    const completeExercise = () => {
        setCurrentExercise(null);
        // Here you could save the session to Supabase
    };
    const goBack = () => {
        setActiveExercise(null);
    };
    if (loading) {
        return (_jsx("div", { className: "min-h-screen bg-background flex items-center justify-center", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto mb-6" }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Loading breathing exercises..." })] }) }));
    }
    if (activeExercise) {
        return (_jsx("div", { className: "min-h-screen bg-background py-8", children: _jsx("div", { className: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8", children: _jsx(BreathingSession, { exercise: activeExercise, onComplete: completeExercise, onBack: goBack }) }) }));
    }
    return (_jsxs("div", { className: "space-y-8", children: [_jsxs("div", { className: "mb-8", children: [_jsxs("div", { className: "flex items-center gap-3 mb-4", children: [_jsx("div", { className: "w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20", children: _jsx(Wind, { className: "w-8 h-8 text-primary", strokeWidth: 1.5 }) }), _jsxs("div", { children: [_jsx("h1", { className: "text-4xl font-bold text-foreground tracking-tight", children: "Breathing Tools" }), _jsx("p", { className: "text-gray-600", children: "Calm your mind and manage cravings with guided breathing" })] })] }), _jsx("div", { className: "bg-info/10 rounded-lg border border-info/20 p-4", children: _jsxs("p", { className: "text-info text-sm", children: [_jsx("strong", { children: "Tip:" }), " Regular breathing exercises can reduce stress, manage cravings, and improve overall well-being. Try to practice for a few minutes daily for best results."] }) })] }), _jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: breathingExercises.map((exercise) => (_jsxs("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6", children: [_jsxs("div", { className: "flex justify-between items-start mb-4", children: [_jsx("h3", { className: "text-xl font-semibold text-gray-900", children: exercise.title }), _jsx("span", { className: `px-2 py-1 rounded-full text-xs font-medium ${exercise.difficulty === 'Beginner'
                                        ? 'bg-accent text-primary'
                                        : exercise.difficulty === 'Intermediate'
                                            ? 'bg-warning-subtle text-warning'
                                            : 'bg-destructive-subtle text-destructive'}`, children: exercise.difficulty })] }), _jsx("p", { className: "text-gray-600 mb-4", children: exercise.description }), _jsxs("div", { className: "flex items-center gap-4 mb-4 text-sm text-gray-500", children: [_jsxs("div", { className: "flex items-center gap-1", children: [_jsx(Clock, { className: "w-4 h-4" }), Math.floor(exercise.duration / 60), " min"] }), _jsxs("div", { className: "flex items-center gap-1", children: [_jsx(Target, { className: "w-4 h-4" }), exercise.inhale_time, "-", exercise.hold_time, "-", exercise.exhale_time, " pattern"] })] }), _jsxs("div", { className: "mb-4", children: [_jsx("h4", { className: "text-sm font-medium text-gray-700 mb-2", children: "Benefits:" }), _jsx("ul", { className: "space-y-1", children: exercise.benefits.slice(0, 2).map((benefit, index) => (_jsxs("li", { className: "text-sm text-gray-600 flex items-center gap-2", children: [_jsx("div", { className: "w-1.5 h-1.5 bg-blue-500 rounded-full" }), benefit] }, index))) })] }), _jsxs("button", { onClick: () => startExercise(exercise), className: "w-full flex items-center justify-center gap-2 px-4 py-2 bg-info text-info-foreground rounded-lg hover:bg-info/90 transition-colors", children: [_jsx(Play, { className: "w-4 h-4" }), "Start Exercise"] })] }, exercise.id))) }), _jsxs("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6", children: [_jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-4", children: "Breathing Exercise Tips" }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700", children: [_jsxs("div", { children: [_jsx("h4", { className: "font-medium mb-2", children: "Before You Start:" }), _jsxs("ul", { className: "space-y-1", children: [_jsx("li", { children: "\u2022 Find a quiet, comfortable space" }), _jsx("li", { children: "\u2022 Sit or lie down with a straight spine" }), _jsx("li", { children: "\u2022 Turn off distractions" })] })] }), _jsxs("div", { children: [_jsx("h4", { className: "font-medium mb-2", children: "During the Exercise:" }), _jsxs("ul", { className: "space-y-1", children: [_jsx("li", { children: "\u2022 Focus on the rhythm and counting" }), _jsx("li", { children: "\u2022 Breathe through your nose when possible" }), _jsx("li", { children: "\u2022 Don't force the breath" })] })] })] })] })] }));
}
export default BreathingToolsPage;
