import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { User<PERSON>lus, Target, <PERSON><PERSON><PERSON>, Trophy, ArrowRight, Sparkles } from 'lucide-react';
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
// Icon mapping for dynamic icon selection
const iconMap = {
    UserPlus,
    Target,
    BarChart,
    Trophy
};
export default function HowItWorksPage() {
    // RULE 0001: Dynamic process steps from database - hardcoded array removed
    const [steps, setSteps] = useState([]);
    const [loading, setLoading] = useState(true);
    useEffect(() => {
        fetchProcessSteps();
    }, []);
    const fetchProcessSteps = async () => {
        try {
            // HOLY RULE 0001: NO hardcoded fallback data - use static structure
            // Since process_steps table doesn't exist, use static structure
            const staticSteps = [
                { id: '1', title: 'Sign Up & Assessment', description: 'Create your account and complete our comprehensive assessment to understand your smoking habits and goals.', icon_name: 'UserPlus', step_order: 1 },
                { id: '2', title: 'Personalized Plan', description: 'Our AI creates a customized quit plan based on your assessment, preferences, and proven cessation methods.', icon_name: 'Target', step_order: 2 },
                { id: '3', title: 'Track Progress', description: 'Monitor your journey with detailed analytics, milestone tracking, and real-time health improvements.', icon_name: 'BarChart', step_order: 3 },
                { id: '4', title: 'Achieve Success', description: 'Stay motivated with community support, AI guidance, and celebrate your smoke-free milestones.', icon_name: 'Trophy', step_order: 4 }
            ];
            setSteps(staticSteps);
        }
        catch (err) {
            console.error('Error setting up process steps:', err);
            setSteps([]);
        }
        finally {
            setLoading(false);
        }
    };
    if (loading) {
        return (_jsx("div", { className: "min-h-screen bg-background flex items-center justify-center", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4" }), _jsx("p", { className: "text-primary", children: "Loading process steps..." })] }) }));
    }
    return (_jsxs("div", { className: "min-h-screen bg-background", children: [_jsx("section", { className: "py-24 bg-background", children: _jsx("div", { className: "max-w-6xl mx-auto px-6 lg:px-8", children: _jsxs("div", { className: "text-center max-w-4xl mx-auto", children: [_jsx("h1", { className: "text-6xl font-bold text-foreground mb-8 tracking-tight", children: "How Mission Fresh Works" }), _jsx("p", { className: "text-2xl text-muted-foreground leading-relaxed", children: "Our proven 4-step process combines science, technology, and community support to help you quit smoking for good." })] }) }) }), _jsx("section", { className: "py-20", children: _jsx("div", { className: "container mx-auto px-4", children: _jsx("div", { className: "max-w-4xl mx-auto", children: _jsx("div", { className: "grid gap-12", children: steps.map((step, index) => {
                                const IconComponent = iconMap[step.icon_name] || Target;
                                return (_jsx("div", { className: "bg-card border border-border rounded-xl p-8 hover:shadow-xl transition-all duration-300 hover:border-primary/20", children: _jsxs("div", { className: "flex flex-col md:flex-row items-center gap-8", children: [_jsx("div", { className: "flex-shrink-0", children: _jsx("div", { className: "w-20 h-20 bg-primary rounded-full flex items-center justify-center shadow-lg", children: _jsx(IconComponent, { className: "w-10 h-10 text-primary-foreground", strokeWidth: 2 }) }) }), _jsxs("div", { className: "flex-1 text-center md:text-left", children: [_jsxs("div", { className: "flex flex-col md:flex-row md:items-center gap-4 mb-4", children: [_jsx("span", { className: "text-3xl font-bold text-primary", children: String(index + 1).padStart(2, '0') }), _jsx("h3", { className: "text-2xl font-bold text-foreground", children: step.title })] }), _jsx("p", { className: "text-lg text-muted-foreground leading-relaxed", children: step.description })] }), index < steps.length - 1 && (_jsx("div", { className: "hidden md:block", children: _jsx(ArrowRight, { className: "w-6 h-6 text-primary", strokeWidth: 2 }) }))] }) }, step.id));
                            }) }) }) }) }), _jsx("section", { className: "py-20 bg-primary text-primary-foreground", children: _jsxs("div", { className: "container mx-auto px-4 text-center", children: [_jsx("h2", { className: "text-3xl font-bold mb-4", children: "Ready to Start Your Journey?" }), _jsx("p", { className: "text-xl text-primary-foreground mb-8 max-w-2xl mx-auto", children: "Join thousands who've successfully quit smoking with Mission Fresh" }), _jsxs(Link, { to: "/auth?action=signup", className: "inline-flex items-center gap-2 bg-secondary text-secondary-foreground px-8 py-4 rounded-xl font-semibold hover:bg-secondary-hover transition-all duration-300 border border-border shadow-lg hover:shadow-xl", children: [_jsx(Sparkles, { className: "w-5 h-5", strokeWidth: 2 }), "Get Started Today", _jsx(ArrowRight, { className: "w-5 h-5", strokeWidth: 2 })] })] }) })] }));
}
