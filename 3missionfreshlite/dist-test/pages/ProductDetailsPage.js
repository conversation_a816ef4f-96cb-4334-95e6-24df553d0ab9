import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Package, Shield, Heart, Star, Loader2, AlertTriangle, CheckCircle, ArrowRight } from 'lucide-react';
export default function ProductDetailsPage() {
    const [activeTab, setActiveTab] = useState('overview');
    const [nrtProducts, setNrtProducts] = useState([]);
    const [alternatives, setAlternatives] = useState([]);
    const [reviews, setReviews] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const tabs = [
        { id: 'overview', label: 'Overview', icon: Package },
        { id: 'nrt', label: 'NRT Products', icon: Shield },
        { id: 'alternatives', label: 'Alternatives', icon: Heart },
        { id: 'reviews', label: 'Reviews', icon: Star },
    ];
    // HOLY RULE 0001: Load real product data from Supabase
    useEffect(() => {
        loadProductData();
    }, []);
    const loadProductData = async () => {
        try {
            setLoading(true);
            // Create Supabase client without schema restriction to access products table
            const { createClient } = await import('@supabase/supabase-js');
            const supabaseNoSchema = createClient('https://yekarqanirdkdckimpna.supabase.co', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc');
            // Fetch NRT products from dedicated nrt_products table - RULE 0001: real data only
            const { data: nrtData, error: nrtError } = await supabaseNoSchema
                .from('nrt_products')
                .select('*')
                .eq('is_active', true)
                .order('effectiveness_rating', { ascending: false });
            if (nrtError) {
                console.error('Error loading NRT products:', nrtError);
                setError('Failed to load NRT products');
            }
            else {
                setNrtProducts(nrtData || []);
            }
            // Fetch alternatives and reviews from Supabase - RULE 0001: real data only
            const [alternativesRes, reviewsRes] = await Promise.all([
                supabaseNoSchema.from('product_alternatives').select('*').eq('product_type', 'nrt'),
                supabaseNoSchema.from('product_reviews').select('*').eq('product_type', 'nrt')
            ]);
            setAlternatives(alternativesRes.data || []);
            setReviews(reviewsRes.data || []);
        }
        catch (error) {
            console.error('Error loading product data:', error);
            setError('Failed to load product information');
        }
        finally {
            setLoading(false);
        }
    };
    return (_jsxs("div", { className: "min-h-screen bg-background", children: [_jsx("section", { className: "bg-background py-24", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6 lg:px-8 text-center", children: [_jsx("div", { className: "w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20", children: _jsx(Package, { className: "w-10 h-10 text-primary", strokeWidth: 1.5 }) }), _jsx("h1", { className: "text-6xl font-bold text-foreground mb-8 tracking-tight", children: "Product Details" }), _jsx("p", { className: "text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto", children: "Comprehensive reviews and comparisons of cessation products and treatments" })] }) }), _jsx("section", { className: "bg-card border-b border-border py-16", children: _jsx("div", { className: "max-w-7xl mx-auto px-6 lg:px-8", children: _jsx("div", { className: "grid grid-cols-2 md:grid-cols-4 gap-6", children: tabs.map((tab) => {
                            const IconComponent = tab.icon;
                            return (_jsxs("button", { onClick: () => setActiveTab(tab.id), className: `p-8 rounded-xl border-2 transition-all duration-300 text-center hover:shadow-lg ${activeTab === tab.id
                                    ? 'border-primary bg-primary/10'
                                    : 'border-border hover:border-primary bg-card'}`, children: [_jsx(IconComponent, { className: `w-10 h-10 mx-auto mb-4 ${activeTab === tab.id ? 'text-primary' : 'text-muted-foreground'}`, strokeWidth: 1.5 }), _jsx("h3", { className: `font-semibold text-lg ${activeTab === tab.id ? 'text-primary' : 'text-card-foreground'}`, children: tab.label })] }, tab.id));
                        }) }) }) }), _jsx("section", { className: "py-12", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6", children: [activeTab === 'overview' && (_jsxs("div", { className: "space-y-8", children: [_jsxs("div", { className: "bg-card p-8 rounded-xl shadow-lg border border-border", children: [_jsx("h2", { className: "text-3xl font-bold text-card-foreground mb-8 tracking-tight", children: "Cessation Product Categories" }), _jsxs("div", { className: "grid md:grid-cols-3 gap-8", children: [_jsxs("div", { className: "text-center", children: [_jsx("div", { className: "w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20", children: _jsx(Shield, { className: "w-10 h-10 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "font-bold text-card-foreground mb-3 text-lg", children: "NRT Products" }), _jsx("p", { className: "text-muted-foreground leading-relaxed", children: "Nicotine replacement therapy options" })] }), _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "w-20 h-20 bg-accent/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-accent/20", children: _jsx(Heart, { className: "w-10 h-10 text-accent", strokeWidth: 1.5 }) }), _jsx("h3", { className: "font-bold text-card-foreground mb-3 text-lg", children: "Medications" }), _jsx("p", { className: "text-muted-foreground leading-relaxed", children: "Prescription cessation aids" })] }), _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20", children: _jsx(Star, { className: "w-10 h-10 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "font-bold text-card-foreground mb-3 text-lg", children: "Alternatives" }), _jsx("p", { className: "text-muted-foreground leading-relaxed", children: "E-cigarettes and other options" })] })] })] }), _jsxs("div", { className: "bg-primary/5 border border-primary/20 p-8 rounded-xl", children: [_jsx("h2", { className: "text-3xl font-bold text-primary mb-8 tracking-tight", children: "Success Rate Comparison" }), _jsxs("div", { className: "grid md:grid-cols-3 gap-8", children: [_jsxs("div", { className: "bg-card p-8 rounded-xl shadow-lg border border-border", children: [_jsx("h3", { className: "font-bold text-card-foreground mb-4 text-lg", children: "Combination Therapy" }), _jsx("div", { className: "text-4xl font-bold text-primary mb-4", children: "65-75%" }), _jsx("p", { className: "text-muted-foreground leading-relaxed", children: "NRT + Behavioral support" })] }), _jsxs("div", { className: "bg-card p-8 rounded-xl shadow-lg border border-border", children: [_jsx("h3", { className: "font-bold text-card-foreground mb-4 text-lg", children: "Prescription Meds" }), _jsx("div", { className: "text-4xl font-bold text-accent mb-4", children: "30-44%" }), _jsx("p", { className: "text-muted-foreground leading-relaxed", children: "Varenicline, Bupropion" })] }), _jsxs("div", { className: "bg-card p-8 rounded-xl shadow-lg border border-border", children: [_jsx("h3", { className: "font-bold text-card-foreground mb-4 text-lg", children: "NRT Alone" }), _jsx("div", { className: "text-4xl font-bold text-primary mb-4", children: "15-25%" }), _jsx("p", { className: "text-muted-foreground leading-relaxed", children: "Patches, gum, lozenges" })] })] })] })] })), activeTab === 'nrt' && (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "text-center mb-12", children: [_jsx("h2", { className: "text-4xl font-bold text-foreground mb-4 tracking-tight", children: "Nicotine Replacement Therapy" }), _jsx("p", { className: "text-xl text-muted-foreground leading-relaxed", children: "FDA-approved products to help manage withdrawal symptoms" })] }), loading ? (_jsxs("div", { className: "flex items-center justify-center py-16", children: [_jsx(Loader2, { className: "w-12 h-12 text-primary animate-spin", strokeWidth: 1.5 }), _jsx("span", { className: "ml-4 text-muted-foreground text-lg", children: "Loading NRT products..." })] })) : error ? (_jsxs("div", { className: "text-center py-16", children: [_jsx("p", { className: "text-destructive mb-6 text-lg", children: error }), _jsx("button", { onClick: loadProductData, className: "bg-primary text-primary-foreground px-8 py-4 rounded-lg hover:bg-primary-hover transition-all duration-300 font-semibold shadow-lg hover:shadow-xl", children: "Try Again" })] })) : nrtProducts.length === 0 ? (_jsx("div", { className: "text-center py-16", children: _jsx("p", { className: "text-muted-foreground text-lg", children: "No NRT products available at this time." }) })) : (_jsx("div", { className: "grid lg:grid-cols-2 gap-6", children: nrtProducts.map((product) => (_jsxs("div", { className: "bg-white p-6 rounded-xl shadow-sm border border-gray-100", children: [_jsxs("div", { className: "flex items-start justify-between mb-4", children: [_jsxs("div", { children: [_jsx("h3", { className: "text-xl font-semibold text-gray-900", children: product.name }), _jsx("p", { className: "text-gray-600", children: product.brand })] }), _jsxs("div", { className: "text-right", children: [_jsxs("div", { className: "flex items-center space-x-1 mb-1", children: [_jsx(Star, { className: "w-4 h-4 text-yellow-400 fill-current" }), _jsx("span", { className: "font-medium", children: product.rating }), _jsxs("span", { className: "text-gray-500 text-sm", children: ["(", product.reviews, ")"] })] }), _jsx("div", { className: "text-lg font-bold text-green-600", children: product.effectiveness })] })] }), _jsxs("div", { className: "grid grid-cols-2 gap-4 mb-4", children: [_jsxs("div", { children: [_jsx("dt", { className: "text-sm font-medium text-gray-500", children: "Dosage" }), _jsx("dd", { className: "text-sm text-gray-900", children: product.dosage })] }), _jsxs("div", { children: [_jsx("dt", { className: "text-sm font-medium text-gray-500", children: "Price Range" }), _jsx("dd", { className: "text-sm text-gray-900", children: product.price })] })] }), _jsxs("div", { className: "grid md:grid-cols-2 gap-4", children: [_jsxs("div", { children: [_jsxs("h4", { className: "text-sm font-medium text-green-800 mb-2 flex items-center", children: [_jsx(CheckCircle, { className: "w-4 h-4 mr-1" }), "Pros"] }), _jsx("ul", { className: "space-y-1", children: product.pros?.map((pro, index) => (_jsxs("li", { className: "text-sm text-gray-600 flex items-start", children: [_jsx(CheckCircle, { className: "w-4 h-4 text-green-500 mr-1 mt-0.5 flex-shrink-0" }), pro] }, index))) })] }), _jsxs("div", { children: [_jsxs("h4", { className: "text-sm font-medium text-red-800 mb-2 flex items-center", children: [_jsx(AlertTriangle, { className: "w-4 h-4 mr-1" }), "Cons"] }), _jsx("ul", { className: "space-y-1", children: product.cons?.map((con, index) => (_jsxs("li", { className: "text-sm text-gray-600 flex items-start", children: [_jsx("span", { className: "w-1 h-1 bg-red-500 rounded-full mt-2 mr-2 flex-shrink-0" }), con] }, index))) })] })] })] }, product.id))) }))] })), activeTab === 'alternatives' && (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "text-center mb-8", children: [_jsx("h2", { className: "text-2xl font-bold text-gray-900 mb-2", children: "Alternative Cessation Methods" }), _jsx("p", { className: "text-gray-600", children: "Beyond traditional NRT - exploring all options" })] }), loading ? (_jsxs("div", { className: "flex items-center justify-center py-12", children: [_jsx(Loader2, { className: "w-8 h-8 text-blue-600 animate-spin" }), _jsx("span", { className: "ml-2 text-gray-600", children: "Loading alternatives..." })] })) : error ? (_jsxs("div", { className: "text-center py-12", children: [_jsx("p", { className: "text-red-600 mb-4", children: error }), _jsx("button", { onClick: loadProductData, className: "bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors", children: "Try Again" })] })) : alternatives.length === 0 ? (_jsx("div", { className: "text-center py-12", children: _jsx("p", { className: "text-gray-600", children: "No alternatives available at this time." }) })) : (_jsx("div", { className: "space-y-6", children: alternatives.map((alt) => (_jsxs("div", { className: "bg-white p-6 rounded-xl shadow-sm border border-gray-100", children: [_jsxs("div", { className: "flex items-start justify-between mb-4", children: [_jsxs("div", { children: [_jsx("h3", { className: "text-xl font-semibold text-gray-900", children: alt.name }), _jsx("span", { className: "inline-block bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded mt-1", children: alt.type })] }), _jsxs("div", { className: "text-right", children: [_jsxs("div", { className: "flex items-center space-x-1 mb-1", children: [_jsx(Star, { className: "w-4 h-4 text-yellow-400 fill-current" }), _jsx("span", { className: "font-medium", children: alt.rating })] }), _jsx("div", { className: "text-lg font-bold text-green-600", children: alt.effectiveness })] })] }), _jsxs("div", { className: "grid md:grid-cols-3 gap-4 mb-6", children: [_jsxs("div", { children: [_jsx("dt", { className: "text-sm font-medium text-gray-500", children: "Mechanism" }), _jsx("dd", { className: "text-sm text-gray-900", children: alt.mechanism })] }), _jsxs("div", { children: [_jsx("dt", { className: "text-sm font-medium text-gray-500", children: "Cost" }), _jsx("dd", { className: "text-sm text-gray-900", children: alt.price })] })] }), _jsxs("div", { className: "grid md:grid-cols-2 gap-6", children: [_jsxs("div", { children: [_jsxs("h4", { className: "text-sm font-medium text-green-800 mb-2 flex items-center", children: [_jsx(CheckCircle, { className: "w-4 h-4 mr-1" }), "Advantages"] }), _jsx("ul", { className: "space-y-1", children: alt.pros?.map((pro, index) => (_jsxs("li", { className: "text-sm text-gray-600 flex items-start", children: [_jsx("span", { className: "w-1 h-1 bg-primary rounded-full mt-2 mr-2 flex-shrink-0" }), pro] }, index))) })] }), _jsxs("div", { children: [_jsxs("h4", { className: "text-sm font-medium text-red-800 mb-2 flex items-center", children: [_jsx(AlertTriangle, { className: "w-4 h-4 mr-1" }), "Limitations"] }), _jsx("ul", { className: "space-y-1", children: alt.cons?.map((con, index) => (_jsxs("li", { className: "text-sm text-gray-600 flex items-start", children: [_jsx("span", { className: "w-1 h-1 bg-red-500 rounded-full mt-2 mr-2 flex-shrink-0" }), con] }, index))) })] })] })] }, alt.id))) }))] })), activeTab === 'reviews' && (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "text-center mb-8", children: [_jsx("h2", { className: "text-2xl font-bold text-gray-900 mb-2", children: "Real User Reviews" }), _jsx("p", { className: "text-gray-600", children: "Authentic experiences from people who have quit smoking" })] }), _jsx("div", { className: "space-y-6", children: reviews.map((review) => (_jsxs("div", { className: "bg-white p-6 rounded-xl shadow-sm border border-gray-100", children: [_jsxs("div", { className: "flex items-start justify-between mb-4", children: [_jsxs("div", { children: [_jsxs("div", { className: "flex items-center space-x-2 mb-1", children: [_jsx("h3", { className: "font-semibold text-gray-900", children: review.user }), review.verified && (_jsxs("span", { className: "bg-primary-muted text-primary-text text-xs font-medium px-2 py-1 rounded flex items-center", children: [_jsx(CheckCircle, { className: "w-3 h-3 mr-1" }), "Verified"] }))] }), _jsx("p", { className: "text-sm text-gray-600", children: review.product })] }), _jsxs("div", { className: "text-right", children: [_jsx("div", { className: "flex items-center space-x-1 mb-1", children: [...Array(5)].map((_, i) => (_jsx(Star, { className: `w-4 h-4 ${i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}` }, i))) }), _jsx("p", { className: "text-sm text-gray-500", children: review.date })] })] }), _jsxs("blockquote", { className: "text-gray-700 italic mb-4 text-sm leading-relaxed", children: ["\"", review.review, "\""] }), _jsxs("div", { className: "grid md:grid-cols-2 gap-4", children: [_jsxs("div", { children: [_jsxs("h4", { className: "text-xs font-medium text-green-800 mb-2 flex items-center uppercase tracking-wide", children: [_jsx(CheckCircle, { className: "w-3 h-3 mr-1" }), "What Worked"] }), _jsx("ul", { className: "space-y-1", children: review.pros?.map((pro, index) => (_jsxs("li", { className: "text-xs text-gray-600 flex items-start", children: [_jsx("span", { className: "w-1 h-1 bg-green-500 rounded-full mt-1.5 mr-2 flex-shrink-0" }), pro] }, index))) })] }), _jsxs("div", { children: [_jsxs("h4", { className: "text-xs font-medium text-red-800 mb-2 flex items-center uppercase tracking-wide", children: [_jsx(AlertTriangle, { className: "w-3 h-3 mr-1" }), "Challenges"] }), _jsx("ul", { className: "space-y-1", children: review.cons?.map((con, index) => (_jsxs("li", { className: "text-xs text-gray-600 flex items-start", children: [_jsx("span", { className: "w-1 h-1 bg-red-500 rounded-full mt-1.5 mr-2 flex-shrink-0" }), con] }, index))) })] })] })] }, review.id))) })] }))] }) }), _jsx("section", { className: "bg-primary/5 border-y border-primary/20 py-20", children: _jsxs("div", { className: "max-w-5xl mx-auto px-8 text-center", children: [_jsx("h2", { className: "text-4xl font-bold text-primary mb-6 tracking-tight", children: "Ready to Find Your Perfect Cessation Method?" }), _jsx("p", { className: "text-xl text-primary/80 mb-12 leading-relaxed max-w-3xl mx-auto", children: "Join Mission Fresh for personalized recommendations, tracking, and support." }), _jsxs("div", { className: "flex flex-col sm:flex-row justify-center gap-6", children: [_jsxs(Link, { to: "/tools/nrt-guide", className: "inline-flex items-center justify-center border-2 border-primary text-primary px-10 py-4 rounded-xl text-lg font-semibold hover:bg-primary hover:text-primary-foreground transition-all duration-300 shadow-lg hover:shadow-xl", children: [_jsx(Shield, { className: "w-6 h-6 mr-3", strokeWidth: 1.5 }), "NRT Guide", _jsx(ArrowRight, { className: "w-6 h-6 ml-3", strokeWidth: 1.5 })] }), _jsxs(Link, { to: "/auth?mode=signup", className: "inline-flex items-center justify-center bg-primary text-primary-foreground px-10 py-4 rounded-xl text-lg font-semibold hover:bg-primary-hover transition-all duration-300 shadow-lg hover:shadow-xl", children: ["Get Started", _jsx(ArrowRight, { className: "w-6 h-6 ml-3", strokeWidth: 1.5 })] })] })] }) })] }));
}
