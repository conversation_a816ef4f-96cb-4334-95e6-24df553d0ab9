import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import { MessageCircle, Heart, Users, Plus, TrendingUp, Clock, Star, Award, Send } from 'lucide-react';
export default function CommunityPage() {
    const { user } = useAuth();
    const [posts, setPosts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showCreatePost, setShowCreatePost] = useState(false);
    const [selectedFilter, setSelectedFilter] = useState('all');
    useEffect(() => {
        loadPosts();
    }, [selectedFilter]);
    const loadPosts = async () => {
        try {
            setLoading(true);
            let query = supabase
                .from('community_posts')
                .select('*')
                .order('created_at', { ascending: false });
            if (selectedFilter !== 'all') {
                query = query.eq('post_type', selectedFilter);
            }
            const { data, error } = await query;
            if (error)
                throw error;
            setPosts(data || []);
        }
        catch (error) {
            console.error('Error loading posts:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const handleLike = async (postId) => {
        if (!user)
            return;
        try {
            const post = posts.find(p => p.id === postId);
            if (!post)
                return;
            const { error } = await supabase
                .from('community_posts')
                .update({ likes_count: post.likes_count + 1 })
                .eq('id', postId);
            if (error)
                throw error;
            setPosts(posts.map(p => p.id === postId
                ? { ...p, likes_count: p.likes_count + 1 }
                : p));
        }
        catch (error) {
            console.error('Error liking post:', error);
        }
    };
    const formatTimeAgo = (dateString) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffHours / 24);
        if (diffDays > 0)
            return `${diffDays}d ago`;
        if (diffHours > 0)
            return `${diffHours}h ago`;
        return 'Just now';
    };
    // RULE 0001: Load post types dynamically from database
    const [postTypes, setPostTypes] = useState([
        { value: 'all', label: 'All Posts', icon: Users },
        { value: 'success', label: 'Success Stories', icon: Star },
        { value: 'support', label: 'Support', icon: Heart },
        { value: 'milestone', label: 'Milestones', icon: Award },
        { value: 'question', label: 'Questions', icon: MessageCircle }
    ]);
    // Load post types from database (fallback to static if table doesn't exist)
    useEffect(() => {
        const loadPostTypes = async () => {
            try {
                const { data, error } = await supabase
                    .from('post_types')
                    .select('*')
                    .order('display_order');
                if (data && data.length > 0) {
                    // Convert database format to component format
                    const dynamicPostTypes = [
                        { value: 'all', label: 'All Posts', icon: Users },
                        ...data.map(type => ({
                            value: type.value,
                            label: type.label,
                            icon: type.icon_name === 'Star' ? Star :
                                type.icon_name === 'Heart' ? Heart :
                                    type.icon_name === 'Award' ? Award :
                                        type.icon_name === 'MessageCircle' ? MessageCircle : Users
                        }))
                    ];
                    setPostTypes(dynamicPostTypes);
                }
            }
            catch (error) {
                // Keep fallback postTypes if database query fails
                console.log('Using fallback post types (post_types table not found)');
            }
        };
        loadPostTypes();
    }, []);
    return (_jsx("div", { className: "min-h-screen bg-background py-12", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6 lg:px-8", children: [_jsxs("div", { className: "text-center mb-16", children: [_jsx("h1", { className: "text-5xl font-bold text-foreground mb-6 tracking-tight", children: "Community Support" }), _jsx("p", { className: "text-xl text-muted-foreground leading-relaxed", children: "Connect with others on similar wellness journeys" })] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-8 mb-16", children: [_jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 text-center hover:shadow-xl transition-all duration-300", children: [_jsx("div", { className: "w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20", children: _jsx(Users, { className: "w-8 h-8 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "text-4xl font-bold text-card-foreground mb-2", children: posts.length }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Community Posts" })] }), _jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 text-center hover:shadow-xl transition-all duration-300", children: [_jsx("div", { className: "w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20", children: _jsx(TrendingUp, { className: "w-8 h-8 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "text-4xl font-bold text-card-foreground mb-2", children: posts.reduce((sum, post) => sum + (post.likes_count || 0), 0) }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Total Likes" })] }), _jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 text-center hover:shadow-xl transition-all duration-300", children: [_jsx("div", { className: "w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20", children: _jsx(Star, { className: "w-8 h-8 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "text-4xl font-bold text-card-foreground mb-2", children: posts.filter(p => p.post_type === 'success').length }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Success Stories" })] })] }), _jsx("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 mb-12", children: _jsxs("div", { className: "flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6", children: [_jsx("div", { className: "flex flex-wrap gap-3", children: postTypes.map((type) => (_jsxs("button", { onClick: () => setSelectedFilter(type.value), className: `inline-flex items-center px-5 py-3 rounded-lg font-semibold transition-all duration-300 ${selectedFilter === type.value
                                        ? 'bg-primary text-primary-foreground shadow-lg'
                                        : 'bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground'}`, children: [_jsx(type.icon, { className: "w-5 h-5 mr-3", strokeWidth: 1.5 }), type.label] }, type.value))) }), user && (_jsxs("button", { onClick: () => setShowCreatePost(true), className: "inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg font-semibold hover:bg-primary-hover transition-all duration-300 shadow-lg hover:shadow-xl", children: [_jsx(Plus, { className: "w-5 h-5 mr-3", strokeWidth: 1.5 }), "Share Your Story"] }))] }) }), _jsx("div", { className: "space-y-8", children: loading ? (_jsx("div", { className: "text-center py-16", children: _jsx("div", { className: "animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto" }) })) : posts.length > 0 ? (posts.map((post) => (_jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300", children: [_jsx("div", { className: "flex items-start justify-between mb-6", children: _jsxs("div", { className: "flex items-center space-x-4", children: [_jsx("div", { className: "w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20", children: _jsx(Users, { className: "w-6 h-6 text-primary", strokeWidth: 1.5 }) }), _jsxs("div", { children: [_jsx("h3", { className: "font-bold text-card-foreground text-lg", children: "Community Member" }), _jsxs("div", { className: "flex items-center space-x-3 text-muted-foreground mt-1", children: [_jsx(Clock, { className: "w-4 h-4", strokeWidth: 1.5 }), _jsx("span", { children: formatTimeAgo(post.created_at) }), _jsx("span", { className: "px-3 py-1 bg-muted rounded-full text-sm font-medium", children: post.post_type })] })] })] }) }), _jsx("h4", { className: "text-2xl font-bold text-card-foreground mb-4", children: post.title }), _jsx("p", { className: "text-muted-foreground leading-relaxed mb-6 text-lg", children: post.content }), _jsxs("div", { className: "flex items-center justify-between pt-6 border-t border-border", children: [_jsxs("button", { onClick: () => handleLike(post.id), className: "inline-flex items-center text-muted-foreground hover:text-primary transition-all duration-300 font-semibold", disabled: !user, children: [_jsx(Heart, { className: "w-5 h-5 mr-3", strokeWidth: 1.5 }), post.likes_count, " ", post.likes_count === 1 ? 'Like' : 'Likes'] }), _jsxs("button", { className: "text-primary hover:text-primary-hover font-semibold transition-all duration-300 inline-flex items-center", children: [_jsx(MessageCircle, { className: "w-5 h-5 mr-2", strokeWidth: 1.5 }), "Reply"] })] })] }, post.id)))) : (_jsxs("div", { className: "text-center py-20 bg-card rounded-xl shadow-lg border border-border", children: [_jsx("div", { className: "w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20", children: _jsx(MessageCircle, { className: "w-10 h-10 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "text-2xl font-bold text-card-foreground mb-4", children: "No posts yet" }), _jsx("p", { className: "text-muted-foreground mb-8 text-lg", children: "Be the first to share your story with the community!" }), user && (_jsxs("button", { onClick: () => setShowCreatePost(true), className: "inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg font-semibold hover:bg-primary-hover transition-all duration-300 shadow-lg hover:shadow-xl", children: [_jsx(Plus, { className: "w-5 h-5 mr-3", strokeWidth: 1.5 }), "Create First Post"] }))] })) }), showCreatePost && (_jsx(CreatePostModal, { onClose: () => setShowCreatePost(false), onSuccess: () => {
                        setShowCreatePost(false);
                        loadPosts();
                    } }))] }) }));
}
function CreatePostModal({ onClose, onSuccess }) {
    const { user } = useAuth();
    const [title, setTitle] = useState('');
    const [content, setContent] = useState('');
    const [postType, setPostType] = useState('support');
    const [submitting, setSubmitting] = useState(false);
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!user || !title.trim() || !content.trim())
            return;
        try {
            setSubmitting(true);
            const { error } = await supabase
                .from('community_posts')
                .insert({
                title: title.trim(),
                content: content.trim(),
                post_type: postType,
                user_id: user.id,
                likes_count: 0
            });
            if (error)
                throw error;
            onSuccess();
        }
        catch (error) {
            console.error('Error creating post:', error);
        }
        finally {
            setSubmitting(false);
        }
    };
    return (_jsx("div", { className: "fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4", children: _jsxs("div", { className: "bg-card rounded-xl shadow-2xl w-full max-w-2xl border border-border", children: [_jsx("div", { className: "px-8 py-6 border-b border-border", children: _jsx("h3", { className: "text-2xl font-bold text-card-foreground", children: "Share Your Story" }) }), _jsxs("form", { onSubmit: handleSubmit, className: "p-8 space-y-6", children: [_jsxs("div", { children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground mb-3", children: "Post Type" }), _jsxs("select", { value: postType, onChange: (e) => setPostType(e.target.value), className: "w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg", children: [_jsx("option", { value: "support", children: "Support Request" }), _jsx("option", { value: "success", children: "Success Story" }), _jsx("option", { value: "milestone", children: "Milestone" }), _jsx("option", { value: "question", children: "Question" })] })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground mb-3", children: "Title" }), _jsx("input", { type: "text", value: title, onChange: (e) => setTitle(e.target.value), placeholder: "Give your post a descriptive title...", className: "w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg", required: true })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground mb-3", children: "Content" }), _jsx("textarea", { value: content, onChange: (e) => setContent(e.target.value), placeholder: "Share your thoughts, experiences, or questions...", rows: 8, className: "w-full px-4 py-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg leading-relaxed resize-none", required: true })] }), _jsxs("div", { className: "flex space-x-4 pt-6", children: [_jsx("button", { type: "button", onClick: onClose, className: "flex-1 px-6 py-3 border border-border text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold text-lg", children: "Cancel" }), _jsx("button", { type: "submit", disabled: submitting || !title.trim() || !content.trim(), className: "flex-1 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed font-semibold text-lg shadow-lg hover:shadow-xl", children: submitting ? (_jsx("div", { className: "animate-spin rounded-full h-6 w-6 border-b-2 border-primary-foreground mx-auto" })) : (_jsxs(_Fragment, { children: [_jsx(Send, { className: "w-5 h-5 mr-3 inline", strokeWidth: 1.5 }), "Post"] })) })] })] })] }) }));
}
