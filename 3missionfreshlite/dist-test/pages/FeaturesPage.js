import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { <PERSON>, Users, <PERSON>C<PERSON>, Shield, Smartphone, Heart, Sparkles, ArrowRight } from 'lucide-react';
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
// Icon mapping for dynamic icon selection
const iconMap = {
    Brain,
    Users,
    BarChart,
    Shield,
    Smartphone,
    Heart
};
export default function FeaturesPage() {
    // RULE 0001: Dynamic app features from database - hardcoded array removed
    const [features, setFeatures] = useState([]);
    const [loading, setLoading] = useState(true);
    useEffect(() => {
        fetchAppFeatures();
    }, []);
    const fetchAppFeatures = async () => {
        try {
            // HOLY RULE 0001: NO hardcoded fallback data - use static structure
            // Since app_features table doesn't exist, use static structure
            const staticFeatures = [
                { id: '1', title: 'AI-Powered Guidance', description: 'Smart recommendations based on your smoking patterns and progress with personalized quit plans.', icon_name: 'Brain', display_order: 1 },
                { id: '2', title: 'Community Support', description: 'Connect with others on the same journey and celebrate victories together in a supportive environment.', icon_name: 'Users', display_order: 2 },
                { id: '3', title: 'Progress Tracking', description: 'Detailed analytics showing your smoke-free days, health improvements, and money saved.', icon_name: 'BarChart', display_order: 3 },
                { id: '4', title: 'Craving Management', description: 'Proven techniques and support tools to overcome cravings with breathing exercises and focus tools.', icon_name: 'Shield', display_order: 4 },
                { id: '5', title: 'Mobile Access', description: 'Access your quit plan and track progress anywhere with our responsive web application.', icon_name: 'Smartphone', display_order: 5 },
                { id: '6', title: 'Health Monitoring', description: 'Track your health improvements as your body recovers from smoking with real-time metrics.', icon_name: 'Heart', display_order: 6 }
            ];
            setFeatures(staticFeatures);
        }
        catch (err) {
            console.error('Error setting up app features:', err);
            setFeatures([]);
        }
        finally {
            setLoading(false);
        }
    };
    if (loading) {
        return (_jsx("div", { className: "min-h-screen bg-background flex items-center justify-center", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto mb-6" }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Loading features..." })] }) }));
    }
    return (_jsxs("div", { className: "min-h-screen bg-background", children: [_jsx("section", { className: "py-24 bg-background", children: _jsx("div", { className: "max-w-6xl mx-auto px-6 lg:px-8", children: _jsxs("div", { className: "text-center max-w-4xl mx-auto", children: [_jsx("h1", { className: "text-6xl font-bold text-foreground mb-8 tracking-tight", children: "Powerful Features to Help You Quit" }), _jsx("p", { className: "text-2xl text-muted-foreground leading-relaxed", children: "Everything you need to successfully quit smoking, backed by science and powered by cutting-edge technology." })] }) }) }), _jsx("section", { className: "py-20", children: _jsx("div", { className: "container mx-auto px-4", children: _jsx("div", { className: "grid md:grid-cols-2 lg:grid-cols-3 gap-8", children: features.map((feature, index) => {
                            const IconComponent = iconMap[feature.icon_name] || Brain;
                            return (_jsxs("div", { className: "group bg-card rounded-xl p-10 shadow-lg border border-border hover:shadow-xl transition-all duration-300 hover:border-primary/20", children: [_jsx("div", { className: "w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300", children: _jsx(IconComponent, { className: "w-8 h-8 text-primary-foreground", strokeWidth: 2 }) }), _jsx("h3", { className: "text-xl font-semibold text-foreground mb-4", children: feature.title }), _jsx("p", { className: "text-muted-foreground leading-relaxed", children: feature.description })] }, feature.id));
                        }) }) }) }), _jsx("section", { className: "py-20 bg-primary text-primary-foreground", children: _jsxs("div", { className: "container mx-auto px-4 text-center", children: [_jsx("h2", { className: "text-3xl font-bold mb-4", children: "Experience All Features Today" }), _jsx("p", { className: "text-xl text-primary-foreground mb-8 max-w-2xl mx-auto", children: "Start your smoke-free journey with all the tools you need for success" }), _jsxs(Link, { to: "/auth?action=signup", className: "inline-flex items-center gap-2 bg-secondary text-secondary-foreground px-8 py-4 rounded-xl font-semibold hover:bg-secondary-hover transition-all duration-300 border border-border shadow-lg hover:shadow-xl", children: [_jsx(Sparkles, { className: "w-5 h-5", strokeWidth: 2 }), "Get Started Now", _jsx(ArrowRight, { className: "w-5 h-5", strokeWidth: 2 })] })] }) })] }));
}
