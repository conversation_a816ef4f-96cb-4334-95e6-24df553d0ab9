import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import { User, Mail, Phone, Calendar, MapPin, Bell, Shield, Download, Trash2, Edit3, Save, X, Settings } from 'lucide-react';
// Icon mapping for dynamic icon selection
const iconMap = {
    Bell,
    Mail,
    Shield,
    Settings
};
export default function AccountPage() {
    const { user, signOut } = useAuth();
    const [profile, setProfile] = useState(null);
    const [isEditing, setIsEditing] = useState(false);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [activeTab, setActiveTab] = useState('profile');
    const [formData, setFormData] = useState({
        first_name: '',
        last_name: '',
        phone: '',
        date_of_birth: '',
        location: '',
        bio: ''
    });
    const [preferences, setPreferences] = useState({
        notifications: true,
        email_updates: true,
        privacy_mode: false,
        data_sharing: false
    });
    // RULE 0001: Dynamic notification preferences from database - hardcoded array removed
    const [notificationPrefs, setNotificationPrefs] = useState([]);
    const [prefsLoading, setPrefsLoading] = useState(true);
    useEffect(() => {
        if (user) {
            loadProfile();
            fetchNotificationPrefs();
        }
        else {
            fetchNotificationPrefs(); // Load preferences config even without user
        }
    }, [user]);
    const fetchNotificationPrefs = async () => {
        try {
            const { data, error } = await supabase
                .from('notification_preferences_config')
                .select('*')
                .order('display_order', { ascending: true });
            if (error) {
                console.error('Error fetching notification preferences config:', error);
                // Fallback to basic structure if database fails
                setNotificationPrefs([
                    { id: '1', key: 'notifications', label: 'Push Notifications', description: 'Get notified about important updates and reminders', icon_name: 'Bell', display_order: 1 },
                    { id: '2', key: 'email_updates', label: 'Email Updates', description: 'Receive weekly progress reports and tips via email', icon_name: 'Mail', display_order: 2 }
                ]);
            }
            else {
                setNotificationPrefs(data || []);
            }
        }
        catch (err) {
            console.error('Error fetching notification preferences config:', err);
            setNotificationPrefs([]);
        }
        finally {
            setPrefsLoading(false);
        }
    };
    const loadProfile = async () => {
        if (!user)
            return;
        try {
            setLoading(true);
            const { data, error } = await supabase
                .from('user_profiles')
                .select('*')
                .eq('id', user.id)
                .single();
            if (error && error.code !== 'PGRST116') {
                console.error('Error loading profile:', error);
                return;
            }
            if (data) {
                setProfile(data);
                setFormData({
                    first_name: data.first_name || '',
                    last_name: data.last_name || '',
                    phone: data.phone || '',
                    date_of_birth: data.date_of_birth || '',
                    location: data.location || '',
                    bio: data.bio || ''
                });
                setPreferences(data.preferences || {
                    notifications: true,
                    email_updates: true,
                    privacy_mode: false,
                    data_sharing: false
                });
            }
            else {
                // Create default profile
                const defaultProfile = {
                    id: user.id,
                    email: user.email || '',
                    first_name: '',
                    last_name: '',
                    phone: '',
                    date_of_birth: '',
                    location: '',
                    bio: '',
                    avatar_url: '',
                    preferences: {
                        notifications: true,
                        email_updates: true,
                        privacy_mode: false,
                        data_sharing: false
                    }
                };
                setProfile(defaultProfile);
            }
        }
        catch (error) {
            console.error('Error loading profile:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const handleSave = async () => {
        if (!user || !profile)
            return;
        try {
            setSaving(true);
            const updatedProfile = {
                ...formData,
                preferences,
                updated_at: new Date().toISOString()
            };
            const { error } = await supabase
                .from('user_profiles')
                .upsert({
                id: user.id,
                email: user.email,
                ...updatedProfile
            });
            if (error) {
                console.error('Error saving profile:', error);
                return;
            }
            setProfile({ ...profile, ...updatedProfile });
            setIsEditing(false);
        }
        catch (error) {
            console.error('Error saving profile:', error);
        }
        finally {
            setSaving(false);
        }
    };
    const handleCancel = () => {
        if (profile) {
            setFormData({
                first_name: profile.first_name || '',
                last_name: profile.last_name || '',
                phone: profile.phone || '',
                date_of_birth: profile.date_of_birth || '',
                location: profile.location || '',
                bio: profile.bio || ''
            });
            setPreferences(profile.preferences || {
                notifications: true,
                email_updates: true,
                privacy_mode: false,
                data_sharing: false
            });
        }
        setIsEditing(false);
    };
    const handleDeleteAccount = async () => {
        if (!user)
            return;
        const confirmed = window.confirm('Are you sure you want to delete your account? This action cannot be undone.');
        if (confirmed) {
            try {
                // Delete user data
                await supabase.from('user_profiles').delete().eq('id', user.id);
                await supabase.from('user_goals').delete().eq('user_id', user.id);
                await supabase.from('health_metrics').delete().eq('user_id', user.id);
                await supabase.from('community_posts').delete().eq('user_id', user.id);
                // Sign out
                await signOut();
            }
            catch (error) {
                console.error('Error deleting account:', error);
            }
        }
    };
    const exportData = async () => {
        if (!user)
            return;
        try {
            // Fetch all user data
            const [profileData, goalsData, metricsData, postsData] = await Promise.all([
                supabase.from('user_profiles').select('*').eq('id', user.id),
                supabase.from('user_goals').select('*').eq('user_id', user.id),
                supabase.from('health_metrics').select('*').eq('user_id', user.id),
                supabase.from('community_posts').select('*').eq('user_id', user.id)
            ]);
            const exportData = {
                profile: profileData.data,
                goals: goalsData.data,
                health_metrics: metricsData.data,
                community_posts: postsData.data,
                exported_at: new Date().toISOString()
            };
            // Create and download file
            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `mission-fresh-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        catch (error) {
            console.error('Error exporting data:', error);
        }
    };
    if (loading) {
        return (_jsx("div", { className: "min-h-screen flex items-center justify-center", children: _jsx("div", { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-primary" }) }));
    }
    return (_jsx("div", { className: "min-h-screen bg-background py-12", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6 lg:px-8", children: [_jsxs("div", { className: "mb-16", children: [_jsx("h1", { className: "text-5xl font-bold text-foreground mb-6 tracking-tight", children: "Account Settings" }), _jsx("p", { className: "text-xl text-muted-foreground leading-relaxed", children: "Manage your profile and preferences" })] }), _jsxs("div", { className: "bg-card rounded-2xl shadow-2xl border border-border mb-12", children: [_jsx("div", { className: "border-b border-border", children: _jsx("nav", { className: "flex space-x-12 px-8", children: [
                                    { id: 'profile', label: 'Profile', icon: User },
                                    { id: 'preferences', label: 'Preferences', icon: Settings },
                                    { id: 'privacy', label: 'Privacy & Data', icon: Shield }
                                ].map((tab) => (_jsxs("button", { onClick: () => setActiveTab(tab.id), className: `flex items-center space-x-3 py-6 px-2 border-b-2 font-semibold text-lg transition-all duration-300 ${activeTab === tab.id
                                        ? 'border-primary text-primary'
                                        : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'}`, children: [_jsx(tab.icon, { className: "w-6 h-6", strokeWidth: 1.5 }), _jsx("span", { children: tab.label })] }, tab.id))) }) }), activeTab === 'profile' && (_jsxs("div", { className: "p-10", children: [_jsxs("div", { className: "flex items-center justify-between mb-10", children: [_jsx("h3", { className: "text-2xl font-bold text-card-foreground", children: "Profile Information" }), !isEditing ? (_jsxs("button", { onClick: () => setIsEditing(true), className: "flex items-center space-x-3 px-6 py-3 text-primary border border-primary rounded-lg hover:bg-primary-subtle transition-all duration-300 font-semibold", children: [_jsx(Edit3, { className: "w-5 h-5", strokeWidth: 1.5 }), _jsx("span", { children: "Edit" })] })) : (_jsxs("div", { className: "flex space-x-4", children: [_jsxs("button", { onClick: handleSave, disabled: saving, className: "flex items-center space-x-3 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover disabled:opacity-50 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl", children: [_jsx(Save, { className: "w-5 h-5", strokeWidth: 1.5 }), _jsx("span", { children: saving ? 'Saving...' : 'Save' })] }), _jsxs("button", { onClick: handleCancel, className: "flex items-center space-x-3 px-6 py-3 text-muted-foreground border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold", children: [_jsx(X, { className: "w-5 h-5", strokeWidth: 1.5 }), _jsx("span", { children: "Cancel" })] })] }))] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [_jsxs("div", { children: [_jsx("label", { className: "block text-sm font-semibold text-muted-foreground mb-3 tracking-wide uppercase", children: "First Name" }), _jsxs("div", { className: "relative", children: [_jsx(User, { className: "absolute left-4 top-4 w-5 h-5 text-muted-foreground", strokeWidth: 1.5 }), _jsx("input", { type: "text", value: formData.first_name, onChange: (e) => setFormData({ ...formData, first_name: e.target.value }), disabled: !isEditing, className: "w-full pl-12 pr-4 py-4 border border-border rounded-xl focus:ring-2 focus:ring-ring focus:border-ring disabled:bg-muted text-foreground placeholder:text-muted-foreground", placeholder: "Enter your first name" })] })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-sm font-semibold text-muted-foreground mb-3 tracking-wide uppercase", children: "Last Name" }), _jsxs("div", { className: "relative", children: [_jsx(User, { className: "absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-muted-foreground" }), _jsx("input", { type: "text", value: formData.last_name, onChange: (e) => setFormData({ ...formData, last_name: e.target.value }), disabled: !isEditing, className: "w-full pl-12 pr-4 py-4 border border-border rounded-xl focus:ring-2 focus:ring-ring focus:border-ring disabled:bg-muted text-foreground placeholder:text-muted-foreground", placeholder: "Enter your last name" })] })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Email" }), _jsxs("div", { className: "relative", children: [_jsx(Mail, { className: "absolute left-3 top-3 w-5 h-5 text-gray-400" }), _jsx("input", { type: "email", value: user?.email || '', disabled: true, className: "w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50" })] })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Phone" }), _jsxs("div", { className: "relative", children: [_jsx(Phone, { className: "absolute left-3 top-3 w-5 h-5 text-gray-400" }), _jsx("input", { type: "tel", value: formData.phone, onChange: (e) => setFormData({ ...formData, phone: e.target.value }), disabled: !isEditing, className: "w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 disabled:bg-gray-50", placeholder: "Enter your phone number" })] })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Date of Birth" }), _jsxs("div", { className: "relative", children: [_jsx(Calendar, { className: "absolute left-3 top-3 w-5 h-5 text-gray-400" }), _jsx("input", { type: "date", value: formData.date_of_birth, onChange: (e) => setFormData({ ...formData, date_of_birth: e.target.value }), disabled: !isEditing, className: "w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 disabled:bg-gray-50" })] })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Location" }), _jsxs("div", { className: "relative", children: [_jsx(MapPin, { className: "absolute left-3 top-3 w-5 h-5 text-gray-400" }), _jsx("input", { type: "text", value: formData.location, onChange: (e) => setFormData({ ...formData, location: e.target.value }), disabled: !isEditing, className: "w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 disabled:bg-gray-50", placeholder: "Enter your location" })] })] }), _jsxs("div", { className: "md:col-span-2", children: [_jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Bio" }), _jsx("textarea", { value: formData.bio, onChange: (e) => setFormData({ ...formData, bio: e.target.value }), disabled: !isEditing, rows: 3, className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 disabled:bg-gray-50", placeholder: "Tell us about yourself..." })] })] })] })), activeTab === 'preferences' && (_jsxs("div", { className: "p-6", children: [_jsx("h3", { className: "text-lg font-medium text-gray-900 mb-6", children: "Notification Preferences" }), _jsx("div", { className: "space-y-4", children: prefsLoading ? (_jsxs("div", { className: "text-center py-4", children: [_jsx("div", { className: "animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2" }), _jsx("p", { className: "text-muted-foreground text-sm", children: "Loading preferences..." })] })) : (notificationPrefs.map((pref) => {
                                        const IconComponent = iconMap[pref.icon_name] || Bell;
                                        return (_jsxs("div", { className: "flex items-center justify-between p-4 border border-gray-200 rounded-lg", children: [_jsxs("div", { className: "flex items-center space-x-3", children: [_jsx(IconComponent, { className: "w-5 h-5 text-gray-600" }), _jsxs("div", { children: [_jsx("h4", { className: "font-medium text-gray-900", children: pref.label }), _jsx("p", { className: "text-sm text-gray-600", children: pref.description })] })] }), _jsxs("label", { className: "relative inline-flex items-center cursor-pointer", children: [_jsx("input", { type: "checkbox", checked: preferences[pref.key], onChange: (e) => setPreferences({ ...preferences, [pref.key]: e.target.checked }), className: "sr-only peer" }), _jsx("div", { className: "w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-focus rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary" })] })] }, pref.key));
                                    })) }), _jsx("div", { className: "mt-6", children: _jsx("button", { onClick: handleSave, disabled: saving, className: "px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover disabled:opacity-50", children: saving ? 'Saving...' : 'Save Preferences' }) })] })), activeTab === 'privacy' && (_jsxs("div", { className: "p-6", children: [_jsx("h3", { className: "text-lg font-medium text-gray-900 mb-6", children: "Privacy & Data Management" }), _jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "border border-gray-200 rounded-lg p-4", children: [_jsx("h4", { className: "font-medium text-gray-900 mb-2", children: "Data Export" }), _jsx("p", { className: "text-sm text-gray-600 mb-4", children: "Download a copy of all your data including profile, goals, metrics, and posts." }), _jsxs("button", { onClick: exportData, className: "flex items-center space-x-2 px-4 py-2 text-primary border border-primary rounded-lg hover:bg-primary-subtle", children: [_jsx(Download, { className: "w-4 h-4" }), _jsx("span", { children: "Export My Data" })] })] }), _jsxs("div", { className: "border border-destructive rounded-lg p-4", children: [_jsx("h4", { className: "font-medium text-destructive mb-2", children: "Delete Account" }), _jsx("p", { className: "text-sm text-muted-foreground mb-4", children: "Permanently delete your account and all associated data. This action cannot be undone." }), _jsxs("button", { onClick: handleDeleteAccount, className: "flex items-center space-x-2 px-4 py-2 bg-destructive text-destructive-foreground rounded-lg hover:bg-destructive-hover", children: [_jsx(Trash2, { className: "w-4 h-4" }), _jsx("span", { children: "Delete Account" })] })] })] })] }))] })] }) }));
}
