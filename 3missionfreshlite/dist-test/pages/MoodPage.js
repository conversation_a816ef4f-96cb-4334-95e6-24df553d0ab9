import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Smile, Meh, Sun, TrendingUp, Calendar, Heart, MessageCircle } from 'lucide-react';
// RULE 0001: Dynamic mood and energy options from database - hardcoded arrays removed
export default function MoodPage() {
    const { user, loading } = useAuth();
    const navigate = useNavigate();
    const [moodEntries, setMoodEntries] = useState([]);
    const [isLoadingEntries, setIsLoadingEntries] = useState(true);
    const [showQuickLog, setShowQuickLog] = useState(false);
    const [selectedPeriod, setSelectedPeriod] = useState('week');
    // RULE 0001: Real database-driven state for mood options
    const [moodOptions, setMoodOptions] = useState([]);
    const [energyLevels, setEnergyLevels] = useState([]);
    const [isLoadingOptions, setIsLoadingOptions] = useState(true);
    // Quick log state
    const [currentMood, setCurrentMood] = useState(3);
    const [currentEnergy, setCurrentEnergy] = useState(3);
    const [currentStress, setCurrentStress] = useState(3);
    const [currentSleep, setCurrentSleep] = useState(3);
    const [currentNotes, setCurrentNotes] = useState('');
    useEffect(() => {
        if (!loading && !user) {
            navigate('/auth');
            return;
        }
        if (user) {
            loadMoodEntries();
            loadMoodOptions();
        }
    }, [user, loading, navigate]);
    const loadMoodOptions = async () => {
        try {
            setIsLoadingOptions(true);
            // RULE 0001: Load mood options from database
            const { data: moodData, error: moodError } = await supabase
                .from('mood_options')
                .select('*')
                .order('display_order');
            const { data: energyData, error: energyError } = await supabase
                .from('energy_levels')
                .select('*')
                .order('display_order');
            if (moodError || !moodData?.length || energyError || !energyData?.length) {
                // Fallback options if tables don't exist
                const fallbackMoodOptions = [
                    { id: '1', score: 1, emoji: '😢', label: 'Very Sad', color: 'text-destructive', display_order: 1 },
                    { id: '2', score: 2, emoji: '😞', label: 'Sad', color: 'text-destructive', display_order: 2 },
                    { id: '3', score: 3, emoji: '😐', label: 'Neutral', color: 'text-muted-foreground', display_order: 3 },
                    { id: '4', score: 4, emoji: '🙂', label: 'Good', color: 'text-success', display_order: 4 },
                    { id: '5', score: 5, emoji: '😊', label: 'Very Good', color: 'text-success', display_order: 5 }
                ];
                const fallbackEnergyLevels = [
                    { id: '1', score: 1, icon_name: 'CloudRain', label: 'Very Low', color: 'text-muted-foreground', display_order: 1 },
                    { id: '2', score: 2, icon_name: 'Cloud', label: 'Low', color: 'text-muted-foreground', display_order: 2 },
                    { id: '3', score: 3, icon_name: 'Meh', label: 'Moderate', color: 'text-accent', display_order: 3 },
                    { id: '4', score: 4, icon_name: 'Sun', label: 'High', color: 'text-orange-500', display_order: 4 },
                    { id: '5', score: 5, icon_name: 'Sun', label: 'Very High', color: 'text-yellow-400', display_order: 5 }
                ];
                setMoodOptions(fallbackMoodOptions);
                setEnergyLevels(fallbackEnergyLevels);
            }
            else {
                setMoodOptions(moodData || []);
                setEnergyLevels(energyData || []);
            }
        }
        catch (error) {
            console.error('Error loading mood options:', error);
            // Fallback on error
            const fallbackMoodOptions = [
                { id: '1', score: 3, emoji: '😐', label: 'Neutral', color: 'text-muted-foreground', display_order: 1 }
            ];
            const fallbackEnergyLevels = [
                { id: '1', score: 3, icon_name: 'Meh', label: 'Moderate', color: 'text-yellow-500', display_order: 1 }
            ];
            setMoodOptions(fallbackMoodOptions);
            setEnergyLevels(fallbackEnergyLevels);
        }
        finally {
            setIsLoadingOptions(false);
        }
    };
    const loadMoodEntries = async () => {
        if (!user)
            return;
        try {
            setIsLoadingEntries(true);
            let query = supabase
                .from('health_metrics')
                .select('*')
                .eq('user_id', user.id)
                .order('date', { ascending: false });
            if (selectedPeriod === 'week') {
                const weekAgo = new Date();
                weekAgo.setDate(weekAgo.getDate() - 7);
                query = query.gte('date', weekAgo.toISOString().split('T')[0]);
            }
            else if (selectedPeriod === 'month') {
                const monthAgo = new Date();
                monthAgo.setMonth(monthAgo.getMonth() - 1);
                query = query.gte('date', monthAgo.toISOString().split('T')[0]);
            }
            const { data, error } = await query;
            if (error)
                throw error;
            setMoodEntries(data || []);
        }
        catch (error) {
            console.error('Error loading mood entries:', error);
        }
        finally {
            setIsLoadingEntries(false);
        }
    };
    useEffect(() => {
        if (user) {
            loadMoodEntries();
        }
    }, [selectedPeriod]);
    const saveMoodEntry = async () => {
        if (!user)
            return;
        const today = new Date().toISOString().split('T')[0];
        try {
            const { data, error } = await supabase
                .from('health_metrics')
                .upsert({
                user_id: user.id,
                date: today,
                mood_score: currentMood,
                energy_level: currentEnergy,
                stress_level: currentStress,
                sleep_quality: currentSleep,
                notes: currentNotes,
                updated_at: new Date().toISOString()
            })
                .select()
                .single();
            if (error)
                throw error;
            setShowQuickLog(false);
            setCurrentNotes('');
            loadMoodEntries();
        }
        catch (error) {
            console.error('Error saving mood entry:', error);
        }
    };
    const getAverageMood = () => {
        if (moodEntries.length === 0)
            return 0;
        const sum = moodEntries.reduce((acc, entry) => acc + (entry.mood_score || 0), 0);
        return Math.round((sum / moodEntries.length) * 10) / 10;
    };
    const getAverageEnergy = () => {
        if (moodEntries.length === 0)
            return 0;
        const sum = moodEntries.reduce((acc, entry) => acc + (entry.energy_level || 0), 0);
        return Math.round((sum / moodEntries.length) * 10) / 10;
    };
    const getMoodTrend = () => {
        if (moodEntries.length < 2)
            return 'stable';
        const recent = moodEntries.slice(0, Math.min(3, moodEntries.length));
        const older = moodEntries.slice(Math.min(3, moodEntries.length), Math.min(6, moodEntries.length));
        if (recent.length === 0 || older.length === 0)
            return 'stable';
        const recentAvg = recent.reduce((acc, entry) => acc + (entry.mood_score || 0), 0) / recent.length;
        const olderAvg = older.reduce((acc, entry) => acc + (entry.mood_score || 0), 0) / older.length;
        if (recentAvg > olderAvg + 0.3)
            return 'improving';
        if (recentAvg < olderAvg - 0.3)
            return 'declining';
        return 'stable';
    };
    const getTrendIcon = () => {
        const trend = getMoodTrend();
        switch (trend) {
            case 'improving': return _jsx(TrendingUp, { className: "h-5 w-5 text-success" });
            case 'declining': return _jsx(TrendingUp, { className: "h-5 w-5 text-destructive rotate-180" });
            default: return _jsx(Meh, { className: "h-5 w-5 text-muted-foreground" });
        }
    };
    // RULE 0001: Loading state check including mood options
    if (loading || isLoadingEntries || isLoadingOptions) {
        return (_jsx("div", { className: "min-h-screen bg-muted", children: _jsx("div", { className: "container mx-auto px-4 py-8", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary" }), _jsx("p", { className: "text-muted-foreground mt-2", children: "Loading..." })] }) }) }));
    }
    return (_jsx("div", { className: "min-h-screen bg-primary-subtle", children: _jsxs("div", { className: "max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8", children: [_jsx("div", { className: "mb-8", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { children: [_jsxs("h1", { className: "text-3xl font-bold text-foreground flex items-center", children: [_jsx(Heart, { className: "mr-3 h-8 w-8 text-primary" }), "Mood Tracker"] }), _jsx("p", { className: "mt-2 text-muted-foreground", children: "Track your emotional wellness and identify patterns in your recovery journey." })] }), _jsxs("button", { onClick: () => setShowQuickLog(true), className: "bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary-hover transition-colors flex items-center", children: [_jsx(MessageCircle, { className: "mr-2 h-5 w-5" }), "Log Mood"] })] }) }), _jsxs("div", { className: "grid gap-6 md:grid-cols-4 mb-8", children: [_jsx("div", { className: "bg-card rounded-lg shadow-sm border border-border p-6", children: _jsxs("div", { className: "flex items-center", children: [_jsx("div", { className: "flex-shrink-0", children: _jsx(Smile, { className: "h-8 w-8 text-primary" }) }), _jsxs("div", { className: "ml-4", children: [_jsx("div", { className: "text-sm font-medium text-muted-foreground", children: "Average Mood" }), _jsxs("div", { className: "text-2xl font-bold text-card-foreground", children: [getAverageMood(), "/5"] })] })] }) }), _jsx("div", { className: "bg-card rounded-lg shadow-sm border border-border p-6", children: _jsxs("div", { className: "flex items-center", children: [_jsx("div", { className: "flex-shrink-0", children: _jsx(Sun, { className: "h-8 w-8 text-primary" }) }), _jsxs("div", { className: "ml-4", children: [_jsx("div", { className: "text-sm font-medium text-muted-foreground", children: "Average Energy" }), _jsxs("div", { className: "text-2xl font-bold text-card-foreground", children: [getAverageEnergy(), "/5"] })] })] }) }), _jsx("div", { className: "bg-card rounded-lg shadow-sm border border-border p-6", children: _jsxs("div", { className: "flex items-center", children: [_jsx("div", { className: "flex-shrink-0", children: getTrendIcon() }), _jsxs("div", { className: "ml-4", children: [_jsx("div", { className: "text-sm font-medium text-muted-foreground", children: "Trend" }), _jsx("div", { className: "text-lg font-bold text-card-foreground capitalize", children: getMoodTrend() })] })] }) }), _jsx("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8", children: _jsxs("div", { className: "flex items-center", children: [_jsx("div", { className: "flex-shrink-0", children: _jsx(Calendar, { className: "h-10 w-10 text-primary", strokeWidth: 1.5 }) }), _jsxs("div", { className: "ml-6", children: [_jsx("div", { className: "text-sm font-semibold text-muted-foreground tracking-wide uppercase", children: "Entries" }), _jsx("div", { className: "text-3xl font-bold text-card-foreground tracking-tight", children: moodEntries.length })] })] }) })] }), _jsx("div", { className: "flex justify-center mb-8", children: _jsx("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-1", children: ['week', 'month', 'all'].map((period) => (_jsx("button", { onClick: () => setSelectedPeriod(period), className: `px-4 py-2 rounded-md text-sm font-medium transition-colors ${selectedPeriod === period
                                ? 'bg-pink-600 text-white'
                                : 'text-gray-600 hover:text-gray-900'}`, children: period === 'week' ? 'This Week' : period === 'month' ? 'This Month' : 'All Time' }, period))) }) }), _jsxs("div", { className: "bg-card rounded-lg shadow-sm border border-border", children: [_jsx("div", { className: "px-6 py-4 border-b border-border", children: _jsx("h3", { className: "text-lg font-medium text-card-foreground", children: "Mood History" }) }), isLoadingEntries ? (_jsxs("div", { className: "p-8 text-center", children: [_jsx("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" }), _jsx("p", { className: "mt-4 text-muted-foreground", children: "Loading entries..." })] })) : moodEntries.length > 0 ? (_jsx("div", { className: "divide-y divide-border", children: moodEntries.map((entry) => (_jsx("div", { className: "p-6", children: _jsx("div", { className: "flex items-start justify-between", children: _jsxs("div", { className: "flex-1", children: [_jsxs("div", { className: "flex items-center space-x-4 mb-3", children: [_jsx("div", { className: "text-2xl", children: MOOD_EMOJIS.find(m => m.score === Math.round(entry.mood_score || 3))?.emoji || '😐' }), _jsxs("div", { children: [_jsx("div", { className: "font-medium text-card-foreground", children: MOOD_EMOJIS.find(m => m.score === Math.round(entry.mood_score || 3))?.label || 'Neutral' }), _jsx("div", { className: "text-sm text-muted-foreground", children: new Date(entry.date).toLocaleDateString('en-US', {
                                                                    weekday: 'long',
                                                                    year: 'numeric',
                                                                    month: 'long',
                                                                    day: 'numeric'
                                                                }) })] })] }), _jsxs("div", { className: "grid grid-cols-3 gap-4 mb-3", children: [_jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-sm text-muted-foreground", children: "Energy" }), _jsxs("div", { className: "font-medium text-card-foreground", children: [entry.energy_level || 0, "/5"] })] }), _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-sm text-muted-foreground", children: "Stress" }), _jsxs("div", { className: "font-medium text-card-foreground", children: [entry.stress_level || 0, "/5"] })] }), _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-sm text-muted-foreground", children: "Sleep" }), _jsxs("div", { className: "font-medium text-card-foreground", children: [entry.sleep_quality || 0, "/5"] })] })] }), entry.notes && (_jsxs("div", { className: "text-muted-foreground text-sm bg-muted rounded-lg p-3", children: ["\"", entry.notes, "\""] }))] }) }) }, entry.id))) })) : (_jsxs("div", { className: "p-8 text-center", children: [_jsx(MessageCircle, { className: "mx-auto h-12 w-12 text-muted-foreground" }), _jsx("h3", { className: "mt-4 text-lg font-medium text-card-foreground", children: "No mood entries yet" }), _jsx("p", { className: "mt-2 text-muted-foreground", children: "Start tracking your mood to identify patterns and improve your well-being." }), _jsx("button", { onClick: () => setShowQuickLog(true), className: "mt-6 bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary-hover transition-colors", children: "Log Your First Entry" })] }))] }), showQuickLog && (_jsx("div", { className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4", children: _jsxs("div", { className: "bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto", children: [_jsxs("div", { className: "px-6 py-4 border-b border-gray-200", children: [_jsx("h3", { className: "text-lg font-medium text-gray-900", children: "How are you feeling today?" }), _jsx("p", { className: "text-sm text-gray-600", children: "Rate each area from 1 (poor) to 5 (excellent)" })] }), _jsxs("div", { className: "p-6 space-y-6", children: [_jsxs("div", { children: [_jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-3", children: ["Mood: ", moodOptions.find(m => m.score === currentMood)?.label || 'Select mood'] }), _jsx("div", { className: "flex justify-between", children: moodOptions.map((mood) => (_jsx("button", { onClick: () => setCurrentMood(mood.score), className: `text-3xl p-2 rounded-lg transition-colors ${currentMood === mood.score ? 'bg-pink-100' : 'hover:bg-gray-100'}`, children: mood.emoji }, mood.score))) })] }), _jsxs("div", { children: [_jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["Energy Level: ", currentEnergy, "/5"] }), _jsx("input", { type: "range", min: "1", max: "5", value: currentEnergy, onChange: (e) => setCurrentEnergy(Number(e.target.value)), className: "w-full" })] }), _jsxs("div", { children: [_jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["Stress Level: ", currentStress, "/5"] }), _jsx("input", { type: "range", min: "1", max: "5", value: currentStress, onChange: (e) => setCurrentStress(Number(e.target.value)), className: "w-full" })] }), _jsxs("div", { children: [_jsxs("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: ["Sleep Quality: ", currentSleep, "/5"] }), _jsx("input", { type: "range", min: "1", max: "5", value: currentSleep, onChange: (e) => setCurrentSleep(Number(e.target.value)), className: "w-full" })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-sm font-medium text-gray-700 mb-2", children: "Notes (Optional)" }), _jsx("textarea", { value: currentNotes, onChange: (e) => setCurrentNotes(e.target.value), placeholder: "How are you feeling? Any thoughts or reflections...", rows: 3, className: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent" })] })] }), _jsxs("div", { className: "px-6 py-4 border-t border-gray-200 flex space-x-3", children: [_jsx("button", { onClick: () => setShowQuickLog(false), className: "flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors", children: "Cancel" }), _jsx("button", { onClick: saveMoodEntry, className: "flex-1 px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors", children: "Save Entry" })] })] }) }))] }) }));
}
