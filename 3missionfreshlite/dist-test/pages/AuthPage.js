import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Mail, Lock, Eye, EyeOff } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate, useSearchParams } from 'react-router-dom';
import Logo from '../components/Logo';
export default function AuthPage() {
    const { signIn, signUp, user } = useAuth();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const [isSignUp, setIsSignUp] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [emailError, setEmailError] = useState('');
    const [passwordError, setPasswordError] = useState('');
    const [confirmPasswordError, setConfirmPasswordError] = useState('');
    const [passwordStrength, setPasswordStrength] = useState(0);
    // Check URL parameters to set initial mode
    useEffect(() => {
        const mode = searchParams.get('mode');
        if (mode === 'signin') {
            setIsSignUp(false);
        }
    }, [searchParams]);
    // Redirect if user is already authenticated
    if (user) {
        navigate('/dashboard');
        return null;
    }
    const validateEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };
    const validatePassword = (password) => {
        return password.length >= 8;
    };
    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');
        setSuccess('');
        setEmailError('');
        setPasswordError('');
        // Validation
        if (!validateEmail(email)) {
            setEmailError('Please enter a valid email address');
            setLoading(false);
            return;
        }
        if (!validatePassword(password)) {
            setPasswordError('Password must be at least 8 characters long');
            setLoading(false);
            return;
        }
        if (isSignUp && password !== confirmPassword) {
            setPasswordError('Passwords do not match');
            setLoading(false);
            return;
        }
        try {
            const { error: authError } = isSignUp
                ? await signUp(email, password)
                : await signIn(email, password);
            if (authError) {
                setError(authError.message);
            }
            else {
                setSuccess(isSignUp ? 'Account created successfully!' : 'Welcome back!');
                setTimeout(() => navigate('/dashboard'), 1500);
            }
        }
        catch (err) {
            setError('An unexpected error occurred. Please try again.');
        }
        finally {
            setLoading(false);
        }
    };
    return (_jsx("div", { className: "min-h-screen bg-background flex items-center justify-center p-6", children: _jsx("div", { className: "max-w-lg w-full", children: _jsxs("div", { className: "bg-card rounded-2xl shadow-2xl border border-border p-12", children: [_jsxs("div", { className: "text-center mb-12", children: [_jsx("div", { className: "flex justify-center mb-8", children: _jsx(Logo, { size: "xl", showText: true, linkTo: "/" }) }), _jsx("p", { className: "text-xl text-muted-foreground leading-relaxed", children: isSignUp ? 'Create your account' : 'Welcome back' })] }), _jsxs("form", { onSubmit: handleSubmit, className: "space-y-8", children: [_jsxs("div", { children: [_jsx("label", { htmlFor: "email", className: "block text-base font-semibold text-card-foreground mb-4", children: "Email" }), _jsxs("div", { className: "relative", children: [_jsx(Mail, { className: "absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-muted-foreground", strokeWidth: 1.5 }), _jsx("input", { type: "email", id: "email", value: email, onChange: (e) => setEmail(e.target.value), className: "w-full pl-12 pr-4 py-4 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg", placeholder: "Enter your email", required: true })] })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: "password", className: "block text-base font-semibold text-card-foreground mb-4", children: "Password" }), _jsxs("div", { className: "relative", children: [_jsx(Lock, { className: "absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-muted-foreground", strokeWidth: 1.5 }), _jsx("input", { type: showPassword ? 'text' : 'password', id: "password", value: password, onChange: (e) => setPassword(e.target.value), className: "w-full pl-12 pr-12 py-4 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg", placeholder: "Enter your password", required: true }), _jsx("button", { type: "button", onClick: () => setShowPassword(!showPassword), className: "absolute right-4 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-primary transition-colors duration-300", children: showPassword ? _jsx(EyeOff, { className: "w-6 h-6", strokeWidth: 1.5 }) : _jsx(Eye, { className: "w-6 h-6", strokeWidth: 1.5 }) })] })] }), error && (_jsx("div", { className: "bg-destructive/10 border border-destructive/20 text-destructive px-6 py-4 rounded-lg font-medium", children: error })), _jsx("button", { type: "submit", disabled: loading, className: "w-full px-8 py-4 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover disabled:opacity-50 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl", children: loading ? 'Please wait...' : (isSignUp ? 'Create Account' : 'Sign In') })] }), _jsx("div", { className: "mt-10 text-center", children: _jsxs("p", { className: "text-muted-foreground text-lg", children: [isSignUp ? 'Already have an account?' : "Don't have an account?", _jsx("button", { onClick: () => setIsSignUp(!isSignUp), className: "ml-2 text-primary hover:text-primary-hover font-semibold transition-colors duration-300", children: isSignUp ? 'Sign In' : 'Sign Up' })] }) })] }) }) }));
}
