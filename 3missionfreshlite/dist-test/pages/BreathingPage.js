import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Wind, Play, Pause, RotateCcw, Clock, Heart, Zap } from 'lucide-react';
// RULE 0001: Dynamic breathing techniques from database - hardcoded array removed
export default function BreathingPage() {
    const { user, loading } = useAuth();
    const navigate = useNavigate();
    // RULE 0001: Real Supabase database integration for breathing techniques
    const [breathingTechniques, setBreathingTechniques] = useState([]);
    const [selectedTechnique, setSelectedTechnique] = useState(null);
    const [isActive, setIsActive] = useState(false);
    const [currentPhase, setCurrentPhase] = useState('inhale');
    const [currentCount, setCurrentCount] = useState(0);
    const [completedCycles, setCompletedCycles] = useState(0);
    const [sessions, setSessions] = useState([]);
    const [isLoadingSessions, setIsLoadingSessions] = useState(true);
    const [isLoadingTechniques, setIsLoadingTechniques] = useState(true);
    const intervalRef = useRef(null);
    useEffect(() => {
        if (!loading && !user) {
            navigate('/auth');
            return;
        }
        if (user) {
            loadSessions();
            loadBreathingTechniques();
        }
    }, [user, loading, navigate]);
    const loadBreathingTechniques = async () => {
        try {
            setIsLoadingTechniques(true);
            // RULE 0001: Load breathing techniques from database
            const { data: techniqueData, error: techniqueError } = await supabase
                .from('breathing_techniques')
                .select('*')
                .order('display_order');
            if (techniqueError || !techniqueData?.length) {
                // Fallback techniques if table doesn't exist
                const fallbackTechniques = [
                    {
                        id: '4-7-8',
                        name: '4-7-8 Breathing',
                        description: 'A calming technique that helps reduce anxiety and cravings',
                        inhale_count: 4,
                        hold_count: 7,
                        exhale_count: 8,
                        cycles: 4,
                        benefits: ['Reduces anxiety', 'Calms cravings', 'Improves sleep', 'Stress relief']
                    },
                    {
                        id: 'box-breathing',
                        name: 'Box Breathing',
                        description: 'Used by Navy SEALs for focus and stress management',
                        inhale_count: 4,
                        hold_count: 4,
                        exhale_count: 4,
                        cycles: 8,
                        benefits: ['Improves focus', 'Reduces stress', 'Calms mind', 'Builds resilience']
                    },
                    {
                        id: 'deep-breathing',
                        name: 'Deep Breathing',
                        description: 'Simple deep breathing for immediate craving relief',
                        inhale_count: 6,
                        hold_count: 2,
                        exhale_count: 6,
                        cycles: 6,
                        benefits: ['Quick relief', 'Easy to learn', 'Instant calm', 'Portable technique']
                    }
                ];
                setBreathingTechniques(fallbackTechniques);
                setSelectedTechnique(fallbackTechniques[0]);
            }
            else {
                setBreathingTechniques(techniqueData);
                setSelectedTechnique(techniqueData[0] || null);
            }
        }
        catch (error) {
            console.error('Error loading breathing techniques:', error);
            // Fallback on error
            const fallbackTechniques = [
                {
                    id: '4-7-8',
                    name: '4-7-8 Breathing',
                    description: 'A calming technique that helps reduce anxiety and cravings',
                    inhale_count: 4,
                    hold_count: 7,
                    exhale_count: 8,
                    cycles: 4,
                    benefits: ['Reduces anxiety', 'Calms cravings', 'Improves sleep', 'Stress relief']
                }
            ];
            setBreathingTechniques(fallbackTechniques);
            setSelectedTechnique(fallbackTechniques[0]);
        }
        finally {
            setIsLoadingTechniques(false);
        }
    };
    useEffect(() => {
        if (isActive) {
            startBreathingCycle();
        }
        else {
            stopBreathingCycle();
        }
        return () => stopBreathingCycle();
    }, [isActive]);
    const loadSessions = async () => {
        if (!user)
            return;
        try {
            setIsLoadingSessions(true);
            const { data, error } = await supabase
                .from('breathing_sessions')
                .select('*')
                .eq('user_id', user.id)
                .order('completed_at', { ascending: false })
                .limit(10);
            if (error)
                throw error;
            setSessions(data || []);
        }
        catch (error) {
            console.error('Error loading breathing sessions:', error);
        }
        finally {
            setIsLoadingSessions(false);
        }
    };
    const startBreathingCycle = () => {
        if (intervalRef.current || !selectedTechnique)
            return;
        let phaseIndex = 0;
        const phases = [
            { phase: 'inhale', count: selectedTechnique.inhale_count },
            { phase: 'hold', count: selectedTechnique.hold_count },
            { phase: 'exhale', count: selectedTechnique.exhale_count }
        ];
        let currentPhaseTime = 0;
        let cyclesCompleted = 0;
        intervalRef.current = setInterval(() => {
            currentPhaseTime++;
            setCurrentCount(currentPhaseTime);
            setCurrentPhase(phases[phaseIndex].phase);
            if (currentPhaseTime >= phases[phaseIndex].count) {
                phaseIndex++;
                currentPhaseTime = 0;
                if (phaseIndex >= phases.length) {
                    // Cycle completed
                    phaseIndex = 0;
                    cyclesCompleted++;
                    setCompletedCycles(cyclesCompleted);
                    if (cyclesCompleted >= selectedTechnique.cycles) {
                        // Session completed
                        setIsActive(false);
                        saveSession();
                    }
                }
            }
        }, 1000);
    };
    const stopBreathingCycle = () => {
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }
    };
    const resetSession = () => {
        setIsActive(false);
        setCurrentPhase('inhale');
        setCurrentCount(0);
        setCompletedCycles(0);
    };
    const saveSession = async () => {
        if (!user)
            return;
        try {
            const { error } = await supabase
                .from('breathing_sessions')
                .insert({
                user_id: user.id,
                technique: selectedTechnique.id,
                duration: selectedTechnique.cycles * (selectedTechnique.inhale_count + selectedTechnique.hold_count + selectedTechnique.exhale_count),
                completed_at: new Date().toISOString()
            });
            if (error)
                throw error;
            loadSessions();
        }
        catch (error) {
            console.error('Error saving breathing session:', error);
        }
    };
    const getPhaseInstruction = () => {
        switch (currentPhase) {
            case 'inhale':
                return 'Breathe in slowly...';
            case 'hold':
                return 'Hold your breath...';
            case 'exhale':
                return 'Breathe out slowly...';
            default:
                return 'Get ready...';
        }
    };
    const getBreathingCircleScale = () => {
        switch (currentPhase) {
            case 'inhale':
                return 'scale-110';
            case 'hold':
                return 'scale-110';
            case 'exhale':
                return 'scale-75';
            default:
                return 'scale-100';
        }
    };
    if (loading) {
        return (_jsx("div", { className: "min-h-screen bg-background flex items-center justify-center", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto" }), _jsx("p", { className: "mt-6 text-muted-foreground text-lg", children: "Loading breathing exercises..." })] }) }));
    }
    return (_jsx("div", { className: "min-h-screen bg-background", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6 lg:px-8 py-12", children: [_jsxs("div", { className: "text-center mb-16", children: [_jsxs("h1", { className: "text-5xl font-bold text-foreground flex items-center justify-center mb-6 tracking-tight", children: [_jsx("div", { className: "w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mr-6 border border-primary/20", children: _jsx(Wind, { className: "h-8 w-8 text-primary", strokeWidth: 1.5 }) }), "Breathing Exercises"] }), _jsx("p", { className: "text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto", children: "Calm your mind and reduce cravings with guided breathing techniques" })] }), _jsxs("div", { className: "grid gap-12 lg:grid-cols-2", children: [_jsx("div", { className: "bg-card rounded-xl shadow-lg border border-border p-12", children: _jsxs("div", { className: "text-center", children: [_jsx("h3", { className: "text-3xl font-bold text-card-foreground mb-4", children: selectedTechnique?.name || 'Select a technique' }), _jsx("p", { className: "text-muted-foreground mb-12 text-lg leading-relaxed", children: selectedTechnique?.description || 'Choose a breathing technique to get started' }), _jsx("div", { className: "flex items-center justify-center mb-12", children: _jsx("div", { className: `w-48 h-48 rounded-full bg-primary/10 border-4 border-primary/20 flex items-center justify-center transform transition-transform duration-1000 ${getBreathingCircleScale()} shadow-lg`, children: _jsxs("div", { className: "text-primary text-center", children: [_jsx("div", { className: "text-4xl font-bold mb-2", children: currentCount }), _jsx("div", { className: "text-lg font-semibold capitalize", children: currentPhase })] }) }) }), _jsxs("div", { className: "mb-10", children: [_jsx("p", { className: "text-2xl text-card-foreground mb-4 font-medium", children: getPhaseInstruction() }), _jsxs("p", { className: "text-muted-foreground text-lg", children: ["Cycle ", completedCycles + 1, " of ", selectedTechnique?.cycles || 0] })] }), _jsxs("div", { className: "flex justify-center space-x-6", children: [_jsx("button", { onClick: () => setIsActive(!isActive), className: "px-8 py-4 rounded-lg font-semibold transition-all duration-300 flex items-center bg-primary text-primary-foreground hover:bg-primary-hover shadow-lg hover:shadow-xl text-lg", children: isActive ? (_jsxs(_Fragment, { children: [_jsx(Pause, { className: "mr-3 h-6 w-6", strokeWidth: 1.5 }), "Pause"] })) : (_jsxs(_Fragment, { children: [_jsx(Play, { className: "mr-3 h-6 w-6", strokeWidth: 1.5 }), "Start"] })) }), _jsxs("button", { onClick: resetSession, className: "px-8 py-4 bg-muted text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 flex items-center font-semibold text-lg", children: [_jsx(RotateCcw, { className: "mr-3 h-6 w-6", strokeWidth: 1.5 }), "Reset"] })] })] }) }), _jsxs("div", { className: "space-y-8", children: [_jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8", children: [_jsx("h3", { className: "text-2xl font-bold text-card-foreground mb-6", children: "Choose Technique" }), _jsx("div", { className: "space-y-4", children: isLoadingTechniques ? (_jsx("div", { className: "text-center py-12", children: _jsx("div", { className: "text-muted-foreground text-lg", children: "Loading techniques..." }) })) : (breathingTechniques.map((technique) => (_jsxs("button", { onClick: () => {
                                                    if (!isActive) {
                                                        setSelectedTechnique(technique);
                                                        resetSession();
                                                    }
                                                }, disabled: isActive, className: `w-full text-left p-6 rounded-lg border transition-all duration-300 ${selectedTechnique?.id === technique.id
                                                    ? 'border-primary bg-primary/5 shadow-lg'
                                                    : 'border-border hover:border-primary/50 hover:bg-accent'} ${isActive ? 'opacity-50 cursor-not-allowed' : ''}`, children: [_jsx("div", { className: "font-bold text-card-foreground text-lg", children: technique.name }), _jsxs("div", { className: "text-muted-foreground mt-2", children: [technique.inhale_count, "-", technique.hold_count, "-", technique.exhale_count, " \u2022 ", technique.cycles, " cycles"] })] }, technique.id)))) })] }), _jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8", children: [_jsxs("h3", { className: "text-2xl font-bold text-card-foreground mb-6 flex items-center", children: [_jsx("div", { className: "w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-3 border border-primary/20", children: _jsx(Heart, { className: "h-5 w-5 text-primary", strokeWidth: 1.5 }) }), "Benefits"] }), _jsx("ul", { className: "space-y-4", children: selectedTechnique ? selectedTechnique.benefits.map((benefit, index) => (_jsxs("li", { className: "flex items-center text-muted-foreground", children: [_jsx("div", { className: "w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center mr-4 border border-primary/20", children: _jsx(Zap, { className: "h-4 w-4 text-primary", strokeWidth: 1.5 }) }), _jsx("span", { className: "text-lg", children: benefit })] }, index))) : (_jsx("div", { className: "text-muted-foreground text-lg", children: "Select a technique to see benefits" })) })] }), _jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8", children: [_jsxs("h3", { className: "text-2xl font-bold text-card-foreground mb-6 flex items-center", children: [_jsx("div", { className: "w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-3 border border-primary/20", children: _jsx(Clock, { className: "h-5 w-5 text-primary", strokeWidth: 1.5 }) }), "Recent Sessions"] }), isLoadingSessions ? (_jsx("p", { className: "text-muted-foreground text-lg", children: "Loading sessions..." })) : sessions.length > 0 ? (_jsx("div", { className: "space-y-4", children: sessions.slice(0, 5).map((session) => (_jsxs("div", { className: "flex justify-between items-center py-4 border-b border-border last:border-b-0", children: [_jsxs("div", { children: [_jsx("div", { className: "font-bold text-card-foreground text-lg", children: breathingTechniques.find(t => t.id === session.technique)?.name || session.technique }), _jsxs("div", { className: "text-muted-foreground mt-1", children: [Math.floor(session.duration / 60), "m ", session.duration % 60, "s"] })] }), _jsx("div", { className: "text-muted-foreground", children: new Date(session.completed_at).toLocaleDateString() })] }, session.id))) })) : (_jsx("p", { className: "text-muted-foreground text-lg", children: "No sessions yet. Start your first breathing exercise!" }))] })] })] })] }) }));
}
