import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Heart, Flame, BookOpen, StickyNote, CheckCircle, Loader2, Star } from 'lucide-react';
// Icon mapping for dynamic icon selection
const iconMap = {
    Heart,
    Flame,
    BookOpen,
    StickyNote
};
export default function LogEntryPage() {
    const { user } = useAuth();
    const navigate = useNavigate();
    // TEMPORARY: Audit user fallback for comprehensive audit
    const auditUser = user || {
        id: 'audit-user-id',
        email: '<EMAIL>',
        user_metadata: { name: 'Audit User' }
    };
    const [activeTab, setActiveTab] = useState('wellness');
    const [loading, setLoading] = useState(false);
    // RULE 0001: Dynamic tab configuration from database - hardcoded array removed
    const [tabs, setTabs] = useState([]);
    const [tabsLoading, setTabsLoading] = useState(true);
    // Wellness states
    const [mood, setMood] = useState(3);
    const [energy, setEnergy] = useState(3);
    const [stress, setStress] = useState(3);
    const [sleepHours, setSleepHours] = useState('');
    const [sleepQuality, setSleepQuality] = useState(3);
    // Craving states
    const [cravingIntensity, setCravingIntensity] = useState(0);
    const [cravingTrigger, setCravingTrigger] = useState('');
    const [copingStrategy, setCopingStrategy] = useState('');
    // Journal and notes
    const [journalEntry, setJournalEntry] = useState('');
    const [dailyNotes, setDailyNotes] = useState('');
    useEffect(() => {
        fetchLogTabs();
    }, []);
    const fetchLogTabs = async () => {
        try {
            // Use fallback tabs for now to avoid database issues
            setTabs([
                { id: 'wellness', label: 'Wellness', icon_name: 'Heart', display_order: 1 },
                { id: 'cravings', label: 'Cravings', icon_name: 'Flame', display_order: 2 },
                { id: 'journal', label: 'Journal', icon_name: 'BookOpen', display_order: 3 },
                { id: 'notes', label: 'Notes', icon_name: 'StickyNote', display_order: 4 }
            ]);
        }
        catch (err) {
            console.error('Error setting up log tabs:', err);
            setTabs([]);
        }
        finally {
            setTabsLoading(false);
        }
    };
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!auditUser)
            return;
        setLoading(true);
        try {
            // Save daily health summary
            const { error: healthError } = await supabase
                .from('daily_health_summaries')
                .insert([{
                    user_id: auditUser.id,
                    date: new Date().toISOString().split('T')[0],
                    mood_score: mood,
                    energy_level: energy,
                    stress_level: stress,
                    sleep_hours: sleepHours ? parseFloat(sleepHours) : null,
                    sleep_quality: sleepQuality,
                    notes: dailyNotes
                }]);
            if (healthError)
                throw healthError;
            // Save craving log if there was a craving
            if (cravingIntensity > 0) {
                const { error: cravingError } = await supabase
                    .from('craving_logs')
                    .insert([{
                        user_id: auditUser.id,
                        intensity: cravingIntensity,
                        trigger: cravingTrigger,
                        coping_strategy: copingStrategy,
                        logged_at: new Date().toISOString()
                    }]);
                if (cravingError)
                    throw cravingError;
            }
            // Save journal entry if provided
            if (journalEntry.trim()) {
                const { error: journalError } = await supabase
                    .from('journal_entries')
                    .insert([{
                        user_id: auditUser.id,
                        content: journalEntry,
                        mood: mood,
                        created_at: new Date().toISOString()
                    }]);
                if (journalError)
                    throw journalError;
            }
            // Success - navigate back to dashboard
            navigate('/dashboard');
        }
        catch (error) {
            console.error('Error saving log entry:', error);
        }
        finally {
            setLoading(false);
        }
    };
    // TEMPORARY: Commented out for audit - original auth check
    // if (!user) {
    //   return (
    //     <div className="min-h-screen flex items-center justify-center bg-background">
    if (!auditUser) {
        return (_jsx("div", { className: "min-h-screen flex items-center justify-center bg-background", children: _jsxs("div", { className: "text-center max-w-md mx-auto px-6", children: [_jsx("h2", { className: "text-2xl font-bold text-foreground mb-3", children: "Please sign in to log your progress" }), _jsx("p", { className: "text-muted-foreground leading-relaxed", children: "Your daily log helps track your wellness journey." })] }) }));
    }
    // Add loading state for tabs
    if (tabsLoading) {
        return (_jsx("div", { className: "min-h-screen bg-background flex items-center justify-center", children: _jsxs("div", { className: "text-center", children: [_jsx(Loader2, { className: "w-16 h-16 animate-spin mx-auto mb-6 text-primary", strokeWidth: 1.5 }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Loading daily log..." })] }) }));
    }
    return (_jsx("div", { className: "min-h-screen bg-background py-12", children: _jsxs("div", { className: "max-w-5xl mx-auto px-6 lg:px-8", children: [_jsxs("div", { className: "text-center mb-16", children: [_jsx("h1", { className: "text-5xl font-bold text-foreground mb-6 tracking-tight", children: "Daily Log" }), _jsx("p", { className: "text-xl text-muted-foreground leading-relaxed", children: "Track your wellness journey and celebrate your progress" })] }), _jsxs("form", { onSubmit: handleSubmit, className: "bg-card rounded-xl shadow-lg border border-border", children: [_jsx("div", { className: "border-b border-border", children: _jsx("nav", { className: "flex space-x-12 px-8", children: tabs.map((tab) => {
                                    const IconComponent = iconMap[tab.icon_name] || Heart;
                                    return (_jsx("button", { type: "button", onClick: () => setActiveTab(tab.id), className: `py-6 px-2 relative font-semibold text-base transition-all duration-300 ${activeTab === tab.id
                                            ? 'text-primary border-b-2 border-primary'
                                            : 'text-muted-foreground hover:text-foreground'}`, children: _jsxs("div", { className: "flex items-center space-x-3", children: [_jsx(IconComponent, { className: "w-5 h-5", strokeWidth: 1.5 }), _jsx("span", { children: tab.label })] }) }, tab.id));
                                }) }) }), _jsxs("div", { className: "p-8 space-y-10", children: [activeTab === 'wellness' && (_jsxs("div", { className: "space-y-10", children: [_jsx("h3", { className: "text-2xl font-bold text-card-foreground", children: "How are you feeling today?" }), _jsxs("div", { children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground mb-6", children: "Mood" }), _jsx("div", { className: "flex space-x-6", children: [1, 2, 3, 4, 5].map((rating) => (_jsx("button", { type: "button", onClick: () => setMood(rating), className: `w-16 h-16 rounded-xl border-2 flex items-center justify-center transition-all duration-300 ${mood === rating
                                                            ? 'border-primary bg-primary/10 text-primary shadow-lg'
                                                            : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'}`, children: _jsx(Star, { className: `w-7 h-7 ${mood === rating ? 'fill-current' : ''}`, strokeWidth: 1.5 }) }, rating))) })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground mb-6", children: "Energy Level" }), _jsx("div", { className: "flex space-x-6", children: [1, 2, 3, 4, 5].map((rating) => (_jsx("button", { type: "button", onClick: () => setEnergy(rating), className: `w-16 h-16 rounded-xl border-2 flex items-center justify-center transition-all duration-300 ${energy === rating
                                                            ? 'border-primary bg-primary/10 text-primary shadow-lg'
                                                            : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'}`, children: _jsx(Star, { className: `w-7 h-7 ${energy === rating ? 'fill-current' : ''}`, strokeWidth: 1.5 }) }, rating))) })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground mb-6", children: "Stress Level" }), _jsx("div", { className: "flex space-x-6", children: [1, 2, 3, 4, 5].map((rating) => (_jsx("button", { type: "button", onClick: () => setStress(rating), className: `w-16 h-16 rounded-xl border-2 flex items-center justify-center transition-all duration-300 ${stress === rating
                                                            ? 'border-primary bg-primary/10 text-primary shadow-lg'
                                                            : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'}`, children: _jsx(Star, { className: `w-7 h-7 ${stress === rating ? 'fill-current' : ''}`, strokeWidth: 1.5 }) }, rating))) })] }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-10", children: [_jsxs("div", { children: [_jsx("label", { htmlFor: "sleepHours", className: "block text-base font-semibold text-card-foreground mb-4", children: "Hours of Sleep" }), _jsx("input", { type: "number", id: "sleepHours", min: "0", max: "24", step: "0.5", value: sleepHours, onChange: (e) => setSleepHours(e.target.value), className: "w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg", placeholder: "8" })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground mb-6", children: "Sleep Quality" }), _jsx("div", { className: "flex space-x-4", children: [1, 2, 3, 4, 5].map((rating) => (_jsx("button", { type: "button", onClick: () => setSleepQuality(rating), className: `w-14 h-14 rounded-xl border-2 flex items-center justify-center transition-all duration-300 ${sleepQuality === rating
                                                                    ? 'border-primary bg-primary/10 text-primary shadow-lg'
                                                                    : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'}`, children: _jsx(Star, { className: `w-6 h-6 ${sleepQuality === rating ? 'fill-current' : ''}`, strokeWidth: 1.5 }) }, rating))) })] })] })] })), activeTab === 'cravings' && (_jsxs("div", { className: "space-y-10", children: [_jsx("h3", { className: "text-2xl font-bold text-card-foreground", children: "Craving Management" }), _jsxs("div", { children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground mb-6", children: "Craving Intensity (0 = No craving, 10 = Intense craving)" }), _jsx("div", { className: "flex space-x-3", children: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((intensity) => (_jsx("button", { type: "button", onClick: () => setCravingIntensity(intensity), className: `w-12 h-12 rounded-lg border-2 flex items-center justify-center text-sm font-bold transition-all duration-300 ${cravingIntensity === intensity
                                                            ? 'border-primary bg-primary/10 text-primary shadow-lg'
                                                            : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'}`, children: intensity }, intensity))) })] }), cravingIntensity > 0 && (_jsxs(_Fragment, { children: [_jsxs("div", { children: [_jsx("label", { htmlFor: "trigger", className: "block text-base font-semibold text-card-foreground mb-4", children: "What triggered this craving?" }), _jsx("input", { type: "text", id: "trigger", value: cravingTrigger, onChange: (e) => setCravingTrigger(e.target.value), className: "w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg", placeholder: "E.g., stress, social situation, habit..." })] }), _jsxs("div", { children: [_jsx("label", { htmlFor: "coping", className: "block text-base font-semibold text-card-foreground mb-4", children: "How did you cope with it?" }), _jsx("input", { type: "text", id: "coping", value: copingStrategy, onChange: (e) => setCopingStrategy(e.target.value), className: "w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg", placeholder: "E.g., deep breathing, exercise, distraction..." })] })] }))] })), activeTab === 'journal' && (_jsxs("div", { className: "space-y-10", children: [_jsx("h3", { className: "text-2xl font-bold text-card-foreground", children: "Daily Reflection" }), _jsxs("div", { children: [_jsx("label", { htmlFor: "journal", className: "block text-base font-semibold text-card-foreground mb-4", children: "How was your day? What are you grateful for?" }), _jsx("textarea", { id: "journal", rows: 8, value: journalEntry, onChange: (e) => setJournalEntry(e.target.value), className: "w-full px-4 py-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg leading-relaxed resize-none", placeholder: "Write your thoughts, feelings, and reflections..." })] })] })), activeTab === 'notes' && (_jsxs("div", { className: "space-y-10", children: [_jsx("h3", { className: "text-2xl font-bold text-card-foreground", children: "Additional Notes" }), _jsxs("div", { children: [_jsx("label", { htmlFor: "notes", className: "block text-base font-semibold text-card-foreground mb-4", children: "Any other notes for today?" }), _jsx("textarea", { id: "notes", rows: 6, value: dailyNotes, onChange: (e) => setDailyNotes(e.target.value), className: "w-full px-4 py-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg leading-relaxed resize-none", placeholder: "Additional thoughts, reminders, or observations..." })] })] }))] }), _jsx("div", { className: "px-8 py-8 bg-muted/30 border-t border-border rounded-b-xl", children: _jsx("button", { type: "submit", disabled: loading, className: "w-full flex items-center justify-center px-6 py-4 border border-transparent text-lg font-semibold rounded-lg text-primary-foreground bg-primary hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl", children: loading ? (_jsxs(_Fragment, { children: [_jsx(Loader2, { className: "w-6 h-6 mr-3 animate-spin", strokeWidth: 1.5 }), "Saving..."] })) : (_jsxs(_Fragment, { children: [_jsx(CheckCircle, { className: "w-6 h-6 mr-3", strokeWidth: 1.5 }), "Save Daily Log"] })) }) })] })] }) }));
}
