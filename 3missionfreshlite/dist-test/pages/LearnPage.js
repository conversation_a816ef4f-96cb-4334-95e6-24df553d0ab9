import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { BookOpen, Video, FileText, Award, Clock, PlayCircle, CheckCircle } from 'lucide-react';
// RULE 0001: Dynamic categories from database - hardcoded array removed
export default function LearnPage() {
    const { user, loading } = useAuth();
    const navigate = useNavigate();
    const [content, setContent] = useState([]);
    const [userProgress, setUserProgress] = useState([]);
    const [categories, setCategories] = useState([]);
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [selectedContent, setSelectedContent] = useState(null);
    const [isLoadingContent, setIsLoadingContent] = useState(true);
    const [searchQuery, setSearchQuery] = useState('');
    useEffect(() => {
        if (!loading && !user) {
            navigate('/auth');
            return;
        }
        if (user) {
            loadContent();
            loadUserProgress();
            loadCategories();
        }
    }, [user, loading, navigate]);
    const loadCategories = async () => {
        try {
            // RULE 0001: Load categories from database or extract from content
            const { data: categoryData, error: categoryError } = await supabase
                .from('learning_categories')
                .select('name')
                .order('display_order');
            if (categoryError || !categoryData?.length) {
                // Fallback: extract unique categories from content
                const uniqueCategories = [...new Set(content.map(item => item.category).filter(Boolean))];
                setCategories(uniqueCategories);
            }
            else {
                setCategories(categoryData.map(cat => cat.name));
            }
        }
        catch (error) {
            console.error('Error loading categories:', error);
            // Fallback: extract unique categories from content
            const uniqueCategories = [...new Set(content.map(item => item.category).filter(Boolean))];
            setCategories(uniqueCategories);
        }
    };
    const loadContent = async () => {
        try {
            setIsLoadingContent(true);
            // HOLY RULE 0001: Load real learning content from Supabase
            const { data: contentData, error: contentError } = await supabase
                .from('learning_content')
                .select('*')
                .order('created_at', { ascending: false });
            if (contentError) {
                console.error('Error loading learning content:', contentError);
                // Fallback to empty array if no table exists yet
                setContent([]);
            }
            else {
                setContent(contentData || []);
            }
        }
        catch (error) {
            console.error('Error loading learning content:', error);
            setContent([]);
        }
        finally {
            setIsLoadingContent(false);
        }
    };
    const loadUserProgress = async () => {
        if (!user)
            return;
        try {
            const { data, error } = await supabase
                .from('user_learning_progress')
                .select('*')
                .eq('user_id', user.id);
            if (error)
                throw error;
            setUserProgress(data || []);
        }
        catch (error) {
            console.error('Error loading user progress:', error);
        }
    };
    const markAsCompleted = async (contentId) => {
        if (!user)
            return;
        try {
            const { data, error } = await supabase
                .from('user_learning_progress')
                .upsert({
                user_id: user.id,
                content_id: contentId,
                completed: true,
                progress_percentage: 100,
                completed_at: new Date().toISOString()
            })
                .select()
                .single();
            if (error)
                throw error;
            loadUserProgress();
        }
        catch (error) {
            console.error('Error marking content as completed:', error);
        }
    };
    const getFilteredContent = () => {
        let filtered = content;
        if (selectedCategory !== 'all') {
            filtered = filtered.filter(item => item.category === selectedCategory);
        }
        if (searchQuery) {
            filtered = filtered.filter(item => item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())));
        }
        return filtered;
    };
    const getProgressForContent = (contentId) => {
        return userProgress.find(p => p.content_id === contentId);
    };
    const getContentTypeIcon = (type) => {
        switch (type) {
            case 'video': return _jsx(Video, { className: "h-5 w-5" });
            case 'course': return _jsx(BookOpen, { className: "h-5 w-5" });
            case 'guide': return _jsx(FileText, { className: "h-5 w-5" });
            default: return _jsx(FileText, { className: "h-5 w-5" });
        }
    };
    const getDifficultyColor = (difficulty) => {
        switch (difficulty) {
            case 'beginner': return 'bg-primary/10 text-primary border border-primary/20';
            case 'intermediate': return 'bg-muted text-muted-foreground border border-border';
            case 'advanced': return 'bg-accent text-accent-foreground border border-border';
            default: return 'bg-muted text-muted-foreground border border-border';
        }
    };
    if (loading) {
        return (_jsx("div", { className: "min-h-screen bg-background flex items-center justify-center", children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto" }), _jsx("p", { className: "mt-6 text-muted-foreground text-lg", children: "Loading learning content..." })] }) }));
    }
    return (_jsx("div", { className: "min-h-screen bg-background", children: _jsxs("div", { className: "max-w-7xl mx-auto px-6 lg:px-8 py-12", children: [_jsxs("div", { className: "mb-16", children: [_jsxs("h1", { className: "text-5xl font-bold text-foreground flex items-center mb-6 tracking-tight", children: [_jsx("div", { className: "w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mr-6 border border-primary/20", children: _jsx(BookOpen, { className: "h-8 w-8 text-primary", strokeWidth: 1.5 }) }), "Learning Center"] }), _jsx("p", { className: "text-xl text-muted-foreground leading-relaxed max-w-3xl", children: "Expand your knowledge with expert-curated content about quitting smoking and wellness." })] }), selectedContent ? (
                /* Content Viewer */
                _jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border", children: [_jsxs("div", { className: "px-8 py-6 border-b border-border flex items-center justify-between", children: [_jsxs("div", { children: [_jsx("h2", { className: "text-3xl font-bold text-card-foreground", children: selectedContent.title }), _jsxs("div", { className: "flex items-center space-x-6 mt-4", children: [_jsxs("span", { className: "flex items-center text-muted-foreground", children: [getContentTypeIcon(selectedContent.content_type), _jsx("span", { className: "ml-2 capitalize font-medium", children: selectedContent.content_type })] }), _jsxs("span", { className: "flex items-center text-muted-foreground", children: [_jsx(Clock, { className: "h-5 w-5 mr-2", strokeWidth: 1.5 }), selectedContent.duration_minutes, " min"] }), _jsx("span", { className: `px-3 py-1 text-sm font-semibold rounded-lg ${getDifficultyColor(selectedContent.difficulty)}`, children: selectedContent.difficulty })] })] }), _jsxs("div", { className: "flex space-x-4", children: [!getProgressForContent(selectedContent.id)?.completed && (_jsxs("button", { onClick: () => markAsCompleted(selectedContent.id), className: "bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary-hover transition-all duration-300 flex items-center font-semibold shadow-lg hover:shadow-xl", children: [_jsx(CheckCircle, { className: "mr-3 h-5 w-5", strokeWidth: 1.5 }), "Mark Complete"] })), _jsx("button", { onClick: () => setSelectedContent(null), className: "bg-muted text-muted-foreground px-6 py-3 rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold", children: "Back to Library" })] })] }), _jsx("div", { className: "p-8", children: _jsx("div", { className: "prose max-w-none", children: selectedContent.content_text?.split('\n').map((line, index) => {
                                    if (line.startsWith('# ')) {
                                        return _jsx("h1", { className: "text-4xl font-bold mt-12 mb-6 text-card-foreground", children: line.slice(2) }, index);
                                    }
                                    if (line.startsWith('## ')) {
                                        return _jsx("h2", { className: "text-3xl font-bold mt-10 mb-5 text-card-foreground", children: line.slice(3) }, index);
                                    }
                                    if (line.startsWith('### ')) {
                                        return _jsx("h3", { className: "text-2xl font-bold mt-8 mb-4 text-card-foreground", children: line.slice(4) }, index);
                                    }
                                    if (line.startsWith('- ')) {
                                        return _jsx("li", { className: "ml-6 mb-2 text-muted-foreground text-lg leading-relaxed", children: line.slice(2) }, index);
                                    }
                                    if (line.match(/^\d+\./)) {
                                        return _jsx("li", { className: "ml-6 mb-2 text-muted-foreground text-lg leading-relaxed", children: line }, index);
                                    }
                                    if (line.trim() === '') {
                                        return _jsx("br", {}, index);
                                    }
                                    return _jsx("p", { className: "mb-6 text-muted-foreground text-lg leading-relaxed", children: line }, index);
                                }) }) })] })) : (
                /* Content Library */
                _jsxs(_Fragment, { children: [_jsx("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 mb-12", children: _jsxs("div", { className: "flex flex-col md:flex-row md:items-center md:justify-between space-y-6 md:space-y-0", children: [_jsx("div", { className: "flex-1 max-w-lg", children: _jsx("input", { type: "text", placeholder: "Search articles, videos, and courses...", value: searchQuery, onChange: (e) => setSearchQuery(e.target.value), className: "w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg" }) }), _jsx("div", { className: "flex space-x-6", children: _jsxs("select", { value: selectedCategory, onChange: (e) => setSelectedCategory(e.target.value), className: "px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg font-medium", children: [_jsx("option", { value: "all", children: "All Categories" }), categories.map((category) => (_jsx("option", { value: category, children: category }, category)))] }) })] }) }), content.filter(item => item.is_featured).length > 0 && (_jsxs("div", { className: "mb-16", children: [_jsxs("h2", { className: "text-3xl font-bold text-foreground mb-8 flex items-center", children: [_jsx("div", { className: "w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 border border-primary/20", children: _jsx(Award, { className: "h-6 w-6 text-primary", strokeWidth: 1.5 }) }), "Featured Content"] }), _jsx("div", { className: "grid gap-8 md:grid-cols-2 lg:grid-cols-3", children: content.filter(item => item.is_featured).map((item) => (_jsx("div", { className: "bg-card rounded-xl shadow-lg border border-border overflow-hidden hover:shadow-xl transition-all duration-300", children: _jsxs("div", { className: "p-8", children: [_jsxs("div", { className: "flex items-start justify-between mb-6", children: [_jsxs("div", { className: "flex items-center space-x-3", children: [getContentTypeIcon(item.content_type), _jsx("span", { className: "text-muted-foreground capitalize font-medium", children: item.content_type })] }), getProgressForContent(item.id)?.completed && (_jsx(CheckCircle, { className: "h-6 w-6 text-primary", strokeWidth: 1.5 }))] }), _jsx("h3", { className: "text-xl font-bold text-card-foreground mb-4", children: item.title }), _jsx("p", { className: "text-muted-foreground mb-6 leading-relaxed", children: item.description }), _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex items-center space-x-4", children: [_jsxs("span", { className: "flex items-center text-muted-foreground", children: [_jsx(Clock, { className: "h-4 w-4 mr-2", strokeWidth: 1.5 }), item.duration_minutes, " min"] }), _jsx("span", { className: `px-3 py-1 text-sm font-semibold rounded-lg ${getDifficultyColor(item.difficulty)}`, children: item.difficulty })] }), _jsxs("button", { onClick: () => setSelectedContent(item), className: "bg-primary text-primary-foreground px-5 py-2 rounded-lg hover:bg-primary-hover transition-all duration-300 flex items-center font-semibold shadow-lg hover:shadow-xl", children: [_jsx(PlayCircle, { className: "mr-2 h-5 w-5", strokeWidth: 1.5 }), "Start"] })] })] }) }, item.id))) })] })), _jsxs("div", { children: [_jsx("h2", { className: "text-3xl font-bold text-foreground mb-8", children: "All Content" }), isLoadingContent ? (_jsxs("div", { className: "text-center py-16", children: [_jsx("div", { className: "animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto" }), _jsx("p", { className: "mt-6 text-muted-foreground text-lg", children: "Loading content..." })] })) : getFilteredContent().length > 0 ? (_jsx("div", { className: "grid gap-8 md:grid-cols-2 lg:grid-cols-3", children: getFilteredContent().map((item) => (_jsx("div", { className: "bg-card rounded-xl shadow-lg border border-border overflow-hidden hover:shadow-xl transition-all duration-300", children: _jsxs("div", { className: "p-8", children: [_jsxs("div", { className: "flex items-start justify-between mb-6", children: [_jsxs("div", { className: "flex items-center space-x-3", children: [getContentTypeIcon(item.content_type), _jsx("span", { className: "text-muted-foreground capitalize font-medium", children: item.content_type })] }), getProgressForContent(item.id)?.completed && (_jsx(CheckCircle, { className: "h-6 w-6 text-primary", strokeWidth: 1.5 }))] }), _jsx("h3", { className: "text-xl font-bold text-card-foreground mb-4", children: item.title }), _jsx("p", { className: "text-muted-foreground mb-4 leading-relaxed", children: item.description }), _jsx("div", { className: "mb-6", children: _jsx("span", { className: "text-muted-foreground font-medium", children: item.category }) }), _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex items-center space-x-4", children: [_jsxs("span", { className: "flex items-center text-muted-foreground", children: [_jsx(Clock, { className: "h-4 w-4 mr-2", strokeWidth: 1.5 }), item.duration_minutes, " min"] }), _jsx("span", { className: `px-3 py-1 text-sm font-semibold rounded-lg ${getDifficultyColor(item.difficulty)}`, children: item.difficulty })] }), _jsxs("button", { onClick: () => setSelectedContent(item), className: "bg-primary text-primary-foreground px-5 py-2 rounded-lg hover:bg-primary-hover transition-all duration-300 flex items-center font-semibold shadow-lg hover:shadow-xl", children: [_jsx(PlayCircle, { className: "mr-2 h-5 w-5", strokeWidth: 1.5 }), getProgressForContent(item.id)?.completed ? 'Review' : 'Start'] })] })] }) }, item.id))) })) : (_jsxs("div", { className: "text-center py-20 bg-card rounded-xl shadow-lg border border-border", children: [_jsx("div", { className: "w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20", children: _jsx(BookOpen, { className: "w-10 h-10 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "text-2xl font-bold text-card-foreground mb-4", children: "No content found" }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Try adjusting your search or category filter." })] }))] })] }))] }) }));
}
