import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Phone, Mail, MessageCircle, Book, Users, Clock, ExternalLink, Search, Filter, Star, Loader2 } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
export default function SupportPage() {
    const { user } = useAuth();
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [activeTab, setActiveTab] = useState('help');
    const [supportArticles, setSupportArticles] = useState([]);
    const [faqs, setFaqs] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    // HOLY RULE 0001: Load real support data from Supabase
    useEffect(() => {
        loadSupportData();
    }, []);
    const loadSupportData = async () => {
        try {
            setLoading(true);
            // Load support articles from database
            const { data: articlesData, error: articlesError } = await supabase
                .from('support_articles')
                .select('*')
                .order('helpful_count', { ascending: false });
            if (articlesError) {
                console.error('Error loading support articles:', articlesError);
                // Fallback to empty array if no table exists yet
                setSupportArticles([]);
            }
            else {
                setSupportArticles(articlesData || []);
            }
            // Load FAQs from database
            const { data: faqsData, error: faqsError } = await supabase
                .from('support_faqs')
                .select('*')
                .order('helpful_count', { ascending: false });
            if (faqsError) {
                console.error('Error loading FAQs:', faqsError);
                // Fallback to empty array if no table exists yet
                setFaqs([]);
            }
            else {
                setFaqs(faqsData || []);
            }
        }
        catch (error) {
            console.error('Error loading support data:', error);
            setError('Failed to load support content');
        }
        finally {
            setLoading(false);
        }
    };
    const categories = ['all', 'Getting Started', 'Health', 'App Usage', 'Goals', 'Account', 'Data', 'Features', 'Privacy', 'Billing'];
    const filteredArticles = supportArticles.filter(article => {
        const matchesSearch = article.title.toLowerCase().includes(searchQuery.toLowerCase());
        const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory;
        return matchesSearch && matchesCategory;
    });
    const filteredFAQs = faqs.filter(faq => {
        const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
            faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
        const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
        return matchesSearch && matchesCategory;
    });
    if (loading) {
        return (_jsx("div", { className: "min-h-screen bg-background flex items-center justify-center", children: _jsxs("div", { className: "text-center", children: [_jsx(Loader2, { className: "w-16 h-16 animate-spin text-primary mx-auto mb-6", strokeWidth: 1.5 }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Loading support content..." })] }) }));
    }
    if (error) {
        return (_jsx("div", { className: "min-h-screen bg-background flex items-center justify-center", children: _jsxs("div", { className: "text-center max-w-md mx-auto px-6", children: [_jsx("p", { className: "text-destructive mb-6 text-lg", children: error }), _jsx("button", { onClick: loadSupportData, className: "px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover transition-all duration-300 font-semibold shadow-lg hover:shadow-xl", children: "Try Again" })] }) }));
    }
    return (_jsx("div", { className: "min-h-screen bg-background py-12", children: _jsxs("div", { className: "max-w-7xl mx-auto px-6 lg:px-8", children: [_jsx("div", { className: "mb-16", children: _jsxs("div", { className: "flex items-center gap-6 mb-6", children: [_jsx("div", { className: "w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20", children: _jsx(Book, { className: "w-8 h-8 text-primary", strokeWidth: 1.5 }) }), _jsxs("div", { children: [_jsx("h1", { className: "text-5xl font-bold text-foreground tracking-tight", children: "Support Center" }), _jsx("p", { className: "text-xl text-muted-foreground leading-relaxed mt-2", children: "Get help and find answers to your questions" })] })] }) }), _jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-8 mb-16", children: [_jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300", children: [_jsxs("div", { className: "flex items-center gap-4 mb-6", children: [_jsx("div", { className: "w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center border border-primary/20", children: _jsx(MessageCircle, { className: "w-6 h-6 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "font-bold text-card-foreground text-lg", children: "Live Chat" })] }), _jsx("p", { className: "text-muted-foreground mb-6 leading-relaxed", children: "Get instant help from our support team" }), _jsx("button", { className: "w-full px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover transition-all duration-300 font-semibold shadow-lg hover:shadow-xl", children: "Start Chat" }), _jsx("p", { className: "text-muted-foreground mt-4 text-center", children: "Available 24/7" })] }), _jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300", children: [_jsxs("div", { className: "flex items-center gap-4 mb-6", children: [_jsx("div", { className: "w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center border border-primary/20", children: _jsx(Mail, { className: "w-6 h-6 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "font-bold text-card-foreground text-lg", children: "Email Support" })] }), _jsx("p", { className: "text-muted-foreground mb-6 leading-relaxed", children: "Send us a detailed message" }), _jsx("button", { className: "w-full px-6 py-3 border border-border text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold", children: "Send Email" }), _jsx("p", { className: "text-muted-foreground mt-4 text-center", children: "Response within 24 hours" })] }), _jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300", children: [_jsxs("div", { className: "flex items-center gap-4 mb-6", children: [_jsx("div", { className: "w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center border border-primary/20", children: _jsx(Phone, { className: "w-6 h-6 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "font-bold text-card-foreground text-lg", children: "Phone Support" })] }), _jsx("p", { className: "text-muted-foreground mb-6 leading-relaxed", children: "Speak directly with our team" }), _jsx("button", { className: "w-full px-6 py-3 border border-border text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold", children: "Call Now" }), _jsx("p", { className: "text-muted-foreground mt-4 text-center", children: "Mon-Fri, 9AM-6PM EST" })] })] }), _jsx("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 mb-12", children: _jsxs("div", { className: "flex flex-col md:flex-row gap-6", children: [_jsxs("div", { className: "flex-1 relative", children: [_jsx(Search, { className: "w-5 h-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground", strokeWidth: 1.5 }), _jsx("input", { type: "text", placeholder: "Search help articles and FAQs...", value: searchQuery, onChange: (e) => setSearchQuery(e.target.value), className: "w-full pl-12 pr-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg" })] }), _jsxs("div", { className: "flex items-center gap-3", children: [_jsx(Filter, { className: "w-5 h-5 text-muted-foreground", strokeWidth: 1.5 }), _jsx("select", { value: selectedCategory, onChange: (e) => setSelectedCategory(e.target.value), className: "px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg font-medium", children: categories.map(category => (_jsx("option", { value: category, children: category === 'all' ? 'All Categories' : category }, category))) })] })] }) }), _jsx("div", { className: "border-b border-border mb-12", children: _jsxs("nav", { className: "flex space-x-12", children: [_jsx("button", { onClick: () => setActiveTab('help'), className: `py-4 px-2 border-b-2 font-semibold text-lg transition-all duration-300 ${activeTab === 'help'
                                    ? 'border-primary text-primary'
                                    : 'border-transparent text-muted-foreground hover:text-foreground'}`, children: "Help Articles" }), _jsx("button", { onClick: () => setActiveTab('faq'), className: `py-4 px-2 border-b-2 font-semibold text-lg transition-all duration-300 ${activeTab === 'faq'
                                    ? 'border-primary text-primary'
                                    : 'border-transparent text-muted-foreground hover:text-foreground'}`, children: "FAQ" })] }) }), activeTab === 'help' && (_jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8", children: filteredArticles.map((article) => (_jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300", children: [_jsxs("div", { className: "flex items-start justify-between mb-6", children: [_jsx("span", { className: "inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-primary/10 text-primary border border-primary/20", children: article.category }), _jsxs("div", { className: "flex items-center gap-2 text-muted-foreground", children: [_jsx(Star, { className: "w-5 h-5 text-accent fill-current", strokeWidth: 1.5 }), _jsx("span", { className: "font-medium", children: article.helpful })] })] }), _jsx("h3", { className: "font-bold text-card-foreground mb-4 text-lg leading-tight", children: article.title }), _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex items-center gap-2 text-muted-foreground", children: [_jsx(Clock, { className: "w-5 h-5", strokeWidth: 1.5 }), _jsx("span", { className: "font-medium", children: article.readTime })] }), _jsxs("button", { className: "text-primary hover:text-primary-hover font-semibold flex items-center gap-2 transition-all duration-300", children: ["Read", _jsx(ExternalLink, { className: "w-4 h-4", strokeWidth: 1.5 })] })] })] }, article.id))) })), activeTab === 'faq' && (_jsx("div", { className: "space-y-6", children: filteredFAQs.map((faq) => (_jsx("div", { className: "bg-card rounded-xl shadow-lg border border-border", children: _jsxs("div", { className: "p-8", children: [_jsxs("div", { className: "flex items-start justify-between mb-6", children: [_jsx("h3", { className: "font-bold text-card-foreground pr-6 text-lg leading-tight", children: faq.question }), _jsx("span", { className: "inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-accent/10 text-accent border border-accent/20 flex-shrink-0", children: faq.category })] }), _jsx("p", { className: "text-muted-foreground leading-relaxed text-lg", children: faq.answer })] }) }, faq.id))) })), ((activeTab === 'help' && filteredArticles.length === 0) || (activeTab === 'faq' && filteredFAQs.length === 0)) && (_jsxs("div", { className: "text-center py-16", children: [_jsx(Search, { className: "w-16 h-16 text-muted-foreground mx-auto mb-6", strokeWidth: 1.5 }), _jsx("h3", { className: "text-2xl font-bold text-foreground mb-4", children: "No results found" }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Try adjusting your search terms or category filter." })] })), _jsxs("div", { className: "bg-primary/5 rounded-xl border border-primary/20 p-8 mt-12", children: [_jsxs("div", { className: "flex items-center gap-4 mb-6", children: [_jsx(Users, { className: "w-6 h-6 text-primary", strokeWidth: 1.5 }), _jsx("h3", { className: "font-bold text-primary text-xl", children: "Still need help?" })] }), _jsx("p", { className: "text-primary/80 mb-8 text-lg leading-relaxed", children: "Our support team is here to help you succeed in your quit journey. Don't hesitate to reach out!" }), _jsxs("div", { className: "flex flex-col sm:flex-row gap-4", children: [_jsxs("button", { className: "flex items-center justify-center gap-3 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover transition-all duration-300 font-semibold shadow-lg hover:shadow-xl", children: [_jsx(MessageCircle, { className: "w-5 h-5", strokeWidth: 1.5 }), "Start Live Chat"] }), _jsxs("button", { className: "flex items-center justify-center gap-3 px-6 py-3 border border-primary text-primary rounded-lg hover:bg-primary hover:text-primary-foreground transition-all duration-300 font-semibold", children: [_jsx(Mail, { className: "w-5 h-5", strokeWidth: 1.5 }), "Send Email"] })] })] })] }) }));
}
