import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Calculator, ArrowRight, Heart, DollarSign, Calendar, TrendingUp, Clock, Zap, CheckCircle, AlertCircle } from 'lucide-react';
import { supabase } from '../lib/supabase';
export default function CalculatorsPage() {
    const [activeCalculator, setActiveCalculator] = useState('savings');
    const [inputs, setInputs] = useState({
        cigarettesPerDay: 20,
        pricePerPack: 12,
        cigarettesPerPack: 20,
        quitDate: '',
        dailyNicotine: 20,
        currentAge: 30,
        smokingYears: 10
    });
    // RULE 0001: Real Supabase database integration for calculators
    const [calculators, setCalculators] = useState([]);
    const [healthMilestones, setHealthMilestones] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    useEffect(() => {
        fetchCalculators();
        fetchHealthMilestones();
    }, []);
    const fetchCalculators = async () => {
        try {
            const { data, error } = await supabase
                .from('calculators')
                .select('*')
                .order('display_order');
            if (error) {
                // Table doesn't exist yet, use fallback calculators
                setCalculators([]);
                return;
            }
            setCalculators(data || []);
        }
        catch (err) {
            // Gracefully handle missing table
            setCalculators([]);
        }
    };
    const fetchHealthMilestones = async () => {
        try {
            const { data, error } = await supabase
                .from('health_milestones')
                .select('*')
                .order('days_milestone');
            if (error) {
                // Table doesn't exist yet, use fallback milestones
                setHealthMilestones([
                    { id: 1, days_milestone: 1, title: '20 min', description: 'Heart rate normalizes' },
                    { id: 2, days_milestone: 7, title: '1 week', description: 'Taste improves' },
                    { id: 3, days_milestone: 30, title: '1 month', description: 'Lung function increases' },
                    { id: 4, days_milestone: 365, title: '1 year', description: 'Heart disease risk halved' }
                ]);
                setLoading(false);
                return;
            }
            setHealthMilestones(data || []);
            setLoading(false);
        }
        catch (err) {
            // Gracefully handle missing table with fallback data
            setHealthMilestones([
                { id: 1, days_milestone: 1, title: '20 min', description: 'Heart rate normalizes' },
                { id: 2, days_milestone: 7, title: '1 week', description: 'Taste improves' },
                { id: 3, days_milestone: 30, title: '1 month', description: 'Lung function increases' },
                { id: 4, days_milestone: 365, title: '1 year', description: 'Heart disease risk halved' }
            ]);
            setLoading(false);
        }
    };
    // RULE 0001: All hardcoded calculators array COMPLETELY REMOVED - using only real database data
    const handleInputChange = (field, value) => {
        setInputs(prev => ({ ...prev, [field]: value }));
    };
    const calculateDailyCost = () => {
        const packsPerDay = inputs.cigarettesPerDay / inputs.cigarettesPerPack;
        return packsPerDay * inputs.pricePerPack;
    };
    const calculateSavings = () => {
        if (!inputs.quitDate)
            return { days: 0, savings: 0 };
        const quitDate = new Date(inputs.quitDate);
        const today = new Date();
        const diffTime = Math.abs(today.getTime() - quitDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        const dailyCost = calculateDailyCost();
        const totalSavings = dailyCost * diffDays;
        return { days: diffDays, savings: totalSavings };
    };
    const getHealthMilestones = () => {
        const savings = calculateSavings();
        const days = savings.days;
        // RULE 0001: All hardcoded milestones array COMPLETELY REMOVED - using only real database data
        return healthMilestones.map(milestone => ({
            ...milestone,
            achieved: days >= milestone.days_milestone
        }));
    };
    const calculateTimeRegained = () => {
        const savings = calculateSavings();
        const minutesPerCigarette = 5; // Average time to smoke one cigarette
        const totalMinutes = inputs.cigarettesPerDay * minutesPerCigarette * savings.days;
        return {
            minutes: totalMinutes,
            hours: Math.floor(totalMinutes / 60),
            days: Math.floor(totalMinutes / (60 * 24))
        };
    };
    return (_jsxs("div", { className: "min-h-screen bg-background", children: [_jsx("section", { className: "bg-background py-24", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6 lg:px-8 text-center", children: [_jsx("div", { className: "w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg", children: _jsx(Calculator, { className: "w-10 h-10 text-primary-foreground", strokeWidth: 2 }) }), _jsx("h1", { className: "text-6xl font-bold text-foreground mb-8 tracking-tight", children: "Wellness Calculator" }), _jsx("p", { className: "text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto", children: "Track your progress, calculate health improvements, and visualize your journey to better wellness" })] }) }), _jsx("section", { className: "bg-card border-b border-border py-16", children: _jsx("div", { className: "max-w-7xl mx-auto px-6 lg:px-8", children: loading ? (_jsx("div", { className: "grid md:grid-cols-2 lg:grid-cols-4 gap-6", children: [...Array(4)].map((_, i) => (_jsxs("div", { className: "p-6 rounded-xl border border-border bg-card", children: [_jsxs("div", { className: "flex items-center space-x-4 mb-4", children: [_jsx("div", { className: "w-8 h-8 bg-muted rounded animate-pulse" }), _jsx("div", { className: "h-6 bg-muted rounded animate-pulse w-32" })] }), _jsx("div", { className: "h-4 bg-muted rounded animate-pulse w-40" })] }, i))) })) : error ? (_jsxs("div", { className: "bg-card p-12 rounded-2xl shadow-lg border border-border text-center", children: [_jsx(AlertCircle, { className: "w-16 h-16 text-destructive mx-auto mb-6", strokeWidth: 1.5 }), _jsx("h3", { className: "text-2xl font-bold text-card-foreground mb-4", children: "Error loading calculators" }), _jsx("p", { className: "text-muted-foreground text-lg", children: error })] })) : (_jsx("div", { className: "grid md:grid-cols-2 lg:grid-cols-4 gap-4", children: calculators.map((calc) => {
                            const getIconComponent = (iconName) => {
                                switch (iconName) {
                                    case 'DollarSign': return DollarSign;
                                    case 'Heart': return Heart;
                                    case 'TrendingUp': return TrendingUp;
                                    case 'Clock': return Clock;
                                    default: return Calculator;
                                }
                            };
                            const IconComponent = getIconComponent(calc.icon_name || 'Calculator');
                            return (_jsxs("button", { onClick: () => setActiveCalculator(calc.id), className: `p-6 rounded-xl border-2 transition-all text-left shadow-lg hover:shadow-xl ${activeCalculator === calc.id
                                    ? 'border-primary bg-accent'
                                    : 'border-border hover:border-primary bg-card'}`, children: [_jsxs("div", { className: "flex items-center space-x-3 mb-2", children: [_jsx(IconComponent, { className: `w-8 h-8 ${activeCalculator === calc.id ? 'text-primary' : 'text-muted-foreground'}`, strokeWidth: 1.5 }), _jsx("h3", { className: `font-bold text-lg ${activeCalculator === calc.id ? 'text-primary' : 'text-card-foreground'}`, children: calc.name })] }), _jsx("p", { className: "text-muted-foreground leading-relaxed", children: calc.description })] }, calc.id));
                        }) })) }) }), _jsx("section", { className: "py-8 bg-muted", children: _jsx("div", { className: "max-w-4xl mx-auto px-6", children: _jsxs("div", { className: "bg-card p-6 rounded-xl shadow-sm border border-border", children: [_jsx("h2", { className: "text-xl font-semibold text-card-foreground mb-4", children: "Your Information" }), _jsxs("div", { className: "grid md:grid-cols-2 lg:grid-cols-3 gap-4", children: [_jsxs("div", { children: [_jsx("label", { className: "block text-sm font-medium text-foreground mb-2", children: "Cigarettes per day" }), _jsx("input", { type: "number", value: inputs.cigarettesPerDay, onChange: (e) => handleInputChange('cigarettesPerDay', parseInt(e.target.value) || 0), className: "w-full px-3 py-2 border border-border rounded-lg bg-input text-foreground focus:ring-2 focus:ring-ring focus:border-ring" })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-sm font-medium text-foreground mb-2", children: "Price per pack ($)" }), _jsx("input", { type: "number", step: "0.01", value: inputs.pricePerPack, onChange: (e) => handleInputChange('pricePerPack', parseFloat(e.target.value) || 0), className: "w-full px-3 py-2 border border-border rounded-lg bg-input text-foreground focus:ring-2 focus:ring-ring focus:border-ring" })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-sm font-medium text-foreground mb-2", children: "Cigarettes per pack" }), _jsx("input", { type: "number", value: inputs.cigarettesPerPack, onChange: (e) => handleInputChange('cigarettesPerPack', parseInt(e.target.value) || 0), className: "w-full px-3 py-2 border border-border rounded-lg bg-input text-foreground focus:ring-2 focus:ring-ring focus:border-ring" })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-sm font-medium text-foreground mb-2", children: "Quit date" }), _jsx("input", { type: "date", value: inputs.quitDate, onChange: (e) => handleInputChange('quitDate', e.target.value), className: "w-full px-3 py-2 border border-border rounded-lg bg-input text-foreground focus:ring-2 focus:ring-ring focus:border-ring" })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-sm font-medium text-foreground mb-2", children: "Current age" }), _jsx("input", { type: "number", value: inputs.currentAge, onChange: (e) => handleInputChange('currentAge', parseInt(e.target.value) || 0), className: "w-full px-3 py-2 border border-border rounded-lg bg-input text-foreground focus:ring-2 focus:ring-ring focus:border-ring" })] }), _jsxs("div", { children: [_jsx("label", { className: "block text-sm font-medium text-foreground mb-2", children: "Years smoking" }), _jsx("input", { type: "number", value: inputs.smokingYears, onChange: (e) => handleInputChange('smokingYears', parseInt(e.target.value) || 0), className: "w-full px-3 py-2 border border-border rounded-lg bg-input text-foreground focus:ring-2 focus:ring-ring focus:border-ring" })] })] })] }) }) }), _jsx("section", { className: "py-12", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6", children: [activeCalculator === 'savings' && (_jsxs("div", { className: "bg-card p-8 rounded-xl shadow-sm border border-border", children: [_jsxs("h2", { className: "text-2xl font-bold text-card-foreground mb-6 flex items-center", children: [_jsx(DollarSign, { className: "w-6 h-6 text-primary mr-2" }), "Money Saved Calculator"] }), inputs.quitDate ? (_jsxs("div", { className: "grid md:grid-cols-3 gap-6", children: [_jsxs("div", { className: "text-center p-6 bg-primary-subtle rounded-lg border border-border", children: [_jsxs("div", { className: "text-3xl font-bold text-primary", children: ["$", calculateSavings().savings.toFixed(2)] }), _jsx("div", { className: "text-muted-foreground", children: "Total Saved" })] }), _jsxs("div", { className: "text-center p-6 bg-primary-subtle rounded-lg border border-border", children: [_jsx("div", { className: "text-3xl font-bold text-primary", children: calculateSavings().days }), _jsx("div", { className: "text-muted-foreground", children: "Days Smoke-Free" })] }), _jsxs("div", { className: "text-center p-6 bg-primary-subtle rounded-lg border border-border", children: [_jsxs("div", { className: "text-3xl font-bold text-primary", children: ["$", calculateDailyCost().toFixed(2)] }), _jsx("div", { className: "text-muted-foreground", children: "Daily Savings" })] })] })) : (_jsxs("div", { className: "text-center py-8", children: [_jsx(Calendar, { className: "w-16 h-16 text-muted-foreground mx-auto mb-4" }), _jsx("p", { className: "text-muted-foreground", children: "Please set your quit date to see your savings." })] }))] })), activeCalculator === 'health' && (_jsxs("div", { className: "bg-white p-8 rounded-xl shadow-sm border border-gray-100", children: [_jsxs("h2", { className: "text-2xl font-bold text-gray-900 mb-6 flex items-center", children: [_jsx(Heart, { className: "w-6 h-6 text-red-500 mr-2" }), "Health Timeline"] }), _jsx("div", { className: "space-y-4", children: getHealthMilestones().map((milestone, index) => (_jsxs("div", { className: `flex items-center p-4 rounded-lg ${milestone.achieved ? 'bg-primary-subtle border border-primary-border' : 'bg-gray-50 border border-gray-200'}`, children: [_jsx("div", { className: `w-8 h-8 rounded-full flex items-center justify-center mr-4 ${milestone.achieved ? 'bg-primary' : 'bg-gray-300'}`, children: milestone.achieved ? (_jsx(Heart, { className: "w-4 h-4 text-white" })) : (_jsx("span", { className: "text-white text-sm font-bold", children: index + 1 })) }), _jsxs("div", { className: "flex-1", children: [_jsx("div", { className: `font-semibold ${milestone.achieved ? 'text-primary' : 'text-card-foreground'}`, children: milestone.title }), _jsx("div", { className: `text-sm ${milestone.achieved ? 'text-primary' : 'text-muted-foreground'}`, children: milestone.description })] }), milestone.achieved && (_jsxs("div", { className: "text-green-600 font-medium flex items-center", children: [_jsx(CheckCircle, { className: "h-4 w-4 mr-1" }), " Achieved"] }))] }, index))) })] })), activeCalculator === 'life' && (_jsxs("div", { className: "bg-white p-8 rounded-xl shadow-sm border border-gray-100", children: [_jsxs("h2", { className: "text-2xl font-bold text-gray-900 mb-6 flex items-center", children: [_jsx(Clock, { className: "w-6 h-6 text-blue-500 mr-2" }), "Time Regained"] }), inputs.quitDate ? (_jsxs("div", { className: "grid md:grid-cols-3 gap-6", children: [_jsxs("div", { className: "text-center p-6 bg-blue-50 rounded-lg", children: [_jsx("div", { className: "text-3xl font-bold text-blue-600", children: calculateTimeRegained().days }), _jsx("div", { className: "text-gray-600", children: "Days of Life" })] }), _jsxs("div", { className: "text-center p-6 bg-purple-50 rounded-lg", children: [_jsx("div", { className: "text-3xl font-bold text-purple-600", children: calculateTimeRegained().hours }), _jsx("div", { className: "text-gray-600", children: "Hours Regained" })] }), _jsxs("div", { className: "text-center p-6 bg-primary-subtle rounded-lg", children: [_jsx("div", { className: "text-3xl font-bold text-green-600", children: Math.floor(calculateTimeRegained().minutes / 60 / 24 * 7) }), _jsx("div", { className: "text-gray-600", children: "Weeks of Time" })] })] })) : (_jsxs("div", { className: "text-center py-8", children: [_jsx(Clock, { className: "w-16 h-16 text-gray-300 mx-auto mb-4" }), _jsx("p", { className: "text-gray-600", children: "Please set your quit date to see time regained." })] }))] })), activeCalculator === 'progress' && (_jsxs("div", { className: "bg-white p-8 rounded-xl shadow-sm border border-gray-100", children: [_jsxs("h2", { className: "text-2xl font-bold text-gray-900 mb-6 flex items-center", children: [_jsx(TrendingUp, { className: "w-6 h-6 text-green-500 mr-2" }), "Quit Progress"] }), inputs.quitDate ? (_jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "grid md:grid-cols-2 gap-6", children: [_jsxs("div", { className: "p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg", children: [_jsx("h3", { className: "font-semibold text-gray-900 mb-2", children: "Cigarettes Not Smoked" }), _jsx("div", { className: "text-2xl font-bold text-green-600", children: inputs.cigarettesPerDay * calculateSavings().days })] }), _jsxs("div", { className: "p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg", children: [_jsx("h3", { className: "font-semibold text-gray-900 mb-2", children: "Packs Avoided" }), _jsx("div", { className: "text-2xl font-bold text-blue-600", children: Math.floor((inputs.cigarettesPerDay * calculateSavings().days) / inputs.cigarettesPerPack) })] })] }), _jsxs("div", { className: "bg-gray-50 p-6 rounded-lg", children: [_jsx("h3", { className: "font-semibold text-gray-900 mb-4", children: "Your Journey Milestones" }), _jsx("div", { className: "grid grid-cols-2 md:grid-cols-4 gap-4", children: getHealthMilestones().slice(0, 4).map((milestone, index) => (_jsxs("div", { className: "text-center", children: [_jsx("div", { className: `text-xl font-bold ${milestone.achieved ? 'text-green-600' : 'text-gray-400'}`, children: milestone.days_milestone === 1 ? '1 Day' :
                                                                    milestone.days_milestone === 7 ? '1 Week' :
                                                                        milestone.days_milestone === 30 ? '1 Month' :
                                                                            milestone.days_milestone === 365 ? '1 Year' :
                                                                                `${milestone.days_milestone} Days` }), _jsx("div", { className: "text-sm text-gray-600", children: milestone.title || milestone.description })] }, milestone.id || index))) })] })] })) : (_jsxs("div", { className: "text-center py-8", children: [_jsx(TrendingUp, { className: "w-16 h-16 text-gray-300 mx-auto mb-4" }), _jsx("p", { className: "text-gray-600", children: "Please set your quit date to see your progress." })] }))] }))] }) }), _jsx("section", { className: "bg-primary text-primary-foreground py-16", children: _jsxs("div", { className: "max-w-4xl mx-auto px-6 text-center", children: [_jsx("h2", { className: "text-3xl font-bold text-primary-foreground mb-4", children: "Ready to Track Your Journey?" }), _jsx("p", { className: "text-xl text-primary-foreground mb-8", children: "Join Mission Fresh for personalized tracking, community support, and comprehensive wellness tools." }), _jsxs("div", { className: "flex flex-col sm:flex-row justify-center gap-4", children: [_jsxs(Link, { to: "/tools/quit-methods", className: "inline-flex items-center justify-center border-2 border-primary-foreground text-primary-foreground px-8 py-3 rounded-lg text-lg font-medium hover:bg-primary-foreground hover:text-primary transition-colors", children: [_jsx(Zap, { className: "w-5 h-5 mr-2" }), "Quit Methods", _jsx(ArrowRight, { className: "w-5 h-5 ml-2" })] }), _jsxs(Link, { to: "/auth?mode=signup", className: "inline-flex items-center justify-center bg-secondary text-secondary-foreground px-8 py-3 rounded-lg text-lg font-medium hover:bg-secondary-hover transition-colors border border-border shadow-lg", children: ["Get Started", _jsx(ArrowRight, { className: "w-5 h-5 ml-2" })] })] })] }) })] }));
}
