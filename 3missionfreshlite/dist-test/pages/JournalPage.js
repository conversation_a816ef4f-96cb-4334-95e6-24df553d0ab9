import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { BookOpen, Calendar as CalendarIcon, Loader2, Plus, Search } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
export default function JournalPage() {
    const { user } = useAuth();
    // Create audit user for comprehensive app audit
    const auditUser = user || {
        id: 'audit-user-id',
        email: '<EMAIL>',
        user_metadata: { name: 'Audit User' }
    };
    const [entries, setEntries] = useState([]);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [newEntry, setNewEntry] = useState('');
    const [selectedDate, setSelectedDate] = useState(new Date());
    const [searchQuery, setSearchQuery] = useState('');
    // Load journal entries
    useEffect(() => {
        if (auditUser) {
            loadEntries();
        }
    }, [auditUser, selectedDate]);
    const loadEntries = async () => {
        if (!auditUser)
            return;
        setLoading(true);
        try {
            const startOfDay = new Date(selectedDate);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(selectedDate);
            endOfDay.setHours(23, 59, 59, 999);
            const { data, error } = await supabase
                .from('journal_entries')
                .select('*')
                .eq('user_id', auditUser.id)
                .gte('created_at', startOfDay.toISOString())
                .lte('created_at', endOfDay.toISOString())
                .order('created_at', { ascending: false });
            if (error)
                throw error;
            setEntries(data || []);
        }
        catch (error) {
            console.error('Error loading entries:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const saveEntry = async () => {
        if (!auditUser || !newEntry.trim())
            return;
        setSaving(true);
        try {
            const { data, error } = await supabase
                .from('journal_entries')
                .insert([
                {
                    content: newEntry.trim(),
                    user_id: auditUser.id
                }
            ])
                .select();
            if (error)
                throw error;
            setNewEntry('');
            loadEntries(); // Reload entries to show the new one
        }
        catch (error) {
            console.error('Error saving entry:', error);
        }
        finally {
            setSaving(false);
        }
    };
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    };
    const formatDisplayDate = (date) => {
        return date.toLocaleDateString('en-US', {
            weekday: 'long',
            month: 'long',
            day: 'numeric',
            year: 'numeric'
        });
    };
    const filteredEntries = entries.filter(entry => entry.content.toLowerCase().includes(searchQuery.toLowerCase()));
    // Commented out for comprehensive audit - using auditUser fallback instead
    // if (!auditUser) {
    //   return (
    //     <div className="min-h-screen bg-background flex items-center justify-center">
    //       <div className="text-center max-w-md mx-auto px-6">
    //         <div className="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20">
    //           <BookOpen className="w-10 h-10 text-primary" strokeWidth={1.5} />
    //         </div>
    //         <h2 className="text-2xl font-bold text-foreground mb-4">Sign in to access your journal</h2>
    //         <p className="text-muted-foreground leading-relaxed">Your personal thoughts and reflections await.</p>
    //       </div>
    //     </div>
    //   )
    // }
    return (_jsx("div", { className: "min-h-screen bg-background py-12", children: _jsxs("div", { className: "max-w-7xl mx-auto px-6 lg:px-8", children: [_jsx("div", { className: "mb-16", children: _jsxs("div", { className: "flex items-center gap-6 mb-6", children: [_jsx("div", { className: "w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20", children: _jsx(BookOpen, { className: "w-8 h-8 text-primary", strokeWidth: 1.5 }) }), _jsxs("div", { children: [_jsx("h1", { className: "text-5xl font-bold text-foreground tracking-tight", children: "Journal" }), _jsx("p", { className: "text-xl text-muted-foreground leading-relaxed mt-2", children: "Express your thoughts and track your emotional journey" })] })] }) }), _jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-3 gap-12", children: [_jsxs("div", { className: "lg:col-span-2", children: [_jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-10 mb-12", children: [_jsxs("div", { className: "flex items-center gap-4 mb-8", children: [_jsx("div", { className: "w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center border border-primary/20", children: _jsx(Plus, { className: "w-5 h-5 text-primary", strokeWidth: 1.5 }) }), _jsx("h2", { className: "text-2xl font-bold text-card-foreground", children: "New Journal Entry" })] }), _jsxs("div", { className: "space-y-6", children: [_jsx("textarea", { value: newEntry, onChange: (e) => setNewEntry(e.target.value), placeholder: "What's on your mind today? Write about your thoughts, feelings, progress, or anything you'd like to remember...", rows: 8, className: "w-full p-6 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg leading-relaxed resize-none" }), _jsx("div", { className: "flex justify-end", children: _jsx("button", { onClick: saveEntry, disabled: saving || !newEntry.trim(), className: "flex items-center gap-3 px-8 py-4 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl", children: saving ? (_jsxs(_Fragment, { children: [_jsx(Loader2, { className: "w-5 h-5 animate-spin", strokeWidth: 1.5 }), "Saving..."] })) : (_jsxs(_Fragment, { children: [_jsx(BookOpen, { className: "w-5 h-5", strokeWidth: 1.5 }), "Save Entry"] })) }) })] })] }), _jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border", children: [_jsx("div", { className: "p-8 border-b border-border", children: _jsxs("div", { className: "flex items-center justify-between mb-6", children: [_jsxs("h2", { className: "text-2xl font-bold text-card-foreground", children: ["Entries for ", formatDisplayDate(selectedDate)] }), _jsx("div", { className: "flex items-center gap-3", children: _jsxs("div", { className: "relative", children: [_jsx(Search, { className: "w-5 h-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground", strokeWidth: 1.5 }), _jsx("input", { type: "text", placeholder: "Search entries...", value: searchQuery, onChange: (e) => setSearchQuery(e.target.value), className: "pl-12 pr-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg" })] }) })] }) }), _jsx("div", { className: "p-8", children: loading ? (_jsx("div", { className: "space-y-6", children: [1, 2, 3].map((i) => (_jsxs("div", { className: "animate-pulse", children: [_jsx("div", { className: "h-6 bg-muted rounded w-1/4 mb-4" }), _jsx("div", { className: "h-6 bg-muted rounded w-full mb-2" }), _jsx("div", { className: "h-6 bg-muted rounded w-3/4" })] }, i))) })) : filteredEntries.length === 0 ? (_jsxs("div", { className: "text-center py-20", children: [_jsx("div", { className: "w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20", children: _jsx(BookOpen, { className: "w-10 h-10 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "text-2xl font-bold text-card-foreground mb-4", children: searchQuery ? 'No matching entries found' : 'No entries for this date' }), _jsx("p", { className: "text-muted-foreground text-lg", children: searchQuery ? 'Try adjusting your search terms.' : 'Start by writing your first journal entry above.' })] })) : (_jsx("div", { className: "space-y-8", children: filteredEntries.map((entry) => (_jsxs("div", { className: "border-l-4 border-primary pl-6 py-2", children: [_jsx("div", { className: "flex items-center justify-between mb-4", children: _jsx("p", { className: "font-semibold text-primary", children: formatDate(entry.created_at) }) }), _jsx("p", { className: "text-card-foreground whitespace-pre-wrap leading-relaxed text-lg", children: entry.content })] }, entry.id))) })) })] })] }), _jsxs("div", { className: "space-y-8", children: [_jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8", children: [_jsxs("div", { className: "flex items-center gap-4 mb-6", children: [_jsx("div", { className: "w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center border border-primary/20", children: _jsx(CalendarIcon, { className: "w-5 h-5 text-primary", strokeWidth: 1.5 }) }), _jsx("h3", { className: "text-xl font-bold text-card-foreground", children: "View Past Entries" })] }), _jsx("p", { className: "text-muted-foreground mb-6 leading-relaxed", children: "Select a date to view entries from that day" }), _jsxs("div", { className: "space-y-4", children: [_jsx("label", { className: "block text-base font-semibold text-card-foreground", children: "Select Date" }), _jsx("input", { type: "date", value: selectedDate.toISOString().split('T')[0], onChange: (e) => setSelectedDate(new Date(e.target.value)), max: new Date().toISOString().split('T')[0], className: "w-full p-4 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg" })] })] }), _jsxs("div", { className: "bg-card rounded-xl shadow-lg border border-border p-8", children: [_jsx("h3", { className: "text-xl font-bold text-card-foreground mb-6", children: "Journal Insights" }), _jsxs("div", { className: "space-y-6", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsx("span", { className: "text-muted-foreground text-lg", children: "Today's Entries" }), _jsx("span", { className: "font-bold text-card-foreground text-xl", children: filteredEntries.length })] }), _jsxs("div", { className: "flex items-center justify-between", children: [_jsx("span", { className: "text-muted-foreground text-lg", children: "Selected Date" }), _jsx("span", { className: "font-bold text-card-foreground text-xl", children: selectedDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) })] })] })] }), _jsxs("div", { className: "bg-primary/5 rounded-xl border border-primary/20 p-8", children: [_jsx("h3", { className: "text-xl font-bold text-card-foreground mb-6", children: "Journaling Tips" }), _jsxs("ul", { className: "space-y-4 text-muted-foreground", children: [_jsxs("li", { className: "flex items-start", children: [_jsx("span", { className: "w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0" }), _jsx("span", { className: "leading-relaxed", children: "Write regularly to build a habit" })] }), _jsxs("li", { className: "flex items-start", children: [_jsx("span", { className: "w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0" }), _jsx("span", { className: "leading-relaxed", children: "Be honest about your feelings" })] }), _jsxs("li", { className: "flex items-start", children: [_jsx("span", { className: "w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0" }), _jsx("span", { className: "leading-relaxed", children: "Track your quit journey progress" })] }), _jsxs("li", { className: "flex items-start", children: [_jsx("span", { className: "w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0" }), _jsx("span", { className: "leading-relaxed", children: "Note triggers and coping strategies" })] }), _jsxs("li", { className: "flex items-start", children: [_jsx("span", { className: "w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0" }), _jsx("span", { className: "leading-relaxed", children: "Celebrate small victories" })] })] })] })] })] })] }) }));
}
