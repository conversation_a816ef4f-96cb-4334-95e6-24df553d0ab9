import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Send, Bot, User, Sparkles } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
// AI Chat service integration
const getAIChatResponse = async (userMessage, userId) => {
    try {
        // RULE 0001: Real AI service integration - no hardcoded responses
        const { data, error } = await supabase.functions.invoke('ai-chat-response', {
            body: { message: userMessage, userId }
        });
        if (error) {
            console.error('AI service error:', error);
            return "I'm here to support your quit smoking journey. Could you please rephrase your question?";
        }
        return data?.response || "I understand you're looking for support. How can I help you with your quit smoking journey today?";
    }
    catch (error) {
        console.error('AI service error:', error);
        return "I'm experiencing some technical difficulties. Please try again in a moment.";
    }
};
export default function FreshAssistantPage() {
    const { user } = useAuth();
    // RULE 0001: No hardcoded messages - start with empty state
    const [messages, setMessages] = useState([]);
    const [inputMessage, setInputMessage] = useState('');
    const [loading, setLoading] = useState(false);
    // RULE 0001: Dynamic quick actions from database - hardcoded array removed
    const [quickActions, setQuickActions] = useState([]);
    const [actionsLoading, setActionsLoading] = useState(true);
    useEffect(() => {
        fetchQuickActions();
    }, []);
    const fetchQuickActions = async () => {
        try {
            const { data, error } = await supabase
                .from('quick_actions')
                .select('*')
                .order('display_order', { ascending: true });
            if (error) {
                console.error('Error fetching quick actions:', error);
                // Fallback to basic structure if database fails
                setQuickActions([
                    { id: '1', title: 'Need help with cravings?', description: 'Get immediate support and techniques', action_type: 'craving_help', display_order: 1 },
                    { id: '2', title: 'Track your mood', description: 'Log how you\'re feeling today', action_type: 'mood_tracking', display_order: 2 },
                    { id: '3', title: 'Set new goals', description: 'Create milestones for your journey', action_type: 'goal_setting', display_order: 3 }
                ]);
            }
            else {
                setQuickActions(data || []);
            }
        }
        catch (err) {
            console.error('Error fetching quick actions:', err);
            setQuickActions([]);
        }
        finally {
            setActionsLoading(false);
        }
    };
    const handleSendMessage = async (e) => {
        e.preventDefault();
        if (!inputMessage.trim() || loading)
            return;
        const userMessage = {
            id: Date.now(),
            type: 'user',
            content: inputMessage
        };
        setMessages([...messages, userMessage]);
        setInputMessage('');
        setLoading(true);
        try {
            // RULE 0001: Real AI service integration - no hardcoded responses
            const aiResponseText = await getAIChatResponse(inputMessage, user?.id);
            const aiResponse = {
                id: Date.now() + 1,
                type: 'bot',
                content: aiResponseText
            };
            setMessages(prev => [...prev, aiResponse]);
        }
        catch (error) {
            console.error('Error getting AI response:', error);
            const errorResponse = {
                id: Date.now() + 1,
                type: 'bot',
                content: "I'm sorry, I'm having trouble responding right now. Please try again in a moment."
            };
            setMessages(prev => [...prev, errorResponse]);
        }
        finally {
            setLoading(false);
        }
    };
    return (_jsx("div", { className: "min-h-screen bg-background", children: _jsxs("div", { className: "max-w-6xl mx-auto px-6 lg:px-8 py-12", children: [_jsxs("div", { className: "text-center mb-16", children: [_jsxs("div", { className: "flex items-center justify-center gap-6 mb-8", children: [_jsx("div", { className: "w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20", children: _jsx(Sparkles, { className: "w-10 h-10 text-primary", strokeWidth: 1.5 }) }), _jsx("h1", { className: "text-6xl font-bold text-foreground tracking-tight", children: "Fresh Assistant" })] }), _jsx("p", { className: "text-2xl text-muted-foreground leading-relaxed", children: "Your AI-powered quit smoking companion" })] }), _jsxs("div", { className: "bg-card rounded-2xl shadow-2xl border border-border overflow-hidden", children: [_jsx("div", { className: "h-[500px] overflow-y-auto p-8 space-y-6", children: messages.map((message) => (_jsxs("div", { className: `flex items-start gap-4 ${message.type === 'user' ? 'flex-row-reverse' : ''}`, children: [_jsx("div", { className: `w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0 border ${message.type === 'user'
                                            ? 'bg-primary text-primary-foreground border-border'
                                            : 'bg-primary-subtle text-primary border-border'}`, children: message.type === 'user' ? (_jsx(User, { className: "w-6 h-6", strokeWidth: 1.5 })) : (_jsx(Bot, { className: "w-6 h-6", strokeWidth: 1.5 })) }), _jsx("div", { className: `max-w-md px-6 py-4 rounded-xl ${message.type === 'user'
                                            ? 'bg-primary text-primary-foreground'
                                            : 'bg-muted text-card-foreground'}`, children: _jsx("p", { className: "text-lg leading-relaxed", children: message.content }) })] }, message.id))) }), _jsx("div", { className: "border-t border-border p-8", children: _jsxs("form", { onSubmit: handleSendMessage, className: "flex gap-4", children: [_jsx("input", { type: "text", value: inputMessage, onChange: (e) => setInputMessage(e.target.value), placeholder: "Ask me anything about quitting smoking...", className: "flex-1 px-6 py-4 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg" }), _jsxs("button", { type: "submit", className: "px-8 py-4 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover transition-all duration-300 flex items-center gap-3 font-semibold shadow-lg hover:shadow-xl disabled:opacity-50", disabled: !inputMessage.trim(), children: [_jsx(Send, { className: "w-5 h-5", strokeWidth: 1.5 }), _jsx("span", { className: "hidden sm:inline", children: "Send" })] })] }) })] }), _jsx("div", { className: "mt-12 grid sm:grid-cols-2 lg:grid-cols-3 gap-6", children: actionsLoading ? (_jsxs("div", { className: "col-span-full text-center py-8", children: [_jsx("div", { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4" }), _jsx("p", { className: "text-muted-foreground text-lg", children: "Loading quick actions..." })] })) : (quickActions.map((action) => (_jsxs("button", { className: "bg-card border border-border rounded-xl p-6 text-left hover:shadow-lg transition-all duration-300 hover:border-primary", children: [_jsx("div", { className: "font-bold text-card-foreground mb-3 text-lg", children: action.title }), _jsx("div", { className: "text-muted-foreground leading-relaxed", children: action.description })] }, action.id)))) })] }) }));
}
