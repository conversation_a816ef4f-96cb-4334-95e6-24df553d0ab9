import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import AppLayout from './components/AppLayout';
import LandingPage from './pages/LandingPage';
import AuthPage from './pages/AuthPage';
import DashboardPage from './pages/DashboardPage';
import ProgressPage from './pages/ProgressPage';
import GoalsPage from './pages/GoalsPage';
import BreathingPage from './pages/BreathingPage';
import FocusPage from './pages/FocusPage';
import MoodPage from './pages/MoodPage';
import CommunityPage from './pages/CommunityPage';
import LearnPage from './pages/LearnPage';
import SettingsPage from './pages/SettingsPage';
import HowItWorksPage from './pages/HowItWorksPage';
import FeaturesPage from './pages/FeaturesPage';
import ToolsPage from './pages/ToolsPage';
import NRTGuidePage from './pages/NRTGuidePage';
import SmokelessDirectoryPage from './pages/SmokelessDirectoryPage';
import QuitMethodsPage from './pages/QuitMethodsPage';
import NRTProductsPage from './pages/NRTProductsPage';
import CalculatorsPage from './pages/CalculatorsPage';
import HolisticHealthPage from './pages/HolisticHealthPage';
import FreshAssistantPage from './pages/FreshAssistantPage';
import AccountPage from './pages/AccountPage';
import LogEntryPage from './pages/LogEntryPage';
import RewardsPage from './pages/RewardsPage';
import HealthIntegrationsPage from './pages/HealthIntegrationsPage';
import JournalPage from './pages/JournalPage';
import SupportPage from './pages/SupportPage';
import { AuthProvider } from './contexts/AuthContext';
export default function Router() {
    return (_jsx(BrowserRouter, { children: _jsx(AuthProvider, { children: _jsxs(Routes, { children: [_jsxs(Route, { path: "/", element: _jsx(Layout, {}), children: [_jsx(Route, { index: true, element: _jsx(LandingPage, {}) }), _jsx(Route, { path: "auth", element: _jsx(AuthPage, {}) }), _jsx(Route, { path: "how-it-works", element: _jsx(HowItWorksPage, {}) }), _jsx(Route, { path: "features", element: _jsx(FeaturesPage, {}) }), _jsx(Route, { path: "tools", element: _jsx(ToolsPage, {}) }), _jsx(Route, { path: "tools/nrt-guide", element: _jsx(NRTGuidePage, {}) }), _jsx(Route, { path: "tools/nrt-products", element: _jsx(NRTProductsPage, {}) }), _jsx(Route, { path: "tools/smokeless-directory", element: _jsx(SmokelessDirectoryPage, {}) }), _jsx(Route, { path: "tools/quit-methods", element: _jsx(QuitMethodsPage, {}) }), _jsx(Route, { path: "tools/calculators", element: _jsx(CalculatorsPage, {}) }), _jsx(Route, { path: "tools/holistic-health", element: _jsx(HolisticHealthPage, {}) }), _jsx(Route, { path: "fresh-assistant", element: _jsx(FreshAssistantPage, {}) }), _jsx(Route, { path: "account", element: _jsx(AccountPage, {}) })] }), _jsxs(Route, { path: "dashboard", element: _jsx(AppLayout, {}), children: [_jsx(Route, { index: true, element: _jsx(DashboardPage, {}) }), _jsx(Route, { path: "progress", element: _jsx(ProgressPage, {}) }), _jsx(Route, { path: "goals", element: _jsx(GoalsPage, {}) }), _jsx(Route, { path: "log", element: _jsx(LogEntryPage, {}) }), _jsx(Route, { path: "rewards", element: _jsx(RewardsPage, {}) }), _jsx(Route, { path: "breathing", element: _jsx(BreathingPage, {}) }), _jsx(Route, { path: "focus", element: _jsx(FocusPage, {}) }), _jsx(Route, { path: "mood", element: _jsx(MoodPage, {}) }), _jsx(Route, { path: "community", element: _jsx(CommunityPage, {}) }), _jsx(Route, { path: "learn", element: _jsx(LearnPage, {}) }), _jsx(Route, { path: "settings", element: _jsx(SettingsPage, {}) }), _jsx(Route, { path: "health-integrations", element: _jsx(HealthIntegrationsPage, {}) }), _jsx(Route, { path: "journal", element: _jsx(JournalPage, {}) }), _jsx(Route, { path: "support", element: _jsx(SupportPage, {}) })] })] }) }) }));
}
