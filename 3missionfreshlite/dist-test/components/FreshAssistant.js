import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useRef, useEffect } from 'react';
import { X, Send, Bot } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
export default function FreshAssistant({ onClose }) {
    // HOLY RULE 0001: No hardcoded messages - start with empty state
    const [messages, setMessages] = useState([]);
    const [input, setInput] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const messagesEndRef = useRef(null);
    const { user } = useAuth();
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };
    useEffect(() => {
        scrollToBottom();
    }, [messages]);
    const handleSend = async () => {
        if (!input.trim() || isLoading)
            return;
        const userMessage = {
            id: Date.now().toString(),
            content: input.trim(),
            isUser: true,
            timestamp: new Date()
        };
        setMessages(prev => [...prev, userMessage]);
        setInput('');
        setIsLoading(true);
        try {
            // Call the Gemini suggestions edge function
            const { data, error } = await supabase.functions.invoke('get-gemini-suggestions', {
                body: {
                    user_input: userMessage.content,
                    user_id: user?.id || 'anonymous'
                }
            });
            if (error)
                throw error;
            const assistantMessage = {
                id: (Date.now() + 1).toString(),
                content: data.suggestion || "I'm here to help with your wellness journey. Could you please rephrase your question?",
                isUser: false,
                timestamp: new Date()
            };
            setMessages(prev => [...prev, assistantMessage]);
        }
        catch (error) {
            console.error('Error getting AI suggestion:', error);
            // Fallback responses based on keywords
            let fallbackResponse = "I'm here to support your smoke-free journey. What specific challenge are you facing today?";
            const inputLower = userMessage.content.toLowerCase();
            if (inputLower.includes('craving') || inputLower.includes('urge')) {
                fallbackResponse = "Cravings are normal and temporary. Try the 4-7-8 breathing technique: breathe in for 4 counts, hold for 7, exhale for 8. What triggers your cravings most?";
            }
            else if (inputLower.includes('quit') || inputLower.includes('stop')) {
                fallbackResponse = "Quitting smoking is a journey. The key is finding the right method for you. Have you considered gradual reduction, cold turkey, or nicotine replacement therapy?";
            }
            else if (inputLower.includes('stress') || inputLower.includes('anxiety')) {
                fallbackResponse = "Stress management is crucial for success. Try mindfulness meditation, physical exercise, or progressive muscle relaxation. What stresses you most?";
            }
            const assistantMessage = {
                id: (Date.now() + 1).toString(),
                content: fallbackResponse,
                isUser: false,
                timestamp: new Date()
            };
            setMessages(prev => [...prev, assistantMessage]);
        }
        finally {
            setIsLoading(false);
        }
    };
    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        }
    };
    return (_jsx("div", { className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4", children: _jsxs("div", { className: "bg-white rounded-2xl shadow-2xl w-full max-w-md h-[600px] flex flex-col", children: [_jsxs("div", { className: "flex items-center justify-between p-4 border-b border-gray-200", children: [_jsxs("div", { className: "flex items-center space-x-3", children: [_jsx("div", { className: "w-10 h-10 bg-card border border-border rounded-full flex items-center justify-center", children: _jsx(Bot, { className: "w-6 h-6 text-green-600" }) }), _jsxs("div", { children: [_jsx("h3", { className: "text-lg font-semibold text-gray-900", children: "Fresh Assistant" }), _jsx("p", { className: "text-sm text-gray-600", children: "Your 24/7 wellness coach" })] })] }), _jsx("button", { onClick: onClose, className: "p-2 hover:bg-gray-100 rounded-full transition-colors", children: _jsx(X, { className: "w-5 h-5 text-gray-500" }) })] }), _jsxs("div", { className: "flex-1 overflow-y-auto p-4 space-y-4", children: [messages.map((message) => (_jsx("div", { className: `flex ${message.isUser ? 'justify-end' : 'justify-start'}`, children: _jsxs("div", { className: `max-w-[80%] p-3 rounded-lg ${message.isUser
                                    ? 'bg-primary text-primary-foreground'
                                    : 'bg-gray-100 text-gray-900'}`, children: [_jsx("p", { className: "text-sm leading-relaxed", children: message.content }), _jsx("p", { className: `text-xs mt-1 ${message.isUser ? 'text-green-100' : 'text-gray-500'}`, children: message.timestamp.toLocaleTimeString([], {
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        }) })] }) }, message.id))), isLoading && (_jsx("div", { className: "flex justify-start", children: _jsx("div", { className: "bg-gray-100 p-3 rounded-lg", children: _jsxs("div", { className: "flex space-x-2", children: [_jsx("div", { className: "w-2 h-2 bg-gray-400 rounded-full animate-bounce" }), _jsx("div", { className: "w-2 h-2 bg-gray-400 rounded-full animate-bounce", style: { animationDelay: '0.1s' } }), _jsx("div", { className: "w-2 h-2 bg-gray-400 rounded-full animate-bounce", style: { animationDelay: '0.2s' } })] }) }) })), _jsx("div", { ref: messagesEndRef })] }), _jsxs("div", { className: "p-4 border-t border-gray-200", children: [_jsxs("div", { className: "flex space-x-2", children: [_jsx("input", { type: "text", value: input, onChange: (e) => setInput(e.target.value), onKeyPress: handleKeyPress, placeholder: "Ask me anything about your wellness journey...", className: "flex-1 px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent", disabled: isLoading }), _jsx("button", { onClick: handleSend, disabled: !input.trim() || isLoading, className: "btn-primary p-2 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed", children: _jsx(Send, { className: "w-5 h-5" }) })] }), _jsx("p", { className: "text-xs text-gray-500 mt-2 text-center", children: "Press Enter to send \u2022 Powered by AI wellness expertise" })] })] }) }));
}
