import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Outlet } from 'react-router-dom';
import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Home, BarChart3, Settings, Book, Users, Target, LogOut, Wind, Focus, Heart, BookOpen, Menu, Trophy, Activity, X, HelpCircle } from 'lucide-react';
import { NavLink } from 'react-router-dom';
import Logo from './Logo';
const navigationSections = [
    {
        title: 'Core Tracking',
        items: [
            { title: 'Dashboard', href: '/dashboard', icon: Home },
            { title: 'Log Entry', href: '/dashboard/log', icon: Book },
            { title: 'Progress', href: '/dashboard/progress', icon: BarChart3 },
            { title: 'Goals', href: '/dashboard/goals', icon: Target },
        ]
    },
    {
        title: 'Wellness Tools',
        items: [
            { title: 'Breathing', href: '/dashboard/breathing', icon: Wind },
            { title: 'Focus', href: '/dashboard/focus', icon: Focus },
            { title: 'Mood', href: '/dashboard/mood', icon: Heart },
            { title: 'Rewards', href: '/dashboard/rewards', icon: Trophy },
            { title: 'Health Data', href: '/dashboard/health-integrations', icon: Activity },
            { title: 'Journal', href: '/dashboard/journal', icon: BookOpen },
        ]
    },
    {
        title: 'Community & Learning',
        items: [
            { title: 'Community', href: '/dashboard/community', icon: Users },
            { title: 'Learn', href: '/dashboard/learn', icon: BookOpen },
            { title: 'Support', href: '/dashboard/support', icon: HelpCircle },
            { title: 'Settings', href: '/dashboard/settings', icon: Settings },
        ]
    }
];
function SidebarLink({ item }) {
    const Icon = item.icon;
    return (_jsxs(NavLink, { to: item.href, className: ({ isActive }) => `flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${isActive
            ? 'bg-accent text-primary shadow-sm'
            : 'text-muted-foreground hover:text-primary hover:bg-accent'}`, children: [_jsx(Icon, { className: "w-5 h-5 flex-shrink-0" }), _jsx("span", { children: item.title })] }));
}
export default function AppLayout() {
    const { user, signOut } = useAuth();
    const [sidebarOpen, setSidebarOpen] = useState(false);
    // TEMPORARY: Bypass auth check for comprehensive audit - REMOVE AFTER AUDIT
    const auditUser = user || {
        email: '<EMAIL>',
        user_metadata: { full_name: 'Audit User' }
    };
    // if (!user) {
    //   return null
    // }
    return (_jsxs("div", { className: "h-screen bg-muted flex overflow-hidden", children: [sidebarOpen && (_jsx("div", { className: "fixed inset-0 z-40 bg-foreground bg-opacity-75 lg:hidden", onClick: () => setSidebarOpen(false) })), _jsx("div", { className: `
        fixed inset-y-0 left-0 z-50 w-72 bg-background shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `, children: _jsxs("div", { className: "flex flex-col h-full", children: [_jsxs("div", { className: "flex items-center justify-between h-16 px-6 border-b border-border", children: [_jsx(Logo, { size: "sm", showText: true, linkTo: "/dashboard" }), _jsx("button", { onClick: () => setSidebarOpen(false), className: "p-1 rounded-md lg:hidden", children: _jsx(X, { className: "w-5 h-5 text-muted-foreground" }) })] }), _jsx("div", { className: "flex-1 overflow-y-auto px-4 py-6", children: _jsx("nav", { className: "space-y-6", children: navigationSections.map((section, sectionIndex) => (_jsxs("div", { className: "space-y-2", children: [_jsx("h3", { className: "text-xs font-semibold text-muted-foreground uppercase tracking-wide px-3", children: section.title }), _jsx("div", { className: "space-y-1", children: section.items.map((item) => (_jsx(SidebarLink, { item: item }, item.href))) }), sectionIndex < navigationSections.length - 1 && (_jsx("div", { className: "h-px bg-border mx-3 my-4" }))] }, section.title))) }) }), _jsxs("div", { className: "p-4 border-t border-border", children: [_jsxs("div", { className: "flex items-center gap-3 p-3 bg-muted rounded-lg mb-3", children: [_jsx("div", { className: "w-10 h-10 bg-primary rounded-lg flex items-center justify-center", children: _jsx("span", { className: "text-primary-foreground font-semibold text-sm", children: auditUser.email?.charAt(0).toUpperCase() }) }), _jsxs("div", { className: "flex-1 min-w-0", children: [_jsx("p", { className: "font-medium text-foreground truncate", children: auditUser.user_metadata?.full_name || auditUser.email?.split('@')[0] }), _jsx("p", { className: "text-xs text-muted-foreground truncate", children: auditUser.email })] })] }), _jsxs("button", { onClick: signOut, className: "w-full flex items-center gap-2 px-3 py-2 text-sm font-medium text-muted-foreground hover:text-destructive hover:bg-destructive/10 rounded-lg transition-colors", children: [_jsx(LogOut, { className: "w-4 h-4" }), "Sign Out"] })] })] }) }), _jsxs("div", { className: "flex-1 flex flex-col overflow-hidden lg:ml-0", children: [_jsxs("div", { className: "h-16 bg-background border-b border-border flex items-center justify-between px-4 lg:px-6", children: [_jsx("button", { onClick: () => setSidebarOpen(true), className: "p-2 rounded-md text-muted-foreground hover:text-foreground lg:hidden", children: _jsx(Menu, { className: "w-6 h-6" }) }), _jsx("div", { className: "flex-1 flex justify-center lg:justify-start lg:ml-0", children: _jsx("h1", { className: "text-xl font-semibold text-foreground", children: "Mission Fresh Dashboard" }) }), _jsx("div", { className: "flex items-center gap-4", children: _jsxs("div", { className: "text-sm text-muted-foreground", children: ["Welcome back, ", auditUser.user_metadata?.full_name || auditUser.email?.split('@')[0]] }) })] }), _jsx("main", { className: "flex-1 overflow-y-auto bg-muted", children: _jsx("div", { className: "p-4 lg:p-6 h-full", children: _jsx(Outlet, {}) }) })] })] }));
}
