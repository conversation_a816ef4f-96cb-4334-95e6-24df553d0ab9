import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, User } from 'lucide-react';
import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import Logo from './Logo';
export default function Header() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const location = useLocation();
    const { user, signOut } = useAuth();
    const isActive = (path) => location.pathname === path;
    return (_jsx("header", { className: "bg-background shadow-sm border-b border-border", children: _jsxs("div", { className: "container mx-auto px-4", children: [_jsxs("div", { className: "flex items-center justify-between h-16", children: [_jsx(Logo, { size: "md", showText: true, linkTo: "/" }), _jsxs("nav", { className: "hidden md:flex items-center space-x-8", children: [_jsx(Link, { to: "/", className: `nav-link ${isActive('/') ? 'text-primary' : 'text-muted-foreground hover:text-primary'}`, children: "Home" }), _jsx(Link, { to: "/how-it-works", className: `nav-link ${isActive('/how-it-works') ? 'text-primary' : 'text-muted-foreground hover:text-primary'}`, children: "How It Works" }), _jsx(Link, { to: "/features", className: `nav-link ${isActive('/features') ? 'text-primary' : 'text-muted-foreground hover:text-primary'}`, children: "Features" }), _jsx(Link, { to: "/tools", className: `nav-link ${isActive('/tools') ? 'text-primary' : 'text-muted-foreground hover:text-primary'}`, children: "Web Tools" }), _jsx(Link, { to: "/fresh-assistant", className: `nav-link ${isActive('/fresh-assistant') ? 'text-primary' : 'text-muted-foreground hover:text-primary'}`, children: "AI Assistant" })] }), _jsx("div", { className: "hidden md:flex items-center space-x-4", children: user ? (_jsxs(_Fragment, { children: [_jsx(Link, { to: "/dashboard", className: "bg-secondary text-secondary-foreground px-4 py-2 rounded-lg font-medium hover:bg-secondary-hover transition-all duration-300 border border-border", children: "Dashboard" }), _jsxs("button", { onClick: signOut, className: "flex items-center gap-2 text-muted-foreground hover:text-primary", children: [_jsx(User, { className: "w-4 h-4" }), "Sign Out"] })] })) : (_jsxs(_Fragment, { children: [_jsx(Link, { to: "/auth?mode=signin", className: "text-muted-foreground hover:text-primary font-medium transition-colors", children: "Sign In" }), _jsx(Link, { to: "/auth", className: "bg-primary text-primary-foreground px-6 py-3 rounded-xl font-semibold hover:bg-primary-hover transition-all duration-300 shadow-lg hover:shadow-xl", children: "Get Started" })] })) }), _jsx("button", { className: "md:hidden p-3 text-muted-foreground hover:text-primary transition-all duration-300 rounded-lg hover:bg-accent", onClick: () => setIsMenuOpen(!isMenuOpen), "aria-label": isMenuOpen ? 'Close menu' : 'Open menu', "aria-expanded": isMenuOpen, children: isMenuOpen ? _jsx(X, { className: "w-6 h-6", strokeWidth: 1.5 }) : _jsx(Menu, { className: "w-6 h-6", strokeWidth: 1.5 }) })] }), isMenuOpen && (_jsx("div", { className: "md:hidden py-6 border-t border-border bg-card/95 backdrop-blur-sm", children: _jsxs("nav", { className: "flex flex-col space-y-6", children: [_jsx(Link, { to: "/", className: "nav-link text-muted-foreground hover:text-primary", onClick: () => setIsMenuOpen(false), children: "Home" }), _jsx(Link, { to: "/how-it-works", className: "nav-link text-muted-foreground hover:text-primary", onClick: () => setIsMenuOpen(false), children: "How It Works" }), _jsx(Link, { to: "/features", className: "nav-link text-muted-foreground hover:text-primary", onClick: () => setIsMenuOpen(false), children: "Features" }), _jsx(Link, { to: "/tools", className: "nav-link text-muted-foreground hover:text-primary", onClick: () => setIsMenuOpen(false), children: "Web Tools" }), _jsx(Link, { to: "/fresh-assistant", className: "nav-link text-muted-foreground hover:text-primary", onClick: () => setIsMenuOpen(false), children: "AI Assistant" }), user ? (_jsxs(_Fragment, { children: [_jsx(Link, { to: "/dashboard", className: "nav-link text-muted-foreground hover:text-primary", onClick: () => setIsMenuOpen(false), children: "Dashboard" }), _jsx("button", { onClick: () => {
                                            signOut();
                                            setIsMenuOpen(false);
                                        }, className: "nav-link text-left text-muted-foreground hover:text-primary", children: "Sign Out" })] })) : (_jsxs(_Fragment, { children: [_jsx(Link, { to: "/auth?mode=signin", className: "nav-link text-muted-foreground hover:text-primary", onClick: () => setIsMenuOpen(false), children: "Sign In" }), _jsx(Link, { to: "/auth", className: "nav-link text-muted-foreground hover:text-primary", onClick: () => setIsMenuOpen(false), children: "Get Started" })] }))] }) }))] }) }));
}
