import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Leaf } from 'lucide-react';
import { Link } from 'react-router-dom';
const sizeConfig = {
    sm: {
        container: 'w-8 h-8',
        icon: 'w-4 h-4',
        text: 'text-lg',
        gap: 'gap-2'
    },
    md: {
        container: 'w-10 h-10',
        icon: 'w-5 h-5',
        text: 'text-xl',
        gap: 'gap-2'
    },
    lg: {
        container: 'w-12 h-12',
        icon: 'w-6 h-6',
        text: 'text-2xl',
        gap: 'gap-3'
    },
    xl: {
        container: 'w-16 h-16',
        icon: 'w-8 h-8',
        text: 'text-4xl',
        gap: 'gap-4'
    }
};
export default function Logo({ size = 'md', showText = true, linkTo = '/', className = '' }) {
    const config = sizeConfig[size];
    const logoContent = (_jsxs("div", { className: `flex items-center ${config.gap} ${className}`, children: [_jsx("div", { className: `
        ${config.container} 
        bg-primary 
        rounded-xl 
        flex items-center justify-center 
        shadow-lg
        hover:shadow-xl
        transition-all duration-300
        hover:scale-105
      `, children: _jsx(Leaf, { className: `${config.icon} text-primary-foreground`, strokeWidth: 2 }) }), showText && (_jsx("span", { className: `${config.text} font-bold text-foreground tracking-tight`, children: "Mission Fresh" }))] }));
    if (linkTo) {
        return (_jsx(Link, { to: linkTo, className: "hover:text-primary transition-colors duration-300", "aria-label": "Mission Fresh - Go to homepage", children: logoContent }));
    }
    return logoContent;
}
