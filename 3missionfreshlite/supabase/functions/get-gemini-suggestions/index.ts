// get-gemini-suggestions Supabase Edge Function
// Provides real AI-powered quit smoking guidance using Google Gemini API

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { user_input, user_id } = await req.json()

    if (!user_input) {
      return new Response(
        JSON.stringify({ error: 'User input is required' }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Initialize Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get Gemini API key from environment
    const geminiApiKey = Deno.env.get('GEMINI_API_KEY')
    if (!geminiApiKey) {
      throw new Error('GEMINI_API_KEY environment variable not set')
    }

    // Prepare system prompt for quit smoking guidance
    const systemPrompt = `You are Fresh Assistant, the world's best AI quit smoking guide and wellness coach. You provide personalized, evidence-based support for people on their smoke-free journey.

Key principles:
- Be empathetic, supportive, and non-judgmental
- Provide specific, actionable advice
- Reference proven quit smoking strategies and techniques
- Acknowledge that quitting is challenging but achievable
- Offer immediate coping strategies for cravings and triggers
- Celebrate progress and milestones
- Provide motivational support during difficult moments

Focus areas:
- Craving management techniques (breathing exercises, distraction methods)
- Trigger identification and avoidance strategies
- Nicotine replacement therapy guidance
- Behavioral modification techniques
- Stress management and alternative coping mechanisms
- Health benefits and progress tracking
- Relapse prevention and recovery strategies
- Building support systems and accountability

Respond in a warm, professional tone as a trusted wellness coach. Keep responses concise but comprehensive, typically 2-4 sentences. Always end with an encouraging question or suggestion to keep the conversation going.`

    // Call Google Gemini API
    const geminiResponse = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${geminiApiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: `${systemPrompt}\n\nUser question: ${user_input}`
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          },
          safetySettings: [
            {
              category: "HARM_CATEGORY_HARASSMENT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_HATE_SPEECH", 
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_DANGEROUS_CONTENT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            }
          ]
        }),
      }
    )

    if (!geminiResponse.ok) {
      throw new Error(`Gemini API error: ${geminiResponse.statusText}`)
    }

    const geminiData = await geminiResponse.json()
    const aiSuggestion = geminiData.candidates?.[0]?.content?.parts?.[0]?.text

    if (!aiSuggestion) {
      throw new Error('No suggestion received from Gemini API')
    }

    // Store conversation in database for user history and learning
    if (user_id && user_id !== 'anonymous') {
      await supabase
        .from('ai_conversations')
        .insert({
          user_id: user_id,
          user_input: user_input,
          ai_response: aiSuggestion,
          created_at: new Date().toISOString(),
          session_type: 'quit_smoking_guidance'
        })
    }

    // Return the AI suggestion
    return new Response(
      JSON.stringify({ 
        suggestion: aiSuggestion,
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('Error in get-gemini-suggestions:', error)
    
    // Return error response
    return new Response(
      JSON.stringify({ 
        error: 'Failed to get AI suggestion',
        details: error.message 
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
