{"name": "missionfreshlite", "version": "1.0.0", "description": "Mission Fresh Lite - Professional minimal structure for smoking cessation support", "type": "module", "scripts": {"dev": "vite --port 5003", "build": "tsc && vite build", "preview": "vite preview --port 5003", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.9", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.7.0", "tailwind-merge": "^1.14.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}