import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Package, Shield, Star, Loader2, AlertTriangle, ArrowRight, Search, Filter } from 'lucide-react'

export default function NRTProductsPage() {
  const [nrtProducts, setNrtProducts] = useState<any[]>([])
  const [filteredProducts, setFilteredProducts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  // HOLY RULE 0001: Load real NRT product data from Supabase
  useEffect(() => {
    loadNRTProducts()
  }, [])

  const loadNRTProducts = async () => {
    try {
      setLoading(true)
      
      // RULE 0001: Load real NRT product data from correct nrt_products table
      const { createClient } = await import('@supabase/supabase-js')
      const supabaseNoSchema = createClient(
        'https://yekarqanirdkdckimpna.supabase.co',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'
      )
      
      // Fetch NRT products from correct nrt_products table - RULE 0001: real data only
      const { data: nrtData, error: nrtError } = await supabaseNoSchema
        .from('nrt_products')
        .select('*')
        .eq('is_active', true)
        .order('effectiveness_rating', { ascending: false })

      if (nrtError) {
        console.error('Error loading NRT products:', nrtError)
        setError('Failed to load NRT products')
        setNrtProducts([])
        setFilteredProducts([])
      } else {
        setNrtProducts(nrtData || [])
        setFilteredProducts(nrtData || [])
        setError(null)
      }
      
    } catch (error) {
      console.error('Error setting up NRT product data:', error)
      setError('Failed to load NRT product information')
    } finally {
      setLoading(false)
    }
  }

  // Filter products based on search term and category
  useEffect(() => {
    let filtered = nrtProducts

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.category?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory)
    }

    setFilteredProducts(filtered)
  }, [nrtProducts, searchTerm, selectedCategory])

  // Get unique categories for filter
  const categories = ['all', ...Array.from(new Set(nrtProducts.map(p => p.category).filter(Boolean)))]

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <section className="bg-background py-24">
        <div className="max-w-6xl mx-auto px-6 lg:px-8 text-center">
          <div className="w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg">
            <Shield className="w-10 h-10 text-primary-foreground" strokeWidth={2} />
          </div>
          <h1 className="text-6xl font-bold text-foreground mb-8 tracking-tight">
            NRT Products
          </h1>
          <p className="text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto">
            Comprehensive nicotine replacement therapy product directory
          </p>
        </div>
      </section>

      {/* Content */}
      <section className="py-24">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-6">Nicotine Replacement Therapy Products</h2>
            <p className="text-xl text-muted-foreground leading-relaxed">FDA-approved products to help manage withdrawal symptoms</p>
          </div>

          {/* Search and Filter */}
          <div className="bg-card rounded-xl p-8 shadow-lg border border-border mb-12">
            <div className="flex flex-col md:flex-row gap-6">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search NRT products..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-12 pr-4 py-3 border border-border rounded-lg bg-input text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                  />
                </div>
              </div>

              {/* Category Filter */}
              <div className="md:w-64">
                <div className="relative">
                  <Filter className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full pl-12 pr-4 py-3 border border-border rounded-lg bg-input text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring appearance-none"
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>
                        {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Results Count */}
            <div className="mt-6 text-center">
              <p className="text-muted-foreground">
                Showing {filteredProducts.length} of {nrtProducts.length} products
              </p>
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-20">
              <Loader2 className="w-16 h-16 text-primary animate-spin" strokeWidth={1.5} />
              <span className="ml-4 text-muted-foreground text-lg">Loading NRT products...</span>
            </div>
          ) : error ? (
            <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-8 text-center">
              <AlertTriangle className="w-12 h-12 text-destructive mx-auto mb-4" strokeWidth={1.5} />
              <p className="text-destructive font-medium text-lg">{error}</p>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredProducts.map((product: any) => (
                <div key={product.id} className="bg-card rounded-2xl shadow-lg border border-border overflow-hidden hover:shadow-2xl transition-all duration-300">
                  <div className="p-8">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="font-bold text-card-foreground text-xl">{product.name}</h3>
                      <div className="flex items-center bg-primary-subtle px-3 py-1 rounded-lg border border-border">
                        <Star className="w-5 h-5 text-primary fill-current" strokeWidth={1.5} />
                        <span className="ml-2 text-primary font-semibold">{product.effectiveness_rating}</span>
                      </div>
                    </div>

                    <p className="text-muted-foreground mb-6 leading-relaxed text-lg">
                      {product.description}
                    </p>
                    
                    <div className="space-y-6">
                      <div>
                        <h4 className="text-sm font-bold text-card-foreground mb-4 uppercase tracking-wide">
                          Key Benefits
                        </h4>
                        <ul className="space-y-3">
                          {product.benefits?.slice(0, 3).map((benefit: string, index: number) => (
                            <li key={index} className="text-muted-foreground flex items-start leading-relaxed">
                              <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-4 flex-shrink-0"></span>
                              {benefit}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="border-t border-border pt-6">
                        <div className="flex justify-between items-center mb-3">
                          <span className="text-muted-foreground">Success Rate:</span>
                          <span className="font-bold text-primary text-lg">{product.success_rate}%</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-muted-foreground">Duration:</span>
                          <span className="font-bold text-card-foreground">{product.duration}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {filteredProducts.length === 0 && !loading && !error && nrtProducts.length > 0 && (
            <div className="text-center py-20">
              <div className="w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                <Search className="w-10 h-10 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-2xl font-bold text-card-foreground mb-4">No Products Found</h3>
              <p className="text-muted-foreground text-lg">Try adjusting your search or filter criteria.</p>
            </div>
          )}

          {nrtProducts.length === 0 && !loading && !error && (
            <div className="text-center py-20">
              <div className="w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                <Package className="w-10 h-10 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-2xl font-bold text-card-foreground mb-4">No NRT Products Found</h3>
              <p className="text-muted-foreground text-lg">Check back later for updated product information.</p>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-primary py-24">
        <div className="max-w-6xl mx-auto px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-primary-foreground mb-6 tracking-tight">
            Ready to Find Your Perfect NRT Solution?
          </h2>
          <p className="text-2xl text-primary-foreground mb-12 max-w-3xl mx-auto leading-relaxed">
            Join Mission Fresh for personalized recommendations, tracking, and support.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-6">
            <Link
              to="/tools/nrt-guide"
              className="inline-flex items-center justify-center border-2 border-primary-foreground text-primary-foreground px-8 py-4 rounded-lg text-lg font-semibold hover:bg-primary-foreground hover:text-primary transition-all duration-300"
            >
              <Shield className="w-6 h-6 mr-3" strokeWidth={1.5} />
              NRT Guide
              <ArrowRight className="w-6 h-6 ml-3" strokeWidth={1.5} />
            </Link>
            <Link
              to="/auth?mode=signup"
              className="inline-flex items-center justify-center bg-card text-primary px-8 py-4 rounded-lg text-lg font-semibold hover:bg-accent hover:text-accent-foreground transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Get Started
              <ArrowRight className="w-6 h-6 ml-3" strokeWidth={1.5} />
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
