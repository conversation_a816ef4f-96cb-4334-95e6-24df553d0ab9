import React, { useState, useEffect } from 'react'
import { Wind, Play, Pause, RotateCcw, ArrowLeft, Clock, Target, Heart } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'

interface BreathingExercise {
  id: string
  title: string
  description: string
  duration: number // in seconds
  inhale_time: number
  hold_time: number
  exhale_time: number
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced'
  benefits: string[]
}

interface BreathingSessionProps {
  exercise: BreathingExercise
  onComplete: () => void
  onBack: () => void
}

function BreathingSession({ exercise, onComplete, onBack }: BreathingSessionProps) {
  const [isActive, setIsActive] = useState(false)
  const [timeLeft, setTimeLeft] = useState(exercise.duration)
  const [currentPhase, setCurrentPhase] = useState<'inhale' | 'hold' | 'exhale'>('inhale')
  const [phaseTimeLeft, setPhaseTimeLeft] = useState(exercise.inhale_time)
  const [cycleCount, setCycleCount] = useState(0)

  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(time => time - 1)
        setPhaseTimeLeft(time => {
          if (time <= 1) {
            // Move to next phase
            if (currentPhase === 'inhale') {
              setCurrentPhase('hold')
              return exercise.hold_time
            } else if (currentPhase === 'hold') {
              setCurrentPhase('exhale')
              return exercise.exhale_time
            } else {
              setCurrentPhase('inhale')
              setCycleCount(count => count + 1)
              return exercise.inhale_time
            }
          }
          return time - 1
        })
      }, 1000)
    } else if (timeLeft === 0) {
      setIsActive(false)
      onComplete()
    }

    return () => clearInterval(interval)
  }, [isActive, timeLeft, currentPhase, exercise, onComplete])

  const toggleSession = () => {
    setIsActive(!isActive)
  }

  const resetSession = () => {
    setIsActive(false)
    setTimeLeft(exercise.duration)
    setCurrentPhase('inhale')
    setPhaseTimeLeft(exercise.inhale_time)
    setCycleCount(0)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getPhaseInstruction = () => {
    switch (currentPhase) {
      case 'inhale':
        return 'Breathe In'
      case 'hold':
        return 'Hold'
      case 'exhale':
        return 'Breathe Out'
    }
  }

  const getPhaseColor = () => {
    switch (currentPhase) {
      case 'inhale':
        return 'bg-primary'
      case 'hold':
        return 'bg-accent'
      case 'exhale':
        return 'bg-primary/80'
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center gap-6">
        <button
          onClick={onBack}
          className="flex items-center gap-3 px-6 py-3 text-muted-foreground hover:text-foreground transition-all duration-300 font-semibold"
        >
          <ArrowLeft className="w-5 h-5" strokeWidth={1.5} />
          Back to Exercises
        </button>
      </div>

      {/* Session Info */}
      <div className="text-center space-y-6">
        <h1 className="text-5xl font-bold text-foreground tracking-tight">{exercise.title}</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">{exercise.description}</p>

        <div className="flex justify-center gap-8 text-muted-foreground">
          <div className="flex items-center gap-2">
            <Clock className="w-5 h-5" strokeWidth={1.5} />
            <span className="font-medium">{formatTime(exercise.duration)}</span>
          </div>
          <div className="flex items-center gap-2">
            <Target className="w-5 h-5" strokeWidth={1.5} />
            <span className="font-medium">{exercise.difficulty}</span>
          </div>
          <div className="flex items-center gap-2">
            <Heart className="w-5 h-5" strokeWidth={1.5} />
            <span className="font-medium">Cycle {cycleCount + 1}</span>
          </div>
        </div>
      </div>

      {/* Breathing Circle */}
      <div className="flex flex-col items-center space-y-8">
        <div className="relative">
          {/* Main breathing circle */}
          <div className={`w-80 h-80 rounded-full flex items-center justify-center transition-all duration-1000 shadow-2xl ${getPhaseColor()}`}>
            <div className="w-64 h-64 rounded-full bg-card bg-opacity-95 flex flex-col items-center justify-center border border-border">
              <div className="text-3xl font-bold text-card-foreground mb-4">{getPhaseInstruction()}</div>
              <div className="text-6xl font-mono text-primary">{phaseTimeLeft}</div>
            </div>
          </div>
          
          {/* Phase indicator ring */}
          <div className="absolute -inset-4">
            <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="48"
                stroke="rgba(0,0,0,0.1)"
                strokeWidth="2"
                fill="none"
              />
              <circle
                cx="50"
                cy="50"
                r="48"
                stroke="hsl(var(--primary))"
                strokeWidth="3"
                fill="none"
                strokeDasharray={`${((phaseTimeLeft / (currentPhase === 'inhale' ? exercise.inhaleTime : currentPhase === 'hold' ? exercise.holdTime : exercise.exhaleTime)) * 301.59)} 301.59`}
                className="transition-all duration-1000"
              />
            </svg>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center gap-6">
          <button
            onClick={toggleSession}
            className={`w-20 h-20 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl ${
              isActive
                ? 'bg-destructive hover:bg-destructive-hover text-destructive-foreground'
                : 'bg-primary hover:bg-primary-hover text-primary-foreground'
            }`}
          >
            {isActive ? <Pause className="w-8 h-8" strokeWidth={1.5} /> : <Play className="w-8 h-8" strokeWidth={1.5} />}
          </button>

          <button
            onClick={resetSession}
            className="w-20 h-20 rounded-full bg-accent hover:bg-accent-hover text-accent-foreground flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            <RotateCcw className="w-8 h-8" strokeWidth={1.5} />
          </button>
        </div>

        {/* Time Remaining */}
        <div className="text-center">
          <div className="text-xl text-muted-foreground font-semibold">Time Remaining</div>
          <div className="text-5xl font-mono font-bold text-foreground">{formatTime(timeLeft)}</div>
        </div>
      </div>

      {/* Benefits */}
      <div className="max-w-md mx-auto">
        <h3 className="text-xl font-bold text-foreground mb-4 text-center tracking-tight">Benefits</h3>
        <ul className="space-y-3">
          {exercise.benefits.map((benefit, index) => (
            <li key={index} className="flex items-center gap-3 text-card-foreground">
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              {benefit}
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}

export default function BreathingToolsPage() {
  const [currentExercise, setCurrentExercise] = useState<BreathingExercise | null>(null)
  const [breathingExercises, setBreathingExercises] = useState<BreathingExercise[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchBreathingExercises()
  }, [])

  const fetchBreathingExercises = async () => {
    try {
      const { data, error } = await supabase
        .from('breathing_exercises')
        .select('*')
        .order('difficulty', { ascending: true })
      
      if (error) {
        console.error('Error fetching breathing exercises:', error)
        // Fallback to basic structure if database fails
        setBreathingExercises([
          { id: '1', title: '4-7-8 Breathing', description: 'A calming technique that reduces anxiety and promotes relaxation.', duration: 240, inhale_time: 4, hold_time: 7, exhale_time: 8, difficulty: 'Beginner', benefits: ['Reduces anxiety', 'Improves sleep', 'Calms nervous system'] },
          { id: '2', title: 'Deep Belly Breathing', description: 'Focus on slow, deep breaths to activate the parasympathetic nervous system.', duration: 300, inhale_time: 6, hold_time: 2, exhale_time: 6, difficulty: 'Beginner', benefits: ['Reduces stress', 'Lowers blood pressure', 'Improves focus'] },
          { id: '3', title: 'Box Breathing', description: 'A square breathing pattern used by Navy SEALs for focus and calm.', duration: 360, inhale_time: 4, hold_time: 4, exhale_time: 4, difficulty: 'Intermediate', benefits: ['Enhances focus', 'Builds resilience', 'Manages stress'] },
          { id: '4', title: 'Triangle Breathing', description: 'Three-part breathing rhythm for balanced energy and clarity.', duration: 180, inhale_time: 4, hold_time: 4, exhale_time: 4, difficulty: 'Intermediate', benefits: ['Improves concentration', 'Balances energy', 'Reduces tension'] }
        ])
      } else {
        setBreathingExercises(data || [])
      }
    } catch (err) {
      console.error('Error fetching breathing exercises:', err)
      setBreathingExercises([])
    } finally {
      setLoading(false)
    }
  }

  const startExercise = (exercise: BreathingExercise) => {
    setCurrentExercise(exercise)
  }

  const completeExercise = () => {
    setCurrentExercise(null)
    // Here you could save the session to Supabase
  }

  const goBack = () => {
    setActiveExercise(null)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto mb-6"></div>
          <p className="text-muted-foreground text-lg">Loading breathing exercises...</p>
        </div>
      </div>
    )
  }

  if (activeExercise) {
    return (
      <div className="min-h-screen bg-background py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <BreathingSession
            exercise={activeExercise}
            onComplete={completeExercise}
            onBack={goBack}
          />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20">
            <Wind className="w-8 h-8 text-primary" strokeWidth={1.5} />
          </div>
          <div>
            <h1 className="text-4xl font-bold text-foreground tracking-tight">Breathing Tools</h1>
            <p className="text-gray-600">Calm your mind and manage cravings with guided breathing</p>
          </div>
        </div>
        
        <div className="bg-info/10 rounded-lg border border-info/20 p-4">
          <p className="text-info text-sm">
            <strong>Tip:</strong> Regular breathing exercises can reduce stress, manage cravings, and improve overall well-being. 
            Try to practice for a few minutes daily for best results.
          </p>
        </div>
      </div>

      {/* Exercises Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {breathingExercises.map((exercise) => (
          <div key={exercise.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-xl font-semibold text-gray-900">{exercise.title}</h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                exercise.difficulty === 'Beginner'
                  ? 'bg-accent text-primary'
                  : exercise.difficulty === 'Intermediate'
                  ? 'bg-warning-subtle text-warning'
                  : 'bg-destructive-subtle text-destructive'
              }`}>
                {exercise.difficulty}
              </span>
            </div>
            
            <p className="text-gray-600 mb-4">{exercise.description}</p>
            
            <div className="flex items-center gap-4 mb-4 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {Math.floor(exercise.duration / 60)} min
              </div>
              <div className="flex items-center gap-1">
                <Target className="w-4 h-4" />
                {exercise.inhale_time}-{exercise.hold_time}-{exercise.exhale_time} pattern
              </div>
            </div>
            
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Benefits:</h4>
              <ul className="space-y-1">
                {exercise.benefits.slice(0, 2).map((benefit, index) => (
                  <li key={index} className="text-sm text-gray-600 flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    {benefit}
                  </li>
                ))}
              </ul>
            </div>
            
            <button
              onClick={() => startExercise(exercise)}
              className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-info text-info-foreground rounded-lg hover:bg-info/90 transition-colors"
            >
              <Play className="w-4 h-4" />
              Start Exercise
            </button>
          </div>
        ))}
      </div>

      {/* Tips Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Breathing Exercise Tips</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
          <div>
            <h4 className="font-medium mb-2">Before You Start:</h4>
            <ul className="space-y-1">
              <li>• Find a quiet, comfortable space</li>
              <li>• Sit or lie down with a straight spine</li>
              <li>• Turn off distractions</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">During the Exercise:</h4>
            <ul className="space-y-1">
              <li>• Focus on the rhythm and counting</li>
              <li>• Breathe through your nose when possible</li>
              <li>• Don't force the breath</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BreathingToolsPage
