import { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Compass, <PERSON>R<PERSON>, CheckCircle, Clock, Users, Brain, Heart, Target, AlertCircle, Play, Bookmark, TrendingUp, Award, Search, Filter } from 'lucide-react'

export default function QuitMethodsPage() {
  const [selectedMethod, setSelectedMethod] = useState('overview')
  const [methods, setMethods] = useState<any[]>([])
  const [filteredMethods, setFilteredMethods] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedDifficulty, setSelectedDifficulty] = useState('all')

  // Fetch real quit methods from Supabase database - RULE 0001: ZERO hardcoding
  useEffect(() => {
    async function fetchQuitMethods() {
      try {
        setLoading(true)
        // Create Supabase client without schema restriction to access quit_methods table
        const { createClient } = await import('@supabase/supabase-js')
        const supabaseNoSchema = createClient(
          'https://yekarqanirdkdckimpna.supabase.co',
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'
        )
        
        // Using correct quit_methods table with proper connection
        const { data, error } = await supabaseNoSchema
          .from('quit_methods')
          .select('*')
          .eq('is_active', true)
          .order('sort_order', { ascending: true })
          .order('name', { ascending: true })
        
        if (error) {
          console.error('Supabase quit_methods query error:', error)
          throw error
        }
        
        // Use real quit methods data directly - RULE 0001: real data only
        const quitMethodsData = data?.map(method => ({
          id: method.id.toString(),
          name: method.name,
          duration: method.timeframe || 'Varies',
          difficulty: method.difficulty || 'Medium',
          successRate: method.success_rate_min && method.success_rate_max 
            ? `${method.success_rate_min}-${method.success_rate_max}%` 
            : 'Varies',
          icon: getMethodIcon(method.name),
          description: method.description,
          pros: method.best_for || [],
          cons: method.considerations || [],
          whoItsFor: method.best_for ? method.best_for.join(', ') : 'General users',
          tips: method.considerations || []
        })) || []
        
        setMethods(quitMethodsData)
        setFilteredMethods(quitMethodsData)
      } catch (err) {
        console.error('Error fetching quit methods:', err)
        console.error('Full error object:', JSON.stringify(err, null, 2))
        setError('Failed to load quit methods: ' + (err?.message || 'Unknown error'))
      } finally {
        setLoading(false)
      }
    }
    fetchQuitMethods()
  }, [])

  // Filter methods based on search term and difficulty
  useEffect(() => {
    let filtered = methods

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(method =>
        method.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        method.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        method.whoItsFor.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by difficulty
    if (selectedDifficulty !== 'all') {
      filtered = filtered.filter(method => method.difficulty === selectedDifficulty)
    }

    setFilteredMethods(filtered)
  }, [methods, searchTerm, selectedDifficulty])

  // Get unique difficulties for filter
  const difficulties = ['all', ...Array.from(new Set(methods.map(m => m.difficulty).filter(Boolean)))]

  // Get icon based on method type - RULE 0001: logic only, no hardcoded data
  const getMethodIcon = (methodName: string) => {
    const name = methodName?.toLowerCase() || ''
    if (name.includes('cold') || name.includes('turkey')) return Target
    if (name.includes('gradual') || name.includes('tapering')) return Clock
    if (name.includes('therapy') || name.includes('counseling')) return Heart
    if (name.includes('mindful') || name.includes('meditation')) return Brain
    if (name.includes('support') || name.includes('group')) return Users
    return Compass
  }

  // RULE 0001: All hardcoded methods array COMPLETELY REMOVED - using only real database data

  const selectedMethodData = methods.find(m => m.id === selectedMethod)

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-muted text-primary border border-border'
      case 'Easy-Medium': return 'bg-muted text-muted-foreground border border-border'
      case 'Medium': return 'bg-accent text-accent-foreground border border-border'
      case 'Hard': return 'bg-destructive-subtle text-destructive border border-border'
      default: return 'bg-muted text-muted-foreground border border-border'
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <section className="bg-background py-24">
        <div className="max-w-6xl mx-auto px-6 lg:px-8 text-center">
          <div className="w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg">
            <Compass className="w-10 h-10 text-primary-foreground" strokeWidth={2} />
          </div>
          <h1 className="text-6xl font-bold text-foreground mb-8 tracking-tight">
            Quit Methods
          </h1>
          <p className="text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto">
            Evidence-based strategies and step-by-step guides for successful smoking cessation and long-term wellness
          </p>
        </div>
      </section>

      {/* Search and Filter */}
      <section className="bg-card border-b border-border py-12">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-6 items-center">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search quit methods..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-12 pr-4 py-3 border border-border rounded-lg bg-input text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
                />
              </div>
            </div>

            {/* Difficulty Filter */}
            <div className="md:w-64">
              <div className="relative">
                <Filter className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <select
                  value={selectedDifficulty}
                  onChange={(e) => setSelectedDifficulty(e.target.value)}
                  className="w-full pl-12 pr-4 py-3 border border-border rounded-lg bg-input text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring appearance-none"
                >
                  {difficulties.map(difficulty => (
                    <option key={difficulty} value={difficulty}>
                      {difficulty === 'all' ? 'All Difficulties' : difficulty}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Results Count */}
          <div className="mt-6 text-center">
            <p className="text-muted-foreground">
              Showing {filteredMethods.length} of {methods.length} methods
            </p>
          </div>
        </div>
      </section>

      {/* Method Selection */}
      <section className="bg-background border-b border-border py-16">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <h2 className="text-4xl font-bold text-foreground mb-12 text-center">
            Choose Your Quit Method
          </h2>

          {loading ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="p-8 rounded-xl border border-border bg-card">
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-8 h-8 bg-muted rounded animate-pulse"></div>
                    <div className="h-6 bg-muted rounded animate-pulse w-32"></div>
                  </div>
                  <div className="h-4 bg-muted rounded animate-pulse mb-2 w-24"></div>
                  <div className="h-4 bg-muted rounded animate-pulse w-28"></div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-6 mb-12">
              <div className="flex items-center space-x-4">
                <AlertCircle className="w-6 h-6 text-destructive flex-shrink-0" strokeWidth={1.5} />
                <p className="text-destructive font-medium text-lg">{error}</p>
              </div>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredMethods.map((method) => {
                const IconComponent = method.icon
                return (
                  <div key={method.id} className="bg-card p-8 rounded-2xl shadow-lg border border-border hover:shadow-2xl transition-all duration-300">
                    {/* Method Header */}
                    <div className="flex items-start justify-between mb-6">
                      <div className="flex items-center space-x-4">
                        <div className="w-16 h-16 bg-primary rounded-xl flex items-center justify-center shadow-lg">
                          <IconComponent className="w-8 h-8 text-primary-foreground" strokeWidth={2} />
                        </div>
                        <div>
                          <h3 className="font-bold text-card-foreground text-xl leading-tight">
                            {method.name}
                          </h3>
                          <div className="flex items-center space-x-3 mt-2">
                            <span className={`inline-block px-3 py-1 rounded-lg text-sm font-semibold ${getDifficultyColor(method.difficulty)}`}>
                              {method.difficulty}
                            </span>
                            {method.successRate && (
                              <div className="flex items-center space-x-2 text-muted-foreground">
                                <TrendingUp className="w-4 h-4" strokeWidth={1.5} />
                                <span className="font-medium">{method.successRate}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Method Details */}
                    <div className="mb-6">
                      <p className="text-muted-foreground mb-6 line-clamp-3 leading-relaxed">
                        {method.description}
                      </p>

                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground font-medium">Duration:</span>
                          <span className="font-semibold text-card-foreground">{method.duration}</span>
                        </div>
                        {method.whoItsFor && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground font-medium">Best for:</span>
                            <span className="font-semibold text-card-foreground text-right">{method.whoItsFor}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Interactive Actions */}
                    <div className="border-t border-border pt-4">
                      <div className="flex space-x-2 mb-3">
                        <button
                          onClick={() => setSelectedMethod(method.id)}
                          className="flex-1 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:opacity-90 transition-colors flex items-center justify-center space-x-2"
                        >
                          <Play className="w-4 h-4" />
                          <span>Start Method</span>
                        </button>
                        <button className="px-3 py-2 border border-border text-muted-foreground rounded-lg hover:bg-muted transition-colors">
                          <Bookmark className="w-4 h-4" />
                        </button>
                      </div>
                      
                      <button 
                        onClick={() => setSelectedMethod(method.id)}
                        className="w-full text-sm text-green-600 hover:text-green-700 transition-colors flex items-center justify-center space-x-1"
                      >
                        <span>View Details</span>
                        <ArrowRight className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                )
              })}
            </div>
          )}

          {/* No Results Found */}
          {filteredMethods.length === 0 && !loading && !error && methods.length > 0 && (
            <div className="text-center py-20">
              <div className="w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                <Search className="w-10 h-10 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-2xl font-bold text-card-foreground mb-4">No Methods Found</h3>
              <p className="text-muted-foreground text-lg">Try adjusting your search or filter criteria.</p>
            </div>
          )}

          {/* No Methods Available */}
          {!loading && !error && methods.length === 0 && (
            <div className="text-center py-12">
              <div className="w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                <Compass className="w-10 h-10 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-2xl font-bold text-card-foreground mb-4">No Methods Available</h3>
              <p className="text-muted-foreground text-lg">Check back later for quit method options.</p>
            </div>
          )}
        </div>
      </section>

      {/* Method Navigation - Secondary */}
      {methods.length > 0 && (
        <section className="bg-background border-b border-border py-6">
          <div className="max-w-4xl mx-auto px-6">
            <div className="flex flex-wrap gap-4 justify-center">
              <button
                onClick={() => setSelectedMethod('overview')}
                className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                  selectedMethod === 'overview'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground hover:bg-accent'
                }`}
              >
                Overview
              </button>
              {methods.map((method) => (
                <button
                  key={method.id}
                  onClick={() => setSelectedMethod(method.id)}
                  className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                    selectedMethod === method.id
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted text-muted-foreground hover:bg-accent'
                  }`}
                >
                  {method.name}
                </button>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Method Details */}
      {selectedMethodData && (
        <section className="py-12">
          <div className="max-w-6xl mx-auto px-6">
            <div className="bg-card rounded-xl shadow-sm border border-border overflow-hidden">
              {/* Method Header */}
              <div className="bg-muted p-6 border-b border-border">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                    <selectedMethodData.icon className="w-6 h-6 text-primary-foreground" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-foreground">{selectedMethodData.name}</h2>
                    <p className="text-muted-foreground">{selectedMethodData.description}</p>
                  </div>
                </div>
              </div>

              {/* Method Content */}
              <div className="p-6">
                <div className="grid lg:grid-cols-2 gap-8">
                  {/* Pros and Cons */}
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                        <CheckCircle className="w-5 h-5 text-primary mr-2" />
                        Advantages
                      </h3>
                      <ul className="space-y-2">
                        {selectedMethodData.pros.map((pro: string, index: number) => (
                          <li key={index} className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 text-primary mt-1 flex-shrink-0" />
                            <span className="text-muted-foreground">{pro}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                        <Target className="w-5 h-5 text-warning mr-2" />
                        Considerations
                      </h3>
                      <ul className="space-y-2">
                        {selectedMethodData.cons.map((con: string, index: number) => (
                          <li key={index} className="flex items-start space-x-2">
                            <Target className="w-4 h-4 text-warning mt-1 flex-shrink-0" />
                            <span className="text-muted-foreground">{con}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* Who It's For and Tips */}
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <Users className="w-5 h-5 text-blue-500 mr-2" />
                        Who It's For
                      </h3>
                      <p className="text-gray-700 bg-blue-50 p-4 rounded-lg">
                        {selectedMethodData.whoItsFor}
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <Brain className="w-5 h-5 text-purple-500 mr-2" />
                        Tips for Success
                      </h3>
                      <ul className="space-y-2">
                        {selectedMethodData.tips.map((tip: string, index: number) => (
                          <li key={index} className="flex items-start space-x-2">
                            <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
                              <span className="text-purple-600 text-xs font-medium">{index + 1}</span>
                            </div>
                            <span className="text-gray-700">{tip}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="mt-8 pt-6 border-t border-gray-100">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900">{selectedMethodData.duration}</div>
                      <div className="text-sm text-gray-600">Duration</div>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900">{selectedMethodData.successRate}</div>
                      <div className="text-sm text-gray-600">Success Rate</div>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(selectedMethodData.difficulty)}`}>
                        {selectedMethodData.difficulty}
                      </div>
                      <div className="text-sm text-gray-600 mt-1">Difficulty</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Overview Section */}
      {selectedMethod === 'overview' && (
        <section className="py-12">
          <div className="max-w-4xl mx-auto px-6">
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                Choosing the Right Method for You
              </h2>
              <div className="space-y-6">
                <p className="text-gray-700 leading-relaxed">
                  There's no one-size-fits-all approach to quitting nicotine. The best method depends on your personal habits, 
                  support system, health status, and preferences. Research shows that combining multiple approaches often 
                  increases success rates.
                </p>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="bg-blue-50 p-6 rounded-lg">
                    <h3 className="font-semibold text-blue-900 mb-3">Consider These Factors:</h3>
                    <ul className="space-y-2 text-blue-800">
                      <li>• How long you've been using nicotine</li>
                      <li>• Your current usage level</li>
                      <li>• Previous quit attempts</li>
                      <li>• Available support system</li>
                      <li>• Health conditions</li>
                      <li>• Personal preferences</li>
                    </ul>
                  </div>
                  <div className="bg-muted p-6 rounded-lg">
                    <h3 className="font-semibold text-green-900 mb-3">Combination Approaches:</h3>
                    <ul className="space-y-2 text-green-800">
                      <li>• NRT + Behavioral therapy</li>
                      <li>• Support groups + Gradual reduction</li>
                      <li>• Professional counseling + Medication</li>
                      <li>• Mindfulness + Community support</li>
                      <li>• Digital tools + Peer accountability</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="bg-gray-100 py-16">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Start Your Journey?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Get personalized support and track your progress with Mission Fresh's comprehensive wellness tools.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              to="/tools/nrt-guide"
              className="btn-secondary inline-flex items-center justify-center px-8 py-3 rounded-lg text-lg font-medium transition-colors"
            >
              <Heart className="w-5 h-5 mr-2" />
              NRT Guide
              <ArrowRight className="w-5 h-5 ml-2" />
            </Link>
            <Link
              to="/auth?mode=signup"
              className="btn-primary inline-flex items-center justify-center px-8 py-3 rounded-lg text-lg font-medium transition-colors"
            >
              Get Started
              <ArrowRight className="w-5 h-5 ml-2" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
