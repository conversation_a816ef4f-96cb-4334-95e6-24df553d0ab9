import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Eye, 
  Bot, 
  Send, 
  TrendingUp, 
  Shield 
} from 'lucide-react'
import { useState, useCallback, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { supabase } from '../lib/supabase'

// RULE 0001: Dynamic conversation starters from database - hardcoded array removed
// Conversation starters will be loaded from conversation_starters table

// AI Message interface
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  suggestions?: string[];
}


// AI Coach service integration
const getAICoachResponse = async (
  userMessage: string,
  conversationHistory?: Array<{ role: string; content: string }>
): Promise<string | null> => {
  if (!userMessage || typeof userMessage !== 'string') {
    return "I'd love to help you! Please share what's on your mind about your quit journey.";
  }

  try {
    // Use direct Gemini API
    const prompt = `You are "FreshA<PERSON>", a friendly, empathetic, and highly knowledgeable AI quit smoking coach. Your goal is to support users in their journey to quit nicotine. Be encouraging, provide practical advice, and help users explore their feelings and triggers. Keep responses concise and actionable (under 200 words). Avoid making medical claims or giving medical advice; instead, suggest users consult healthcare professionals for medical concerns.

Current user message:
"${userMessage}"

Conversation context (if any):`;

    let fullPrompt = prompt;
    if (conversationHistory && conversationHistory.length > 0) {
      const historySnippet = conversationHistory
        .slice(-4)
        .map(msg => `${msg.role === 'user' ? 'User' : 'Coach'}: ${msg.content}`)
        .join('\n');
      fullPrompt += `\n${historySnippet}`;
    } else {
      fullPrompt += "\nNo previous conversation context for this session.";
    }

    fullPrompt += `\n\nPlease provide a supportive, encouraging response as FreshAI, the quit smoking coach. Be warm, understanding, and offer practical advice.`;

    console.log(' Calling Supabase Gemini function for AI Coach response...');

    // Use Supabase function that securely handles Gemini API
    const { data, error } = await supabase.rpc('get_gemini_suggestions', {
      user_input: userMessage,
      user_id: 'anonymous' // For non-authenticated users
    });

    if (error) {
      console.log('Error calling Supabase Gemini function:', error);
      return generateLocalAIResponse(userMessage);
    }

    if (data?.suggestion) {
      console.log(' Supabase Gemini function response received successfully');
      return data.suggestion;
    } else {
      console.log(' Supabase Gemini function failed, using local AI coach');
      return generateLocalAIResponse(userMessage);
    }

  } catch (error) {
    console.log('Error calling Supabase Gemini function, using local AI coach:', error);
    // Use local AI response generator as fallback
    return generateLocalAIResponse(userMessage);
  }
};

export default function LandingPage() {
  // AI Chat state directly in component
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [featuresLoading, setFeaturesLoading] = useState(true);
  const [conversationStarters, setConversationStarters] = useState<string[]>([]);

  // CRITICAL: Add execution flags to prevent infinite loops
  const isExecutingRef = useRef(false);
  const lastExecutionRef = useRef(0);

  // Load conversation starters from database - HOLY RULE 1 compliance
  useEffect(() => {
    const loadConversationStarters = async () => {
      try {
        const { data, error } = await supabase
          .from('conversation_starters')
          .select('text')
          .eq('active', true)
          .order('sort_order', { ascending: true });

        if (error) {
          console.error('Error loading conversation starters:', error);
          // HOLY RULE 12: Static conversation starters are acceptable as frontend fallbacks
          setConversationStarters([
            "How do I start my quit journey?",
            "What are the best quit methods?",
            "How do I handle cravings?",
            "Tell me about withdrawal symptoms"
          ]);
        } else {
          const starters = data?.map(item => item.text) || [];
          setConversationStarters(starters.length > 0 ? starters : [
            "How do I start my quit journey?",
            "What are the best quit methods?",
            "How do I handle cravings?",
            "Tell me about withdrawal symptoms"
          ]);
        }
      } catch (error) {
        console.error('Error loading conversation starters:', error);
        // HOLY RULE 12: Static conversation starters are acceptable as frontend fallbacks
        setConversationStarters([
          "How do I start my quit journey?",
          "What are the best quit methods?",
          "How do I handle cravings?",
          "Tell me about withdrawal symptoms"
        ]);
      }
    };

    loadConversationStarters();
  }, []);

  const handleSendMessage = useCallback(async (message: string) => {
    if (!message.trim()) return;

    // CRITICAL: Prevent infinite loops
    const now = Date.now();
    if (isExecutingRef.current || (now - lastExecutionRef.current < 1000)) {
      console.log(' LOOP PREVENTION: handleSendMessage blocked - too frequent calls');
      return;
    }

    isExecutingRef.current = true;
    lastExecutionRef.current = now;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: message,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    try {
      // Use the working AI service with Gemini API integration
      const conversationHistory = messages.map(m => ({
        role: m.sender === 'user' ? 'user' : 'assistant',
        content: m.content
      }));

      const aiResponseText = await getAICoachResponse(message, conversationHistory);

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: aiResponseText || "I'm here to help with your quit smoking journey! How can I support you today?",
        sender: 'ai',
        timestamp: new Date(),
        suggestions: conversationStarters.slice(0, 3)
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error calling AI coach:', error);
      // Provide helpful fallback response
      const fallbackMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "I'm here to support your quit smoking journey! I can help with cravings, withdrawal symptoms, motivation, and more. What would you like to talk about?",
        sender: 'ai',
        timestamp: new Date(),
        suggestions: conversationStarters.slice(0, 3)
      };
      setMessages(prev => [...prev, fallbackMessage]);
    } finally {
      setIsTyping(false);
      // Reset execution flag after delay
      setTimeout(() => {
        isExecutingRef.current = false;
      }, 1000);
    }
  }, [messages]);

  const handleStartChat = useCallback(async (event?: React.MouseEvent) => {
    // ULTRA-CRITICAL: Bulletproof loop prevention
    const now = Date.now();
    if (isExecutingRef.current || (now - lastExecutionRef.current < 2000)) {
      console.log(' LOOP PREVENTION: handleStartChat blocked - too frequent calls');
      return;
    }
    
    // Prevent default and stop propagation immediately
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    
    // Set execution flags IMMEDIATELY to prevent any possibility of re-entry
    isExecutingRef.current = true;
    lastExecutionRef.current = now;
    
    console.log(' DEBUG: handleStartChat called safely - execution flags set');
    
    try {
      setShowChat(true);
      console.log(' DEBUG: showChat set to true');
      
      if (messages.length === 0) {
        setIsTyping(true);
        console.log(' DEBUG: Creating welcome message safely...');
        
        // Create welcome message with proper safeguards
        const fallbackMessage: Message = {
          id: 'welcome-' + Date.now(),
          content: "Hi there! I'm Fresh Assistant, your 24/7 wellness coach. I'm here to help you on your journey to a smoke-free life. How can I support you today?",
          sender: 'ai',
          timestamp: new Date(),
          suggestions: conversationStarters.slice(0, 4)
        };
        
        setTimeout(() => {
          setMessages([fallbackMessage]);
          setIsTyping(false);
          console.log(' DEBUG: Welcome message created safely');
        }, 800);
      }
    } catch (error) {
      console.error(' Error in handleStartChat:', error);
      setIsTyping(false);
    } finally {
      // Reset execution flag after extended delay to prevent rapid re-execution
      setTimeout(() => {
        isExecutingRef.current = false;
        console.log(' DEBUG: handleStartChat execution flag reset');
      }, 3000);
    }
  }, [setMessages, setIsTyping, setShowChat, messages.length]);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-16 bg-background">
        <div className="container max-w-7xl mx-auto px-6 flex flex-col-reverse lg:flex-row items-start gap-12">
          {/* Left Column - Hero Content */}
          <div className="w-full lg:w-2/3">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className=""
            >
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-semibold text-foreground leading-tight tracking-tight mb-6">
                Take the first step
                <span className="block font-bold text-primary">to a smoke-free future</span>
              </h1>

              <p className="text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto lg:mx-0 mb-8">
                Get instant, personalized support for your quit smoking journey.
                Our Fresh Assistant provides proven strategies and motivates you
                every step of the way.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start mt-8">
                <Link
                  to="/auth?action=signup"
                  className="group font-semibold min-w-[200px] h-14 text-lg shadow-lg hover:shadow-xl transition-all duration-200 bg-primary text-primary-foreground flex items-center justify-center gap-2 rounded-lg px-6"
                  aria-label="Start your smoke-free journey by signing up"
                  role="button"
                >
                  <Sparkles className="w-4 h-4" strokeWidth={2} aria-hidden="true" />
                  <span>Start Your Journey</span>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" strokeWidth={2} aria-hidden="true" />
                </Link>

                <Link
                  to="/how-it-works"
                  className="group font-semibold min-w-[200px] h-14 text-lg shadow-lg hover:shadow-xl transition-all duration-200 border border-border bg-background text-foreground flex items-center justify-center gap-2 rounded-lg px-6 hover:bg-accent"
                >
                  <Eye className="w-4 h-4" strokeWidth={2} aria-hidden="true" />
                  <span>Learn How It Works</span>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" strokeWidth={2} aria-hidden="true" />
                </Link>
              </div>
            </motion.div>
          </div>

          {/* Right Column - AI Chat */}
          <div className="w-full lg:w-1/3 mt-8 lg:mt-0">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1, duration: 0.6 }}
            >
              <div className="w-full h-auto bg-background border border-border/50 shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg">
                <div className="p-6 border-b border-border/50 bg-background rounded-t-lg">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-primary flex items-center justify-center border border-primary rounded-full">
                      <Bot className="w-6 h-6 text-primary-foreground" strokeWidth={2} />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-foreground tracking-tight">Fresh Assistant</h3>
                      <p className="text-muted-foreground text-sm">
                        Your 24/7 wellness coach
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-6 space-y-4">
                  <p className="text-lg text-muted-foreground">Start a conversation:</p>
                  <div className="flex flex-col gap-4 w-full">
                    {conversationStarters.slice(0, 3).map((starter) => (
                      <button
                        key={starter}
                        className="w-full h-14 text-base font-medium text-left justify-start px-6 py-4 transition-all duration-200 border border-border bg-background text-foreground rounded-3xl hover:bg-accent"
                        onClick={async () => {
                          setInputValue(starter);
                          await handleSendMessage(starter);
                        }}
                      >
                        {starter}
                      </button>
                    ))}
                  </div>

                  <div className="mt-6 pt-6 border-t border-border">
                    <div className="flex items-center gap-4">
                      <input
                        type="text"
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onKeyDown={async (e) => {
                          if (e.key === 'Enter' && inputValue.trim()) {
                            await handleSendMessage(inputValue);
                          }
                        }}
                        placeholder="Type your question..."
                        className="flex-1 h-14 px-6 text-lg border border-border bg-input text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring rounded-3xl"
                      />
                      <button
                        onClick={async () => {
                          if (inputValue.trim()) {
                            await handleSendMessage(inputValue);
                          }
                        }}
                        disabled={!inputValue.trim()}
                        className="w-14 h-14 flex-shrink-0 transition-all duration-200 bg-primary text-primary-foreground rounded-full flex items-center justify-center disabled:opacity-50"
                      >
                        <Send className="w-5 h-5" strokeWidth={2} />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="bg-card border-y border-border py-20">
        <div className="container max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-semibold text-foreground tracking-tight mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground mx-auto leading-relaxed max-w-3xl">
              Our proven process helps you quit smoking naturally, with personalized support every step of the way.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1, duration: 0.6 }}
              className="text-center bg-background p-8 rounded-lg border border-border shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <Target className="w-8 h-8 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-4">Sign Up & Define Your Journey</h3>
              <p className="text-muted-foreground leading-relaxed">
                Create your personalized quit plan based on your smoking habits, goals, and preferences.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="text-center bg-background p-8 rounded-lg border border-border shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <TrendingUp className="w-8 h-8 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-4">Track Your Daily Progress</h3>
              <p className="text-muted-foreground leading-relaxed">
                Monitor your journey with real-time tracking of milestones, health improvements, and savings.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-center bg-background p-8 rounded-lg border border-border shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <Eye className="w-8 h-8 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-4">Visualize Your Growth</h3>
              <p className="text-muted-foreground leading-relaxed">
                See your progress through beautiful charts and celebrate every milestone achieved.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Web Tools Showcase Section */}
      <section className="py-20 bg-background">
        <div className="container max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground tracking-tight mb-6">
              Comprehensive Wellness Tools
            </h2>
            <p className="text-xl text-muted-foreground mx-auto leading-relaxed max-w-3xl">
              Access our complete suite of evidence-based tools and resources designed to support every aspect of your quit smoking journey and long-term wellness.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1, duration: 0.8, ease: "easeOut" }}
              className="bg-card border border-border rounded-xl p-10 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-6">
                <Shield className="w-6 h-6 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">NRT Guide</h3>
              <p className="text-muted-foreground leading-relaxed mb-4">
                Expert guidance and personalized recommendations for nicotine replacement therapy, tailored to your needs.
              </p>
              <Link
                to="/tools/nrt-guide"
                className="text-primary hover:text-primary/80 font-medium"
                aria-label="Learn more about NRT Guide - Expert guidance for nicotine replacement therapy"
              >
                Learn More →
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="bg-card border border-border rounded-xl p-10 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-6">
                <Target className="w-6 h-6 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Smokeless Directory</h3>
              <p className="text-muted-foreground leading-relaxed mb-4">
                Comprehensive, curated directory of smokeless alternatives with expert reviews and recommendations.
              </p>
              <Link to="/tools/smokeless-directory" className="text-primary hover:text-primary/80 font-medium">
                Explore Directory →
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="bg-card border border-border rounded-xl p-10 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-6">
                <Brain className="w-6 h-6 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Quitting Methods</h3>
              <p className="text-muted-foreground leading-relaxed mb-4">
                Evidence-based strategies and step-by-step guides for successful smoking cessation and long-term wellness.
              </p>
              <Link to="/tools/quit-methods" className="text-primary hover:text-primary/80 font-medium">
                Find Your Method →
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className="bg-card border border-border rounded-xl p-10 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-6">
                <TrendingUp className="w-6 h-6 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Wellness Calculator</h3>
              <p className="text-muted-foreground leading-relaxed mb-4">
                Track your progress, calculate health improvements, and visualize your journey to better wellness.
              </p>
              <Link to="/tools/calculators" className="text-primary hover:text-primary/80 font-medium">
                Calculate Progress →
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.5, duration: 0.6 }}
              className="bg-card border border-border rounded-xl p-10 hover:shadow-xl transition-all duration-300"
            >
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-6">
                <Users className="w-6 h-6 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Holistic Wellness</h3>
              <p className="text-muted-foreground leading-relaxed mb-4">
                Comprehensive guides and tools for improving sleep quality, energy levels, and mental focus.
              </p>
              <Link to="/tools/holistic-health" className="text-primary hover:text-primary/80 font-medium">
                Explore Wellness →
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="bg-card border border-border rounded-lg p-8 hover:shadow-lg transition-all duration-200"
            >
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-6">
                <ArrowRight className="w-6 h-6 text-primary-foreground" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Explore All Tools</h3>
              <p className="text-muted-foreground leading-relaxed mb-4">
                Access our complete suite of wellness tools, resources, and expert guidance for your journey.
              </p>
              <Link to="/tools" className="text-primary hover:text-primary/80 font-medium">
                View All Tools →
              </Link>
            </motion.div>
          </div>
        </div>
      </section>



      {/* Features Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Why Choose Mission Fresh?
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our comprehensive approach combines cutting-edge technology with proven
              methods to give you the best chance of success.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-card border border-border rounded-full flex items-center justify-center mx-auto mb-4">
                <Brain className="w-8 h-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">AI-Powered Support</h3>
              <p className="text-muted-foreground">
                Get personalized guidance from our intelligent assistant that learns
                your patterns and provides tailored advice.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-card border border-border rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="w-8 h-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Personalized Plans</h3>
              <p className="text-muted-foreground">
                Customized quit plans based on your smoking history, preferences,
                and goals for maximum effectiveness.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-card border border-border rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-3">Community Support</h3>
              <p className="text-muted-foreground">
                Connect with others on the same journey. Share experiences, get
                encouragement, and celebrate victories together.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-semibold mb-4">
            Ready to Start Your Smoke-Free Journey?
          </h2>
          <p className="text-xl text-primary-foreground/80 mb-8 max-w-2xl mx-auto">
            Join thousands who've successfully quit with Mission Fresh
          </p>
          <Link
            to="/auth?action=signup"
            className="inline-flex items-center gap-2 bg-secondary text-secondary-foreground px-8 py-4 rounded-xl font-semibold hover:bg-secondary-hover transition-all duration-300 border border-border shadow-lg hover:shadow-xl"
          >
            <Sparkles className="w-5 h-5" strokeWidth={2} />
            Get Started Now
            <ArrowRight className="w-5 h-5" strokeWidth={2} />
          </Link>
        </div>
      </section>
    </div>
  )
}
