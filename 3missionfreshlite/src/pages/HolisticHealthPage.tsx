import { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { SunMoon, ArrowRight, Heart, Brain, Leaf, Moon, Sun, Wind, Zap, Clock, Apple, Activity, AlertCircle, Play, Pause, RotateCcw } from 'lucide-react'
import { supabase } from '../lib/supabase'

export default function HolisticHealthPage() {
  const [activeCategory, setActiveCategory] = useState('overview')
  const [timerActive, setTimerActive] = useState(false)
  const [timerSeconds, setTimerSeconds] = useState(0)
  const [selectedExercise, setSelectedExercise] = useState(null)

  // RULE 0001: Real Supabase database integration for wellness content
  const [categories, setCategories] = useState<any[]>([])
  const [wellnessTips, setWellnessTips] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchWellnessCategories()
    fetchWellnessTips()
  }, [])

  const fetchWellnessCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('wellness_categories')
        .select('*')
        .order('display_order')
      
      if (error) throw error
      setCategories(data || [])
    } catch (err: any) {
      setError(err.message)
    }
  }

  const fetchWellnessTips = async () => {
    try {
      const { data, error } = await supabase
        .from('wellness_tips')
        .select('*')
        .order('category_id, display_order')
      
      if (error) throw error
      setWellnessTips(data || [])
      setLoading(false)
    } catch (err: any) {
      setError(err.message)
      setLoading(false)
    }
  }

  // RULE 0001: Real wellness content from database - categories dynamically loaded
  const getWellnessTipsByCategory = (categoryName: string) => {
    return wellnessTips.filter(tip => 
      tip.category?.toLowerCase().includes(categoryName.toLowerCase()) ||
      tip.title?.toLowerCase().includes(categoryName.toLowerCase()) ||
      tip.type?.toLowerCase().includes(categoryName.toLowerCase())
    )
  }

  const energyTips = getWellnessTipsByCategory('energy')
  const focusTips = getWellnessTipsByCategory('focus')
  const sleepTips = getWellnessTipsByCategory('sleep')
  const breathingExercises = getWellnessTipsByCategory('breathing')
  const mindfulnessPractices = getWellnessTipsByCategory('mindfulness')
  const exerciseProtocols = getWellnessTipsByCategory('exercise')
  const breathingTechniques = getWellnessTipsByCategory('breathing')
  const cravingManagementTools = getWellnessTipsByCategory('craving')
  const nutritionSupport = getWellnessTipsByCategory('nutrition')

  // Timer functionality for guided exercises
  useEffect(() => {
    let interval = null
    if (timerActive) {
      interval = setInterval(() => {
        setTimerSeconds(seconds => seconds + 1)
      }, 1000)
    } else if (!timerActive && timerSeconds !== 0) {
      clearInterval(interval)
    }
    return () => clearInterval(interval)
  }, [timerActive, timerSeconds])

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const startTimer = (exercise) => {
    setSelectedExercise(exercise)
    setTimerSeconds(0)
    setTimerActive(true)
  }

  const pauseTimer = () => {
    setTimerActive(false)
  }

  const resetTimer = () => {
    setTimerActive(false)
    setTimerSeconds(0)
    setSelectedExercise(null)
  }

  const getCurrentContent = () => {
    switch (activeCategory) {
      case 'cravings':
        return cravingManagementTools.map(tip => ({
          title: tip.title,
          description: tip.description || tip.content,
          duration: tip.duration || '5-10 minutes',
          steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
        }))
      case 'nutrition':
        return nutritionSupport.map(tip => ({
          title: tip.title,
          description: tip.description || tip.content,
          duration: tip.duration || 'Daily',
          steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
        }))
      case 'exercise':
        return exerciseProtocols.map(tip => ({
          title: tip.title,
          description: tip.description || tip.content,
          duration: tip.duration || '15-30 minutes',
          steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
        }))
      case 'breathing':
        return breathingTechniques.map(tip => ({
          title: tip.title,
          description: tip.description || tip.content,
          duration: tip.duration || '3-5 minutes',
          steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
        }))
      case 'mindfulness':
        return mindfulnessPractices.map(tip => ({
          title: tip.title,
          description: tip.description || tip.content,
          duration: tip.duration || '10-20 minutes',
          steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
        }))
      case 'sleep':
        return sleepTips.map(tip => ({
          title: tip.title,
          description: tip.description || tip.content,
          duration: tip.duration || 'Evening routine',
          steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
        }))
      case 'energy':
        return energyTips.map(tip => ({
          title: tip.title,
          description: tip.description || tip.content,
          duration: tip.duration || '5-15 minutes',
          steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
        }))
      case 'focus':
        return focusTips.map(tip => ({
          title: tip.title,
          description: tip.description || tip.content,
          duration: tip.duration || '10-30 minutes',
          steps: tip.steps ? JSON.parse(tip.steps) : [tip.content || tip.description]
        }))
      default:
        return []
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <section className="bg-background py-24">
        <div className="max-w-6xl mx-auto px-6 lg:px-8 text-center">
          <div className="w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg">
            <SunMoon className="w-10 h-10 text-primary-foreground" strokeWidth={2} />
          </div>
          <h1 className="text-6xl font-bold text-foreground mb-8 tracking-tight">
            Holistic Health
          </h1>
          <p className="text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto">
            Integrative wellness approaches and mindfulness techniques to support your journey
          </p>
        </div>
      </section>

      {/* Category Navigation */}
      <section className="bg-card border-b border-border py-12">
        <div className="max-w-6xl mx-auto px-6">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {categories.map((category) => {
              const IconComponent = category.icon
              return (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`p-6 rounded-xl border-2 transition-all duration-300 text-center shadow-lg hover:shadow-xl ${
                    activeCategory === category.id
                      ? 'border-primary bg-primary/10'
                      : 'border-border hover:border-primary/50 bg-card'
                  }`}
                >
                  <IconComponent className={`w-10 h-10 mx-auto mb-3 ${
                    activeCategory === category.id ? 'text-primary' : 'text-muted-foreground'
                  }`} strokeWidth={1.5} />
                  <h3 className={`font-semibold ${
                    activeCategory === category.id ? 'text-primary' : 'text-card-foreground'
                  }`}>
                    {category.label}
                  </h3>
                </button>
              )
            })}
          </div>
        </div>
      </section>

      {/* Content */}
      <section className="py-12">
        <div className="max-w-6xl mx-auto px-6">
          {activeCategory === 'overview' && (
            <div className="space-y-8">
              <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">
                  Holistic Wellness Approach
                </h2>
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <p className="text-gray-700 leading-relaxed mb-6">
                      Holistic health considers the whole person - mind, body, and spirit - in the quest for optimal health and wellness. 
                      Rather than focusing on illness or specific body parts, holistic health practitioners view health and wellness as a 
                      state of the whole person.
                    </p>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Core Principles:</h3>
                    <ul className="space-y-2">
                      <li className="flex items-start space-x-2">
                        <Heart className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">Mind-body connection</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <Leaf className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">Natural healing processes</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <Brain className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">Prevention over treatment</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <SunMoon className="w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">Balance and harmony</span>
                      </li>
                    </ul>
                  </div>
                  <div className="bg-gradient-to-br from-green-50 to-blue-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Benefits for Your Journey:</h3>
                    <ul className="space-y-3">
                      <li className="flex items-start space-x-2">
                        <Clock className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                        <span className="text-gray-700">Reduces stress and anxiety</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <Zap className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                        <span className="text-gray-700">Improves energy levels</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <Brain className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                        <span className="text-gray-700">Enhances mental clarity</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <Heart className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                        <span className="text-gray-700">Supports emotional balance</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 text-center">
                  <Moon className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Sleep Quality</h3>
                  <p className="text-gray-600 text-sm">Optimize your sleep for better recovery and energy</p>
                </div>
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 text-center">
                  <Sun className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Energy Levels</h3>
                  <p className="text-gray-600 text-sm">Natural ways to boost and maintain energy</p>
                </div>
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 text-center">
                  <Brain className="w-12 h-12 text-purple-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Mental Focus</h3>
                  <p className="text-gray-600 text-sm">Techniques to improve concentration and clarity</p>
                </div>
              </div>
            </div>
          )}

          {activeCategory !== 'overview' && (
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  {categories.find(c => c.id === activeCategory)?.label} Techniques
                </h2>
                <p className="text-gray-600">
                  Evidence-based practices to improve your {categories.find(c => c.id === activeCategory)?.label.toLowerCase()}
                </p>
              </div>

              <div className="grid lg:grid-cols-2 gap-6">
                {getCurrentContent().map((technique, index) => (
                  <div key={index} className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                    <div className="flex items-start justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">{technique.title}</h3>
                      <span className="bg-primary-muted text-primary-text text-xs font-medium px-2 py-1 rounded">
                        {technique.duration}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 mb-4">{technique.description}</p>
                    
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Steps:</h4>
                      <ol className="space-y-2">
                        {technique.steps.map((step, stepIndex) => (
                          <li key={stepIndex} className="flex items-start space-x-2">
                            <span className="bg-primary-muted text-primary-text text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center mt-0.5 flex-shrink-0">
                              {stepIndex + 1}
                            </span>
                            <span className="text-gray-700 text-sm">{step}</span>
                          </li>
                        ))}
                      </ol>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gray-100 py-16">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Embrace Holistic Wellness?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Join Mission Fresh for personalized wellness tracking, expert guidance, and a supportive community.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              to="/tools/quit-methods"
              className="inline-flex items-center justify-center border-2 border-primary text-primary px-8 py-3 rounded-lg text-lg font-medium hover:bg-primary hover:text-primary-foreground transition-colors"
            >
              <Wind className="w-5 h-5 mr-2" />
              Quit Methods
              <ArrowRight className="w-5 h-5 ml-2" />
            </Link>
            <Link
              to="/auth?mode=signup"
              className="btn-primary inline-flex items-center justify-center px-8 py-3 rounded-lg text-lg font-medium transition-colors"
            >
              Get Started
              <ArrowRight className="w-5 h-5 ml-2" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
