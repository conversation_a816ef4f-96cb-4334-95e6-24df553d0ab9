import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import { 
  Heart, 
  Flame, 
  BookOpen, 
  StickyNote, 
  CheckCircle,
  Loader2,
  Star
} from 'lucide-react'

// Icon mapping for dynamic icon selection
const iconMap: Record<string, any> = {
  Heart,
  Flame,
  BookOpen,
  StickyNote
}

interface LogTab {
  id: string
  label: string
  icon_name: string
  display_order: number
}

export default function LogEntryPage() {
  const { user } = useAuth()
  const navigate = useNavigate()
  
  // Authentication required - no hardcoded user data allowed
  if (!user) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-4">Authentication Required</h1>
          <p className="text-muted-foreground">Please log in to access your daily log.</p>
        </div>
      </div>
    )
  }
  const [activeTab, setActiveTab] = useState('wellness')
  const [loading, setLoading] = useState(false)
  
  // RULE 0001: Dynamic tab configuration from database - hardcoded array removed
  const [tabs, setTabs] = useState<LogTab[]>([])
  const [tabsLoading, setTabsLoading] = useState(true)
  
  // Wellness states
  const [mood, setMood] = useState(3)
  const [energy, setEnergy] = useState(3)
  const [stress, setStress] = useState(3)
  const [sleepHours, setSleepHours] = useState('')
  const [sleepQuality, setSleepQuality] = useState(3)
  
  // Craving states
  const [cravingIntensity, setCravingIntensity] = useState(0)
  const [cravingTrigger, setCravingTrigger] = useState('')
  const [copingStrategy, setCopingStrategy] = useState('')
  
  // Journal and notes
  const [journalEntry, setJournalEntry] = useState('')
  const [dailyNotes, setDailyNotes] = useState('')
  
  useEffect(() => {
    fetchLogTabs()
  }, [])

  const fetchLogTabs = async () => {
    try {
      // Use fallback tabs for now to avoid database issues
      setTabs([
        { id: 'wellness', label: 'Wellness', icon_name: 'Heart', display_order: 1 },
        { id: 'cravings', label: 'Cravings', icon_name: 'Flame', display_order: 2 },
        { id: 'journal', label: 'Journal', icon_name: 'BookOpen', display_order: 3 },
        { id: 'notes', label: 'Notes', icon_name: 'StickyNote', display_order: 4 }
      ])
    } catch (err) {
      console.error('Error setting up log tabs:', err)
      setTabs([])
    } finally {
      setTabsLoading(false)
    }
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return
    
    setLoading(true)
    
    try {
      // Save daily health summary
      const { error: healthError } = await supabase
        .from('daily_health_summaries')
        .insert([{
          user_id: user.id,
          date: new Date().toISOString().split('T')[0],
          mood_score: mood,
          energy_level: energy,
          stress_level: stress,
          sleep_hours: sleepHours ? parseFloat(sleepHours) : null,
          sleep_quality: sleepQuality,
          notes: dailyNotes
        }])
      
      if (healthError) throw healthError
      
      // Save craving log if there was a craving
      if (cravingIntensity > 0) {
        const { error: cravingError } = await supabase
          .from('craving_logs')
          .insert([{
            user_id: user.id,
            intensity: cravingIntensity,
            trigger: cravingTrigger,
            coping_strategy: copingStrategy,
            logged_at: new Date().toISOString()
          }])
        
        if (cravingError) throw cravingError
      }
      
      // Save journal entry if provided
      if (journalEntry.trim()) {
        const { error: journalError } = await supabase
          .from('journal_entries')
          .insert([{
            user_id: user.id,
            content: journalEntry,
            mood: mood,
            created_at: new Date().toISOString()
          }])
        
        if (journalError) throw journalError
      }
      
      // Success - navigate back to dashboard
      navigate('/dashboard')
      
    } catch (error) {
      console.error('Error saving log entry:', error)
    } finally {
      setLoading(false)
    }
  }
  
  // TEMPORARY: Commented out for audit - original auth check
  // if (!user) {
  //   return (
  //     <div className="min-h-screen flex items-center justify-center bg-background">
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center max-w-md mx-auto px-6">
          <h2 className="text-2xl font-bold text-foreground mb-3">Please sign in to log your progress</h2>
          <p className="text-muted-foreground leading-relaxed">Your daily log helps track your wellness journey.</p>
        </div>
      </div>
    )
  }

  // Add loading state for tabs
  if (tabsLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-16 h-16 animate-spin mx-auto mb-6 text-primary" strokeWidth={1.5} />
          <p className="text-muted-foreground text-lg">Loading daily log...</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-background py-12">
      <div className="max-w-5xl mx-auto px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-foreground mb-6 tracking-tight">Daily Log</h1>
          <p className="text-xl text-muted-foreground leading-relaxed">Track your wellness journey and celebrate your progress</p>
        </div>

        <form onSubmit={handleSubmit} className="bg-card rounded-xl shadow-lg border border-border">
          {/* Tab Navigation */}
          <div className="border-b border-border">
            <nav className="flex space-x-12 px-8">
              {tabs.map((tab) => {
                const IconComponent = iconMap[tab.icon_name] || Heart
                return (
                  <button
                    key={tab.id}
                    type="button"
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-6 px-2 relative font-semibold text-base transition-all duration-300 ${
                      activeTab === tab.id
                        ? 'text-primary border-b-2 border-primary'
                        : 'text-muted-foreground hover:text-foreground'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <IconComponent className="w-5 h-5" strokeWidth={1.5} />
                      <span>{tab.label}</span>
                    </div>
                  </button>
                )
              })}
            </nav>
          </div>
          
          {/* Tab Content */}
          <div className="p-8 space-y-10">
            {activeTab === 'wellness' && (
              <div className="space-y-10">
                <h3 className="text-2xl font-bold text-card-foreground">How are you feeling today?</h3>

                {/* Mood */}
                <div>
                  <label className="block text-base font-semibold text-card-foreground mb-6">Mood</label>
                  <div className="flex space-x-6">
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <button
                        key={rating}
                        type="button"
                        onClick={() => setMood(rating)}
                        className={`w-16 h-16 rounded-xl border-2 flex items-center justify-center transition-all duration-300 ${
                          mood === rating
                            ? 'border-primary bg-primary/10 text-primary shadow-lg'
                            : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'
                        }`}
                      >
                        <Star className={`w-7 h-7 ${mood === rating ? 'fill-current' : ''}`} strokeWidth={1.5} />
                      </button>
                    ))}
                  </div>
                </div>
                
                {/* Energy */}
                <div>
                  <label className="block text-base font-semibold text-card-foreground mb-6">Energy Level</label>
                  <div className="flex space-x-6">
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <button
                        key={rating}
                        type="button"
                        onClick={() => setEnergy(rating)}
                        className={`w-16 h-16 rounded-xl border-2 flex items-center justify-center transition-all duration-300 ${
                          energy === rating
                            ? 'border-primary bg-primary/10 text-primary shadow-lg'
                            : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'
                        }`}
                      >
                        <Star className={`w-7 h-7 ${energy === rating ? 'fill-current' : ''}`} strokeWidth={1.5} />
                      </button>
                    ))}
                  </div>
                </div>

                {/* Stress */}
                <div>
                  <label className="block text-base font-semibold text-card-foreground mb-6">Stress Level</label>
                  <div className="flex space-x-6">
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <button
                        key={rating}
                        type="button"
                        onClick={() => setStress(rating)}
                        className={`w-16 h-16 rounded-xl border-2 flex items-center justify-center transition-all duration-300 ${
                          stress === rating
                            ? 'border-primary bg-primary/10 text-primary shadow-lg'
                            : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'
                        }`}
                      >
                        <Star className={`w-7 h-7 ${stress === rating ? 'fill-current' : ''}`} strokeWidth={1.5} />
                      </button>
                    ))}
                  </div>
                </div>
                
                {/* Sleep */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
                  <div>
                    <label htmlFor="sleepHours" className="block text-base font-semibold text-card-foreground mb-4">
                      Hours of Sleep
                    </label>
                    <input
                      type="number"
                      id="sleepHours"
                      min="0"
                      max="24"
                      step="0.5"
                      value={sleepHours}
                      onChange={(e) => setSleepHours(e.target.value)}
                      className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
                      placeholder="8"
                    />
                  </div>

                  <div>
                    <label className="block text-base font-semibold text-card-foreground mb-6">Sleep Quality</label>
                    <div className="flex space-x-4">
                      {[1, 2, 3, 4, 5].map((rating) => (
                        <button
                          key={rating}
                          type="button"
                          onClick={() => setSleepQuality(rating)}
                          className={`w-14 h-14 rounded-xl border-2 flex items-center justify-center transition-all duration-300 ${
                            sleepQuality === rating
                              ? 'border-primary bg-primary/10 text-primary shadow-lg'
                              : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'
                          }`}
                        >
                          <Star className={`w-6 h-6 ${sleepQuality === rating ? 'fill-current' : ''}`} strokeWidth={1.5} />
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === 'cravings' && (
              <div className="space-y-10">
                <h3 className="text-2xl font-bold text-card-foreground">Craving Management</h3>

                {/* Craving Intensity */}
                <div>
                  <label className="block text-base font-semibold text-card-foreground mb-6">
                    Craving Intensity (0 = No craving, 10 = Intense craving)
                  </label>
                  <div className="flex space-x-3">
                    {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((intensity) => (
                      <button
                        key={intensity}
                        type="button"
                        onClick={() => setCravingIntensity(intensity)}
                        className={`w-12 h-12 rounded-lg border-2 flex items-center justify-center text-sm font-bold transition-all duration-300 ${
                          cravingIntensity === intensity
                            ? 'border-primary bg-primary/10 text-primary shadow-lg'
                            : 'border-border text-muted-foreground hover:border-primary/50 hover:bg-accent'
                        }`}
                      >
                        {intensity}
                      </button>
                    ))}
                  </div>
                </div>
                
                {cravingIntensity > 0 && (
                  <>
                    {/* Trigger */}
                    <div>
                      <label htmlFor="trigger" className="block text-base font-semibold text-card-foreground mb-4">
                        What triggered this craving?
                      </label>
                      <input
                        type="text"
                        id="trigger"
                        value={cravingTrigger}
                        onChange={(e) => setCravingTrigger(e.target.value)}
                        className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
                        placeholder="E.g., stress, social situation, habit..."
                      />
                    </div>

                    {/* Coping Strategy */}
                    <div>
                      <label htmlFor="coping" className="block text-base font-semibold text-card-foreground mb-4">
                        How did you cope with it?
                      </label>
                      <input
                        type="text"
                        id="coping"
                        value={copingStrategy}
                        onChange={(e) => setCopingStrategy(e.target.value)}
                        className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
                        placeholder="E.g., deep breathing, exercise, distraction..."
                      />
                    </div>
                  </>
                )}
              </div>
            )}
            
            {activeTab === 'journal' && (
              <div className="space-y-10">
                <h3 className="text-2xl font-bold text-card-foreground">Daily Reflection</h3>
                <div>
                  <label htmlFor="journal" className="block text-base font-semibold text-card-foreground mb-4">
                    How was your day? What are you grateful for?
                  </label>
                  <textarea
                    id="journal"
                    rows={8}
                    value={journalEntry}
                    onChange={(e) => setJournalEntry(e.target.value)}
                    className="w-full px-4 py-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg leading-relaxed resize-none"
                    placeholder="Write your thoughts, feelings, and reflections..."
                  />
                </div>
              </div>
            )}

            {activeTab === 'notes' && (
              <div className="space-y-10">
                <h3 className="text-2xl font-bold text-card-foreground">Additional Notes</h3>
                <div>
                  <label htmlFor="notes" className="block text-base font-semibold text-card-foreground mb-4">
                    Any other notes for today?
                  </label>
                  <textarea
                    id="notes"
                    rows={6}
                    value={dailyNotes}
                    onChange={(e) => setDailyNotes(e.target.value)}
                    className="w-full px-4 py-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg leading-relaxed resize-none"
                    placeholder="Additional thoughts, reminders, or observations..."
                  />
                </div>
              </div>
            )}
          </div>
          
          {/* Submit Button */}
          <div className="px-8 py-8 bg-muted/30 border-t border-border rounded-b-xl">
            <button
              type="submit"
              disabled={loading}
              className="w-full flex items-center justify-center px-6 py-4 border border-transparent text-lg font-semibold rounded-lg text-primary-foreground bg-primary hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              {loading ? (
                <>
                  <Loader2 className="w-6 h-6 mr-3 animate-spin" strokeWidth={1.5} />
                  Saving...
                </>
              ) : (
                <>
                  <CheckCircle className="w-6 h-6 mr-3" strokeWidth={1.5} />
                  Save Daily Log
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
