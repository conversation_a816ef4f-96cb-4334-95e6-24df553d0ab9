import React, { useState, useEffect } from 'react'
import { Phone, Mail, MessageCircle, Book, Users, Clock, CheckCircle, ExternalLink, Search, Filter, Star, Loader2, ArrowRight } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'

interface SupportArticle {
  id: string
  title: string
  category: string
  readTime: string
  helpful: number
}

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
}

export default function SupportPage() {
  const { user } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [activeTab, setActiveTab] = useState('help')
  const [supportArticles, setSupportArticles] = useState<SupportArticle[]>([])
  const [faqs, setFaqs] = useState<FAQItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // HOLY RULE 0001: Load real support data from Supabase
  useEffect(() => {
    loadSupportData()
  }, [])

  const loadSupportData = async () => {
    try {
      setLoading(true)
      
      // Load support articles from database
      const { data: articlesData, error: articlesError } = await supabase
        .from('support_articles')
        .select('*')
        .order('helpful_count', { ascending: false })
      
      if (articlesError) {
        console.error('Error loading support articles:', articlesError)
        // Fallback to empty array if no table exists yet
        setSupportArticles([])
      } else {
        setSupportArticles(articlesData || [])
      }

      // Load FAQs from database
      const { data: faqsData, error: faqsError } = await supabase
        .from('support_faqs')
        .select('*')
        .order('helpful_count', { ascending: false })
      
      if (faqsError) {
        console.error('Error loading FAQs:', faqsError)
        // Fallback to empty array if no table exists yet
        setFaqs([])
      } else {
        setFaqs(faqsData || [])
      }
      
    } catch (error) {
      console.error('Error loading support data:', error)
      setError('Failed to load support content')
    } finally {
      setLoading(false)
    }
  }

  const categories = ['all', 'Getting Started', 'Health', 'App Usage', 'Goals', 'Account', 'Data', 'Features', 'Privacy', 'Billing']

  const filteredArticles = supportArticles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) || 
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-16 h-16 animate-spin text-primary mx-auto mb-6" strokeWidth={1.5} />
          <p className="text-muted-foreground text-lg">Loading support content...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-6">
          <p className="text-destructive mb-6 text-lg">{error}</p>
          <button
            onClick={loadSupportData}
            className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover transition-all duration-300 font-semibold shadow-lg hover:shadow-xl"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background py-12">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Header */}
        <div className="mb-16">
          <div className="flex items-center gap-6 mb-6">
            <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20">
              <Book className="w-8 h-8 text-primary" strokeWidth={1.5} />
            </div>
            <div>
              <h1 className="text-5xl font-bold text-foreground tracking-tight">Support Center</h1>
              <p className="text-xl text-muted-foreground leading-relaxed mt-2">Get help and find answers to your questions</p>
            </div>
          </div>
        </div>

        {/* Quick Contact Options */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center border border-primary/20">
                <MessageCircle className="w-6 h-6 text-primary" strokeWidth={1.5} />
              </div>
              <h3 className="font-bold text-card-foreground text-lg">Live Chat</h3>
            </div>
            <p className="text-muted-foreground mb-6 leading-relaxed">Get instant help from our support team</p>
            <button className="w-full px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover transition-all duration-300 font-semibold shadow-lg hover:shadow-xl">
              Start Chat
            </button>
            <p className="text-muted-foreground mt-4 text-center">Available 24/7</p>
          </div>

          <div className="bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center border border-primary/20">
                <Mail className="w-6 h-6 text-primary" strokeWidth={1.5} />
              </div>
              <h3 className="font-bold text-card-foreground text-lg">Email Support</h3>
            </div>
            <p className="text-muted-foreground mb-6 leading-relaxed">Send us a detailed message</p>
            <button className="w-full px-6 py-3 border border-border text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold">
              Send Email
            </button>
            <p className="text-muted-foreground mt-4 text-center">Response within 24 hours</p>
          </div>

          <div className="bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center border border-primary/20">
                <Phone className="w-6 h-6 text-primary" strokeWidth={1.5} />
              </div>
              <h3 className="font-bold text-card-foreground text-lg">Phone Support</h3>
            </div>
            <p className="text-muted-foreground mb-6 leading-relaxed">Speak directly with our team</p>
            <button className="w-full px-6 py-3 border border-border text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold">
              Call Now
            </button>
            <p className="text-muted-foreground mt-4 text-center">Mon-Fri, 9AM-6PM EST</p>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="bg-card rounded-xl shadow-lg border border-border p-8 mb-12">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex-1 relative">
              <Search className="w-5 h-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground" strokeWidth={1.5} />
              <input
                type="text"
                placeholder="Search help articles and FAQs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
              />
            </div>
            <div className="flex items-center gap-3">
              <Filter className="w-5 h-5 text-muted-foreground" strokeWidth={1.5} />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg font-medium"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-border mb-12">
          <nav className="flex space-x-12">
            <button
              onClick={() => setActiveTab('help')}
              className={`py-4 px-2 border-b-2 font-semibold text-lg transition-all duration-300 ${
                activeTab === 'help'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
            >
              Help Articles
            </button>
            <button
              onClick={() => setActiveTab('faq')}
              className={`py-4 px-2 border-b-2 font-semibold text-lg transition-all duration-300 ${
                activeTab === 'faq'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
            >
              FAQ
            </button>
          </nav>
        </div>

      {/* Content */}
      {activeTab === 'help' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredArticles.map((article) => (
            <div key={article.id} className="bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300">
              <div className="flex items-start justify-between mb-6">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-primary/10 text-primary border border-primary/20">
                  {article.category}
                </span>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Star className="w-5 h-5 text-accent fill-current" strokeWidth={1.5} />
                  <span className="font-medium">{article.helpful}</span>
                </div>
              </div>

              <h3 className="font-bold text-card-foreground mb-4 text-lg leading-tight">{article.title}</h3>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-5 h-5" strokeWidth={1.5} />
                  <span className="font-medium">{article.readTime}</span>
                </div>
                <button className="text-primary hover:text-primary-hover font-semibold flex items-center gap-2 transition-all duration-300">
                  Read
                  <ExternalLink className="w-4 h-4" strokeWidth={1.5} />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'faq' && (
        <div className="space-y-6">
          {filteredFAQs.map((faq) => (
            <div key={faq.id} className="bg-card rounded-xl shadow-lg border border-border">
              <div className="p-8">
                <div className="flex items-start justify-between mb-6">
                  <h3 className="font-bold text-card-foreground pr-6 text-lg leading-tight">{faq.question}</h3>
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-accent/10 text-accent border border-accent/20 flex-shrink-0">
                    {faq.category}
                  </span>
                </div>
                <p className="text-muted-foreground leading-relaxed text-lg">{faq.answer}</p>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* No Results */}
      {((activeTab === 'help' && filteredArticles.length === 0) || (activeTab === 'faq' && filteredFAQs.length === 0)) && (
        <div className="text-center py-16">
          <Search className="w-16 h-16 text-muted-foreground mx-auto mb-6" strokeWidth={1.5} />
          <h3 className="text-2xl font-bold text-foreground mb-4">No results found</h3>
          <p className="text-muted-foreground text-lg">Try adjusting your search terms or category filter.</p>
        </div>
      )}

      {/* Contact Footer */}
      <div className="bg-primary/5 rounded-xl border border-primary/20 p-8 mt-12">
        <div className="flex items-center gap-4 mb-6">
          <Users className="w-6 h-6 text-primary" strokeWidth={1.5} />
          <h3 className="font-bold text-primary text-xl">Still need help?</h3>
        </div>
        <p className="text-primary/80 mb-8 text-lg leading-relaxed">
          Our support team is here to help you succeed in your quit journey. Don't hesitate to reach out!
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <button className="flex items-center justify-center gap-3 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover transition-all duration-300 font-semibold shadow-lg hover:shadow-xl">
            <MessageCircle className="w-5 h-5" strokeWidth={1.5} />
            Start Live Chat
          </button>
          <button className="flex items-center justify-center gap-3 px-6 py-3 border border-primary text-primary rounded-lg hover:bg-primary hover:text-primary-foreground transition-all duration-300 font-semibold">
            <Mail className="w-5 h-5" strokeWidth={1.5} />
            Send Email
          </button>
        </div>
      </div>
      </div>
    </div>
  )
}
