import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import { Settings, User, Bell, Shield, Download, Trash2, Eye, EyeOff, Save, Check } from 'lucide-react'

interface UserSettings {
  id: string
  user_id: string
  notifications_enabled: boolean
  email_reminders: boolean
  privacy_mode: boolean
  data_sharing: boolean
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  updated_at: string
}

interface UserProfile {
  id: string
  user_id: string
  display_name: string
  avatar_url?: string
  bio?: string
  phone?: string
  emergency_contact?: string
  updated_at: string
}

export default function SettingsPage() {
  const { user, loading, signOut } = useAuth()
  const navigate = useNavigate()
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [isLoadingSettings, setIsLoadingSettings] = useState(true)
  const [isLoadingProfile, setIsLoadingProfile] = useState(true)
  const [activeTab, setActiveTab] = useState<'profile' | 'preferences' | 'privacy' | 'account'>('profile')
  const [isSaving, setIsSaving] = useState(false)
  const [saveMessage, setSaveMessage] = useState('')
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  // Profile form state
  const [displayName, setDisplayName] = useState('')
  const [bio, setBio] = useState('')
  const [phone, setPhone] = useState('')
  const [emergencyContact, setEmergencyContact] = useState('')

  // Settings form state
  const [notificationsEnabled, setNotificationsEnabled] = useState(true)
  const [emailReminders, setEmailReminders] = useState(true)
  const [privacyMode, setPrivacyMode] = useState(false)
  const [dataSharing, setDataSharing] = useState(false)
  const [theme, setTheme] = useState<'light' | 'dark' | 'auto'>('light')
  const [language, setLanguage] = useState('en')
  const [timezone, setTimezone] = useState('UTC')

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth')
      return
    }
    if (user) {
      loadSettings()
      loadProfile()
    }
  }, [user, loading, navigate])

  const loadSettings = async () => {
    if (!user) return

    try {
      setIsLoadingSettings(true)
      const { data, error } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', user.id)
        .single()

      if (error && error.code !== 'PGRST116') throw error
      
      if (data) {
        setSettings(data)
        setNotificationsEnabled(data.notifications_enabled)
        setEmailReminders(data.email_reminders)
        setPrivacyMode(data.privacy_mode)
        setDataSharing(data.data_sharing)
        setTheme(data.theme)
        setLanguage(data.language)
        setTimezone(data.timezone)
      }
    } catch (error) {
      console.error('Error loading settings:', error)
    } finally {
      setIsLoadingSettings(false)
    }
  }

  const loadProfile = async () => {
    if (!user) return

    try {
      setIsLoadingProfile(true)
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single()

      if (error && error.code !== 'PGRST116') throw error
      
      if (data) {
        setProfile(data)
        setDisplayName(data.display_name || '')
        setBio(data.bio || '')
        setPhone(data.phone || '')
        setEmergencyContact(data.emergency_contact || '')
      }
    } catch (error) {
      console.error('Error loading profile:', error)
    } finally {
      setIsLoadingProfile(false)
    }
  }

  const saveProfile = async () => {
    if (!user) return

    try {
      setIsSaving(true)
      const { data, error } = await supabase
        .from('user_profiles')
        .upsert({
          user_id: user.id,
          display_name: displayName,
          bio: bio,
          phone: phone,
          emergency_contact: emergencyContact,
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) throw error
      setProfile(data)
      setSaveMessage('Profile updated successfully!')
      setTimeout(() => setSaveMessage(''), 3000)
    } catch (error) {
      console.error('Error saving profile:', error)
      setSaveMessage('Error saving profile. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const saveSettings = async () => {
    if (!user) return

    try {
      setIsSaving(true)
      const { data, error } = await supabase
        .from('user_settings')
        .upsert({
          user_id: user.id,
          notifications_enabled: notificationsEnabled,
          email_reminders: emailReminders,
          privacy_mode: privacyMode,
          data_sharing: dataSharing,
          theme: theme,
          language: language,
          timezone: timezone,
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) throw error
      setSettings(data)
      setSaveMessage('Settings updated successfully!')
      setTimeout(() => setSaveMessage(''), 3000)
    } catch (error) {
      console.error('Error saving settings:', error)
      setSaveMessage('Error saving settings. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const exportData = async () => {
    if (!user) return

    try {
      // Export user data
      const { data: profileData } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)

      const { data: settingsData } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', user.id)

      const { data: healthData } = await supabase
        .from('health_metrics')
        .select('*')
        .eq('user_id', user.id)

      const exportData = {
        profile: profileData,
        settings: settingsData,
        health_metrics: healthData,
        exported_at: new Date().toISOString()
      }

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `mission-fresh-data-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      setSaveMessage('Data exported successfully!')
      setTimeout(() => setSaveMessage(''), 3000)
    } catch (error) {
      console.error('Error exporting data:', error)
      setSaveMessage('Error exporting data. Please try again.')
    }
  }

  const deleteAccount = async () => {
    if (!user) return

    try {
      // In a real app, this would trigger a server-side deletion process
      // For now, just sign out and show a message
      await signOut()
      navigate('/')
      alert('Account deletion initiated. Please contact support to complete the process.')
    } catch (error) {
      console.error('Error deleting account:', error)
      setSaveMessage('Error initiating account deletion. Please contact support.')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto"></div>
          <p className="mt-6 text-muted-foreground text-lg">Loading settings...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-6xl mx-auto px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="mb-16">
          <h1 className="text-5xl font-bold text-foreground flex items-center mb-6 tracking-tight">
            <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mr-6 border border-primary/20">
              <Settings className="h-8 w-8 text-primary" strokeWidth={1.5} />
            </div>
            Settings
          </h1>
          <p className="text-xl text-muted-foreground leading-relaxed">
            Manage your account, preferences, and privacy settings.
          </p>
        </div>

        {/* Save Message */}
        {saveMessage && (
          <div className={`mb-8 p-6 rounded-xl flex items-center shadow-lg border ${
            saveMessage.includes('Error')
              ? 'bg-destructive/10 text-destructive border-destructive/20'
              : 'bg-primary/10 text-primary border-primary/20'
          }`}>
            <Check className="mr-3 h-6 w-6" strokeWidth={1.5} />
            <span className="text-lg font-medium">{saveMessage}</span>
          </div>
        )}

        <div className="bg-card rounded-xl shadow-lg border border-border">
          {/* Tabs */}
          <div className="border-b border-border">
            <nav className="flex space-x-12 px-8">
              {[
                { id: 'profile', label: 'Profile', icon: User },
                { id: 'preferences', label: 'Preferences', icon: Settings },
                { id: 'privacy', label: 'Privacy', icon: Shield },
                { id: 'account', label: 'Account', icon: Trash2 }
              ].map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-6 px-2 border-b-2 font-semibold text-lg flex items-center transition-all duration-300 ${
                      activeTab === tab.id
                        ? 'border-primary text-primary'
                        : 'border-transparent text-muted-foreground hover:text-foreground'
                    }`}
                  >
                    <Icon className="mr-3 h-5 w-5" strokeWidth={1.5} />
                    {tab.label}
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-10">
            {activeTab === 'profile' && (
              <div className="space-y-8">
                <h3 className="text-2xl font-bold text-card-foreground">Profile Information</h3>

                <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
                  <div>
                    <label className="block text-base font-semibold text-card-foreground mb-3">
                      Display Name
                    </label>
                    <input
                      type="text"
                      value={displayName}
                      onChange={(e) => setDisplayName(e.target.value)}
                      className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
                      placeholder="How should we address you?"
                    />
                  </div>

                  <div>
                    <label className="block text-base font-semibold text-card-foreground mb-3">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
                      placeholder="Your phone number"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-base font-semibold text-card-foreground mb-3">
                    Bio
                  </label>
                  <textarea
                    value={bio}
                    onChange={(e) => setBio(e.target.value)}
                    rows={4}
                    className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg leading-relaxed resize-none"
                    placeholder="Tell us a bit about yourself..."
                  />
                </div>

                <div>
                  <label className="block text-base font-semibold text-card-foreground mb-3">
                    Emergency Contact
                  </label>
                  <input
                    type="text"
                    value={emergencyContact}
                    onChange={(e) => setEmergencyContact(e.target.value)}
                    className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
                    placeholder="Name and phone number"
                  />
                </div>

                <button
                  onClick={saveProfile}
                  disabled={isSaving}
                  className="bg-primary text-primary-foreground px-8 py-4 rounded-lg hover:bg-primary-hover disabled:opacity-50 transition-all duration-300 flex items-center font-semibold text-lg shadow-lg hover:shadow-xl"
                >
                  <Save className="mr-3 h-5 w-5" strokeWidth={1.5} />
                  {isSaving ? 'Saving...' : 'Save Profile'}
                </button>
              </div>
            )}

            {activeTab === 'preferences' && (
              <div className="space-y-8">
                <h3 className="text-2xl font-bold text-card-foreground">Preferences</h3>

                <div className="space-y-6">
                  <div className="flex items-center justify-between p-6 border border-border rounded-xl">
                    <div>
                      <div className="font-bold text-card-foreground text-lg">Notifications</div>
                      <div className="text-muted-foreground">Receive app notifications</div>
                    </div>
                    <button
                      onClick={() => setNotificationsEnabled(!notificationsEnabled)}
                      className={`relative inline-flex h-8 w-14 items-center rounded-full transition-all duration-300 ${
                        notificationsEnabled ? 'bg-primary' : 'bg-muted'
                      }`}
                    >
                      <span
                        className={`inline-block h-6 w-6 transform rounded-full bg-white transition-transform shadow-lg ${
                          notificationsEnabled ? 'translate-x-7' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  <div className="flex items-center justify-between p-6 border border-border rounded-xl">
                    <div>
                      <div className="font-bold text-card-foreground text-lg">Email Reminders</div>
                      <div className="text-muted-foreground">Get reminders via email</div>
                    </div>
                    <button
                      onClick={() => setEmailReminders(!emailReminders)}
                      className={`relative inline-flex h-8 w-14 items-center rounded-full transition-all duration-300 ${
                        emailReminders ? 'bg-primary' : 'bg-muted'
                      }`}
                    >
                      <span
                        className={`inline-block h-6 w-6 transform rounded-full bg-white transition-transform shadow-lg ${
                          emailReminders ? 'translate-x-7' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
                  <div>
                    <label className="block text-base font-semibold text-card-foreground mb-3">
                      Theme
                    </label>
                    <select
                      value={theme}
                      onChange={(e) => setTheme(e.target.value as 'light' | 'dark' | 'auto')}
                      className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
                    >
                      <option value="light">Light</option>
                      <option value="dark">Dark</option>
                      <option value="auto">Auto</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-base font-semibold text-card-foreground mb-3">
                      Language
                    </label>
                    <select
                      value={language}
                      onChange={(e) => setLanguage(e.target.value)}
                      className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
                    >
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                      <option value="de">German</option>
                    </select>
                  </div>
                </div>

                <button
                  onClick={saveSettings}
                  disabled={isSaving}
                  className="bg-primary text-primary-foreground px-8 py-4 rounded-lg hover:bg-primary-hover disabled:opacity-50 transition-all duration-300 flex items-center font-semibold text-lg shadow-lg hover:shadow-xl"
                >
                  <Save className="mr-3 h-5 w-5" strokeWidth={1.5} />
                  {isSaving ? 'Saving...' : 'Save Preferences'}
                </button>
              </div>
            )}

            {activeTab === 'privacy' && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-foreground tracking-tight">Privacy & Data</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">Privacy Mode</div>
                      <div className="text-sm text-gray-600">Hide your activity from other users</div>
                    </div>
                    <button
                      onClick={() => setPrivacyMode(!privacyMode)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        privacyMode ? 'bg-primary' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          privacyMode ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">Data Sharing</div>
                      <div className="text-sm text-gray-600">Share anonymized data for research</div>
                    </div>
                    <button
                      onClick={() => setDataSharing(!dataSharing)}
                      className={`relative inline-flex h-6 w-11 items-centers rounded-full transition-colors ${
                        dataSharing ? 'bg-primary' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          dataSharing ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>

                <div className="border-t border-gray-200 pt-6">
                  <h4 className="font-medium text-gray-900 mb-4">Data Management</h4>
                  <div className="space-y-3">
                    <button
                      onClick={exportData}
                      className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Export My Data
                    </button>
                  </div>
                </div>

                <button
                  onClick={saveSettings}
                  disabled={isSaving}
                  className="btn-primary px-6 py-3 rounded-lg disabled:opacity-50 transition-colors flex items-center"
                >
                  <Save className="mr-2 h-4 w-4" />
                  {isSaving ? 'Saving...' : 'Save Privacy Settings'}
                </button>
              </div>
            )}

            {activeTab === 'account' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Account Management</h3>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <Settings className="h-5 w-5 text-yellow-600" />
                    </div>
                    <div className="ml-3">
                      <h4 className="text-sm font-medium text-yellow-800">Account Information</h4>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>Email: {user?.email}</p>
                        <p>Member since: {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border-t border-gray-200 pt-6">
                  <h4 className="font-medium text-gray-900 mb-4 text-red-600">Danger Zone</h4>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <Trash2 className="h-5 w-5 text-red-600" />
                      </div>
                      <div className="ml-3 flex-1">
                        <h4 className="text-sm font-medium text-red-800">Delete Account</h4>
                        <p className="mt-1 text-sm text-red-700">
                          Permanently delete your account and all associated data. This action cannot be undone.
                        </p>
                        <div className="mt-4">
                          {!showDeleteConfirm ? (
                            <button
                              onClick={() => setShowDeleteConfirm(true)}
                              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm"
                            >
                              Delete Account
                            </button>
                          ) : (
                            <div className="space-y-3">
                              <p className="text-sm font-medium text-red-800">
                                Are you absolutely sure? This action cannot be undone.
                              </p>
                              <div className="flex space-x-3">
                                <button
                                  onClick={deleteAccount}
                                  className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm"
                                >
                                  Yes, Delete My Account
                                </button>
                                <button
                                  onClick={() => setShowDeleteConfirm(false)}
                                  className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-sm"
                                >
                                  Cancel
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
