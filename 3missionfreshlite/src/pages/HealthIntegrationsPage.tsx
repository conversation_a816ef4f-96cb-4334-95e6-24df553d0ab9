import React, { useState, useEffect } from 'react'
import { Heart, Activity, Watch, CheckCircle, Link as LinkIcon, Unlink, Loader2, Smartphone, Wifi, AlertCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'

type ConnectionStatus = 'disconnected' | 'connected' | 'pending'

interface IntegrationCardProps {
  title: string
  description: string
  Icon: React.ElementType
  status: ConnectionStatus
  onConnect: () => void
  onDisconnect: () => void
}

function StatusBadge({ status }: { status: ConnectionStatus }) {
  switch (status) {
    case 'connected':
      return (
        <span className="inline-flex items-center px-3 py-1 rounded-lg font-semibold bg-primary/10 text-primary border border-primary/20">
          Connected
        </span>
      )
    case 'pending':
      return (
        <span className="inline-flex items-center px-3 py-1 rounded-lg font-semibold bg-muted text-muted-foreground border border-border">
          Pending
        </span>
      )
    default:
      return (
        <span className="inline-flex items-center px-3 py-1 rounded-lg font-semibold bg-muted text-muted-foreground border border-border">
          Disconnected
        </span>
      )
  }
}

function IntegrationCard({ title, description, Icon, status, onConnect, onDisconnect }: IntegrationCardProps) {
  return (
    <div className="bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300">
      <div className="flex items-start justify-between mb-6">
        <div className="flex items-start gap-4">
          <div className="w-14 h-14 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20">
            <Icon className="w-7 h-7 text-primary" strokeWidth={1.5} />
          </div>
          <div>
            <h3 className="font-bold text-card-foreground text-lg">{title}</h3>
            <p className="text-muted-foreground mt-2 leading-relaxed">{description}</p>
          </div>
        </div>
        <StatusBadge status={status} />
      </div>

      <div className="mt-6">
        {status === 'connected' ? (
          <button
            onClick={onDisconnect}
            className="w-full flex items-center justify-center gap-3 px-6 py-3 border border-border rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold"
          >
            <Unlink className="w-5 h-5" strokeWidth={1.5} />
            Disconnect
          </button>
        ) : (
          <button
            onClick={onConnect}
            disabled={status === 'pending'}
            className="w-full flex items-center justify-center gap-3 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-semibold shadow-lg hover:shadow-xl"
          >
            {status === 'pending' ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" strokeWidth={1.5} />
                Connecting...
              </>
            ) : (
              <>
                <LinkIcon className="w-5 h-5" strokeWidth={1.5} />
                Connect
              </>
            )}
          </button>
        )}
      </div>
    </div>
  )
}

export default function HealthIntegrationsPage() {
  // Restored state variables to fix TypeScript errors
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [healthIntegrations, setHealthIntegrations] = useState<any[]>([])
  const [userConnections, setUserConnections] = useState<{ [key: string]: ConnectionStatus }>({})
  
  // Re-enable useEffect for data fetching
  useEffect(() => {
    fetchHealthIntegrations()
    fetchUserConnections()
  }, [])

  const fetchHealthIntegrations = async () => {
    try {
      const { data, error } = await supabase
        .from('health_integrations')
        .select('*')
        .order('display_order')
      
      if (error) {
        // Fallback integrations if table doesn't exist
        setHealthIntegrations([
          { id: 1, name: 'Apple Health', key: 'apple', description: 'Sync steps, sleep, heart rate, and wellness metrics from your iOS device.', icon: 'heart', provider: 'apple' },
          { id: 2, name: 'Google Fit', key: 'google', description: 'Import health and fitness data from your Android device.', icon: 'activity', provider: 'google' },
          { id: 3, name: 'Fitbit', key: 'fitbit', description: 'Connect your Fitbit device to track activity and sleep patterns.', icon: 'watch', provider: 'fitbit' },
          { id: 4, name: 'Garmin Connect', key: 'garmin', description: 'Sync training data and health metrics from Garmin devices.', icon: 'watch', provider: 'garmin' },
          { id: 5, name: 'Samsung Health', key: 'samsung', description: 'Connect Samsung Health to import wellness and activity data.', icon: 'smartphone', provider: 'samsung' },
          { id: 6, name: 'Strava', key: 'strava', description: 'Import workout data and activity tracking from Strava.', icon: 'activity', provider: 'strava' }
        ])
        return
      }
      setHealthIntegrations(data || [])
    } catch (err: any) {
      setError(err.message)
    }
  }

  const fetchUserConnections = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        setLoading(false)
        return
      }

      const { data, error } = await supabase
        .from('user_health_connections')
        .select('*')
        .eq('user_id', user.id)
      
      if (error) throw error
      // Transform array to object keyed by integration_key
      const connectionsObj = (data || []).reduce((acc: { [key: string]: ConnectionStatus }, conn: any) => {
        acc[conn.integration_key] = conn.status as ConnectionStatus
        return acc
      }, {})
      setUserConnections(connectionsObj)
      setLoading(false)
    } catch (err: any) {
      setError(err.message)
      setLoading(false)
    }
  }

  const handleConnect = async (integrationKey: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      const { error } = await supabase
        .from('user_health_connections')
        .upsert({
          user_id: user.id,
          integration_key: integrationKey,
          status: 'connected',
          connected_at: new Date().toISOString()
        })
      
      if (error) throw error
      await fetchUserConnections()
    } catch (err: any) {
      setError(err.message)
    }
  }

  const handleDisconnect = async (integrationKey: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      const { error } = await supabase
        .from('user_health_connections')
        .delete()
        .eq('user_id', user.id)
        .eq('integration_key', integrationKey)
      
      if (error) throw error
      await fetchUserConnections()
    } catch (err: any) {
      setError(err.message)
    }
  }

  const getConnectionStatus = (integrationKey: string): ConnectionStatus => {
    return userConnections[integrationKey] || 'disconnected'
  }

  const getIconComponent = (iconName: string) => {
    const iconMap: Record<string, React.ElementType> = {
      heart: Heart,
      activity: Activity,
      watch: Watch,
      smartphone: Smartphone,
      wifi: Wifi
    }
    return iconMap[iconName] || Activity
  }

  const connectedCount = userConnections.length

  return (
    <div className="min-h-screen bg-background py-12">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Header */}
        <div className="mb-16">
          <h1 className="text-5xl font-bold text-foreground mb-6 tracking-tight">Health Integrations</h1>
          <p className="text-xl text-muted-foreground leading-relaxed max-w-3xl">
            Sync your health data for a more holistic view of your wellness journey.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground font-medium text-lg">Connected Apps</p>
                <p className="text-4xl font-bold text-primary mt-2">{connectedCount}</p>
              </div>
              <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20">
                <LinkIcon className="w-8 h-8 text-primary" strokeWidth={1.5} />
              </div>
            </div>
          </div>

          <div className="bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground font-medium text-lg">Available Integrations</p>
                <p className="text-4xl font-bold text-primary mt-2">{healthIntegrations.length}</p>
              </div>
              <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20">
                <Activity className="w-8 h-8 text-primary" strokeWidth={1.5} />
              </div>
            </div>
          </div>

          <div className="bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-muted-foreground font-medium text-lg">Data Sync Status</p>
                <p className="text-4xl font-bold text-primary mt-2">Live</p>
              </div>
              <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20">
                <Wifi className="w-8 h-8 text-primary" strokeWidth={1.5} />
              </div>
            </div>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-20">
            <div className="inline-block animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
            <p className="text-muted-foreground mt-6 text-lg">Loading health integrations...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-6 mb-12">
            <div className="flex items-center space-x-4">
              <AlertCircle className="w-6 h-6 text-destructive flex-shrink-0" strokeWidth={1.5} />
              <p className="text-destructive font-medium text-lg">{error}</p>
            </div>
          </div>
        )}

        {/* Integration Cards - Real Database Data - RESTORED */}
        {!loading && !error && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {healthIntegrations.map((integration) => {
              const IconComponent = getIconComponent(integration.icon)
              return (
                <IntegrationCard
                  key={integration.key}
                  title={integration.name}
                  description={integration.description}
                  Icon={IconComponent}
                  status={getConnectionStatus(integration.key)}
                  onConnect={() => handleConnect(integration.key)}
                  onDisconnect={() => handleDisconnect(integration.key)}
                />
              )
            })}
          </div>
        )}

        {/* No Integrations Available */}
        {!loading && !error && healthIntegrations.length === 0 && (
          <div className="text-center py-20">
            <div className="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20">
              <Activity className="w-10 h-10 text-primary" strokeWidth={1.5} />
            </div>
            <h3 className="text-2xl font-bold text-card-foreground mb-4">No Integrations Available</h3>
            <p className="text-muted-foreground text-lg">Check back later for health integration options.</p>
          </div>
        )}

        {/* Privacy Information */}
        <div className="bg-card rounded-xl shadow-lg border border-border p-10">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-card-foreground mb-4">Your Data Privacy</h2>
            <p className="text-muted-foreground text-lg leading-relaxed">
              We take your privacy seriously. Your health data is always protected.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[
              'Encrypted and stored securely using industry-standard protocols',
              'Never shared with third parties without your explicit consent',
              'Used only to provide you with personalized health insights',
              'Deletable by you at any time from your account settings'
            ].map((item, index) => (
              <div key={index} className="flex items-start gap-4">
                <CheckCircle className="w-6 h-6 text-primary flex-shrink-0 mt-1" strokeWidth={1.5} />
                <span className="text-muted-foreground leading-relaxed">{item}</span>
              </div>
            ))}
          </div>

          <div className="mt-10 p-8 bg-primary/5 rounded-xl border border-primary/20">
            <div className="flex items-start gap-4">
              <Activity className="w-6 h-6 text-primary flex-shrink-0 mt-1" strokeWidth={1.5} />
              <div>
                <h3 className="font-bold text-card-foreground mb-3 text-lg">Data Sync Benefits</h3>
                <p className="text-muted-foreground leading-relaxed">
                  Connecting your health apps allows us to provide more accurate insights into your wellness journey,
                  track correlations between your quit progress and physical health, and offer personalized recommendations.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
