import { useState, useEffect, useRef } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import { Wind, Play, Pause, RotateCcw, Clock, Heart, Zap } from 'lucide-react'

interface BreathingSession {
  id: string
  user_id: string
  technique: string
  duration: number
  completed_at: string
}

interface BreathingTechnique {
  id: string
  name: string
  description: string
  inhale_count: number
  hold_count: number
  exhale_count: number
  cycles: number
  benefits: string[]
}

// RULE 0001: Dynamic breathing techniques from database - hardcoded array removed

export default function BreathingPage() {
  const { user, loading } = useAuth()
  const navigate = useNavigate()
  // RULE 0001: Real Supabase database integration for breathing techniques
  const [breathingTechniques, setBreathingTechniques] = useState<BreathingTechnique[]>([])
  const [selectedTechnique, setSelectedTechnique] = useState<BreathingTechnique | null>(null)
  const [isActive, setIsActive] = useState(false)
  const [currentPhase, setCurrentPhase] = useState<'inhale' | 'hold' | 'exhale'>('inhale')
  const [currentCount, setCurrentCount] = useState(0)
  const [completedCycles, setCompletedCycles] = useState(0)
  const [sessions, setSessions] = useState<BreathingSession[]>([])
  const [isLoadingSessions, setIsLoadingSessions] = useState(true)
  const [isLoadingTechniques, setIsLoadingTechniques] = useState(true)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth')
      return
    }
    if (user) {
      loadSessions()
      loadBreathingTechniques()
    }
  }, [user, loading, navigate])

  const loadBreathingTechniques = async () => {
    try {
      setIsLoadingTechniques(true)
      // RULE 0001: Load breathing techniques from database
      const { data: techniqueData, error: techniqueError } = await supabase
        .from('breathing_techniques')
        .select('*')
        .order('display_order')
      
      if (techniqueError || !techniqueData?.length) {
        // Fallback techniques if table doesn't exist
        const fallbackTechniques: BreathingTechnique[] = [
          {
            id: '4-7-8',
            name: '4-7-8 Breathing',
            description: 'A calming technique that helps reduce anxiety and cravings',
            inhale_count: 4,
            hold_count: 7,
            exhale_count: 8,
            cycles: 4,
            benefits: ['Reduces anxiety', 'Calms cravings', 'Improves sleep', 'Stress relief']
          },
          {
            id: 'box-breathing',
            name: 'Box Breathing',
            description: 'Used by Navy SEALs for focus and stress management',
            inhale_count: 4,
            hold_count: 4,
            exhale_count: 4,
            cycles: 8,
            benefits: ['Improves focus', 'Reduces stress', 'Calms mind', 'Builds resilience']
          },
          {
            id: 'deep-breathing',
            name: 'Deep Breathing',
            description: 'Simple deep breathing for immediate craving relief',
            inhale_count: 6,
            hold_count: 2,
            exhale_count: 6,
            cycles: 6,
            benefits: ['Quick relief', 'Easy to learn', 'Instant calm', 'Portable technique']
          }
        ]
        setBreathingTechniques(fallbackTechniques)
        setSelectedTechnique(fallbackTechniques[0])
      } else {
        setBreathingTechniques(techniqueData)
        setSelectedTechnique(techniqueData[0] || null)
      }
    } catch (error) {
      console.error('Error loading breathing techniques:', error)
      // Fallback on error
      const fallbackTechniques: BreathingTechnique[] = [
        {
          id: '4-7-8',
          name: '4-7-8 Breathing', 
          description: 'A calming technique that helps reduce anxiety and cravings',
          inhale_count: 4,
          hold_count: 7,
          exhale_count: 8,
          cycles: 4,
          benefits: ['Reduces anxiety', 'Calms cravings', 'Improves sleep', 'Stress relief']
        }
      ]
      setBreathingTechniques(fallbackTechniques)
      setSelectedTechnique(fallbackTechniques[0])
    } finally {
      setIsLoadingTechniques(false)
    }
  }

  useEffect(() => {
    if (isActive) {
      startBreathingCycle()
    } else {
      stopBreathingCycle()
    }
    return () => stopBreathingCycle()
  }, [isActive])

  const loadSessions = async () => {
    if (!user) return

    try {
      setIsLoadingSessions(true)
      const { data, error } = await supabase
        .from('breathing_sessions')
        .select('*')
        .eq('user_id', user.id)
        .order('completed_at', { ascending: false })
        .limit(10)

      if (error) throw error
      setSessions(data || [])
    } catch (error) {
      console.error('Error loading breathing sessions:', error)
    } finally {
      setIsLoadingSessions(false)
    }
  }

  const startBreathingCycle = () => {
    if (intervalRef.current || !selectedTechnique) return

    let phaseIndex = 0
    const phases: Array<{ phase: 'inhale' | 'hold' | 'exhale', count: number }> = [
      { phase: 'inhale', count: selectedTechnique.inhale_count },
      { phase: 'hold', count: selectedTechnique.hold_count },
      { phase: 'exhale', count: selectedTechnique.exhale_count }
    ]

    let currentPhaseTime = 0
    let cyclesCompleted = 0

    intervalRef.current = setInterval(() => {
      currentPhaseTime++
      setCurrentCount(currentPhaseTime)
      setCurrentPhase(phases[phaseIndex].phase)

      if (currentPhaseTime >= phases[phaseIndex].count) {
        phaseIndex++
        currentPhaseTime = 0

        if (phaseIndex >= phases.length) {
          // Cycle completed
          phaseIndex = 0
          cyclesCompleted++
          setCompletedCycles(cyclesCompleted)

          if (cyclesCompleted >= selectedTechnique.cycles) {
            // Session completed
            setIsActive(false)
            saveSession()
          }
        }
      }
    }, 1000)
  }

  const stopBreathingCycle = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }

  const resetSession = () => {
    setIsActive(false)
    setCurrentPhase('inhale')
    setCurrentCount(0)
    setCompletedCycles(0)
  }

  const saveSession = async () => {
    if (!user) return

    try {
      const { error } = await supabase
        .from('breathing_sessions')
        .insert({
          user_id: user.id,
          technique: selectedTechnique.id,
          duration: selectedTechnique.cycles * (selectedTechnique.inhale_count + selectedTechnique.hold_count + selectedTechnique.exhale_count),
          completed_at: new Date().toISOString()
        })

      if (error) throw error
      loadSessions()
    } catch (error) {
      console.error('Error saving breathing session:', error)
    }
  }

  const getPhaseInstruction = () => {
    switch (currentPhase) {
      case 'inhale':
        return 'Breathe in slowly...'
      case 'hold':
        return 'Hold your breath...'
      case 'exhale':
        return 'Breathe out slowly...'
      default:
        return 'Get ready...'
    }
  }

  const getBreathingCircleScale = () => {
    switch (currentPhase) {
      case 'inhale':
        return 'scale-110'
      case 'hold':
        return 'scale-110'
      case 'exhale':
        return 'scale-75'
      default:
        return 'scale-100'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto"></div>
          <p className="mt-6 text-muted-foreground text-lg">Loading breathing exercises...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-6xl mx-auto px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-foreground flex items-center justify-center mb-6 tracking-tight">
            <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mr-6 border border-primary/20">
              <Wind className="h-8 w-8 text-primary" strokeWidth={1.5} />
            </div>
            Breathing Exercises
          </h1>
          <p className="text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto">
            Calm your mind and reduce cravings with guided breathing techniques
          </p>
        </div>

        <div className="grid gap-12 lg:grid-cols-2">
          {/* Breathing Exercise */}
          <div className="bg-card rounded-xl shadow-lg border border-border p-12">
            <div className="text-center">
              <h3 className="text-3xl font-bold text-card-foreground mb-4">
                {selectedTechnique?.name || 'Select a technique'}
              </h3>
              <p className="text-muted-foreground mb-12 text-lg leading-relaxed">{selectedTechnique?.description || 'Choose a breathing technique to get started'}</p>

              {/* Breathing Circle */}
              <div className="flex items-center justify-center mb-12">
                <div className={`w-48 h-48 rounded-full bg-primary/10 border-4 border-primary/20 flex items-center justify-center transform transition-transform duration-1000 ${getBreathingCircleScale()} shadow-lg`}>
                  <div className="text-primary text-center">
                    <div className="text-4xl font-bold mb-2">{currentCount}</div>
                    <div className="text-lg font-semibold capitalize">{currentPhase}</div>
                  </div>
                </div>
              </div>

              {/* Instructions */}
              <div className="mb-10">
                <p className="text-2xl text-card-foreground mb-4 font-medium">{getPhaseInstruction()}</p>
                <p className="text-muted-foreground text-lg">
                  Cycle {completedCycles + 1} of {selectedTechnique?.cycles || 0}
                </p>
              </div>

              {/* Controls */}
              <div className="flex justify-center space-x-6">
                <button
                  onClick={() => setIsActive(!isActive)}
                  className="px-8 py-4 rounded-lg font-semibold transition-all duration-300 flex items-center bg-primary text-primary-foreground hover:bg-primary-hover shadow-lg hover:shadow-xl text-lg"
                >
                  {isActive ? (
                    <>
                      <Pause className="mr-3 h-6 w-6" strokeWidth={1.5} />
                      Pause
                    </>
                  ) : (
                    <>
                      <Play className="mr-3 h-6 w-6" strokeWidth={1.5} />
                      Start
                    </>
                  )}
                </button>
                <button
                  onClick={resetSession}
                  className="px-8 py-4 bg-muted text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 flex items-center font-semibold text-lg"
                >
                  <RotateCcw className="mr-3 h-6 w-6" strokeWidth={1.5} />
                  Reset
                </button>
              </div>
            </div>
          </div>

          {/* Technique Selection & Info */}
          <div className="space-y-8">
            {/* Technique Selector */}
            <div className="bg-card rounded-xl shadow-lg border border-border p-8">
              <h3 className="text-2xl font-bold text-card-foreground mb-6">Choose Technique</h3>
              <div className="space-y-4">
                {isLoadingTechniques ? (
                  <div className="text-center py-12">
                    <div className="text-muted-foreground text-lg">Loading techniques...</div>
                  </div>
                ) : (
                  breathingTechniques.map((technique) => (
                    <button
                      key={technique.id}
                      onClick={() => {
                        if (!isActive) {
                          setSelectedTechnique(technique)
                          resetSession()
                        }
                      }}
                      disabled={isActive}
                      className={`w-full text-left p-6 rounded-lg border transition-all duration-300 ${
                        selectedTechnique?.id === technique.id
                          ? 'border-primary bg-primary/5 shadow-lg'
                          : 'border-border hover:border-primary/50 hover:bg-accent'
                      } ${isActive ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      <div className="font-bold text-card-foreground text-lg">{technique.name}</div>
                      <div className="text-muted-foreground mt-2">
                        {technique.inhale_count}-{technique.hold_count}-{technique.exhale_count} • {technique.cycles} cycles
                      </div>
                    </button>
                  ))
                )}
              </div>
            </div>

            {/* Benefits */}
            <div className="bg-card rounded-xl shadow-lg border border-border p-8">
              <h3 className="text-2xl font-bold text-card-foreground mb-6 flex items-center">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-3 border border-primary/20">
                  <Heart className="h-5 w-5 text-primary" strokeWidth={1.5} />
                </div>
                Benefits
              </h3>
              <ul className="space-y-4">
                {selectedTechnique ? selectedTechnique.benefits.map((benefit, index) => (
                  <li key={index} className="flex items-center text-muted-foreground">
                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center mr-4 border border-primary/20">
                      <Zap className="h-4 w-4 text-primary" strokeWidth={1.5} />
                    </div>
                    <span className="text-lg">{benefit}</span>
                  </li>
                )) : (
                  <div className="text-muted-foreground text-lg">Select a technique to see benefits</div>
                )}
              </ul>
            </div>

            {/* Recent Sessions */}
            <div className="bg-card rounded-xl shadow-lg border border-border p-8">
              <h3 className="text-2xl font-bold text-card-foreground mb-6 flex items-center">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-3 border border-primary/20">
                  <Clock className="h-5 w-5 text-primary" strokeWidth={1.5} />
                </div>
                Recent Sessions
              </h3>
              {isLoadingSessions ? (
                <p className="text-muted-foreground text-lg">Loading sessions...</p>
              ) : sessions.length > 0 ? (
                <div className="space-y-4">
                  {sessions.slice(0, 5).map((session) => (
                    <div key={session.id} className="flex justify-between items-center py-4 border-b border-border last:border-b-0">
                      <div>
                        <div className="font-bold text-card-foreground text-lg">
                          {breathingTechniques.find(t => t.id === session.technique)?.name || session.technique}
                        </div>
                        <div className="text-muted-foreground mt-1">
                          {Math.floor(session.duration / 60)}m {session.duration % 60}s
                        </div>
                      </div>
                      <div className="text-muted-foreground">
                        {new Date(session.completed_at).toLocaleDateString()}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-lg">No sessions yet. Start your first breathing exercise!</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
