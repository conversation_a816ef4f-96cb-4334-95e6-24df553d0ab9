import { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Store, ArrowRight, Star, Filter, Search, Heart, ShoppingCart, AlertCircle, ExternalLink, DollarSign } from 'lucide-react'
import { supabase } from '../lib/supabase'

export default function SmokelessDirectoryPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedRating, setSelectedRating] = useState('all')
  const [products, setProducts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch real products from Supabase database - RULE 0001: ZERO hardcoding
  useEffect(() => {
    async function fetchProducts() {
      try {
        setLoading(true)
        
        // Use the REAL deprecated_smokeless_products table from database
        const { data, error } = await supabase
          .from('deprecated_smokeless_products')
          .select('*')
          .order('average_rating', { ascending: false })
          .order('name', { ascending: true })
        
        if (error) throw error
        setProducts(data || [])
      } catch (err) {
        console.error('Error fetching smokeless products:', err)
        setError('Failed to load products')
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [])

  // Generate categories dynamically from real database products - RULE 0001: ZERO hardcoding
  const categories = [
    { id: 'all', label: 'All Categories' },
    ...Array.from(new Set(products.map(p => p.brand).filter(Boolean))).map(brand => ({
      id: brand,
      label: brand
    }))
  ]

  // Filter products using real database data - RULE 0001: ZERO hardcoding
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || product.brand === selectedCategory
    const matchesRating = selectedRating === 'all' ||
                         (selectedRating === '4+' && (product.average_rating || 0) >= 4) ||
                         (selectedRating === '4.5+' && (product.average_rating || 0) >= 4.5)
    return matchesSearch && matchesCategory && matchesRating
  })

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-5 h-5 ${i < Math.floor(rating) ? 'text-primary fill-current' : 'text-muted-foreground'}`}
        strokeWidth={1.5}
      />
    ))
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <section className="bg-background py-24">
        <div className="max-w-6xl mx-auto px-6 lg:px-8 text-center">
          <div className="w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg">
            <Store className="w-10 h-10 text-primary-foreground" strokeWidth={2} />
          </div>
          <h1 className="text-6xl font-bold text-foreground mb-8 tracking-tight">
            Smokeless Directory
          </h1>
          <p className="text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto">
            Comprehensive, curated directory of smokeless alternatives with expert reviews and recommendations
          </p>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="bg-card border-b border-border py-12">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-6 items-center">
            {/* Search */}
            <div className="relative flex-1 max-w-lg">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-6 h-6" strokeWidth={1.5} />
              <input
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-4 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
              />
            </div>

            {/* Category Filter */}
            <div className="flex items-center space-x-3">
              <Filter className="w-6 h-6 text-muted-foreground" strokeWidth={1.5} />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="border border-border rounded-lg px-4 py-4 focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg font-medium"
              >
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Rating Filter */}
            <div className="flex items-center space-x-3">
              <Star className="w-6 h-6 text-muted-foreground" strokeWidth={1.5} />
              <select
                value={selectedRating}
                onChange={(e) => setSelectedRating(e.target.value)}
                className="border border-border rounded-lg px-4 py-4 focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg font-medium"
              >
                <option value="all">All Ratings</option>
                <option value="4+">4+ Stars</option>
                <option value="4.5+">4.5+ Stars</option>
              </select>
            </div>
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-12">
        <div className="max-w-6xl mx-auto px-6">
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-foreground tracking-tight">
              {filteredProducts.length} Products Found
            </h2>
            <p className="text-muted-foreground text-lg leading-relaxed">
              Expertly curated smokeless alternatives with verified reviews
            </p>
          </div>

          {loading ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                  <div className="h-48 bg-gray-200 animate-pulse"></div>
                  <div className="p-6">
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded animate-pulse mb-4 w-2/3"></div>
                    <div className="h-12 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100 text-center">
              <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Error loading products</h3>
              <p className="text-gray-600">{error}</p>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredProducts.map((product) => (
                <div key={product.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                  {/* Product Image */}
                  <div className="h-48 bg-gray-100 flex items-center justify-center">
                    <Store className="w-12 h-12 text-gray-400" />
                  </div>

                  {/* Product Info */}
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{product.name}</h3>
                      <span className="text-lg font-bold text-green-600">{product.price_range || 'Price varies'}</span>
                    </div>

                    {/* Brand Badge */}
                    {product.brand && (
                      <div className="mb-2">
                        <span className="inline-block bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded">
                          {product.brand}
                        </span>
                      </div>
                    )}

                    {/* Rating */}
                    {product.effectiveness_rating && (
                      <div className="flex items-center space-x-2 mb-2">
                        <div className="flex items-center">
                          {renderStars(product.average_rating || 0)}
                          <span className="ml-1 text-sm text-gray-600">({product.review_count || 0})</span>
                        </div> 
                        <span className="text-sm text-gray-600">
                          {product.effectiveness_rating}/5.0 effectiveness
                        </span>
                      </div>
                    )}

                    {/* Nicotine Strengths */}
                    {product.nicotine_strength && (
                      <div className="mb-3">
                        <p className="text-sm text-gray-600 mb-2">Nicotine Strength:</p>
                        <div className="flex flex-wrap gap-1">
                          <span className="px-2 py-1 rounded text-xs bg-green-100 text-green-800">
                            {product.nicotine_strength}mg
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Description */}
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {product.description}
                    </p>

                    {/* Tags (Features) */}
                    {product.tags && product.tags.length > 0 && (
                      <div className="mb-4">
                        <div className="flex flex-wrap gap-1">
                          {product.tags.slice(0, 3).map((tag: string, index: number) => (
                            <span key={index} className="inline-block bg-primary-subtle text-primary text-xs px-2 py-1 rounded">
                              {tag}
                            </span>
                          ))}
                          {product.tags.length > 3 && (
                            <span className="inline-block bg-gray-50 text-gray-600 text-xs px-2 py-1 rounded">
                              +{product.tags.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Price and Monetization Section */}
                    <div className="border-t border-gray-100 pt-4 mt-4">
                      {product.price && (
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-2">
                            <DollarSign className="w-4 h-4 text-green-600" />
                            <span className="text-2xl font-bold text-green-600">
                              ${product.price}
                            </span>
                          </div>
                          {product.price_per_unit && (
                            <span className="text-sm text-gray-500">
                              {product.price_per_unit}
                            </span>
                          )}
                        </div>
                      )}

                      {/* Vendor/Store Information */}
                      {(product.vendor_name || product.store_name) && (
                        <div className="mb-3">
                          <span className="text-sm text-gray-600">Available at: </span>
                          <span className="text-sm font-medium text-gray-900">
                            {product.vendor_name || product.store_name}
                          </span>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex space-x-2">
                        {product.buy_link ? (
                          <a
                            href={product.buy_link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex-1 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary-hover transition-colors flex items-center justify-center space-x-2"
                          >
                            <ShoppingCart className="w-4 h-4" />
                            <span>Buy Now</span>
                            <ExternalLink className="w-3 h-3" />
                          </a>
                        ) : (
                          <button className="flex-1 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary-hover transition-colors flex items-center justify-center space-x-2">
                            <ShoppingCart className="w-4 h-4" />
                            <span>View Details</span>
                          </button>
                        )}
                        
                        {product.compare_link && (
                          <a
                            href={product.compare_link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                          >
                            Compare
                          </a>
                        )}
                        
                        <button className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                          <Heart className="w-4 h-4 text-gray-600" />
                        </button>
                      </div>

                      {/* Affiliate Disclosure */}
                      {(product.affiliate_note || product.buy_link) && (
                        <p className="text-xs text-gray-500 mt-3">
                          {product.affiliate_note || 'We may earn a commission from purchases made through affiliate links.'}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {filteredProducts.length === 0 && (
                <div className="text-center py-12 col-span-full">
                  <Store className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No products found</h3>
                  <p className="text-gray-600">Try adjusting your search or filter criteria</p>
                </div>
              )}
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gray-100 py-16">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Need Personalized Recommendations?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Get expert guidance and track your journey with Mission Fresh's comprehensive wellness tools.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              to="/tools/nrt-guide"
              className="inline-flex items-center justify-center border-2 border-primary text-primary px-8 py-3 rounded-lg text-lg font-medium hover:bg-primary hover:text-primary-foreground transition-colors"
            >
              <Heart className="w-5 h-5 mr-2" />
              NRT Guide
              <ArrowRight className="w-5 h-5 ml-2" />
            </Link>
            <Link
              to="/auth?mode=signup"
              className="inline-flex items-center justify-center bg-primary text-primary-foreground px-8 py-3 rounded-lg text-lg font-medium hover:bg-primary-hover transition-colors"
            >
              Get Started
              <ArrowRight className="w-5 h-5 ml-2" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
