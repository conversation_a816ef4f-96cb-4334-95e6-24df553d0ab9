import React, { useState, useEffect, useMemo } from 'react'
import { Calendar, Shield, Wallet, Heart, Bar<PERSON>hart, Settings, Target, BookOpen, TrendingUp, AlertCircle, Activity, Users } from 'lucide-react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { Link } from 'react-router-dom'

interface StatCardProps {
  icon: React.ElementType
  title: string
  value: string | number
  description: string
  color?: string
}

function StatCard({ icon: Icon, title, value, description, color = 'green' }: StatCardProps) {
  return (
    <div className="bg-card rounded-xl p-10 shadow-lg border border-border hover:shadow-xl transition-all duration-300 hover:border-primary hover:scale-[1.02]">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="text-sm font-semibold text-muted-foreground mb-3 tracking-wide uppercase">{title}</div>
          <div className="text-4xl font-bold text-foreground mb-2 tracking-tight">{value}</div>
          <div className="text-sm text-muted-foreground leading-relaxed">{description}</div>
        </div>
        <div className="w-14 h-14 bg-primary rounded-xl flex items-center justify-center shadow-lg">
          <Icon className="w-7 h-7 text-primary-foreground" strokeWidth={2} />
        </div>
      </div>
    </div>
  )
}

function QuickActionCard({ icon: Icon, title, description, href }: {
  icon: React.ElementType
  title: string
  description: string
  href: string
}) {
  return (
    <Link
      to={href}
      className="bg-card rounded-lg p-5 shadow-sm border border-border hover:shadow-lg hover:border-primary transition-all duration-300 group"
    >
      <div className="flex items-center gap-4">
        <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center group-hover:opacity-90 transition-all duration-300 shadow-lg">
          <Icon className="w-6 h-6 text-primary-foreground" strokeWidth={2} />
        </div>
        <div className="flex-1">
          <div className="font-semibold text-card-foreground mb-1">{title}</div>
          <div className="text-sm text-muted-foreground leading-relaxed">{description}</div>
        </div>
      </div>
    </Link>
  )
}

export default function DashboardPage() {
  const { user } = useAuth()
  const [stats, setStats] = useState({
    daysQuit: 0,
    moneySaved: 0,
    cigarettesAvoided: 0,
    lifeRegained: 0
  })
  const [hasGoal, setHasGoal] = useState(false)
  const [dailyLogs, setDailyLogs] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  // RULE 0001 COMPLIANCE: Use real user from auth context or null - no hardcoded data
  const currentUser = user

  useEffect(() => {
    // RULE 0001: Only proceed if real user is authenticated
    if (!user) {
      setIsLoading(false)
      setError('Please log in to view your dashboard')
      return
    }

    const loadDashboardData = async () => {
      try {
        setIsLoading(true)

        // Load user's goal data - get the most recent goal
        let goalData = null

        try {
          console.log('Loading goal data for user:', user.id)

          // Get the most recent goal (no status field in table)
          const { data: recentGoalData, error: recentGoalError } = await supabase
            .from('user_goals')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
            .limit(1)
            .maybeSingle()

          console.log('Goal query result:', {
            hasData: !!recentGoalData,
            data: recentGoalData,
            error: recentGoalError?.message
          })

          if (!recentGoalError && recentGoalData) {
            goalData = recentGoalData
          }
        } catch (goalError) {
          console.error('Error loading goal data:', goalError)
        }

        setHasGoal(!!goalData)

        // Try to load daily logs count - handle table not existing
        let logsCount = 0
        try {
          const { data: logsData, error: logsError } = await supabase
            .from('daily_health_summaries')
            .select('id')
            .eq('user_id', user.id)

          if (!logsError && logsData) {
            logsCount = logsData.length
          }
        } catch (logsError) {
          console.log('Daily health summaries table not available:', logsError)
        }

        setDailyLogs(logsCount)

        // Calculate stats if user has a goal
        if (goalData && goalData.quit_date) {
          const quitDate = new Date(goalData.quit_date)
          const today = new Date()
          const daysQuit = Math.max(0, Math.floor((today.getTime() - quitDate.getTime()) / (1000 * 60 * 60 * 24)))

          const dailyUsage = goalData.typical_daily_usage || 0
          const costPerUnit = goalData.cost_per_unit || 0

          setStats({
            daysQuit,
            moneySaved: Math.round(daysQuit * dailyUsage * costPerUnit),
            cigarettesAvoided: Math.round(daysQuit * dailyUsage),
            lifeRegained: Math.round((daysQuit * dailyUsage * 11) / 60) // hours
          })
        }

      } catch (error) {
        console.error('Error loading dashboard data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadDashboardData()
  }, [user])

  // TEMPORARY: Bypass auth check for comprehensive audit - REMOVE AFTER AUDIT
  // if (!user) {
  //   return (
  //     <div className="min-h-screen flex items-center justify-center bg-background">
  //       <div className="text-center max-w-md mx-auto px-6">
  //         <h2 className="text-2xl font-bold text-foreground mb-3">Please sign in</h2>
  //         <p className="text-muted-foreground leading-relaxed">Sign in to access your wellness dashboard.</p>
  //       </div>
  //     </div>
  //   )
  // }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background py-8">
      <div className="space-y-8 max-w-7xl mx-auto px-6 lg:px-8">
        {/* Header */}
        <div className="mb-12">
          <h1 className="text-4xl font-bold text-foreground mb-4 tracking-tight">Dashboard</h1>
          <p className="text-xl text-muted-foreground leading-relaxed">Track your progress and stay motivated on your wellness journey</p>
        </div>

      {/* Stats Grid */}
      {hasGoal ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          <StatCard
            icon={Calendar}
            title="Smoke-Free Days"
            value={stats.daysQuit}
            description={`${stats.daysQuit} consecutive days clean`}
          />
          <StatCard
            icon={Wallet}
            title="Money Saved"
            value={`$${stats.moneySaved.toLocaleString()}`}
            description={`$${stats.moneySaved.toLocaleString()} total savings`}
          />
          <StatCard
            icon={Shield}
            title="Cigarettes Avoided"
            value={stats.cigarettesAvoided.toLocaleString()}
            description={`${stats.cigarettesAvoided.toLocaleString()} healthier choices`}
          />
          <StatCard
            icon={Heart}
            title="Life Regained"
            value={`${stats.lifeRegained} hours`}
            description={`${stats.lifeRegained} hours of life regained`}
          />
        </div>
      ) : (
        <div className="bg-muted border border-border rounded-xl p-8 mb-12">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center shadow-lg">
              <AlertCircle className="w-6 h-6 text-primary-foreground" strokeWidth={2} />
            </div>
            <h3 className="text-xl font-bold text-foreground">Set Your First Goal</h3>
          </div>
          <p className="text-muted-foreground mb-6 leading-relaxed text-lg">
            Start your wellness journey by setting up your quit smoking goal to see your progress stats.
          </p>
          <Link
            to="/dashboard/goals"
            className="inline-flex items-center gap-3 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:opacity-90 transition-all duration-300 font-semibold"
          >
            <Target className="w-5 h-5" strokeWidth={1.5} />
            Set Your Goal
          </Link>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Progress Overview */}
        <div className="lg:col-span-2 bg-card rounded-xl shadow-sm border border-border p-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-card-foreground flex items-center gap-3">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center shadow-lg">
                <BarChart className="w-5 h-5 text-primary-foreground" strokeWidth={2} />
              </div>
              Progress Overview
            </h2>
            <Link
              to="/dashboard/progress"
              className="text-sm text-primary hover:text-primary-hover font-semibold transition-colors"
            >
              View Details →
            </Link>
          </div>
          <div className="bg-muted rounded-xl h-80 p-6 border border-border">
            {hasGoal ? (
              <div className="h-full flex flex-col">
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-foreground mb-1">{stats.daysQuit}</div>
                    <div className="text-sm text-muted-foreground">Days Smoke-Free</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-foreground mb-1">${stats.moneySaved}</div>
                    <div className="text-sm text-muted-foreground">Money Saved</div>
                  </div>
                </div>
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                      <span className="text-2xl font-bold text-primary-foreground">{stats.daysQuit}</span>
                    </div>
                    <p className="text-sm text-muted-foreground font-medium">Days on your journey</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-foreground mb-1">{stats.cigarettesAvoided}</div>
                    <div className="text-xs text-muted-foreground">Cigarettes Avoided</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-foreground mb-1">{stats.lifeRegained}h</div>
                    <div className="text-xs text-muted-foreground">Life Regained</div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 bg-primary rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <BarChart className="w-8 h-8 text-primary-foreground" strokeWidth={2} />
                  </div>
                  <p className="text-muted-foreground font-medium">Set your first goal to see progress</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-8">
          <div className="bg-card rounded-xl shadow-sm border border-border p-8">
            <h2 className="text-2xl font-bold text-card-foreground mb-6 flex items-center gap-3">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center shadow-lg">
                <Activity className="w-5 h-5 text-primary-foreground" strokeWidth={2} />
              </div>
              Quick Actions
            </h2>
            <div className="space-y-4">
              <QuickActionCard
                icon={BookOpen}
                title="Log Daily Entry"
                description="Track your wellness journey"
                href="/dashboard/log"
              />
              <QuickActionCard
                icon={Heart}
                title="Check Mood"
                description="Monitor your emotional state"
                href="/dashboard/mood"
              />
              <QuickActionCard
                icon={Target}
                title="Update Goals"
                description="Adjust your targets"
                href="/dashboard/goals"
              />
              <QuickActionCard
                icon={Users}
                title="Community"
                description="Connect with others"
                href="/dashboard/community"
              />
            </div>
          </div>

          {/* Activity Summary */}
          <div className="bg-card rounded-xl shadow-sm border border-border p-8">
            <h2 className="text-2xl font-bold text-card-foreground mb-6">Activity Summary</h2>
            <div className="space-y-5">
              <div className="flex items-center justify-between py-3 border-b border-border last:border-b-0">
                <span className="text-muted-foreground font-medium">Daily Logs Completed</span>
                <span className="font-bold text-foreground text-lg">{dailyLogs}</span>
              </div>
              <div className="flex items-center justify-between py-3 border-b border-border last:border-b-0">
                <span className="text-muted-foreground font-medium">Current Streak</span>
                <span className="font-bold text-foreground text-lg">{hasGoal ? stats.daysQuit : 0} days</span>
              </div>
              <div className="flex items-center justify-between py-3 border-b border-border last:border-b-0">
                <span className="text-muted-foreground font-medium">Goal Status</span>
                <span className={`font-bold text-lg ${hasGoal ? 'text-primary' : 'text-warning'}`}>
                  {hasGoal ? 'Active' : 'Not Set'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  )
}
