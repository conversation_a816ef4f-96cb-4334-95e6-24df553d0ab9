import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'
import { 
  MessageCircle, 
  Heart, 
  Users, 
  Plus, 
  TrendingUp, 
  Clock,
  Star,
  Award,
  Send
} from 'lucide-react'

interface CommunityPost {
  id: string
  title: string
  content: string
  post_type: string
  likes_count: number
  created_at: string
  updated_at: string
  user_id: string

}

export default function CommunityPage() {
  const { user } = useAuth()
  const [posts, setPosts] = useState<CommunityPost[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreatePost, setShowCreatePost] = useState(false)
  const [selectedFilter, setSelectedFilter] = useState('all')

  useEffect(() => {
    loadPosts()
  }, [selectedFilter])

  const loadPosts = async () => {
    try {
      setLoading(true)
      let query = supabase
        .from('community_posts')
        .select('*')
        .order('created_at', { ascending: false })

      if (selectedFilter !== 'all') {
        query = query.eq('post_type', selectedFilter)
      }

      const { data, error } = await query

      if (error) throw error
      setPosts(data || [])
    } catch (error) {
      console.error('Error loading posts:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLike = async (postId: string) => {
    if (!user) return

    try {
      const post = posts.find(p => p.id === postId)
      if (!post) return

      const { error } = await supabase
        .from('community_posts')
        .update({ likes_count: post.likes_count + 1 })
        .eq('id', postId)

      if (error) throw error

      setPosts(posts.map(p => 
        p.id === postId 
          ? { ...p, likes_count: p.likes_count + 1 }
          : p
      ))
    } catch (error) {
      console.error('Error liking post:', error)
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)

    if (diffDays > 0) return `${diffDays}d ago`
    if (diffHours > 0) return `${diffHours}h ago`
    return 'Just now'
  }

  // RULE 0001: Load post types dynamically from database
  const [postTypes, setPostTypes] = useState([
    { value: 'all', label: 'All Posts', icon: Users },
    { value: 'success', label: 'Success Stories', icon: Star },
    { value: 'support', label: 'Support', icon: Heart },
    { value: 'milestone', label: 'Milestones', icon: Award },
    { value: 'question', label: 'Questions', icon: MessageCircle }
  ])

  // Load post types from database (fallback to static if table doesn't exist)
  useEffect(() => {
    const loadPostTypes = async () => {
      try {
        const { data, error } = await supabase
          .from('post_types')
          .select('*')
          .order('display_order')
        
        if (data && data.length > 0) {
          // Convert database format to component format
          const dynamicPostTypes = [
            { value: 'all', label: 'All Posts', icon: Users },
            ...data.map(type => ({
              value: type.value,
              label: type.label,
              icon: type.icon_name === 'Star' ? Star : 
                    type.icon_name === 'Heart' ? Heart :
                    type.icon_name === 'Award' ? Award :
                    type.icon_name === 'MessageCircle' ? MessageCircle : Users
            }))
          ]
          setPostTypes(dynamicPostTypes)
        }
      } catch (error) {
        // Keep fallback postTypes if database query fails
        console.log('Using fallback post types (post_types table not found)')
      }
    }
    loadPostTypes()
  }, [])

  return (
    <div className="min-h-screen bg-background py-12">
      <div className="max-w-6xl mx-auto px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-foreground mb-6 tracking-tight">Community Support</h1>
          <p className="text-xl text-muted-foreground leading-relaxed">Connect with others on similar wellness journeys</p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="bg-card rounded-xl shadow-lg border border-border p-8 text-center hover:shadow-xl transition-all duration-300">
            <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20">
              <Users className="w-8 h-8 text-primary" strokeWidth={1.5} />
            </div>
            <h3 className="text-4xl font-bold text-card-foreground mb-2">{posts.length}</h3>
            <p className="text-muted-foreground text-lg">Community Posts</p>
          </div>

          <div className="bg-card rounded-xl shadow-lg border border-border p-8 text-center hover:shadow-xl transition-all duration-300">
            <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20">
              <TrendingUp className="w-8 h-8 text-primary" strokeWidth={1.5} />
            </div>
            <h3 className="text-4xl font-bold text-card-foreground mb-2">
              {posts.reduce((sum, post) => sum + (post.likes_count || 0), 0)}
            </h3>
            <p className="text-muted-foreground text-lg">Total Likes</p>
          </div>

          <div className="bg-card rounded-xl shadow-lg border border-border p-8 text-center hover:shadow-xl transition-all duration-300">
            <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20">
              <Star className="w-8 h-8 text-primary" strokeWidth={1.5} />
            </div>
            <h3 className="text-4xl font-bold text-card-foreground mb-2">
              {posts.filter(p => p.post_type === 'success').length}
            </h3>
            <p className="text-muted-foreground text-lg">Success Stories</p>
          </div>
        </div>

        {/* Actions and Filters */}
        <div className="bg-card rounded-xl shadow-lg border border-border p-8 mb-12">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
            <div className="flex flex-wrap gap-3">
              {postTypes.map((type) => (
                <button
                  key={type.value}
                  onClick={() => setSelectedFilter(type.value)}
                  className={`inline-flex items-center px-5 py-3 rounded-lg font-semibold transition-all duration-300 ${
                    selectedFilter === type.value
                      ? 'bg-primary text-primary-foreground shadow-lg'
                      : 'bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                  }`}
                >
                  <type.icon className="w-5 h-5 mr-3" strokeWidth={1.5} />
                  {type.label}
                </button>
              ))}
            </div>

            {user && (
              <button
                onClick={() => setShowCreatePost(true)}
                className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg font-semibold hover:bg-primary-hover transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                <Plus className="w-5 h-5 mr-3" strokeWidth={1.5} />
                Share Your Story
              </button>
            )}
          </div>
        </div>

        {/* Posts */}
        <div className="space-y-8">
          {loading ? (
            <div className="text-center py-16">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto"></div>
            </div>
          ) : posts.length > 0 ? (
            posts.map((post) => (
              <div key={post.id} className="bg-card rounded-xl shadow-lg border border-border p-8 hover:shadow-xl transition-all duration-300">
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20">
                      <Users className="w-6 h-6 text-primary" strokeWidth={1.5} />
                    </div>
                    <div>
                      <h3 className="font-bold text-card-foreground text-lg">
                        Community Member
                      </h3>
                      <div className="flex items-center space-x-3 text-muted-foreground mt-1">
                        <Clock className="w-4 h-4" strokeWidth={1.5} />
                        <span>{formatTimeAgo(post.created_at)}</span>
                        <span className="px-3 py-1 bg-muted rounded-full text-sm font-medium">
                          {post.post_type}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <h4 className="text-2xl font-bold text-card-foreground mb-4">{post.title}</h4>
                <p className="text-muted-foreground leading-relaxed mb-6 text-lg">{post.content}</p>

                <div className="flex items-center justify-between pt-6 border-t border-border">
                  <button
                    onClick={() => handleLike(post.id)}
                    className="inline-flex items-center text-muted-foreground hover:text-primary transition-all duration-300 font-semibold"
                    disabled={!user}
                  >
                    <Heart className="w-5 h-5 mr-3" strokeWidth={1.5} />
                    {post.likes_count} {post.likes_count === 1 ? 'Like' : 'Likes'}
                  </button>

                  <button className="text-primary hover:text-primary-hover font-semibold transition-all duration-300 inline-flex items-center">
                    <MessageCircle className="w-5 h-5 mr-2" strokeWidth={1.5} />
                    Reply
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-20 bg-card rounded-xl shadow-lg border border-border">
              <div className="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20">
                <MessageCircle className="w-10 h-10 text-primary" strokeWidth={1.5} />
              </div>
              <h3 className="text-2xl font-bold text-card-foreground mb-4">No posts yet</h3>
              <p className="text-muted-foreground mb-8 text-lg">Be the first to share your story with the community!</p>
              {user && (
                <button
                  onClick={() => setShowCreatePost(true)}
                  className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg font-semibold hover:bg-primary-hover transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  <Plus className="w-5 h-5 mr-3" strokeWidth={1.5} />
                  Create First Post
                </button>
              )}
            </div>
          )}
        </div>

        {/* Create Post Modal */}
        {showCreatePost && (
          <CreatePostModal
            onClose={() => setShowCreatePost(false)}
            onSuccess={() => {
              setShowCreatePost(false)
              loadPosts()
            }}
          />
        )}
      </div>
    </div>
  )
}

// Create Post Modal Component
interface CreatePostModalProps {
  onClose: () => void
  onSuccess: () => void
}

function CreatePostModal({ onClose, onSuccess }: CreatePostModalProps) {
  const { user } = useAuth()
  const [title, setTitle] = useState('')
  const [content, setContent] = useState('')
  const [postType, setPostType] = useState('support')
  const [submitting, setSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !title.trim() || !content.trim()) return

    try {
      setSubmitting(true)
      const { error } = await supabase
        .from('community_posts')
        .insert({
          title: title.trim(),
          content: content.trim(),
          post_type: postType,
          user_id: user.id,
          likes_count: 0
        })

      if (error) throw error
      onSuccess()
    } catch (error) {
      console.error('Error creating post:', error)
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-card rounded-xl shadow-2xl w-full max-w-2xl border border-border">
        <div className="px-8 py-6 border-b border-border">
          <h3 className="text-2xl font-bold text-card-foreground">Share Your Story</h3>
        </div>

        <form onSubmit={handleSubmit} className="p-8 space-y-6">
          <div>
            <label className="block text-base font-semibold text-card-foreground mb-3">
              Post Type
            </label>
            <select
              value={postType}
              onChange={(e) => setPostType(e.target.value)}
              className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
            >
              <option value="support">Support Request</option>
              <option value="success">Success Story</option>
              <option value="milestone">Milestone</option>
              <option value="question">Question</option>
            </select>
          </div>

          <div>
            <label className="block text-base font-semibold text-card-foreground mb-3">
              Title
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Give your post a descriptive title..."
              className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
              required
            />
          </div>

          <div>
            <label className="block text-base font-semibold text-card-foreground mb-3">
              Content
            </label>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="Share your thoughts, experiences, or questions..."
              rows={8}
              className="w-full px-4 py-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg leading-relaxed resize-none"
              required
            />
          </div>

          <div className="flex space-x-4 pt-6">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-6 py-3 border border-border text-muted-foreground rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold text-lg"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={submitting || !title.trim() || !content.trim()}
              className="flex-1 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed font-semibold text-lg shadow-lg hover:shadow-xl"
            >
              {submitting ? (
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-foreground mx-auto"></div>
              ) : (
                <>
                  <Send className="w-5 h-5 mr-3 inline" strokeWidth={1.5} />
                  Post
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
