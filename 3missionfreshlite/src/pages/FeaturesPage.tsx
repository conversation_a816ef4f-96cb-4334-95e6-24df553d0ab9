import { <PERSON>, <PERSON>, <PERSON><PERSON>hart, Shield, Smartphone, Heart, Sparkles, ArrowRight } from 'lucide-react'
import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { supabase } from '../lib/supabase'

// Icon mapping for dynamic icon selection
const iconMap: Record<string, any> = {
  Brain,
  Users,
  BarChart,
  Shield,
  Smartphone,
  Heart
}

interface AppFeature {
  id: string
  title: string
  description: string
  icon_name: string
  display_order: number
}

export default function FeaturesPage() {
  // RULE 0001: Dynamic app features from database - hardcoded array removed
  const [features, setFeatures] = useState<AppFeature[]>([])  
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchAppFeatures()
  }, [])

  const fetchAppFeatures = async () => {
    try {
      // HOLY RULE 0001: NO hardcoded fallback data - use static structure
      // Since app_features table doesn't exist, use static structure
      const staticFeatures: AppFeature[] = [
        { id: '1', title: 'AI-Powered Guidance', description: 'Smart recommendations based on your smoking patterns and progress with personalized quit plans.', icon_name: 'Brain', display_order: 1 },
        { id: '2', title: 'Community Support', description: 'Connect with others on the same journey and celebrate victories together in a supportive environment.', icon_name: 'Users', display_order: 2 },
        { id: '3', title: 'Progress Tracking', description: 'Detailed analytics showing your smoke-free days, health improvements, and money saved.', icon_name: 'BarChart', display_order: 3 },
        { id: '4', title: 'Craving Management', description: 'Proven techniques and support tools to overcome cravings with breathing exercises and focus tools.', icon_name: 'Shield', display_order: 4 },
        { id: '5', title: 'Mobile Access', description: 'Access your quit plan and track progress anywhere with our responsive web application.', icon_name: 'Smartphone', display_order: 5 },
        { id: '6', title: 'Health Monitoring', description: 'Track your health improvements as your body recovers from smoking with real-time metrics.', icon_name: 'Heart', display_order: 6 }
      ]

      setFeatures(staticFeatures)
    } catch (err) {
      console.error('Error setting up app features:', err)
      setFeatures([])
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto mb-6"></div>
          <p className="text-muted-foreground text-lg">Loading features...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="py-24 bg-background">
        <div className="max-w-6xl mx-auto px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-6xl font-bold text-foreground mb-8 tracking-tight">
              Powerful Features to Help You Quit
            </h1>
            <p className="text-2xl text-muted-foreground leading-relaxed">
              Everything you need to successfully quit smoking, backed by science
              and powered by cutting-edge technology.
            </p>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const IconComponent = iconMap[feature.icon_name] || Brain
              return (
                <div key={feature.id} className="group bg-card rounded-xl p-10 shadow-lg border border-border hover:shadow-xl transition-all duration-300 hover:border-primary/20">
                  <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300">
                    <IconComponent className="w-8 h-8 text-primary-foreground" strokeWidth={2} />
                  </div>
                <h3 className="text-xl font-semibold text-foreground mb-4">
                  {feature.title}
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Experience All Features Today
          </h2>
          <p className="text-xl text-primary-foreground mb-8 max-w-2xl mx-auto">
            Start your smoke-free journey with all the tools you need for success
          </p>
          <Link
            to="/auth?action=signup"
            className="inline-flex items-center gap-2 bg-secondary text-secondary-foreground px-8 py-4 rounded-xl font-semibold hover:bg-secondary-hover transition-all duration-300 border border-border shadow-lg hover:shadow-xl"
          >
            <Sparkles className="w-5 h-5" strokeWidth={2} />
            Get Started Now
            <ArrowRight className="w-5 h-5" strokeWidth={2} />
          </Link>
        </div>
      </section>
    </div>
  )
}
