import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Package, Shield, Heart, Star, Loader2, AlertTriangle, CheckCircle, ArrowRight } from 'lucide-react'

export default function ProductDetailsPage() {
  const [activeTab, setActiveTab] = useState('overview')
  const [nrtProducts, setNrtProducts] = useState<any[]>([])
  const [alternatives, setAlternatives] = useState<any[]>([])
  const [reviews, setReviews] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Package },
    { id: 'nrt', label: 'NRT Products', icon: Shield },
    { id: 'alternatives', label: 'Alternatives', icon: Heart },
    { id: 'reviews', label: 'Reviews', icon: Star },
  ]

  // HOLY RULE 0001: Load real product data from Supabase
  useEffect(() => {
    loadProductData()
  }, [])

  const loadProductData = async () => {
    try {
      setLoading(true)
      
      // Create Supabase client without schema restriction to access products table
      const { createClient } = await import('@supabase/supabase-js')
      const supabaseNoSchema = createClient(
        'https://yekarqanirdkdckimpna.supabase.co',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'
      )
      
      // Fetch NRT products from dedicated nrt_products table - RULE 0001: real data only
      const { data: nrtData, error: nrtError } = await supabaseNoSchema
        .from('nrt_products')
        .select('*')
        .eq('is_active', true)
        .order('effectiveness_rating', { ascending: false })

      if (nrtError) {
        console.error('Error loading NRT products:', nrtError)
        setError('Failed to load NRT products')
      } else {
        setNrtProducts(nrtData || [])
      }

      // Fetch alternatives and reviews from Supabase - RULE 0001: real data only
      const [alternativesRes, reviewsRes] = await Promise.all([
        supabaseNoSchema.from('product_alternatives').select('*').eq('product_type', 'nrt'),
        supabaseNoSchema.from('product_reviews').select('*').eq('product_type', 'nrt')
      ])

      setAlternatives(alternativesRes.data || [])
      setReviews(reviewsRes.data || [])
      
    } catch (error) {
      console.error('Error loading product data:', error)
      setError('Failed to load product information')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <section className="bg-background py-24">
        <div className="max-w-6xl mx-auto px-6 lg:px-8 text-center">
          <div className="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20">
            <Package className="w-10 h-10 text-primary" strokeWidth={1.5} />
          </div>
          <h1 className="text-6xl font-bold text-foreground mb-8 tracking-tight">
            Product Details
          </h1>
          <p className="text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto">
            Comprehensive reviews and comparisons of cessation products and treatments
          </p>
        </div>
      </section>

      {/* Tab Navigation */}
      <section className="bg-card border-b border-border py-16">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {tabs.map((tab) => {
              const IconComponent = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`p-8 rounded-xl border-2 transition-all duration-300 text-center hover:shadow-lg ${
                    activeTab === tab.id
                      ? 'border-primary bg-primary/10'
                      : 'border-border hover:border-primary bg-card'
                  }`}
                >
                  <IconComponent className={`w-10 h-10 mx-auto mb-4 ${
                    activeTab === tab.id ? 'text-primary' : 'text-muted-foreground'
                  }`} strokeWidth={1.5} />
                  <h3 className={`font-semibold text-lg ${
                    activeTab === tab.id ? 'text-primary' : 'text-card-foreground'
                  }`}>
                    {tab.label}
                  </h3>
                </button>
              )
            })}
          </div>
        </div>
      </section>

      {/* Content */}
      <section className="py-12">
        <div className="max-w-6xl mx-auto px-6">
          {activeTab === 'overview' && (
            <div className="space-y-8">
              <div className="bg-card p-8 rounded-xl shadow-lg border border-border">
                <h2 className="text-3xl font-bold text-card-foreground mb-8 tracking-tight">
                  Cessation Product Categories
                </h2>
                <div className="grid md:grid-cols-3 gap-8">
                  <div className="text-center">
                    <div className="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20">
                      <Shield className="w-10 h-10 text-primary" strokeWidth={1.5} />
                    </div>
                    <h3 className="font-bold text-card-foreground mb-3 text-lg">NRT Products</h3>
                    <p className="text-muted-foreground leading-relaxed">Nicotine replacement therapy options</p>
                  </div>
                  <div className="text-center">
                    <div className="w-20 h-20 bg-accent/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-accent/20">
                      <Heart className="w-10 h-10 text-accent" strokeWidth={1.5} />
                    </div>
                    <h3 className="font-bold text-card-foreground mb-3 text-lg">Medications</h3>
                    <p className="text-muted-foreground leading-relaxed">Prescription cessation aids</p>
                  </div>
                  <div className="text-center">
                    <div className="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20">
                      <Star className="w-10 h-10 text-primary" strokeWidth={1.5} />
                    </div>
                    <h3 className="font-bold text-card-foreground mb-3 text-lg">Alternatives</h3>
                    <p className="text-muted-foreground leading-relaxed">E-cigarettes and other options</p>
                  </div>
                </div>
              </div>

              <div className="bg-primary/5 border border-primary/20 p-8 rounded-xl">
                <h2 className="text-3xl font-bold text-primary mb-8 tracking-tight">Success Rate Comparison</h2>
                <div className="grid md:grid-cols-3 gap-8">
                  <div className="bg-card p-8 rounded-xl shadow-lg border border-border">
                    <h3 className="font-bold text-card-foreground mb-4 text-lg">Combination Therapy</h3>
                    <div className="text-4xl font-bold text-primary mb-4">65-75%</div>
                    <p className="text-muted-foreground leading-relaxed">NRT + Behavioral support</p>
                  </div>
                  <div className="bg-card p-8 rounded-xl shadow-lg border border-border">
                    <h3 className="font-bold text-card-foreground mb-4 text-lg">Prescription Meds</h3>
                    <div className="text-4xl font-bold text-accent mb-4">30-44%</div>
                    <p className="text-muted-foreground leading-relaxed">Varenicline, Bupropion</p>
                  </div>
                  <div className="bg-card p-8 rounded-xl shadow-lg border border-border">
                    <h3 className="font-bold text-card-foreground mb-4 text-lg">NRT Alone</h3>
                    <div className="text-4xl font-bold text-primary mb-4">15-25%</div>
                    <p className="text-muted-foreground leading-relaxed">Patches, gum, lozenges</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'nrt' && (
            <div className="space-y-6">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold text-foreground mb-4 tracking-tight">Nicotine Replacement Therapy</h2>
                <p className="text-xl text-muted-foreground leading-relaxed">FDA-approved products to help manage withdrawal symptoms</p>
              </div>

              {loading && (
                <div className="flex items-center justify-center py-16">
                  <Loader2 className="w-12 h-12 text-primary animate-spin" strokeWidth={1.5} />
                  <span className="ml-4 text-muted-foreground text-lg">Loading NRT products...</span>
                </div>
              )}

              {!loading && error && (
                <div className="text-center py-16">
                  <p className="text-destructive mb-6 text-lg">{error}</p>
                  <button
                    onClick={loadProductData}
                    className="bg-primary text-primary-foreground px-8 py-4 rounded-lg hover:bg-primary-hover transition-all duration-300 font-semibold shadow-lg hover:shadow-xl"
                  >
                    Try Again
                  </button>
                </div>
              )}

              {!loading && !error && nrtProducts.length === 0 && (
                <div className="text-center py-16">
                  <p className="text-muted-foreground text-lg">No NRT products available at this time.</p>
                </div>
              )}

              {!loading && !error && nrtProducts.length > 0 && (
                <div className="grid lg:grid-cols-2 gap-6">
                  {nrtProducts.map((product) => (
                    <div key={product.id} className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900">{product.name}</h3>
                          <p className="text-gray-600">{product.brand}</p>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center space-x-1 mb-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="font-medium">{product.rating}</span>
                            <span className="text-gray-500 text-sm">({product.reviews})</span>
                          </div>
                          <div className="text-lg font-bold text-green-600">{product.effectiveness}</div>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Dosage</dt>
                          <dd className="text-sm text-gray-900">{product.dosage}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Price Range</dt>
                          <dd className="text-sm text-gray-900">{product.price}</dd>
                        </div>
                      </div>

                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="text-sm font-medium text-green-800 mb-2 flex items-center">
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Pros
                          </h4>
                          <ul className="space-y-1">
                            {product.pros?.map((pro: string, index: number) => (
                              <li key={index} className="text-sm text-gray-600 flex items-start">
                                <CheckCircle className="w-4 h-4 text-green-500 mr-1 mt-0.5 flex-shrink-0" />
                                {pro}
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-red-800 mb-2 flex items-center">
                            <AlertTriangle className="w-4 h-4 mr-1" />
                            Cons
                          </h4>
                          <ul className="space-y-1">
                            {product.cons?.map((con: string, index: number) => (
                              <li key={index} className="text-sm text-gray-600 flex items-start">
                                <span className="w-1 h-1 bg-red-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                {con}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'alternatives' && (
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Alternative Cessation Methods</h2>
                <p className="text-gray-600">Beyond traditional NRT - exploring all options</p>
              </div>

              {loading && (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
                  <span className="ml-2 text-gray-600">Loading alternatives...</span>
                </div>
              )}

              {!loading && error && (
                <div className="text-center py-12">
                  <p className="text-red-600 mb-4">{error}</p>
                  <button 
                    onClick={loadProductData}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Try Again
                  </button>
                </div>
              )}

              {!loading && !error && alternatives.length === 0 && (
                <div className="text-center py-12">
                  <p className="text-gray-600">No alternatives available at this time.</p>
                </div>
              )}

              {!loading && !error && alternatives.length > 0 && (
                <div className="space-y-6">
                  {alternatives.map((alt) => (
                    <div key={alt.id} className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900">{alt.name}</h3>
                          <span className="inline-block bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded mt-1">
                            {alt.type}
                          </span>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center space-x-1 mb-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="font-medium">{alt.rating}</span>
                          </div>
                          <div className="text-lg font-bold text-green-600">{alt.effectiveness}</div>
                        </div>
                      </div>

                      <div className="grid md:grid-cols-3 gap-4 mb-6">
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Mechanism</dt>
                          <dd className="text-sm text-gray-900">{alt.mechanism}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Cost</dt>
                          <dd className="text-sm text-gray-900">{alt.price}</dd>
                        </div>
                      </div>

                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-sm font-medium text-green-800 mb-2 flex items-center">
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Advantages
                          </h4>
                          <ul className="space-y-1">
                            {alt.pros?.map((pro: string, index: number) => (
                              <li key={index} className="text-sm text-gray-600 flex items-start">
                                <span className="w-1 h-1 bg-primary rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                {pro}
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-red-800 mb-2 flex items-center">
                            <AlertTriangle className="w-4 h-4 mr-1" />
                            Limitations
                          </h4>
                          <ul className="space-y-1">
                            {alt.cons?.map((con: string, index: number) => (
                              <li key={index} className="text-sm text-gray-600 flex items-start">
                                <span className="w-1 h-1 bg-red-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                {con}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

          {(activeTab as string) === 'reviews' && (
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Real User Reviews</h2>
                <p className="text-gray-600">Authentic experiences from people who have quit smoking</p>
              </div>

              <div className="space-y-6">
                {reviews.map((review) => (
                  <div key={review.id} className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="font-semibold text-gray-900">{review.user}</h3>
                          {review.verified && (
                            <span className="bg-primary-muted text-primary-text text-xs font-medium px-2 py-1 rounded flex items-center">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Verified
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">{review.product}</p>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-1 mb-1">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`w-4 h-4 ${
                                i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <p className="text-sm text-gray-500">{review.date}</p>
                      </div>
                    </div>

                    <blockquote className="text-gray-700 italic mb-4 text-sm leading-relaxed">
                      "{review.review}"
                    </blockquote>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-xs font-medium text-green-800 mb-2 flex items-center uppercase tracking-wide">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          What Worked
                        </h4>
                        <ul className="space-y-1">
                          {review.pros?.map((pro: string, index: number) => (
                            <li key={index} className="text-xs text-gray-600 flex items-start">
                              <span className="w-1 h-1 bg-green-500 rounded-full mt-1.5 mr-2 flex-shrink-0"></span>
                              {pro}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="text-xs font-medium text-red-800 mb-2 flex items-center uppercase tracking-wide">
                          <AlertTriangle className="w-3 h-3 mr-1" />
                          Challenges
                        </h4>
                        <ul className="space-y-1">
                          {review.cons?.map((con: string, index: number) => (
                            <li key={index} className="text-xs text-gray-600 flex items-start">
                              <span className="w-1 h-1 bg-red-500 rounded-full mt-1.5 mr-2 flex-shrink-0"></span>
                              {con}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )
      }
      </div>
      </section>

      {/* CTA Section */}
      <section className="bg-primary/5 border-y border-primary/20 py-20">
        <div className="max-w-5xl mx-auto px-8 text-center">
          <h2 className="text-4xl font-bold text-primary mb-6 tracking-tight">
            Ready to Find Your Perfect Cessation Method?
          </h2>
          <p className="text-xl text-primary/80 mb-12 leading-relaxed max-w-3xl mx-auto">
            Join Mission Fresh for personalized recommendations, tracking, and support.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-6">
            <Link
              to="/tools/nrt-guide"
              className="inline-flex items-center justify-center border-2 border-primary text-primary px-10 py-4 rounded-xl text-lg font-semibold hover:bg-primary hover:text-primary-foreground transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <Shield className="w-6 h-6 mr-3" strokeWidth={1.5} />
              NRT Guide
              <ArrowRight className="w-6 h-6 ml-3" strokeWidth={1.5} />
            </Link>
            <Link
              to="/auth?mode=signup"
              className="inline-flex items-center justify-center bg-primary text-primary-foreground px-10 py-4 rounded-xl text-lg font-semibold hover:bg-primary-hover transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Get Started
              <ArrowRight className="w-6 h-6 ml-3" strokeWidth={1.5} />
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
