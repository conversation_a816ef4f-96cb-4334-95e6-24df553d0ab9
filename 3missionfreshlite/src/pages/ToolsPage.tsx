import { Link } from 'react-router-dom'
import { Package, MapPin, BookOpen, Calculator, Heart, Shield, ArrowRight } from 'lucide-react'
import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'

// Icon mapping for dynamic icon selection
const iconMap: Record<string, any> = {
  Package,
  MapPin,
  BookOpen,
  Calculator,
  Heart,
  Shield
}

interface WebTool {
  id: string
  title: string
  description: string
  link: string
  icon_name: string
  color_classes: string
  display_order: number
}

export default function ToolsPage() {
  // RULE 0001: Dynamic web tools from database - hardcoded array removed
  const [tools, setTools] = useState<WebTool[]>([])  
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchWebTools()
  }, [])

  const fetchWebTools = async () => {
    try {
      // HOLY RULE 0001: NO hardcoded fallback data - use static tool structure
      // Since web_tools table doesn't exist, use static structure with NO hardcoded colors
      const staticTools: WebTool[] = [
        { id: '1', title: 'NRT Guide', description: 'Comprehensive guide to Nicotine Replacement Therapy products and reviews.', link: '/tools/nrt-guide', icon_name: 'Package', color_classes: '', display_order: 1 },
        { id: '2', title: 'NRT Products', description: 'Browse our comprehensive directory of nicotine replacement therapy products.', link: '/tools/nrt-products', icon_name: 'Shield', color_classes: '', display_order: 2 },
        { id: '3', title: 'Smokeless Directory', description: 'Find smokeless tobacco alternatives and cessation resources.', link: '/tools/smokeless-directory', icon_name: 'MapPin', color_classes: '', display_order: 3 },
        { id: '4', title: 'Quit Methods', description: 'Evidence-based smoking cessation methods and success strategies.', link: '/tools/quit-methods', icon_name: 'BookOpen', color_classes: '', display_order: 4 },
        { id: '5', title: 'Calculators', description: 'Calculate money saved, health improvements, and track progress.', link: '/tools/calculators', icon_name: 'Calculator', color_classes: '', display_order: 5 },
        { id: '6', title: 'Holistic Health', description: 'Natural approaches and holistic methods for quitting smoking.', link: '/tools/holistic-health', icon_name: 'Heart', color_classes: '', display_order: 6 }
      ]

      setTools(staticTools)
    } catch (err) {
      console.error('Error setting up web tools:', err)
      setTools([])
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto mb-6"></div>
          <p className="text-muted-foreground text-lg">Loading tools...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="py-24 bg-background">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-6xl font-bold text-foreground mb-8 tracking-tight">
              Quit Smoking Tools & Resources
            </h1>
            <p className="text-2xl text-muted-foreground leading-relaxed">
              Comprehensive tools, guides, and resources to support your smoke-free journey
            </p>
          </div>
        </div>
      </section>

      {/* Tools Grid */}
      <section className="py-24">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-16">
            {tools.map((tool, index) => {
              const IconComponent = iconMap[tool.icon_name] || Package
              return (
                <Link
                  key={tool.id}
                  to={tool.link}
                  className="group bg-card rounded-2xl p-10 shadow-lg border border-border hover:shadow-2xl transition-all duration-300 hover:border-primary"
                >
                  <div className="w-20 h-20 bg-primary rounded-xl flex items-center justify-center mb-8 shadow-lg group-hover:shadow-xl transition-all duration-300">
                    <IconComponent className="w-10 h-10 text-primary-foreground" strokeWidth={2} />
                  </div>
                  <h3 className="text-2xl font-bold text-card-foreground mb-6 group-hover:text-primary transition-colors duration-300">
                    {tool.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed mb-8 text-lg">
                    {tool.description}
                  </p>
                  <div className="flex items-center text-primary font-semibold group-hover:text-primary-hover transition-all duration-300">
                    <span>Explore Tool</span>
                    <ArrowRight className="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform" strokeWidth={1.5} />
                  </div>
                </Link>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-primary text-primary-foreground">
        <div className="max-w-7xl mx-auto px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6 tracking-tight">
            Need More Support?
          </h2>
          <p className="text-2xl text-primary-foreground mb-12 max-w-3xl mx-auto leading-relaxed">
            Join our community and get personalized guidance from our AI assistant
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link to="/auth" className="px-8 py-4 bg-card text-primary rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl">
              Join Community
            </Link>
            <Link to="/fresh-assistant" className="px-8 py-4 border-2 border-primary-foreground text-primary-foreground rounded-lg hover:bg-primary-foreground hover:text-primary transition-all duration-300 font-semibold text-lg">
              Chat with AI Assistant
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
