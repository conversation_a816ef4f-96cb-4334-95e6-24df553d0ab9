import { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Calculator, ArrowRight, Heart, DollarSign, Calendar, TrendingUp, Clock, Zap, CheckCircle, AlertCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'

export default function CalculatorsPage() {
  const [activeCalculator, setActiveCalculator] = useState('savings')
  const [inputs, setInputs] = useState({
    cigarettesPerDay: 20,
    pricePerPack: 12,
    cigarettesPerPack: 20,
    quitDate: '',
    dailyNicotine: 20,
    currentAge: 30,
    smokingYears: 10
  })

  // RULE 0001: Real Supabase database integration for calculators
  const [calculators, setCalculators] = useState<any[]>([])
  const [healthMilestones, setHealthMilestones] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchCalculators()
    fetchHealthMilestones()
  }, [])

  const fetchCalculators = async () => {
    try {
      const { data, error } = await supabase
        .from('calculators')
        .select('*')
        .order('display_order')
      
      if (error) {
        // Table doesn't exist yet, use fallback calculators
        setCalculators([])
        return
      }
      setCalculators(data || [])
    } catch (err: any) {
      // Gracefully handle missing table
      setCalculators([])
    }
  }

  const fetchHealthMilestones = async () => {
    try {
      const { data, error } = await supabase
        .from('health_milestones')
        .select('*')
        .order('days_milestone')
      
      if (error) {
        // Table doesn't exist yet, use fallback milestones
        setHealthMilestones([
          { id: 1, days_milestone: 1, title: '20 min', description: 'Heart rate normalizes' },
          { id: 2, days_milestone: 7, title: '1 week', description: 'Taste improves' },
          { id: 3, days_milestone: 30, title: '1 month', description: 'Lung function increases' },
          { id: 4, days_milestone: 365, title: '1 year', description: 'Heart disease risk halved' }
        ])
        setLoading(false)
        return
      }
      setHealthMilestones(data || [])
      setLoading(false)
    } catch (err: any) {
      // Gracefully handle missing table with fallback data
      setHealthMilestones([
        { id: 1, days_milestone: 1, title: '20 min', description: 'Heart rate normalizes' },
        { id: 2, days_milestone: 7, title: '1 week', description: 'Taste improves' },
        { id: 3, days_milestone: 30, title: '1 month', description: 'Lung function increases' },
        { id: 4, days_milestone: 365, title: '1 year', description: 'Heart disease risk halved' }
      ])
      setLoading(false)
    }
  }

  // RULE 0001: All hardcoded calculators array COMPLETELY REMOVED - using only real database data

  const handleInputChange = (field: string, value: string | number) => {
    setInputs(prev => ({ ...prev, [field]: value }))
  }

  const calculateDailyCost = () => {
    const packsPerDay = inputs.cigarettesPerDay / inputs.cigarettesPerPack
    return packsPerDay * inputs.pricePerPack
  }

  const calculateSavings = () => {
    if (!inputs.quitDate) return { days: 0, savings: 0 }
    
    const quitDate = new Date(inputs.quitDate)
    const today = new Date()
    const diffTime = Math.abs(today.getTime() - quitDate.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    const dailyCost = calculateDailyCost()
    const totalSavings = dailyCost * diffDays
    
    return { days: diffDays, savings: totalSavings }
  }

  const getHealthMilestones = () => {
    const savings = calculateSavings()
    const days = savings.days
    
    // RULE 0001: All hardcoded milestones array COMPLETELY REMOVED - using only real database data
    return healthMilestones.map(milestone => ({
      ...milestone,
      achieved: days >= milestone.days_milestone
    }))
  }

  const calculateTimeRegained = () => {
    const savings = calculateSavings()
    const minutesPerCigarette = 5 // Average time to smoke one cigarette
    const totalMinutes = inputs.cigarettesPerDay * minutesPerCigarette * savings.days
    
    return {
      minutes: totalMinutes,
      hours: Math.floor(totalMinutes / 60),
      days: Math.floor(totalMinutes / (60 * 24))
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <section className="bg-background py-24">
        <div className="max-w-6xl mx-auto px-6 lg:px-8 text-center">
          <div className="w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg">
            <Calculator className="w-10 h-10 text-primary-foreground" strokeWidth={2} />
          </div>
          <h1 className="text-6xl font-bold text-foreground mb-8 tracking-tight">
            Wellness Calculator
          </h1>
          <p className="text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto">
            Track your progress, calculate health improvements, and visualize your journey to better wellness
          </p>
        </div>
      </section>

      {/* Calculator Selection */}
      <section className="bg-card border-b border-border py-16">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          {loading ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="p-6 rounded-xl border border-border bg-card">
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-8 h-8 bg-muted rounded animate-pulse"></div>
                    <div className="h-6 bg-muted rounded animate-pulse w-32"></div>
                  </div>
                  <div className="h-4 bg-muted rounded animate-pulse w-40"></div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="bg-card p-12 rounded-2xl shadow-lg border border-border text-center">
              <AlertCircle className="w-16 h-16 text-destructive mx-auto mb-6" strokeWidth={1.5} />
              <h3 className="text-2xl font-bold text-card-foreground mb-4">Error loading calculators</h3>
              <p className="text-muted-foreground text-lg">{error}</p>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
              {calculators.map((calc) => {
                const getIconComponent = (iconName: string) => {
                  switch(iconName) {
                    case 'DollarSign': return DollarSign
                    case 'Heart': return Heart
                    case 'TrendingUp': return TrendingUp
                    case 'Clock': return Clock
                    default: return Calculator
                  }
                }
                const IconComponent = getIconComponent(calc.icon_name || 'Calculator')
                return (
                  <button
                    key={calc.id}
                    onClick={() => setActiveCalculator(calc.id)}
                    className={`p-6 rounded-xl border-2 transition-all text-left shadow-lg hover:shadow-xl ${
                      activeCalculator === calc.id
                        ? 'border-primary bg-accent'
                        : 'border-border hover:border-primary bg-card'
                    }`}
                  >
                    <div className="flex items-center space-x-3 mb-2">
                      <IconComponent className={`w-8 h-8 ${
                        activeCalculator === calc.id ? 'text-primary' : 'text-muted-foreground'
                      }`} strokeWidth={1.5} />
                      <h3 className={`font-bold text-lg ${
                        activeCalculator === calc.id ? 'text-primary' : 'text-card-foreground'
                      }`}>
                        {calc.name}
                      </h3>
                    </div>
                    <p className="text-muted-foreground leading-relaxed">{calc.description}</p>
                  </button>
                )
              })}
            </div>
          )}
        </div>
      </section>

      {/* Input Section */}
      <section className="py-8 bg-muted">
        <div className="max-w-4xl mx-auto px-6">
          <div className="bg-card p-6 rounded-xl shadow-sm border border-border">
            <h2 className="text-xl font-semibold text-card-foreground mb-4">Your Information</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Cigarettes per day
                </label>
                <input
                  type="number"
                  value={inputs.cigarettesPerDay}
                  onChange={(e) => handleInputChange('cigarettesPerDay', parseInt(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-input text-foreground focus:ring-2 focus:ring-ring focus:border-ring"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Price per pack ($)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={inputs.pricePerPack}
                  onChange={(e) => handleInputChange('pricePerPack', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-input text-foreground focus:ring-2 focus:ring-ring focus:border-ring"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Cigarettes per pack
                </label>
                <input
                  type="number"
                  value={inputs.cigarettesPerPack}
                  onChange={(e) => handleInputChange('cigarettesPerPack', parseInt(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-input text-foreground focus:ring-2 focus:ring-ring focus:border-ring"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Quit date
                </label>
                <input
                  type="date"
                  value={inputs.quitDate}
                  onChange={(e) => handleInputChange('quitDate', e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-input text-foreground focus:ring-2 focus:ring-ring focus:border-ring"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Current age
                </label>
                <input
                  type="number"
                  value={inputs.currentAge}
                  onChange={(e) => handleInputChange('currentAge', parseInt(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-input text-foreground focus:ring-2 focus:ring-ring focus:border-ring"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Years smoking
                </label>
                <input
                  type="number"
                  value={inputs.smokingYears}
                  onChange={(e) => handleInputChange('smokingYears', parseInt(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-input text-foreground focus:ring-2 focus:ring-ring focus:border-ring"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Calculator Results */}
      <section className="py-12">
        <div className="max-w-6xl mx-auto px-6">
          {activeCalculator === 'savings' && (
            <div className="bg-card p-8 rounded-xl shadow-sm border border-border">
              <h2 className="text-2xl font-bold text-card-foreground mb-6 flex items-center">
                <DollarSign className="w-6 h-6 text-primary mr-2" />
                Money Saved Calculator
              </h2>
              
              {inputs.quitDate ? (
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="text-center p-6 bg-muted rounded-lg border border-border">
                    <div className="text-3xl font-bold text-primary">
                      ${calculateSavings().savings.toFixed(2)}
                    </div>
                    <div className="text-muted-foreground">Total Saved</div>
                  </div>
                  <div className="text-center p-6 bg-muted rounded-lg border border-border">
                    <div className="text-3xl font-bold text-primary">
                      {calculateSavings().days}
                    </div>
                    <div className="text-muted-foreground">Days Smoke-Free</div>
                  </div>
                  <div className="text-center p-6 bg-muted rounded-lg border border-border">
                    <div className="text-3xl font-bold text-primary">
                      ${calculateDailyCost().toFixed(2)}
                    </div>
                    <div className="text-muted-foreground">Daily Savings</div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">Please set your quit date to see your savings.</p>
                </div>
              )}
            </div>
          )}

          {activeCalculator === 'health' && (
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <Heart className="w-6 h-6 text-red-500 mr-2" />
                Health Timeline
              </h2>
              
              <div className="space-y-4">
                {getHealthMilestones().map((milestone, index) => (
                  <div key={index} className={`flex items-center p-4 rounded-lg ${
                    milestone.achieved ? 'bg-primary-subtle border border-primary-border' : 'bg-gray-50 border border-gray-200'
                  }`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-4 ${
                      milestone.achieved ? 'bg-primary' : 'bg-gray-300'
                    }`}>
                      {milestone.achieved ? (
                        <Heart className="w-4 h-4 text-white" />
                      ) : (
                        <span className="text-white text-sm font-bold">{index + 1}</span>
                      )}
                    </div>
                    <div className="flex-1">
                      <div className={`font-semibold ${
                        milestone.achieved ? 'text-primary' : 'text-card-foreground'
                      }`}>
                        {milestone.title}
                      </div>
                      <div className={`text-sm ${
                        milestone.achieved ? 'text-primary' : 'text-muted-foreground'
                      }`}>
                        {milestone.description}
                      </div>
                    </div>
                    {milestone.achieved && (
                      <div className="text-green-600 font-medium flex items-center"><CheckCircle className="h-4 w-4 mr-1" /> Achieved</div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeCalculator === 'life' && (
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <Clock className="w-6 h-6 text-blue-500 mr-2" />
                Time Regained
              </h2>
              
              {inputs.quitDate ? (
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="text-center p-6 bg-blue-50 rounded-lg">
                    <div className="text-3xl font-bold text-blue-600">
                      {calculateTimeRegained().days}
                    </div>
                    <div className="text-gray-600">Days of Life</div>
                  </div>
                  <div className="text-center p-6 bg-purple-50 rounded-lg">
                    <div className="text-3xl font-bold text-purple-600">
                      {calculateTimeRegained().hours}
                    </div>
                    <div className="text-gray-600">Hours Regained</div>
                  </div>
                  <div className="text-center p-6 bg-primary-subtle rounded-lg">
                    <div className="text-3xl font-bold text-green-600">
                      {Math.floor(calculateTimeRegained().minutes / 60 / 24 * 7)}
                    </div>
                    <div className="text-gray-600">Weeks of Time</div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Clock className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-600">Please set your quit date to see time regained.</p>
                </div>
              )}
            </div>
          )}

          {activeCalculator === 'progress' && (
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <TrendingUp className="w-6 h-6 text-green-500 mr-2" />
                Quit Progress
              </h2>
              
              {inputs.quitDate ? (
                <div className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                      <h3 className="font-semibold text-gray-900 mb-2">Cigarettes Not Smoked</h3>
                      <div className="text-2xl font-bold text-green-600">
                        {inputs.cigarettesPerDay * calculateSavings().days}
                      </div>
                    </div>
                    <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                      <h3 className="font-semibold text-gray-900 mb-2">Packs Avoided</h3>
                      <div className="text-2xl font-bold text-blue-600">
                        {Math.floor((inputs.cigarettesPerDay * calculateSavings().days) / inputs.cigarettesPerPack)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h3 className="font-semibold text-gray-900 mb-4">Your Journey Milestones</h3>
                    {/* RULE 0001: Real milestone data from health_milestones database table */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {getHealthMilestones().slice(0, 4).map((milestone, index) => (
                        <div key={milestone.id || index} className="text-center">
                          <div className={`text-xl font-bold ${
                            milestone.achieved ? 'text-green-600' : 'text-gray-400'
                          }`}>
                            {milestone.days_milestone === 1 ? '1 Day' :
                             milestone.days_milestone === 7 ? '1 Week' :
                             milestone.days_milestone === 30 ? '1 Month' :
                             milestone.days_milestone === 365 ? '1 Year' :
                             `${milestone.days_milestone} Days`}
                          </div>
                          <div className="text-sm text-gray-600">
                            {milestone.title || milestone.description}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <TrendingUp className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-600">Please set your quit date to see your progress.</p>
                </div>
              )}
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-primary text-primary-foreground py-16">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold text-primary-foreground mb-4">
            Ready to Track Your Journey?
          </h2>
          <p className="text-xl text-primary-foreground mb-8">
            Join Mission Fresh for personalized tracking, community support, and comprehensive wellness tools.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              to="/tools/quit-methods"
              className="inline-flex items-center justify-center border-2 border-primary-foreground text-primary-foreground px-8 py-3 rounded-lg text-lg font-medium hover:bg-primary-foreground hover:text-primary transition-colors"
            >
              <Zap className="w-5 h-5 mr-2" />
              Quit Methods
              <ArrowRight className="w-5 h-5 ml-2" />
            </Link>
            <Link
              to="/auth?mode=signup"
              className="inline-flex items-center justify-center bg-secondary text-secondary-foreground px-8 py-3 rounded-lg text-lg font-medium hover:bg-secondary-hover transition-colors border border-border shadow-lg"
            >
              Get Started
              <ArrowRight className="w-5 h-5 ml-2" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
