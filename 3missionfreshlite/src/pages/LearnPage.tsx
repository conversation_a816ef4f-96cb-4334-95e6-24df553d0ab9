import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import { BookOpen, Video, FileText, Award, Clock, PlayCircle, CheckCircle } from 'lucide-react'

interface LearningContent {
  id: string
  title: string
  description: string
  content_type: 'article' | 'video' | 'course' | 'guide'
  category: string
  duration_minutes: number
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  content_url?: string
  content_text?: string
  is_featured: boolean
  tags: string[]
}

interface UserProgress {
  id: string
  user_id: string
  content_id: string
  completed: boolean
  progress_percentage: number
  completed_at?: string
}

// RULE 0001: Dynamic categories from database - hardcoded array removed

export default function LearnPage() {
  const { user, loading } = useAuth()
  const navigate = useNavigate()
  const [content, setContent] = useState<LearningContent[]>([])
  const [userProgress, setUserProgress] = useState<UserProgress[]>([])
  const [categories, setCategories] = useState<string[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedContent, setSelectedContent] = useState<LearningContent | null>(null)
  const [isLoadingContent, setIsLoadingContent] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth')
      return
    }
    if (user) {
      loadContent()
      loadUserProgress()
      loadCategories()
    }
  }, [user, loading, navigate])

  const loadCategories = async () => {
    try {
      // RULE 0001: Load categories from database or extract from content
      const { data: categoryData, error: categoryError } = await supabase
        .from('learning_categories')
        .select('name')
        .order('display_order')
      
      if (categoryError || !categoryData?.length) {
        // Fallback: extract unique categories from content
        const uniqueCategories = [...new Set(content.map(item => item.category).filter(Boolean))]
        setCategories(uniqueCategories)
      } else {
        setCategories(categoryData.map(cat => cat.name))
      }
    } catch (error) {
      console.error('Error loading categories:', error)
      // Fallback: extract unique categories from content
      const uniqueCategories = [...new Set(content.map(item => item.category).filter(Boolean))]
      setCategories(uniqueCategories)
    }
  }

  const loadContent = async () => {
    try {
      setIsLoadingContent(true)
      // HOLY RULE 0001: Load real learning content from Supabase
      const { data: contentData, error: contentError } = await supabase
        .from('learning_content')
        .select('*')
        .order('created_at', { ascending: false })
      
      if (contentError) {
        console.error('Error loading learning content:', contentError)
        // Fallback to empty array if no table exists yet
        setContent([])
      } else {
        setContent(contentData || [])
      }
      
    } catch (error) {
      console.error('Error loading learning content:', error)
      setContent([])
    } finally {
      setIsLoadingContent(false)
    }
  }

  const loadUserProgress = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from('user_learning_progress')
        .select('*')
        .eq('user_id', user.id)

      if (error) throw error
      setUserProgress(data || [])
    } catch (error) {
      console.error('Error loading user progress:', error)
    }
  }

  const markAsCompleted = async (contentId: string) => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from('user_learning_progress')
        .upsert({
          user_id: user.id,
          content_id: contentId,
          completed: true,
          progress_percentage: 100,
          completed_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) throw error
      loadUserProgress()
    } catch (error) {
      console.error('Error marking content as completed:', error)
    }
  }

  const getFilteredContent = () => {
    let filtered = content

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory)
    }

    if (searchQuery) {
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    return filtered
  }

  const getProgressForContent = (contentId: string) => {
    return userProgress.find(p => p.content_id === contentId)
  }

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="h-5 w-5" />
      case 'course': return <BookOpen className="h-5 w-5" />
      case 'guide': return <FileText className="h-5 w-5" />
      default: return <FileText className="h-5 w-5" />
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-primary/10 text-primary border border-primary/20'
      case 'intermediate': return 'bg-muted text-muted-foreground border border-border'
      case 'advanced': return 'bg-accent text-accent-foreground border border-border'
      default: return 'bg-muted text-muted-foreground border border-border'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto"></div>
          <p className="mt-6 text-muted-foreground text-lg">Loading learning content...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="mb-16">
          <h1 className="text-5xl font-bold text-foreground flex items-center mb-6 tracking-tight">
            <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mr-6 border border-primary/20">
              <BookOpen className="h-8 w-8 text-primary" strokeWidth={1.5} />
            </div>
            Learning Center
          </h1>
          <p className="text-xl text-muted-foreground leading-relaxed max-w-3xl">
            Expand your knowledge with expert-curated content about quitting smoking and wellness.
          </p>
        </div>

        {selectedContent ? (
          /* Content Viewer */
          <div className="bg-card rounded-xl shadow-lg border border-border">
            <div className="px-8 py-6 border-b border-border flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold text-card-foreground">{selectedContent.title}</h2>
                <div className="flex items-center space-x-6 mt-4">
                  <span className="flex items-center text-muted-foreground">
                    {getContentTypeIcon(selectedContent.content_type)}
                    <span className="ml-2 capitalize font-medium">{selectedContent.content_type}</span>
                  </span>
                  <span className="flex items-center text-muted-foreground">
                    <Clock className="h-5 w-5 mr-2" strokeWidth={1.5} />
                    {selectedContent.duration_minutes} min
                  </span>
                  <span className={`px-3 py-1 text-sm font-semibold rounded-lg ${getDifficultyColor(selectedContent.difficulty)}`}>
                    {selectedContent.difficulty}
                  </span>
                </div>
              </div>
              <div className="flex space-x-4">
                {!getProgressForContent(selectedContent.id)?.completed && (
                  <button
                    onClick={() => markAsCompleted(selectedContent.id)}
                    className="bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:opacity-90 transition-all duration-300 flex items-center font-semibold shadow-lg hover:shadow-xl"
                  >
                    <CheckCircle className="mr-3 h-5 w-5" strokeWidth={1.5} />
                    Mark Complete
                  </button>
                )}
                <button
                  onClick={() => setSelectedContent(null)}
                  className="bg-muted text-muted-foreground px-6 py-3 rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold"
                >
                  Back to Library
                </button>
              </div>
            </div>
            
            <div className="p-8">
              <div className="prose max-w-none">
                {selectedContent.content_text?.split('\n').map((line, index) => {
                  if (line.startsWith('# ')) {
                    return <h1 key={index} className="text-4xl font-bold mt-12 mb-6 text-card-foreground">{line.slice(2)}</h1>
                  }
                  if (line.startsWith('## ')) {
                    return <h2 key={index} className="text-3xl font-bold mt-10 mb-5 text-card-foreground">{line.slice(3)}</h2>
                  }
                  if (line.startsWith('### ')) {
                    return <h3 key={index} className="text-2xl font-bold mt-8 mb-4 text-card-foreground">{line.slice(4)}</h3>
                  }
                  if (line.startsWith('- ')) {
                    return <li key={index} className="ml-6 mb-2 text-muted-foreground text-lg leading-relaxed">{line.slice(2)}</li>
                  }
                  if (line.match(/^\d+\./)) {
                    return <li key={index} className="ml-6 mb-2 text-muted-foreground text-lg leading-relaxed">{line}</li>
                  }
                  if (line.trim() === '') {
                    return <br key={index} />
                  }
                  return <p key={index} className="mb-6 text-muted-foreground text-lg leading-relaxed">{line}</p>
                })}
              </div>
            </div>
          </div>
        ) : (
          /* Content Library */
          <>
            {/* Search and Filters */}
            <div className="bg-card rounded-xl shadow-lg border border-border p-8 mb-12">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-6 md:space-y-0">
                <div className="flex-1 max-w-lg">
                  <input
                    type="text"
                    placeholder="Search articles, videos, and courses..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
                  />
                </div>

                <div className="flex space-x-6">
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="px-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg font-medium"
                  >
                    <option value="all">All Categories</option>
                    {categories.map((category) => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Featured Content */}
            {content.filter(item => item.is_featured).length > 0 && (
              <div className="mb-16">
                <h2 className="text-3xl font-bold text-foreground mb-8 flex items-center">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 border border-primary/20">
                    <Award className="h-6 w-6 text-primary" strokeWidth={1.5} />
                  </div>
                  Featured Content
                </h2>
                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                  {content.filter(item => item.is_featured).map((item) => (
                    <div key={item.id} className="bg-card rounded-xl shadow-lg border border-border overflow-hidden hover:shadow-xl transition-all duration-300">
                      <div className="p-8">
                        <div className="flex items-start justify-between mb-6">
                          <div className="flex items-center space-x-3">
                            {getContentTypeIcon(item.content_type)}
                            <span className="text-muted-foreground capitalize font-medium">{item.content_type}</span>
                          </div>
                          {getProgressForContent(item.id)?.completed && (
                            <CheckCircle className="h-6 w-6 text-primary" strokeWidth={1.5} />
                          )}
                        </div>

                        <h3 className="text-xl font-bold text-card-foreground mb-4">{item.title}</h3>
                        <p className="text-muted-foreground mb-6 leading-relaxed">{item.description}</p>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <span className="flex items-center text-muted-foreground">
                              <Clock className="h-4 w-4 mr-2" strokeWidth={1.5} />
                              {item.duration_minutes} min
                            </span>
                            <span className={`px-3 py-1 text-sm font-semibold rounded-lg ${getDifficultyColor(item.difficulty)}`}>
                              {item.difficulty}
                            </span>
                          </div>
                          <button
                            onClick={() => setSelectedContent(item)}
                            className="bg-primary text-primary-foreground px-5 py-2 rounded-lg hover:bg-primary-hover transition-all duration-300 flex items-center font-semibold shadow-lg hover:shadow-xl"
                          >
                            <PlayCircle className="mr-2 h-5 w-5" strokeWidth={1.5} />
                            Start
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* All Content */}
            <div>
              <h2 className="text-3xl font-bold text-foreground mb-8">All Content</h2>
              {isLoadingContent ? (
                <div className="text-center py-16">
                  <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-6 text-muted-foreground text-lg">Loading content...</p>
                </div>
              ) : getFilteredContent().length > 0 ? (
                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                  {getFilteredContent().map((item) => (
                    <div key={item.id} className="bg-card rounded-xl shadow-lg border border-border overflow-hidden hover:shadow-xl transition-all duration-300">
                      <div className="p-8">
                        <div className="flex items-start justify-between mb-6">
                          <div className="flex items-center space-x-3">
                            {getContentTypeIcon(item.content_type)}
                            <span className="text-muted-foreground capitalize font-medium">{item.content_type}</span>
                          </div>
                          {getProgressForContent(item.id)?.completed && (
                            <CheckCircle className="h-6 w-6 text-primary" strokeWidth={1.5} />
                          )}
                        </div>

                        <h3 className="text-xl font-bold text-card-foreground mb-4">{item.title}</h3>
                        <p className="text-muted-foreground mb-4 leading-relaxed">{item.description}</p>

                        <div className="mb-6">
                          <span className="text-muted-foreground font-medium">{item.category}</span>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <span className="flex items-center text-muted-foreground">
                              <Clock className="h-4 w-4 mr-2" strokeWidth={1.5} />
                              {item.duration_minutes} min
                            </span>
                            <span className={`px-3 py-1 text-sm font-semibold rounded-lg ${getDifficultyColor(item.difficulty)}`}>
                              {item.difficulty}
                            </span>
                          </div>
                          <button
                            onClick={() => setSelectedContent(item)}
                            className="bg-primary text-primary-foreground px-5 py-2 rounded-lg hover:opacity-90 transition-all duration-300 flex items-center font-semibold shadow-lg hover:shadow-xl"
                          >
                            <PlayCircle className="mr-2 h-5 w-5" strokeWidth={1.5} />
                            {getProgressForContent(item.id)?.completed ? 'Review' : 'Start'}
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-20 bg-card rounded-xl shadow-lg border border-border">
                  <div className="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20">
                    <BookOpen className="w-10 h-10 text-primary" strokeWidth={1.5} />
                  </div>
                  <h3 className="text-2xl font-bold text-card-foreground mb-4">No content found</h3>
                  <p className="text-muted-foreground text-lg">
                    Try adjusting your search or category filter.
                  </p>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  )
}
