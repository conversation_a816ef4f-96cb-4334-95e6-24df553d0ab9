import React, { useState, useEffect, useRef, useCallback } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import { Clock, Play, Pause, RotateCcw, Brain, Target, Volume2, Loader2 } from 'lucide-react'

// RULE 0001: Database interfaces for focus features
interface FocusSession {
  id: string
  user_id: string
  duration_minutes: number
  completed_at: string
  session_type: 'focus' | 'break'
}

interface FocusTip {
  id: string
  category: string
  tip_text: string
  display_order: number
}

interface FocusDuration {
  id: string
  label: string
  value: number
  display_order: number
}

const FocusPage: React.FC = () => {
  const { user, loading } = useAuth()
  const navigate = useNavigate()
  
  // RULE 0001: Real database-driven state management
  const [isActive, setIsActive] = useState(false)
  const [time, setTime] = useState(25 * 60) // 25 minutes in seconds
  const [selectedDuration, setSelectedDuration] = useState(25)
  const [sessionType, setSessionType] = useState<'focus' | 'break'>('focus')
  const [focusSessions, setFocusSessions] = useState<FocusSession[]>([])
  const [focusTips, setFocusTips] = useState<FocusTip[]>([])
  const [focusDurations, setFocusDurations] = useState<FocusDuration[]>([])
  const [isLoadingSessions, setIsLoadingSessions] = useState(true)
  const [isLoadingTips, setIsLoadingTips] = useState(true)
  const [isLoadingDurations, setIsLoadingDurations] = useState(true)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // RULE 0001: Authentication and data loading
  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth')
      return
    }
    if (user) {
      loadFocusSessions()
      loadFocusTips()
      loadFocusDurations()
    }
  }, [user, loading, navigate])

  // Timer effect
  useEffect(() => {
    if (isActive && time > 0) {
      intervalRef.current = setInterval(() => {
        setTime((prevTime) => {
          if (prevTime <= 1) {
            setIsActive(false)
            saveFocusSession()
            return 0
          }
          return prevTime - 1
        })
      }, 1000)
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isActive, time])

  const loadFocusSessions = async () => {
    if (!user) return
    try {
      setIsLoadingSessions(true)
      const { data, error } = await supabase
        .from('focus_sessions')
        .select('*')
        .eq('user_id', user.id)
        .order('completed_at', { ascending: false })
        .limit(10)
      
      if (error) throw error
      setFocusSessions(data || [])
    } catch (error) {
      console.error('Error loading focus sessions:', error)
    } finally {
      setIsLoadingSessions(false)
    }
  }

  const loadFocusTips = async () => {
    try {
      setIsLoadingTips(true)
      const { data, error } = await supabase
        .from('focus_tips')
        .select('*')
        .order('display_order')
      
      if (error || !data?.length) {
        // Fallback tips if table doesn't exist
        const fallbackTips: FocusTip[] = [
          { id: '1', category: 'workspace', tip_text: 'Remove distractions from your workspace', display_order: 1 },
          { id: '2', category: 'task', tip_text: 'Choose one specific task to focus on', display_order: 2 },
          { id: '3', category: 'notifications', tip_text: 'Turn off notifications during work sessions', display_order: 3 },
          { id: '4', category: 'health', tip_text: 'Keep water and healthy snacks nearby', display_order: 4 },
          { id: '5', category: 'ergonomics', tip_text: 'Use comfortable lighting and ergonomics', display_order: 5 }
        ]
        setFocusTips(fallbackTips)
      } else {
        setFocusTips(data)
      }
    } catch (error) {
      console.error('Error loading focus tips:', error)
      // Fallback on error
      const fallbackTips: FocusTip[] = [
        { id: '1', category: 'focus', tip_text: 'Remove distractions and focus on one task at a time', display_order: 1 }
      ]
      setFocusTips(fallbackTips)
    } finally {
      setIsLoadingTips(false)
    }
  }

  const loadFocusDurations = async () => {
    try {
      setIsLoadingDurations(true)
      const { data, error } = await supabase
        .from('focus_durations')
        .select('*')
        .order('display_order')
      
      if (error || !data?.length) {
        // Fallback durations if table doesn't exist
        const fallbackDurations: FocusDuration[] = [
          { id: '1', label: '15 min', value: 15, display_order: 1 },
          { id: '2', label: '25 min', value: 25, display_order: 2 },
          { id: '3', label: '30 min', value: 30, display_order: 3 },
          { id: '4', label: '45 min', value: 45, display_order: 4 },
          { id: '5', label: '60 min', value: 60, display_order: 5 }
        ]
        setFocusDurations(fallbackDurations)
      } else {
        setFocusDurations(data)
      }
    } catch (error) {
      console.error('Error loading focus durations:', error)
      // Fallback on error
      const fallbackDurations: FocusDuration[] = [
        { id: '1', label: '25 min', value: 25, display_order: 1 }
      ]
      setFocusDurations(fallbackDurations)
    } finally {
      setIsLoadingDurations(false)
    }
  }

  const saveFocusSession = async () => {
    if (!user) return
    try {
      const { error } = await supabase
        .from('focus_sessions')
        .insert({
          user_id: user.id,
          duration_minutes: selectedDuration,
          session_type: sessionType,
          completed_at: new Date().toISOString()
        })
      
      if (error) throw error
      loadFocusSessions() // Reload sessions to show the new one
    } catch (error) {
      console.error('Error saving focus session:', error)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const handleStart = () => {
    setIsActive(true)
  }

  const handlePause = () => {
    setIsActive(false)
  }

  const handleReset = () => {
    setIsActive(false)
    setTime(selectedDuration * 60)
  }

  const handleDurationChange = (duration: number) => {
    setSelectedDuration(duration)
    setTime(duration * 60)
    setIsActive(false)
  }

  // RULE 0001: Loading state check
  if (loading || isLoadingSessions || isLoadingTips || isLoadingDurations) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-6xl mx-auto px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mr-6 border border-primary/20">
              <Brain className="w-8 h-8 text-primary" strokeWidth={1.5} />
            </div>
            <h1 className="text-5xl font-bold text-foreground tracking-tight">Focus Timer</h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Use the Pomodoro Technique to improve focus and productivity. Work in focused intervals with regular breaks.
          </p>
        </div>

        {/* Main Timer */}
        <div className="max-w-3xl mx-auto">
          <div className="bg-card rounded-2xl shadow-xl border border-border p-12 text-center mb-12">
            <div className="mb-10">
              <div className="text-8xl font-mono font-bold text-foreground mb-4 tracking-tight">
                {formatTime(time)}
              </div>
              <div className="text-2xl text-muted-foreground capitalize font-medium">
                {sessionType} Session
              </div>
            </div>

            {/* Timer Controls */}
            <div className="flex items-center justify-center gap-6 mb-12">
              <button
                onClick={isActive ? handlePause : handleStart}
                className="bg-primary text-primary-foreground px-10 py-5 text-xl font-semibold rounded-xl flex items-center gap-4 hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                {isActive ? (
                  <>
                    <Pause className="w-7 h-7" strokeWidth={1.5} />
                    Pause
                  </>
                ) : (
                  <>
                    <Play className="w-7 h-7" strokeWidth={1.5} />
                    Start
                  </>
                )}
              </button>
              <button
                onClick={handleReset}
                className="bg-muted text-muted-foreground px-8 py-5 text-xl font-semibold rounded-xl flex items-center gap-3 hover:bg-accent hover:text-accent-foreground transition-all duration-300"
              >
                <RotateCcw className="w-6 h-6" strokeWidth={1.5} />
                Reset
              </button>
            </div>

            {/* Duration Selection */}
            <div className="border-t border-border pt-8">
              <h3 className="text-2xl font-bold text-foreground mb-6">Session Duration</h3>
              <div className="flex flex-wrap justify-center gap-4">
                {focusDurations.map((duration) => (
                  <button
                    key={duration.value}
                    onClick={() => handleDurationChange(duration.value)}
                    className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 text-lg ${
                      selectedDuration === duration.value
                        ? 'bg-primary text-primary-foreground shadow-lg'
                        : 'bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                    }`}
                  >
                    {duration.label}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Focus Tips */}
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-card rounded-xl shadow-lg border border-border p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 border border-primary/20">
                  <Target className="w-6 h-6 text-primary" strokeWidth={1.5} />
                </div>
                <h3 className="text-2xl font-bold text-foreground">Focus Tips</h3>
              </div>
              <ul className="space-y-4 text-muted-foreground">
                {focusTips.map((tip) => (
                  <li key={tip.id} className="flex items-start">
                    <div className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></div>
                    <span className="text-lg leading-relaxed">{tip.tip_text}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-card rounded-xl shadow-lg border border-border p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 border border-primary/20">
                  <Volume2 className="w-6 h-6 text-primary" strokeWidth={1.5} />
                </div>
                <h3 className="text-2xl font-bold text-foreground">Breathing Exercise</h3>
              </div>
              <div className="text-muted-foreground">
                <p className="mb-6 text-lg">Try the 4-7-8 breathing technique:</p>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></div>
                    <span className="text-lg leading-relaxed">Inhale for 4 counts</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></div>
                    <span className="text-lg leading-relaxed">Hold breath for 7 counts</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></div>
                    <span className="text-lg leading-relaxed">Exhale for 8 counts</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></div>
                    <span className="text-lg leading-relaxed">Repeat 3-4 times</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Session History */}
          <div className="mt-12 bg-card rounded-xl shadow-lg border border-border p-8">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4 border border-primary/20">
                <Clock className="w-6 h-6 text-primary" strokeWidth={1.5} />
              </div>
              <h3 className="text-2xl font-bold text-foreground">Recent Sessions</h3>
            </div>
            {focusSessions.length > 0 ? (
              <div className="space-y-4">
                {focusSessions.slice(0, 5).map((session) => (
                  <div key={session.id} className="flex justify-between items-center py-4 border-b border-border last:border-b-0">
                    <div>
                      <div className="font-bold text-foreground capitalize text-lg">
                        {session.session_type} Session
                      </div>
                      <div className="text-muted-foreground mt-1">
                        {session.duration_minutes} minutes
                      </div>
                    </div>
                    <div className="text-muted-foreground">
                      {new Date(session.completed_at).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center text-muted-foreground py-16">
                <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20">
                  <Clock className="w-8 h-8 text-primary" strokeWidth={1.5} />
                </div>
                <p className="text-lg">Start your first focus session to begin tracking!</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default FocusPage
