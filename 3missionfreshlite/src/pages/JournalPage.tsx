import React, { useState, useEffect } from 'react'
import { BookOpen, Calendar as CalendarIcon, Loader2, Plus, Search, Filter } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'

interface JournalEntry {
  id: string
  content: string
  created_at: string
  updated_at: string
  user_id: string
}

export default function JournalPage() {
  const { user } = useAuth()
  
  // Authentication required - no hardcoded user data allowed
  if (!user) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-4">Authentication Required</h1>
          <p className="text-muted-foreground">Please log in to access your journal.</p>
        </div>
      </div>
    )
  }
  
  const [entries, setEntries] = useState<JournalEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [newEntry, setNewEntry] = useState('')
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [searchQuery, setSearchQuery] = useState('')

  // Load journal entries
  useEffect(() => {
    if (user) {
      loadEntries()
    }
  }, [user, selectedDate])

  const loadEntries = async () => {
    if (!user) return

    setLoading(true)
    try {
      const startOfDay = new Date(selectedDate)
      startOfDay.setHours(0, 0, 0, 0)
      
      const endOfDay = new Date(selectedDate)
      endOfDay.setHours(23, 59, 59, 999)

      const { data, error } = await supabase
        .from('journal_entries')
        .select('*')
        .eq('user_id', user.id)
        .gte('created_at', startOfDay.toISOString())
        .lte('created_at', endOfDay.toISOString())
        .order('created_at', { ascending: false })

      if (error) throw error
      setEntries(data || [])
    } catch (error) {
      console.error('Error loading entries:', error)
    } finally {
      setLoading(false)
    }
  }

  const saveEntry = async () => {
    if (!user || !newEntry.trim()) return

    setSaving(true)
    try {
      const { data, error } = await supabase
        .from('journal_entries')
        .insert([
          {
            content: newEntry.trim(),
            user_id: user.id
          }
        ])
        .select()

      if (error) throw error
      
      setNewEntry('')
      loadEntries() // Reload entries to show the new one
    } catch (error) {
      console.error('Error saving entry:', error)
    } finally {
      setSaving(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const formatDisplayDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const filteredEntries = entries.filter(entry =>
    entry.content.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Commented out for comprehensive audit - using auditUser fallback instead
  // if (!auditUser) {
  //   return (
  //     <div className="min-h-screen bg-background flex items-center justify-center">
  //       <div className="text-center max-w-md mx-auto px-6">
  //         <div className="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20">
  //           <BookOpen className="w-10 h-10 text-primary" strokeWidth={1.5} />
  //         </div>
  //         <h2 className="text-2xl font-bold text-foreground mb-4">Sign in to access your journal</h2>
  //         <p className="text-muted-foreground leading-relaxed">Your personal thoughts and reflections await.</p>
  //       </div>
  //     </div>
  //   )
  // }

  return (
    <div className="min-h-screen bg-background py-12">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Header */}
        <div className="mb-16">
          <div className="flex items-center gap-6 mb-6">
            <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20">
              <BookOpen className="w-8 h-8 text-primary" strokeWidth={1.5} />
            </div>
            <div>
              <h1 className="text-5xl font-bold text-foreground tracking-tight">Journal</h1>
              <p className="text-xl text-muted-foreground leading-relaxed mt-2">Express your thoughts and track your emotional journey</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* New Entry Form */}
          <div className="lg:col-span-2">
            <div className="bg-card rounded-xl shadow-lg border border-border p-10 mb-12">
              <div className="flex items-center gap-4 mb-8">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center border border-primary/20">
                  <Plus className="w-5 h-5 text-primary" strokeWidth={1.5} />
                </div>
                <h2 className="text-2xl font-bold text-card-foreground">New Journal Entry</h2>
              </div>

              <div className="space-y-6">
                <textarea
                  value={newEntry}
                  onChange={(e) => setNewEntry(e.target.value)}
                  placeholder="What's on your mind today? Write about your thoughts, feelings, progress, or anything you'd like to remember..."
                  rows={8}
                  className="w-full p-6 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg leading-relaxed resize-none"
                />

                <div className="flex justify-end">
                  <button
                    onClick={saveEntry}
                    disabled={saving || !newEntry.trim()}
                    className="flex items-center gap-3 px-8 py-4 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl"
                  >
                    {saving ? (
                      <>
                        <Loader2 className="w-5 h-5 animate-spin" strokeWidth={1.5} />
                        Saving...
                      </>
                    ) : (
                      <>
                        <BookOpen className="w-5 h-5" strokeWidth={1.5} />
                        Save Entry
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Entries List */}
            <div className="bg-card rounded-xl shadow-lg border border-border">
              <div className="p-8 border-b border-border">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-card-foreground">
                    Entries for {formatDisplayDate(selectedDate)}
                  </h2>
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <Search className="w-5 h-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground" strokeWidth={1.5} />
                      <input
                        type="text"
                        placeholder="Search entries..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-12 pr-4 py-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-8">
                {loading ? (
                  <div className="space-y-6">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="animate-pulse">
                        <div className="h-6 bg-muted rounded w-1/4 mb-4"></div>
                        <div className="h-6 bg-muted rounded w-full mb-2"></div>
                        <div className="h-6 bg-muted rounded w-3/4"></div>
                      </div>
                    ))}
                  </div>
                ) : filteredEntries.length === 0 ? (
                  <div className="text-center py-20">
                    <div className="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20">
                      <BookOpen className="w-10 h-10 text-primary" strokeWidth={1.5} />
                    </div>
                    <h3 className="text-2xl font-bold text-card-foreground mb-4">
                      {searchQuery ? 'No matching entries found' : 'No entries for this date'}
                    </h3>
                    <p className="text-muted-foreground text-lg">
                      {searchQuery ? 'Try adjusting your search terms.' : 'Start by writing your first journal entry above.'}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-8">
                    {filteredEntries.map((entry) => (
                      <div key={entry.id} className="border-l-4 border-primary pl-6 py-2">
                        <div className="flex items-center justify-between mb-4">
                          <p className="font-semibold text-primary">
                            {formatDate(entry.created_at)}
                          </p>
                        </div>
                        <p className="text-card-foreground whitespace-pre-wrap leading-relaxed text-lg">
                          {entry.content}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
          </div>
        </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Date Picker */}
            <div className="bg-card rounded-xl shadow-lg border border-border p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center border border-primary/20">
                  <CalendarIcon className="w-5 h-5 text-primary" strokeWidth={1.5} />
                </div>
                <h3 className="text-xl font-bold text-card-foreground">View Past Entries</h3>
              </div>
              <p className="text-muted-foreground mb-6 leading-relaxed">Select a date to view entries from that day</p>

              <div className="space-y-4">
                <label className="block text-base font-semibold text-card-foreground">Select Date</label>
                <input
                  type="date"
                  value={selectedDate.toISOString().split('T')[0]}
                  onChange={(e) => setSelectedDate(new Date(e.target.value))}
                  max={new Date().toISOString().split('T')[0]}
                  className="w-full p-4 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-foreground text-lg"
                />
              </div>
            </div>

            {/* Journal Stats */}
            <div className="bg-card rounded-xl shadow-lg border border-border p-8">
              <h3 className="text-xl font-bold text-card-foreground mb-6">Journal Insights</h3>
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground text-lg">Today's Entries</span>
                  <span className="font-bold text-card-foreground text-xl">{filteredEntries.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground text-lg">Selected Date</span>
                  <span className="font-bold text-card-foreground text-xl">
                    {selectedDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  </span>
                </div>
              </div>
            </div>

            {/* Tips */}
            <div className="bg-primary/5 rounded-xl border border-primary/20 p-8">
              <h3 className="text-xl font-bold text-card-foreground mb-6">Journaling Tips</h3>
              <ul className="space-y-4 text-muted-foreground">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></span>
                  <span className="leading-relaxed">Write regularly to build a habit</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></span>
                  <span className="leading-relaxed">Be honest about your feelings</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></span>
                  <span className="leading-relaxed">Track your quit journey progress</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></span>
                  <span className="leading-relaxed">Note triggers and coping strategies</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary rounded-full mt-3 mr-4 flex-shrink-0"></span>
                  <span className="leading-relaxed">Celebrate small victories</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
