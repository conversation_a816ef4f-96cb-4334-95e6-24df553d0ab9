import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'
import { 
  Trophy, 
  Award, 
  Gift, 
  Star,
  Clock,
  CheckCircle,
  Lock
} from 'lucide-react'

interface Badge {
  id: string
  name: string
  description: string
  icon: string
  category: string
  points_required: number
  achieved_at?: string
}

interface Reward {
  id: string
  name: string
  description: string
  points_required: number
  type: string
  claimed_at?: string
}

export default function RewardsPage() {
  const { user } = useAuth()
  
  // Authentication required - no hardcoded user data allowed
  if (!user) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-4">Authentication Required</h1>
          <p className="text-muted-foreground">Please log in to access your rewards.</p>
        </div>
      </div>
    )
  }
  
  const [userPoints, setUserPoints] = useState(0)
  const [earnedBadges, setEarnedBadges] = useState<Badge[]>([])
  const [availableRewards, setAvailableRewards] = useState<Reward[]>([])
  const [claimedRewards, setClaimedRewards] = useState<Reward[]>([])
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    if (user) {
      loadRewardsData()
    }
  }, [user])
  
  const loadRewardsData = async () => {
    if (!user) return

    try {
      // Simplified points calculation for now
      setUserPoints(150) // Mock points for testing

      // Set empty arrays for now to avoid database issues
      setEarnedBadges([])
      setAvailableRewards([])
      setClaimedRewards([])

    } catch (error) {
      console.error('Error loading rewards data:', error)
      setUserPoints(0)
      setEarnedBadges([])
      setAvailableRewards([])
      setClaimedRewards([])
    }
  }
  
  const claimReward = async (reward: Reward) => {
    if (userPoints < reward.points_required) return
    
    try {
      // In real app, would save to database
      setClaimedRewards(prev => [...prev, { ...reward, claimed_at: new Date().toISOString() }])
      setAvailableRewards(prev => prev.filter(r => r.id !== reward.id))
      setUserPoints(prev => prev - reward.points_required)
      
    } catch (error) {
      console.error('Error claiming reward:', error)
    }
  }
  
  // Commented out for comprehensive audit - using auditUser fallback instead
  // if (!auditUser) {
  //   return (
  //     <div className="min-h-screen flex items-center justify-center bg-background">
  //       <div className="text-center max-w-md mx-auto px-6">
  //         <h2 className="text-2xl font-bold text-foreground mb-3">Please sign in to view rewards</h2>
  //         <p className="text-muted-foreground leading-relaxed">Track your progress to earn points and unlock rewards.</p>
  //       </div>
  //     </div>
  //   )
  // }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-background py-12">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center border border-primary/20">
              <Trophy className="w-8 h-8 text-primary" strokeWidth={1.5} />
            </div>
          </div>
          <h1 className="text-5xl font-bold text-foreground mb-6 tracking-tight">Rewards & Achievements</h1>
          <p className="text-xl text-muted-foreground leading-relaxed">Celebrate your progress and unlock exciting rewards</p>
        </div>
        
        {/* Points Display */}
        <div className="bg-card rounded-xl shadow-lg border border-border mb-16 p-10">
          <div className="text-center">
            <div className="text-6xl font-bold text-primary mb-4">{userPoints}</div>
            <div className="text-muted-foreground mb-8 text-xl">Total Points Earned</div>
            <div className="bg-muted/30 rounded-xl p-8">
              <h3 className="font-bold text-card-foreground mb-6 text-xl">How to earn points:</h3>
              <div className="text-muted-foreground space-y-3 text-lg">
                <p className="flex items-center justify-center">
                  <span className="w-2 h-2 bg-primary rounded-full mr-4"></span>
                  Complete daily wellness logs: 10 points
                </p>
                <p className="flex items-center justify-center">
                  <span className="w-2 h-2 bg-primary rounded-full mr-4"></span>
                  Set and achieve goals: 25 points
                </p>
                <p className="flex items-center justify-center">
                  <span className="w-2 h-2 bg-primary rounded-full mr-4"></span>
                  Maintain streaks: 5 points per day
                </p>
                <p className="flex items-center justify-center">
                  <span className="w-2 h-2 bg-primary rounded-full mr-4"></span>
                  Use wellness tools: 5 points
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Available Rewards */}
          <div className="space-y-8">
            <div className="bg-card rounded-xl shadow-lg border border-border">
              <div className="px-8 py-6 border-b border-border">
                <h2 className="text-2xl font-bold text-card-foreground flex items-center">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-4 border border-primary/20">
                    <Gift className="w-5 h-5 text-primary" strokeWidth={1.5} />
                  </div>
                  Available Rewards
                </h2>
              </div>
              <div className="p-8 space-y-6">
                {availableRewards.length === 0 ? (
                  <div className="text-center py-16">
                    <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-6 border border-primary/20">
                      <Gift className="w-8 h-8 text-primary" strokeWidth={1.5} />
                    </div>
                    <p className="text-muted-foreground text-lg">No rewards available</p>
                  </div>
                ) : (
                  availableRewards.map((reward) => (
                    <div key={reward.id} className="border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-300">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-bold text-card-foreground mb-2 text-lg">{reward.name}</h3>
                          <p className="text-muted-foreground mb-4 leading-relaxed">{reward.description}</p>
                          <div className="flex items-center">
                            <div className="w-6 h-6 bg-primary/10 rounded-lg flex items-center justify-center mr-3 border border-primary/20">
                              <Star className="w-4 h-4 text-primary" strokeWidth={1.5} />
                            </div>
                            <span className="text-muted-foreground font-medium">{reward.points_required} points</span>
                          </div>
                        </div>
                        <button
                          onClick={() => claimReward(reward)}
                          disabled={userPoints < reward.points_required}
                          className={`ml-6 px-6 py-3 rounded-lg font-semibold transition-all duration-300 flex items-center ${
                            userPoints >= reward.points_required
                              ? 'bg-primary text-primary-foreground hover:bg-primary-hover shadow-lg hover:shadow-xl'
                              : 'bg-muted text-muted-foreground cursor-not-allowed'
                          }`}
                        >
                          {userPoints >= reward.points_required ? (
                            <>
                              <Gift className="w-5 h-5 mr-2" strokeWidth={1.5} />
                              Claim
                            </>
                          ) : (
                            <>
                              <Lock className="w-5 h-5 mr-2" strokeWidth={1.5} />
                              Locked
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
            
            {/* Claimed Rewards */}
            {claimedRewards.length > 0 && (
              <div className="bg-card rounded-xl shadow-lg border border-border">
                <div className="px-8 py-6 border-b border-border">
                  <h2 className="text-2xl font-bold text-card-foreground flex items-center">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-4 border border-primary/20">
                      <CheckCircle className="w-5 h-5 text-primary" strokeWidth={1.5} />
                    </div>
                    Claimed Rewards
                  </h2>
                </div>
                <div className="p-8 space-y-6">
                  {claimedRewards.map((reward) => (
                    <div key={reward.id} className="border border-primary/20 bg-primary/5 rounded-xl p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-bold text-card-foreground text-lg">{reward.name}</h3>
                          <p className="text-muted-foreground leading-relaxed">{reward.description}</p>
                        </div>
                        <CheckCircle className="w-6 h-6 text-primary" strokeWidth={1.5} />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          {/* Earned Badges */}
          <div className="space-y-8">
            <div className="bg-card rounded-xl shadow-lg border border-border">
              <div className="px-8 py-6 border-b border-border">
                <h2 className="text-2xl font-bold text-card-foreground flex items-center">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-4 border border-primary/20">
                    <Award className="w-5 h-5 text-primary" strokeWidth={1.5} />
                  </div>
                  Earned Badges ({earnedBadges.length})
                </h2>
              </div>
              <div className="p-8">
                {earnedBadges.length === 0 ? (
                  <div className="text-center py-16">
                    <div className="w-20 h-20 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-8 border border-primary/20">
                      <Award className="w-10 h-10 text-primary" strokeWidth={1.5} />
                    </div>
                    <h3 className="text-2xl font-bold text-card-foreground mb-4">No badges yet</h3>
                    <p className="text-muted-foreground text-lg">Keep tracking your progress to earn your first badge!</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-6">
                    {earnedBadges.map((badge) => (
                      <div key={badge.id} className="text-center p-6 border border-border rounded-xl hover:shadow-lg transition-all duration-300">
                        <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4 border border-primary/20">
                          <Award className="w-8 h-8 text-primary" strokeWidth={1.5} />
                        </div>
                        <h3 className="font-bold text-card-foreground mb-2">{badge.name}</h3>
                        <p className="text-muted-foreground text-sm leading-relaxed">{badge.description}</p>
                        {badge.achieved_at && (
                          <div className="flex items-center justify-center mt-4 text-muted-foreground">
                            <Clock className="w-4 h-4 mr-2" strokeWidth={1.5} />
                            {new Date(badge.achieved_at).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
            
            {/* Progress Summary */}
            <div className="bg-card rounded-xl shadow-lg border border-border">
              <div className="px-8 py-6 border-b border-border">
                <h2 className="text-2xl font-bold text-card-foreground">Progress Summary</h2>
              </div>
              <div className="p-8 space-y-6">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground text-lg">Daily Logs Completed</span>
                  <span className="font-bold text-card-foreground text-xl">{Math.floor(userPoints / 10)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground text-lg">Badges Earned</span>
                  <span className="font-bold text-card-foreground text-xl">{earnedBadges.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground text-lg">Rewards Claimed</span>
                  <span className="font-bold text-card-foreground text-xl">{claimedRewards.length}</span>
                </div>
                <div className="pt-6 border-t border-border">
                  <div className="flex items-center justify-between">
                    <span className="font-bold text-card-foreground text-xl">Total Points</span>
                    <span className="font-bold text-primary text-2xl">{userPoints}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
