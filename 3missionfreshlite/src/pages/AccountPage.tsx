import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'
import { 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  MapPin, 
  Bell, 
  Shield, 
  CreditCard,
  Download,
  Trash2,
  Edit3,
  Save,
  X,
  <PERSON>,
  Settings
} from 'lucide-react'

// Icon mapping for dynamic icon selection
const iconMap: Record<string, any> = {
  Bell,
  Mail,
  Shield,
  Settings
}

interface NotificationPreference {
  id: string
  key: string
  label: string
  description: string
  icon_name: string
  display_order: number
}

interface UserProfile {
  id: string
  email: string
  first_name: string
  last_name: string
  phone: string
  date_of_birth: string
  location: string
  bio: string
  avatar_url: string
  preferences: {
    notifications: boolean
    email_updates: boolean
    privacy_mode: boolean
    data_sharing: boolean
  }
}

export default function AccountPage() {
  const { user, signOut } = useAuth()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('profile')
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    phone: '',
    date_of_birth: '',
    location: '',
    bio: ''
  })
  const [preferences, setPreferences] = useState({
    notifications: true,
    email_updates: true,
    privacy_mode: false,
    data_sharing: false
  })
  
  // RULE 0001: Dynamic notification preferences from database - hardcoded array removed
  const [notificationPrefs, setNotificationPrefs] = useState<NotificationPreference[]>([])
  const [prefsLoading, setPrefsLoading] = useState(true)

  useEffect(() => {
    if (user) {
      loadProfile()
      fetchNotificationPrefs()
    } else {
      fetchNotificationPrefs() // Load preferences config even without user
    }
  }, [user])

  const fetchNotificationPrefs = async () => {
    try {
      const { data, error } = await supabase
        .from('notification_preferences_config')
        .select('*')
        .order('display_order', { ascending: true })
      
      if (error) {
        console.error('Error fetching notification preferences config:', error)
        // Fallback to basic structure if database fails
        setNotificationPrefs([
          { id: '1', key: 'notifications', label: 'Push Notifications', description: 'Get notified about important updates and reminders', icon_name: 'Bell', display_order: 1 },
          { id: '2', key: 'email_updates', label: 'Email Updates', description: 'Receive weekly progress reports and tips via email', icon_name: 'Mail', display_order: 2 }
        ])
      } else {
        setNotificationPrefs(data || [])
      }
    } catch (err) {
      console.error('Error fetching notification preferences config:', err)
      setNotificationPrefs([])
    } finally {
      setPrefsLoading(false)
    }
  }

  const loadProfile = async () => {
    if (!user) return

    try {
      setLoading(true)
      
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (error && error.code !== 'PGRST116') {
        console.error('Error loading profile:', error)
        return
      }

      if (data) {
        setProfile(data)
        setFormData({
          first_name: data.first_name || '',
          last_name: data.last_name || '',
          phone: data.phone || '',
          date_of_birth: data.date_of_birth || '',
          location: data.location || '',
          bio: data.bio || ''
        })
        setPreferences(data.preferences || {
          notifications: true,
          email_updates: true,
          privacy_mode: false,
          data_sharing: false
        })
      } else {
        // Create default profile
        const defaultProfile = {
          id: user.id,
          email: user.email || '',
          first_name: '',
          last_name: '',
          phone: '',
          date_of_birth: '',
          location: '',
          bio: '',
          avatar_url: '',
          preferences: {
            notifications: true,
            email_updates: true,
            privacy_mode: false,
            data_sharing: false
          }
        }
        setProfile(defaultProfile)
      }
    } catch (error) {
      console.error('Error loading profile:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    if (!user || !profile) return

    try {
      setSaving(true)

      const updatedProfile = {
        ...formData,
        preferences,
        updated_at: new Date().toISOString()
      }

      const { error } = await supabase
        .from('user_profiles')
        .upsert({
          id: user.id,
          email: user.email,
          ...updatedProfile
        })

      if (error) {
        console.error('Error saving profile:', error)
        return
      }

      setProfile({ ...profile, ...updatedProfile })
      setIsEditing(false)
    } catch (error) {
      console.error('Error saving profile:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    if (profile) {
      setFormData({
        first_name: profile.first_name || '',
        last_name: profile.last_name || '',
        phone: profile.phone || '',
        date_of_birth: profile.date_of_birth || '',
        location: profile.location || '',
        bio: profile.bio || ''
      })
      setPreferences(profile.preferences || {
        notifications: true,
        email_updates: true,
        privacy_mode: false,
        data_sharing: false
      })
    }
    setIsEditing(false)
  }

  const handleDeleteAccount = async () => {
    if (!user) return
    
    const confirmed = window.confirm(
      'Are you sure you want to delete your account? This action cannot be undone.'
    )
    
    if (confirmed) {
      try {
        // Delete user data
        await supabase.from('user_profiles').delete().eq('id', user.id)
        await supabase.from('user_goals').delete().eq('user_id', user.id)
        await supabase.from('health_metrics').delete().eq('user_id', user.id)
        await supabase.from('community_posts').delete().eq('user_id', user.id)
        
        // Sign out
        await signOut()
      } catch (error) {
        console.error('Error deleting account:', error)
      }
    }
  }

  const exportData = async () => {
    if (!user) return

    try {
      // Fetch all user data
      const [profileData, goalsData, metricsData, postsData] = await Promise.all([
        supabase.from('user_profiles').select('*').eq('id', user.id),
        supabase.from('user_goals').select('*').eq('user_id', user.id),
        supabase.from('health_metrics').select('*').eq('user_id', user.id),
        supabase.from('community_posts').select('*').eq('user_id', user.id)
      ])

      const exportData = {
        profile: profileData.data,
        goals: goalsData.data,
        health_metrics: metricsData.data,
        community_posts: postsData.data,
        exported_at: new Date().toISOString()
      }

      // Create and download file
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `mission-fresh-data-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error exporting data:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background py-12">
      <div className="max-w-6xl mx-auto px-6 lg:px-8">
        {/* Header */}
        <div className="mb-16">
          <h1 className="text-5xl font-bold text-foreground mb-6 tracking-tight">Account Settings</h1>
          <p className="text-xl text-muted-foreground leading-relaxed">Manage your profile and preferences</p>
        </div>

        {/* Tabs */}
        <div className="bg-card rounded-2xl shadow-2xl border border-border mb-12">
          <div className="border-b border-border">
            <nav className="flex space-x-12 px-8">
              {[
                { id: 'profile', label: 'Profile', icon: User },
                { id: 'preferences', label: 'Preferences', icon: Settings },
                { id: 'privacy', label: 'Privacy & Data', icon: Shield }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-3 py-6 px-2 border-b-2 font-semibold text-lg transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
                  }`}
                >
                  <tab.icon className="w-6 h-6" strokeWidth={1.5} />
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Profile Tab */}
          {activeTab === 'profile' && (
            <div className="p-10">
              <div className="flex items-center justify-between mb-10">
                <h3 className="text-2xl font-bold text-card-foreground">Profile Information</h3>
                {!isEditing ? (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="flex items-center space-x-3 px-6 py-3 text-primary border border-primary rounded-lg hover:bg-primary-subtle transition-all duration-300 font-semibold"
                  >
                    <Edit3 className="w-5 h-5" strokeWidth={1.5} />
                    <span>Edit</span>
                  </button>
                ) : (
                  <div className="flex space-x-4">
                    <button
                      onClick={handleSave}
                      disabled={saving}
                      className="flex items-center space-x-3 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover disabled:opacity-50 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl"
                    >
                      <Save className="w-5 h-5" strokeWidth={1.5} />
                      <span>{saving ? 'Saving...' : 'Save'}</span>
                    </button>
                    <button
                      onClick={handleCancel}
                      className="flex items-center space-x-3 px-6 py-3 text-muted-foreground border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-all duration-300 font-semibold"
                    >
                      <X className="w-5 h-5" strokeWidth={1.5} />
                      <span>Cancel</span>
                    </button>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-muted-foreground mb-3 tracking-wide uppercase">
                    First Name
                  </label>
                  <div className="relative">
                    <User className="absolute left-4 top-4 w-5 h-5 text-muted-foreground" strokeWidth={1.5} />
                    <input
                      type="text"
                      value={formData.first_name}
                      onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
                      disabled={!isEditing}
                      className="w-full pl-12 pr-4 py-4 border border-border rounded-xl focus:ring-2 focus:ring-ring focus:border-ring disabled:bg-muted text-foreground placeholder:text-muted-foreground"
                      placeholder="Enter your first name"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-muted-foreground mb-3 tracking-wide uppercase">
                    Last Name
                  </label>
                  <div className="relative">
                    <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-muted-foreground" />
                    <input
                      type="text"
                      value={formData.last_name}
                      onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                      disabled={!isEditing}
                      className="w-full pl-12 pr-4 py-4 border border-border rounded-xl focus:ring-2 focus:ring-ring focus:border-ring disabled:bg-muted text-foreground placeholder:text-muted-foreground"
                      placeholder="Enter your last name"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                    <input
                      type="email"
                      value={user?.email || ''}
                      disabled
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      disabled={!isEditing}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 disabled:bg-gray-50"
                      placeholder="Enter your phone number"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Date of Birth
                  </label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                    <input
                      type="date"
                      value={formData.date_of_birth}
                      onChange={(e) => setFormData({ ...formData, date_of_birth: e.target.value })}
                      disabled={!isEditing}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 disabled:bg-gray-50"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location
                  </label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      value={formData.location}
                      onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                      disabled={!isEditing}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 disabled:bg-gray-50"
                      placeholder="Enter your location"
                    />
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bio
                  </label>
                  <textarea
                    value={formData.bio}
                    onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
                    disabled={!isEditing}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 disabled:bg-gray-50"
                    placeholder="Tell us about yourself..."
                  />
                </div>
              </div>
            </div>
          )}

          {/* Preferences Tab */}
          {activeTab === 'preferences' && (
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-6">Notification Preferences</h3>
              
              <div className="space-y-4">
                {prefsLoading ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
                    <p className="text-muted-foreground text-sm">Loading preferences...</p>
                  </div>
                ) : (
                  notificationPrefs.map((pref) => {
                    const IconComponent = iconMap[pref.icon_name] || Bell
                    return (
                      <div key={pref.key} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <IconComponent className="w-5 h-5 text-gray-600" />
                          <div>
                            <h4 className="font-medium text-gray-900">{pref.label}</h4>
                            <p className="text-sm text-gray-600">{pref.description}</p>
                          </div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={preferences[pref.key as keyof typeof preferences]}
                            onChange={(e) => setPreferences({ ...preferences, [pref.key]: e.target.checked })}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-focus rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                        </label>
                      </div>
                    )
                  })
                )}
              </div>

              <div className="mt-6">
                <button
                  onClick={handleSave}
                  disabled={saving}
                  className="px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary-hover disabled:opacity-50"
                >
                  {saving ? 'Saving...' : 'Save Preferences'}
                </button>
              </div>
            </div>
          )}

          {/* Privacy & Data Tab */}
          {activeTab === 'privacy' && (
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-6">Privacy & Data Management</h3>
              
              <div className="space-y-6">
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">Data Export</h4>
                  <p className="text-sm text-gray-600 mb-4">
                    Download a copy of all your data including profile, goals, metrics, and posts.
                  </p>
                  <button
                    onClick={exportData}
                    className="flex items-center space-x-2 px-4 py-2 text-primary border border-primary rounded-lg hover:bg-primary-subtle"
                  >
                    <Download className="w-4 h-4" />
                    <span>Export My Data</span>
                  </button>
                </div>

                <div className="border border-destructive rounded-lg p-4">
                  <h4 className="font-medium text-destructive mb-2">Delete Account</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Permanently delete your account and all associated data. This action cannot be undone.
                  </p>
                  <button
                    onClick={handleDeleteAccount}
                    className="flex items-center space-x-2 px-4 py-2 bg-destructive text-destructive-foreground rounded-lg hover:bg-destructive-hover"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>Delete Account</span>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
