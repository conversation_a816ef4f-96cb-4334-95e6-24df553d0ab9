import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Tablet, ArrowRight, Heart, CheckCircle, AlertCircle, Info, ShoppingCart, Star, ExternalLink } from 'lucide-react'
import { supabase } from '../lib/supabase'

export default function NRTGuidePage() {
  const [activeTab, setActiveTab] = useState('overview')
  const [nrtProducts, setNrtProducts] = useState<any[]>([])
  const [nrtVendors, setNrtVendors] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch real NRT products and vendors from database
  useEffect(() => {
    async function fetchNRTData() {
      try {
        setLoading(true)
        
        // Fetch real NRT products
        const { data: productsData, error: productsError } = await supabase
          .from('nrt_products')
          .select('*')
          .order('category', { ascending: true })
          .order('effectiveness_rating', { ascending: false })
        
        if (productsError) throw productsError
        
        // Fetch real NRT vendors for benefits/considerations data
        const { data: vendorsData, error: vendorsError } = await supabase
          .from('nrt_vendors')
          .select('features, specialties, description')
          .not('features', 'is', null)
          .not('specialties', 'is', null)
        
        if (vendorsError) throw vendorsError
        
        setNrtProducts(productsData || [])
        setNrtVendors(vendorsData || [])
      } catch (err) {
        console.error('Error fetching NRT data:', err)
        setError('Failed to load NRT data')
      } finally {
        setLoading(false)
      }
    }

    fetchNRTData()
  }, [])

  // Generate tabs dynamically from real database categories
  const tabs = [
    { id: 'overview', label: 'Overview' },
    ...Array.from(new Set(nrtProducts.map(p => p.category))).map(category => ({
      id: category,
      label: category.charAt(0).toUpperCase() + category.slice(1)
    }))
  ]

  // Extract real benefits from vendor specialties and features
  const benefits = React.useMemo(() => {
    const allSpecialties = nrtVendors.flatMap(vendor => vendor.specialties || [])
    const allFeatures = nrtVendors.flatMap(vendor => vendor.features || [])
    const uniqueBenefits = [...new Set([...allSpecialties, ...allFeatures])]
    return uniqueBenefits.filter(benefit => benefit && benefit.length > 0)
  }, [nrtVendors])

  // Extract real considerations from vendor descriptions
  const considerations = React.useMemo(() => {
    const descriptions = nrtVendors
      .map(vendor => vendor.description)
      .filter(desc => desc && desc.length > 0)
    return descriptions.slice(0, 5) // Take first 5 real descriptions as considerations
  }, [nrtVendors])

  // Get products for current active tab
  const getProductsForTab = () => {
    if (activeTab === 'overview') return []
    return nrtProducts.filter(p => p.category === activeTab)
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <section className="bg-background py-24">
        <div className="max-w-6xl mx-auto px-6 lg:px-8 text-center">
          <div className="w-20 h-20 bg-primary rounded-xl flex items-center justify-center mx-auto mb-8 shadow-lg">
            <Tablet className="w-10 h-10 text-primary-foreground" strokeWidth={2} />
          </div>
          <h1 className="text-6xl font-bold text-foreground mb-4 tracking-tight">
            Nicotine Replacement Therapy
          </h1>
          <h2 className="text-6xl font-bold text-foreground mb-8 tracking-tight">
            Guide
          </h2>
          <p className="text-2xl text-muted-foreground leading-relaxed max-w-4xl mx-auto">
            Understanding your NRT options to find the right solution for your wellness journey
          </p>
        </div>
      </section>

      {/* Navigation Tabs */}
      <section className="bg-card border-b border-border">
        <div className="max-w-6xl mx-auto px-6">
          <nav className="flex space-x-8 overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-6 px-4 border-b-2 font-semibold text-base whitespace-nowrap transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </section>

      {/* Content */}
      <section className="py-12">
        <div className="max-w-6xl mx-auto px-6">
          {activeTab === 'overview' && (
            <div className="space-y-12">
              {/* What is NRT */}
              <div>
                <h3 className="text-3xl font-bold text-foreground mb-8 tracking-tight">What is NRT?</h3>
                <div className="bg-card p-8 rounded-xl shadow-lg border border-border">
                  <p className="text-card-foreground leading-relaxed mb-6 text-lg">
                    Nicotine Replacement Therapy (NRT) is a medically-approved way to take nicotine by means other than tobacco. It can help reduce unpleasant withdrawal effects such as mood swings, cravings and anxiety that may occur when you stop smoking or using nicotine products.
                  </p>
                  <p className="text-card-foreground leading-relaxed text-lg">
                    NRT is available as skin patches, chewing gum, inhalers, tablets, oral strips, lozenges, nasal and mouth spray. These products provide low, controlled doses of nicotine without the tar, carbon monoxide and other poisonous chemicals present in tobacco smoke.
                  </p>
                </div>
              </div>

              {/* Benefits */}
              <div>
                <h3 className="text-2xl font-bold text-foreground mb-6">Benefits of NRT</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="flex items-start space-x-3 bg-card p-4 rounded-lg shadow-sm border border-border">
                      <CheckCircle className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                      <span className="text-card-foreground">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Considerations */}
              <div>
                <h3 className="text-2xl font-bold text-foreground mb-6">Considerations</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  {considerations.map((consideration, index) => (
                    <div key={index} className="flex items-start space-x-3 bg-card p-4 rounded-lg shadow-sm border border-border">
                      <Info className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                      <span className="text-card-foreground">{consideration}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab !== 'overview' && (
            <div className="space-y-6">
              {loading ? (
                <div className="bg-card p-8 rounded-xl shadow-sm border border-border text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Loading NRT products...</p>
                </div>
              ) : error ? (
                <div className="bg-card p-8 rounded-xl shadow-sm border border-border text-center">
                  <AlertCircle className="w-12 h-12 text-destructive mx-auto mb-4" />
                  <p className="text-destructive mb-2">Error loading products</p>
                  <p className="text-muted-foreground">{error}</p>
                </div>
              ) : (
                <div className="grid gap-6">
                  {getProductsForTab().map((product) => (
                    <div key={product.id} className="bg-card p-8 rounded-xl shadow-sm border border-border">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-2xl font-bold text-card-foreground mb-2">{product.name}</h3>
                          <div className="flex items-center space-x-4 mb-2">
                            {product.brand && (
                              <span className="bg-primary-subtle text-primary text-sm font-medium px-3 py-1 rounded-full">
                                {product.brand}
                              </span>
                            )}
                            {product.strength && (
                              <span className="bg-primary-subtle text-primary text-sm font-medium px-3 py-1 rounded-full">
                                {product.strength}
                              </span>
                            )}
                            {product.price_range && (
                              <span className="bg-primary-subtle text-primary text-sm font-medium px-3 py-1 rounded-full">
                                {product.price_range}
                              </span>
                            )}
                          </div>
                        </div>
                        {product.effectiveness_rating && (
                          <div className="text-right">
                            <div className="flex items-center space-x-1">
                              {[...Array(5)].map((_, i) => (
                                <span
                                  key={i}
                                  className={`text-lg ${
                                    i < Math.floor(product.effectiveness_rating)
                                      ? 'text-primary'
                                      : 'text-muted-foreground'
                                  }`}
                                >
                                  ★
                                </span>
                              ))}
                            </div>
                            <p className="text-sm text-muted-foreground mt-1">
                              {product.effectiveness_rating}/5.0 effectiveness
                            </p>
                          </div>
                        )}
                      </div>

                      <p className="text-card-foreground leading-relaxed mb-6">{product.description}</p>

                      {product.duration_hours && (
                        <div className="mb-6">
                          <p className="text-sm text-muted-foreground">
                            <strong>Duration:</strong> {product.duration_hours} hours per dose
                          </p>
                        </div>
                      )}
                      
                      <div className="grid md:grid-cols-2 gap-6">
                        {product.pros && product.pros.length > 0 && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-3">Advantages:</h4>
                            <ul className="space-y-2">
                              {product.pros.map((pro: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <CheckCircle className="w-4 h-4 text-green-500 mt-1 flex-shrink-0" />
                                  <span className="text-gray-700">{pro}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        
                        {product.cons && product.cons.length > 0 && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-3">Considerations:</h4>
                            <ul className="space-y-2">
                              {product.cons.map((con: string, index: number) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <AlertCircle className="w-4 h-4 text-orange-500 mt-1 flex-shrink-0" />
                                  <span className="text-gray-700">{con}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                      
                      {product.usage_instructions && (
                        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                          <h4 className="font-semibold text-blue-900 mb-2">Usage Instructions:</h4>
                          <p className="text-blue-800">{product.usage_instructions}</p>
                        </div>
                      )}
                      
                      {product.side_effects && product.side_effects.length > 0 && (
                        <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                          <h4 className="font-semibold text-yellow-900 mb-2">Possible Side Effects:</h4>
                          <div className="flex flex-wrap gap-2">
                            {product.side_effects.map((effect: string, index: number) => (
                              <span key={index} className="bg-yellow-100 text-yellow-800 text-sm px-2 py-1 rounded">
                                {effect}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  
                  {getProductsForTab().length === 0 && (
                    <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100 text-center">
                      <Info className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">No products found</h3>
                      <p className="text-gray-600">No NRT products available for this category yet.</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}



          {/* Product Cards Section - Real Database Data */}
          {activeTab !== 'overview' && (
            <div className="space-y-8">
              {/* Category Information */}
              <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">
                  {activeTab === 'lozenges' && 'Nicotine Lozenges'}
                  {activeTab === 'inhalers' && 'Nicotine Inhalers'}
                  {activeTab === 'sprays' && 'Nicotine Sprays'}
                  {activeTab === 'gum' && 'Nicotine Gum'}
                  {activeTab === 'patches' && 'Nicotine Patches'}
                </h3>
                <p className="text-gray-700 leading-relaxed mb-4">
                  {activeTab === 'lozenges' && 'Nicotine lozenges dissolve slowly in your mouth to provide controlled nicotine release. They\'re available in different strengths and flavors.'}
                  {activeTab === 'inhalers' && 'Nicotine inhalers provide nicotine vapor that is absorbed through the mouth and throat. They mimic the hand-to-mouth action of smoking.'}
                  {activeTab === 'sprays' && 'Nicotine sprays deliver nicotine quickly through the mouth or nose. They provide fast relief but require careful dosing.'}
                  {activeTab === 'gum' && 'Nicotine gum provides nicotine through chewing and helps control withdrawal symptoms. Available in various strengths and flavors.'}
                  {activeTab === 'patches' && 'Nicotine patches provide steady nicotine delivery through the skin over 16-24 hours. They offer consistent craving control.'}
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
                  <div className="flex items-start space-x-3">
                    <Info className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
                    <p className="text-blue-800">
                      Consult with a healthcare provider to determine the best NRT option for your specific needs and medical history.
                    </p>
                  </div>
                </div>
              </div>

              {/* Product Cards Grid - Real Database Products */}
              {getProductsForTab().length > 0 && (
                <div>
                  <h4 className="text-xl font-bold text-gray-900 mb-6">Recommended Products</h4>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {getProductsForTab().map((product) => (
                      <div key={product.id} className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                        {/* Product Header */}
                        <div className="flex items-start justify-between mb-4">
                          <h5 className="font-bold text-gray-900 text-lg leading-tight">
                            {product.name}
                          </h5>
                          {product.effectiveness_rating && (
                            <div className="flex items-center space-x-1 bg-primary-subtle px-2 py-1 rounded-full">
                              <Star className="w-3 h-3 text-green-600 fill-current" />
                              <span className="text-sm font-medium text-green-700">
                                {product.effectiveness_rating}/5
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Product Details */}
                        <div className="space-y-3 mb-6">
                          {product.strength && (
                            <div className="flex justify-between">
                              <span className="text-gray-600">Strength:</span>
                              <span className="font-medium text-gray-900">{product.strength}</span>
                            </div>
                          )}
                          {product.duration && (
                            <div className="flex justify-between">
                              <span className="text-gray-600">Duration:</span>
                              <span className="font-medium text-gray-900">{product.duration}</span>
                            </div>
                          )}
                          {product.manufacturer && (
                            <div className="flex justify-between">
                              <span className="text-gray-600">Brand:</span>
                              <span className="font-medium text-gray-900">{product.manufacturer}</span>
                            </div>
                          )}
                        </div>

                        {/* Price and Buy Button */}
                        <div className="border-t border-gray-100 pt-4">
                          {product.price && (
                            <div className="flex items-center justify-between mb-4">
                              <span className="text-2xl font-bold text-green-600">
                                ${product.price}
                              </span>
                              {product.price_per_unit && (
                                <span className="text-sm text-gray-500">
                                  {product.price_per_unit}
                                </span>
                              )}
                            </div>
                          )}
                          
                          <div className="flex space-x-2">
                            {product.buy_link && (
                              <a
                                href={product.buy_link}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex-1 btn-primary px-4 py-2 rounded-lg font-medium text-center transition-colors flex items-center justify-center space-x-2"
                              >
                                <ShoppingCart className="w-4 h-4" />
                                <span>Buy Now</span>
                                <ExternalLink className="w-3 h-3" />
                              </a>
                            )}
                            {product.compare_link && (
                              <a
                                href={product.compare_link}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                              >
                                Compare
                              </a>
                            )}
                          </div>
                          
                          {product.affiliate_note && (
                            <p className="text-xs text-gray-500 mt-2">
                              {product.affiliate_note}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* No Products Message */}
              {getProductsForTab().length === 0 && (
                <div className="bg-gray-50 p-8 rounded-xl border border-gray-200 text-center">
                  <Tablet className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    Products Coming Soon
                  </h4>
                  <p className="text-gray-600">
                    We're working on adding more {activeTab} options to help you on your wellness journey.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-primary text-primary-foreground py-16">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold text-primary-foreground mb-4">
            Ready for Personalized Support?
          </h2>
          <p className="text-xl text-primary-foreground mb-8">
            Mission Fresh offers comprehensive tools to track your progress, manage cravings, and boost your well-being during your wellness journey.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              to="/tools/smokeless-directory"
              className="inline-flex items-center justify-center border-2 border-primary-foreground text-primary-foreground px-8 py-3 rounded-lg text-lg font-medium hover:bg-primary-foreground hover:text-primary transition-colors"
            >
              <Heart className="w-5 h-5 mr-2" />
              Explore Alternatives
              <ArrowRight className="w-5 h-5 ml-2" />
            </Link>
            <Link
              to="/auth?mode=signup"
              className="inline-flex items-center justify-center bg-secondary text-secondary-foreground px-8 py-3 rounded-lg text-lg font-medium hover:bg-secondary-hover transition-colors border border-border shadow-lg"
            >
              Get Started
              <ArrowRight className="w-5 h-5 ml-2" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
