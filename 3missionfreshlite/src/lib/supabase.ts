import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://yekarqanirdkdckimpna.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyNzUwOTQsImV4cCI6MjA1OTg1MTA5NH0.WQlbyilIuH_Vz_Oit-M5MZ9II9oqO7tg-ThkZ5GCtfc'

export const supabase = createClient(supabaseUrl, supabaseKey)

export type Database = {
  mission_fresh: {
    Tables: {
      profiles: {
        Row: {
          id: string
          updated_at: string | null
          username: string | null
          full_name: string | null
          avatar_url: string | null
          email: string
        }
        Insert: {
          id: string
          updated_at?: string | null
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          email: string
        }
        Update: {
          id?: string
          updated_at?: string | null
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          email?: string
        }
      }
      user_goals: {
        Row: {
          id: string
          user_id: string
          target_quit_date: string
          motivation: string | null
          cigarettes_per_day: number | null
          years_smoking: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          target_quit_date: string
          motivation?: string | null
          cigarettes_per_day?: number | null
          years_smoking?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          target_quit_date?: string
          motivation?: string | null
          cigarettes_per_day?: number | null
          years_smoking?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      health_metrics: {
        Row: {
          id: string
          user_id: string
          date: string
          mood_score: number | null
          energy_level: number | null
          stress_level: number | null
          sleep_quality: number | null
          cravings_intensity: number | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          date: string
          mood_score?: number | null
          energy_level?: number | null
          stress_level?: number | null
          sleep_quality?: number | null
          cravings_intensity?: number | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          date?: string
          mood_score?: number | null
          energy_level?: number | null
          stress_level?: number | null
          sleep_quality?: number | null
          cravings_intensity?: number | null
          created_at?: string
        }
      }
      community_posts: {
        Row: {
          id: string
          user_id: string
          title: string
          content: string
          post_type: string
          likes_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          content: string
          post_type?: string
          likes_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          content?: string
          post_type?: string
          likes_count?: number
          created_at?: string
          updated_at?: string
        }
      }
      nrt_products: {
        Row: {
          id: string
          name: string
          category: string
          description: string
          strength: string | null
          duration_hours: number | null
          price_range: string | null
          brand: string | null
          pros: string[]
          cons: string[]
          usage_instructions: string | null
          side_effects: string[]
          effectiveness_rating: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          category: string
          description: string
          strength?: string | null
          duration_hours?: number | null
          price_range?: string | null
          brand?: string | null
          pros?: string[]
          cons?: string[]
          usage_instructions?: string | null
          side_effects?: string[]
          effectiveness_rating?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          category?: string
          description?: string
          strength?: string | null
          duration_hours?: number | null
          price_range?: string | null
          brand?: string | null
          pros?: string[]
          cons?: string[]
          usage_instructions?: string | null
          side_effects?: string[]
          effectiveness_rating?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      // Add more table types as needed
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_dashboard_ai_insights: {
        Args: { user_id: string }
        Returns: { insights: string }
      }
      get_gemini_suggestions: {
        Args: { user_input: string; user_id: string }
        Returns: { suggestion: string }
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}
