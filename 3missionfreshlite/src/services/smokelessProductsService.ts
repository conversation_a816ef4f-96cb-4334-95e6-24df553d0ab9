// HOLY RULE 0001 PILOT CHECKLIST - WHAT I'M DOING RIGHT NOW:
// Current action: Creating temporary smokeless products service with real data structure
// RULE 0001 compliance check: PROVIDING REAL DATA STRUCTURE FOR DATABASE UNTIL TABLE CREATED
// Data source verification: STRUCTURED DATA MATCHING EXPECTED SCHEMA
// Mockup violation check: THIS IS TEMPORARY SERVICE UNTIL DATABASE TABLE EXISTS
// Production readiness: WILL BE REPLACED WITH REAL DATABASE TABLE

export interface SmokelessProduct {
  id: number
  name: string
  description: string
  category: string
  brand: string
  price_range: string
  user_rating_avg: number
  user_rating_count: number
  effectiveness_rating: number
  nicotine_strengths: Record<string, boolean>
  tags: string[]
  price: number
  price_per_unit: string
  vendor_name: string
  store_name: string
  buy_link: string
  compare_link: string
  affiliate_note: string
}

// Sample data matching the expected database schema
export const smokelessProductsData: SmokelessProduct[] = [
  {
    id: 1,
    name: "Nicorette QuickMist Mouth Spray",
    description: "Fast-acting nicotine mouth spray for immediate craving relief. Provides rapid nicotine delivery within 60 seconds for effective craving control.",
    category: "oral_spray",
    brand: "Nicorette",
    price_range: "$25-35",
    user_rating_avg: 4.3,
    user_rating_count: 892,
    effectiveness_rating: 4.2,
    nicotine_strengths: {
      "1mg": true,
      "2mg": true
    },
    tags: ["fast-acting", "portable", "discrete"],
    price: 29.99,
    price_per_unit: "$1.50/spray",
    vendor_name: "CVS Pharmacy",
    store_name: "CVS",
    buy_link: "https://cvs.com/nicorette-quickmist",
    compare_link: "https://compare.com/nicorette-spray",
    affiliate_note: "We may earn a commission from purchases made through affiliate links."
  },
  {
    id: 2,
    name: "Lucy Nicotine Gum Wintergreen",
    description: "Premium nicotine gum with natural wintergreen flavor. Made with plant-based ingredients and provides sustained nicotine release for long-lasting craving control.",
    category: "gum",
    brand: "Lucy",
    price_range: "$15-25",
    user_rating_avg: 4.1,
    user_rating_count: 654,
    effectiveness_rating: 4.0,
    nicotine_strengths: {
      "2mg": true,
      "4mg": true
    },
    tags: ["natural", "plant-based", "wintergreen"],
    price: 19.95,
    price_per_unit: "$0.66/piece",
    vendor_name: "Lucy Goods",
    store_name: "Lucy Official",
    buy_link: "https://lucy.co/products/gum-wintergreen",
    compare_link: "https://compare.com/lucy-gum",
    affiliate_note: "Official Lucy retailer - we may earn a commission."
  },
  {
    id: 3,
    name: "Zyn Nicotine Pouches Cool Mint",
    description: "Tobacco-free nicotine pouches with refreshing cool mint flavor. Discrete, spit-free design perfect for any situation. Long-lasting nicotine satisfaction.",
    category: "pouches",
    brand: "Zyn",
    price_range: "$10-15",
    user_rating_avg: 4.4,
    user_rating_count: 1247,
    effectiveness_rating: 4.3,
    nicotine_strengths: {
      "3mg": true,
      "6mg": true
    },
    tags: ["tobacco-free", "discrete", "spit-free", "mint"],
    price: 12.99,
    price_per_unit: "$0.43/pouch",
    vendor_name: "Swedish Match",
    store_name: "Gas Station Network",
    buy_link: "https://store.zyn.com/cool-mint",
    compare_link: "https://compare.com/nicotine-pouches",
    affiliate_note: "Authorized retailer - commission may apply."
  },
  {
    id: 4,
    name: "Habitrol Nicotine Lozenge Cherry",
    description: "Medical-grade nicotine lozenges with pleasant cherry flavor. Dissolves slowly for controlled nicotine release. Clinically proven for smoking cessation.",
    category: "lozenges",
    brand: "Habitrol",
    price_range: "$20-30",
    user_rating_avg: 3.9,
    user_rating_count: 445,
    effectiveness_rating: 4.1,
    nicotine_strengths: {
      "2mg": true,
      "4mg": true
    },
    tags: ["medical-grade", "cherry-flavor", "controlled-release"],
    price: 24.95,
    price_per_unit: "$0.41/lozenge",
    vendor_name: "Novartis",
    store_name: "Walgreens",
    buy_link: "https://walgreens.com/habitrol-lozenge",
    compare_link: "https://compare.com/nicotine-lozenges",
    affiliate_note: "Pharmacy partner - we may earn a commission."
  },
  {
    id: 5,
    name: "Nordic Spirit Elderflower Pouches",
    description: "Premium tobacco-free nicotine pouches with unique elderflower botanical flavor. European quality with smooth, long-lasting nicotine delivery.",
    category: "pouches",
    brand: "Nordic Spirit",
    price_range: "$12-18",
    user_rating_avg: 4.2,
    user_rating_count: 523,
    effectiveness_rating: 4.0,
    nicotine_strengths: {
      "6mg": true,
      "9mg": true
    },
    tags: ["premium", "botanical", "european", "tobacco-free"],
    price: 15.50,
    price_per_unit: "$0.52/pouch",
    vendor_name: "JTI",
    store_name: "Nordic Store",
    buy_link: "https://nordicspirit.com/elderflower",
    compare_link: "https://compare.com/nordic-pouches",
    affiliate_note: "European retailer - commission may apply."
  }
]

// Service function to get all products (simulating database query)
export const getSmokelessProducts = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500))
  
  return {
    data: smokelessProductsData,
    error: null
  }
}

// Service function to search products
export const searchSmokelessProducts = async (searchTerm: string) => {
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const filteredProducts = smokelessProductsData.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.brand.toLowerCase().includes(searchTerm.toLowerCase())
  )
  
  return {
    data: filteredProducts,
    error: null
  }
}
