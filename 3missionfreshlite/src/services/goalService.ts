import { supabase } from '../lib/supabase'

// PRODUCTION READY: Complete database schema types for Goals
export interface UserGoal {
  id: string
  user_id: string
  goal_type: string
  method: string
  quit_date: string
  motivation: string
  typical_daily_usage: number
  cost_per_unit: number
  years_smoking: number
  replacement_method?: string
  support_system?: string
  status: string
  created_at: string
  updated_at: string
}

export interface UserGoalInsert {
  user_id: string
  goal_type: string
  method: string
  quit_date: string
  motivation: string
  typical_daily_usage: number
  cost_per_unit: number
  years_smoking: number
  replacement_method?: string
  support_system?: string
  status?: string
}

export interface UserGoalUpdate {
  goal_type?: string
  method?: string
  quit_date?: string
  motivation?: string
  typical_daily_usage?: number
  cost_per_unit?: number
  years_smoking?: number
  replacement_method?: string
  support_system?: string
  status?: string
  updated_at?: string
}

/**
 * Get the current active goal for the authenticated user
 */
export const getUserGoal = async (): Promise<UserGoal | null> => {
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) return null

  const { data, error } = await supabase
    .from('user_goals')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })
    .limit(1)
    .maybeSingle()

  if (error) {
    console.error('Error fetching user goal:', error)
    throw error
  }

  return data
}

/**
 * Get all goals for the authenticated user
 */
export const getUserGoals = async (): Promise<UserGoal[]> => {
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) return []

  const { data, error } = await supabase
    .from('user_goals')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching goals', error)
    throw error
  }

  return data || []
}

/**
 * Get a specific goal by ID for the authenticated user
 */
export const getGoalById = async (goalId: string): Promise<UserGoal> => {
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) throw new Error("User not authenticated")

  const { data, error } = await supabase
    .from('user_goals')
    .select('*')
    .eq('id', goalId)
    .eq('user_id', user.id)
    .single()

  if (error) {
    console.error('Error fetching goal by ID:', error)
    throw error
  }

  return data
}

/**
 * Add a new goal for the authenticated user
 */
export const saveUserGoal = async (goal: Omit<UserGoalInsert, 'user_id'>): Promise<UserGoal> => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error("User not authenticated")

    const goalToInsert: UserGoalInsert = {
      ...goal,
      user_id: user.id,
      status: 'in_progress'
    }

    const { data, error } = await supabase
      .from('user_goals')
      .insert(goalToInsert)
      .select()
      .single()

    if (error) {
      console.error('Error adding goal:', error)
      throw error
    }

    return data
  } catch (error: unknown) {
    console.error('Failed to save goal:', error)
    throw error
  }
}

/**
 * Update an existing goal for the authenticated user
 */
export const updateUserGoal = async (goalId: string, updates: UserGoalUpdate): Promise<UserGoal | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error("User not authenticated")
    
    const { data, error } = await supabase
      .from('user_goals')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', goalId)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating user goal:', error)
      throw error
    }
    
    return data
  } catch (error: unknown) {
    console.error('Error in updateUserGoal:', error)
    return null
  }
}

/**
 * Delete a goal for the authenticated user
 */
export const deleteUserGoal = async (goalId: string): Promise<boolean> => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error("User not authenticated")

    const { error } = await supabase
      .from('user_goals')
      .delete()
      .eq('id', goalId)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error deleting goal:', error)
      throw error
    }

    return true
  } catch (error: unknown) {
    console.error('Error deleting goal:', error)
    return false
  }
}
