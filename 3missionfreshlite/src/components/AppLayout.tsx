import { Outlet } from 'react-router-dom'
import { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import {
  Home,
  BarChart3,
  Settings,
  Book,
  Users,
  Target,
  LogOut,
  Calendar,
  Wind,
  Focus,
  Heart,
  AlertCircle,
  BookOpen,
  Menu,
  Trophy,
  Activity,
  Brain,
  X,
  HelpCircle
} from 'lucide-react'
import { NavLink } from 'react-router-dom'
import Logo from './Logo'

const navigationSections = [
  {
    title: 'Core Tracking',
    items: [
      { title: 'Dashboard', href: '/dashboard', icon: Home },
      { title: 'Log Entry', href: '/dashboard/log', icon: Book },
      { title: 'Progress', href: '/dashboard/progress', icon: BarChart3 },
      { title: 'Goals', href: '/dashboard/goals', icon: Target },
    ]
  },
  {
    title: 'Wellness Tools',
    items: [
      { title: 'Breathing', href: '/dashboard/breathing', icon: Wind },
      { title: 'Focus', href: '/dashboard/focus', icon: Focus },
      { title: 'Mood', href: '/dashboard/mood', icon: Heart },
      { title: 'Rewards', href: '/dashboard/rewards', icon: Trophy },
      { title: 'Health Data', href: '/dashboard/health-integrations', icon: Activity },
      { title: 'Journal', href: '/dashboard/journal', icon: BookOpen },
    ]
  },
  {
    title: 'Community & Learning',
    items: [
      { title: 'Community', href: '/dashboard/community', icon: Users },
      { title: 'Learn', href: '/dashboard/learn', icon: BookOpen },
      { title: 'Support', href: '/dashboard/support', icon: HelpCircle },
      { title: 'Settings', href: '/dashboard/settings', icon: Settings },
    ]
  }
];

interface SidebarLinkProps {
  item: {
    title: string
    href: string
    icon: React.ComponentType<{ className?: string }>
  }
}

function SidebarLink({ item }: SidebarLinkProps) {
  const Icon = item.icon
  
  return (
    <NavLink
      to={item.href}
      className={({ isActive }) =>
        `flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
          isActive
            ? 'bg-accent text-primary shadow-sm'
            : 'text-muted-foreground hover:text-primary hover:bg-accent'
        }`
      }
    >
      <Icon className="w-5 h-5 flex-shrink-0" />
      <span>{item.title}</span>
    </NavLink>
  )
}

export default function AppLayout() {
  const { user, signOut } = useAuth()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  
  // RULE 0001 COMPLIANCE: Only use real authenticated user data
  if (!user) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-4">Authentication Required</h1>
          <p className="text-muted-foreground">Please log in to access the dashboard.</p>
        </div>
      </div>
    )
  }
  
  // if (!user) {
  //   return null
  // }
  
  return (
    <div className="h-screen bg-muted flex overflow-hidden">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-foreground bg-opacity-75 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
      
      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-72 bg-background shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex flex-col h-full">
          {/* Sidebar header */}
          <div className="flex items-center justify-between h-16 px-6 border-b border-border">
            <Logo size="sm" showText={true} linkTo="/dashboard" />
            <button
              onClick={() => setSidebarOpen(false)}
              className="p-1 rounded-md lg:hidden"
            >
              <X className="w-5 h-5 text-muted-foreground" />
            </button>
          </div>
          
          {/* Navigation */}
          <div className="flex-1 overflow-y-auto px-4 py-6">
            <nav className="space-y-6">
              {navigationSections.map((section, sectionIndex) => (
                <div key={section.title} className="space-y-2">
                  <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide px-3">
                    {section.title}
                  </h3>
                  <div className="space-y-1">
                    {section.items.map((item) => (
                      <SidebarLink key={item.href} item={item} />
                    ))}
                  </div>
                  {sectionIndex < navigationSections.length - 1 && (
                    <div className="h-px bg-border mx-3 my-4" />
                  )}
                </div>
              ))}
            </nav>
          </div>
          
          {/* User section */}
          <div className="p-4 border-t border-border">
            <div className="flex items-center gap-3 p-3 bg-muted rounded-lg mb-3">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-primary-foreground font-semibold text-sm">
                  {user.email?.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-foreground truncate">
                  {user.user_metadata?.full_name || user.email?.split('@')[0]}
                </p>
                <p className="text-xs text-muted-foreground truncate">{user.email}</p>
              </div>
            </div>
            <button
              onClick={signOut}
              className="w-full flex items-center gap-2 px-3 py-2 text-sm font-medium text-muted-foreground hover:text-destructive hover:bg-destructive/10 rounded-lg transition-colors"
            >
              <LogOut className="w-4 h-4" />
              Sign Out
            </button>
          </div>
        </div>
      </div>
      
      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden lg:ml-0">
        {/* Top navigation */}
        <div className="h-16 bg-background border-b border-border flex items-center justify-between px-4 lg:px-6">
          <button
            onClick={() => setSidebarOpen(true)}
            className="p-2 rounded-md text-muted-foreground hover:text-foreground lg:hidden"
          >
            <Menu className="w-6 h-6" />
          </button>
          
          <div className="flex-1 flex justify-center lg:justify-start lg:ml-0">
            <h1 className="text-xl font-semibold text-foreground">
              Mission Fresh Dashboard
            </h1>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="text-sm text-muted-foreground">
              Welcome back, {user.user_metadata?.full_name || user.email?.split('@')[0]}
            </div>
          </div>
        </div>
        
        {/* Page content */}
        <main className="flex-1 overflow-y-auto bg-muted">
          <div className="p-4 lg:p-6 h-full">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  )
}
