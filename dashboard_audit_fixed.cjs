const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: {width: 1920, height: 1080}
  });
  
  const page = await browser.newPage();
  await page.setViewport({width: 1920, height: 1080});
  
  console.log('📊 DASHBOARD COMPREHENSIVE AUDIT');
  
  // Navigate to dashboard (need to login first)
  await page.goto('http://localhost:5001/auth');
  await page.waitForSelector('form', {timeout: 10000});
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Login with test credentials using correct puppeteer methods
  const emailInput = await page.$('input[type="email"]');
  const passwordInput = await page.$('input[type="password"]');
  
  if (emailInput && passwordInput) {
    await emailInput.type('<EMAIL>');
    await passwordInput.type('J4913836j');
    
    const submitButton = await page.$('button[type="submit"]');
    await submitButton?.click();
    
    console.log('🔑 Login credentials entered, waiting for navigation...');
    
    // Wait for navigation or dashboard load
    try {
      await page.waitForNavigation({timeout: 15000});
      await new Promise(resolve => setTimeout(resolve, 3000));
    } catch (error) {
      console.log('⚠️ Navigation timeout, checking current state...');
    }
    
    const currentUrl = page.url();
    console.log('📍 Current URL after login attempt:', currentUrl);
    
    // Check if we're on dashboard
    if (currentUrl.includes('/app/dashboard') || currentUrl.includes('/dashboard')) {
      console.log('✅ Successfully reached dashboard');
      
      // Audit dashboard elements
      const widgets = await page.$$('[class*="card"], [class*="widget"], .bg-card');
      console.log('📱 Dashboard widgets/cards found:', widgets.length);
      
      const buttons = await page.$$('button');
      console.log('🔘 Dashboard buttons found:', buttons.length);
      
      const charts = await page.$$('svg, canvas, [class*="chart"]');
      console.log('📈 Charts/visualizations found:', charts.length);
      
      // Check for loading states
      const loaders = await page.$$('[class*="loading"], .spinner, [class*="skeleton"]');
      console.log('⏳ Loading elements found:', loaders.length);
      
      // Check for error states  
      const errors = await page.$$('[class*="error"], .text-destructive, .text-red');
      console.log('❌ Error elements found:', errors.length);
      
      // Test navigation menu
      const navItems = await page.$$('nav a, [role="navigation"] a');
      console.log('🧭 Navigation items found:', navItems.length);
      
      // Check headings consistency
      const headings = await page.$$eval('h1, h2, h3, h4, h5, h6', elements => {
        return elements.map(el => ({
          tag: el.tagName,
          text: el.textContent?.substring(0, 30),
          fontSize: window.getComputedStyle(el).fontSize
        }));
      });
      
      console.log('\n📝 DASHBOARD HEADINGS:');
      headings.forEach((h, i) => {
        console.log(`  ${h.tag}: ${h.fontSize} - "${h.text}"`);
      });
      
      // Take screenshot
      await page.screenshot({path: 'dashboard_audit_success.png', fullPage: true});
      console.log('\n📸 Dashboard audit screenshot saved');
      
    } else if (currentUrl.includes('/auth')) {
      console.log('❌ Still on auth page - login failed or validation error');
      
      // Check for auth errors
      const authErrors = await page.$$('.text-destructive, .error');
      if (authErrors.length > 0) {
        const errorText = await authErrors[0].evaluate(el => el.textContent);
        console.log('Auth error:', errorText);
      }
      
      await page.screenshot({path: 'dashboard_login_failed.png'});
    } else {
      console.log('❌ Unexpected URL after login:', currentUrl);
      await page.screenshot({path: 'dashboard_unexpected_state.png'});
    }
  } else {
    console.log('❌ Could not find email/password inputs');
  }
  
  await browser.close();
})();
