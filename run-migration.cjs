const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or Service Key');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration() {
  try {
    console.log('🔍 Creating craving_logs table directly...');
    
    // Test connection first
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (testError) {
      console.error('Connection test failed:', testError);
      process.exit(1);
    }
    
    console.log('✅ Database connection successful');
    
    // Try to check if table already exists
    console.log('🔍 Checking if craving_logs table exists...');
    
    const { data: existingData, error: existingError } = await supabase
      .schema('mission_fresh')
      .from('craving_logs')
      .select('*')
      .limit(1);
    
    if (!existingError) {
      console.log('✅ craving_logs table already exists!');
      return;
    }
    
    console.log('📝 Table does not exist, will need to create it manually via Supabase dashboard');
    console.log('SQL to execute:');
    
    const sql = `
-- Create craving_logs table
CREATE TABLE IF NOT EXISTS mission_fresh.craving_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  intensity INTEGER NOT NULL CHECK (intensity >= 1 AND intensity <= 10),
  trigger TEXT,
  notes TEXT,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_craving_logs_user_id ON mission_fresh.craving_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_craving_logs_timestamp ON mission_fresh.craving_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_craving_logs_created_at ON mission_fresh.craving_logs(created_at);

-- Enable RLS (Row Level Security)
ALTER TABLE mission_fresh.craving_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for users to access only their own data
CREATE POLICY "Users can access their own craving logs" ON mission_fresh.craving_logs
  FOR ALL USING (auth.uid() = user_id);
`;
    
    console.log(sql);
    console.log('\n📋 Copy the above SQL and execute it in Supabase SQL Editor');
    
  } catch (error) {
    console.error('Migration check failed:', error);
    process.exit(1);
  }
}

runMigration();