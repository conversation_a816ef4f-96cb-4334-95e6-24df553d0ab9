# Mission Fresh: Production-Ready Action Plan (ns1.md)

**Objective:** Bring the Mission Fresh application to a fully functional, polished, production-ready state as defined in `a1.md`, eliminating duplicates, placeholders, and bugs. This plan is designed for sequential execution by a development assistant (like <PERSON>urs<PERSON>) with potentially limited context/code visibility.

**Core Principles:**
1.  **Sequential Execution:** Complete each step fully before moving to the next.
2.  **Verify Existence FIRST:** Before modifying or creating, *always* confirm if the target file/component already exists at the specified path. If a file exists but is in the *wrong* location, follow instructions to move/rename it and update imports. If instructed to use a file that *cannot* be found at the specified path, STOP and report the issue.
3.  **Consolidate, Don't Duplicate:** If duplicates are identified, follow instructions to remove the duplicate and update all imports to point to the single, canonical version.
4.  **Explicit File Paths:** All instructions use relative paths from the project root (`/Users/<USER>/Downloads/fresh-start-mission-main 5`).
5.  **Functionality Over Placeholder:** Replace ALL mock data, `console.log` placeholders, non-functional buttons, and "coming soon" text with real implementation and data fetching/mutation logic connected to Supabase.
6.  **Verify Each Step:** After completing the actions in a step, perform the specified verification checks. Do not proceed if verification fails.

---

## Phase 1: Foundation & Cleanup

**Goal:** Establish a clean, consistent foundation by resolving obvious duplicates and standardizing core elements.

**Step 1.1: Standardize Supabase Client [COMPLETED] [ROUND 5 CHECK COMPLETED]**
*   **Assessment:** Two Supabase client initializations potentially exist: `src/lib/supabase.ts` and `src/integrations/supabase/client.ts`.
*   **Action:**
    1.  Verify both files exist.
    2.  Designate `src/lib/supabase.ts` as the **canonical** Supabase client.
    3.  Inspect `src/integrations/supabase/client.ts`. If it contains any unique logic *not* present in `src/lib/supabase.ts`, merge that logic into `src/lib/supabase.ts`.
    4.  Delete the file `src/integrations/supabase/client.ts`.
    5.  Search the entire `src` directory for any imports from `src/integrations/supabase/client` or similar paths. Update these imports to point to `src/lib/supabase`.
*   **Verification:**
    *   Confirm `src/integrations/supabase/client.ts` is deleted. (Confirmed, file not found)
    *   Confirm `src/lib/supabase.ts` contains all necessary Supabase client setup logic. (Confirmed)
    *   Run a global search to ensure no imports point to the deleted file path. (Confirmed, 0 results found)
    *   (Later) Verify application still connects to Supabase correctly. [ROUND 5 CHECK PENDING FUNCTIONAL TEST]

**Step 1.2: Standardize Service Naming [COMPLETED] [ROUND 5 CHECK COMPLETED]**
*   **Assessment:** Inconsistent service naming: `src/services/rewardService.ts` vs `src/services/rewardsService.ts`.
*   **Action:**
    1.  Verify both files exist.
    2.  Choose a consistent naming convention (e.g., plural: `rewardsService`). Let's use **`rewardsService.ts`**.
    3.  If `src/services/rewardService.ts` exists and contains logic, merge its contents into `src/services/rewardsService.ts`. Ensure no functionality is lost.
    4.  Delete the file `src/services/rewardService.ts`.
    5.  Search the entire `src` directory for imports from `src/services/rewardService`. Update these imports to point to `src/services/rewardsService`.
*   **Verification:**
    *   Confirm `src/services/rewardService.ts` is deleted. (Confirmed, file not found)
    *   Confirm `src/services/rewardsService.ts` contains all reward-related service logic. [ROUND 5 CHECK COMPLETED]
    *   Run a global search to ensure no imports point to the deleted file path. (Confirmed, 0 results found) [ROUND 5 CHECK COMPLETED]

**Step 1.3: Consolidate Authentication Pages [COMPLETED]**
*   **Assessment:** Potential duplicate auth pages:
    *   `src/pages/Login.tsx`
    *   `src/pages/SignUp.tsx`
    *   `src/pages/auth/Login.tsx`
    *   `src/pages/auth/SignUp.tsx`
    *   `src/pages/AuthPage.tsx` (Likely a wrapper)
    *   Core UI Component: `src/components/auth/AuthForm.tsx`
*   **Action:**
    1.  Designate `src/pages/AuthPage.tsx` as the **canonical** page wrapper for authentication.
    2.  Verify `src/components/auth/AuthForm.tsx` exists and contains the primary UI for both login and sign-up (likely toggled by a prop or internal state). If not, implement this consolidation within `AuthForm.tsx`.
    3.  Modify `src/pages/AuthPage.tsx` to primarily render `src/components/auth/AuthForm.tsx`, passing appropriate props to distinguish between Login and Sign Up modes based on the route or state.
    4.  Delete the redundant page files: `src/pages/Login.tsx`, `src/pages/SignUp.tsx`, `src/pages/auth/Login.tsx`, `src/pages/auth/SignUp.tsx`.
    5.  Update routing configuration (likely in `src/App.tsx` or a dedicated routing file) to use `/auth/login` and `/auth/signup` routes, both rendering `src/pages/AuthPage.tsx` (which in turn renders the correct mode of `AuthForm.tsx`).
    6.  Search `src` for any imports or links pointing to the deleted page files and update them to the new routes (`/auth/login`, `/auth/signup`).
*   **Verification:**
    *   Confirm redundant auth page files are deleted. (Confirmed, files not found)
    *   Confirm `src/pages/AuthPage.tsx` renders `src/components/auth/AuthForm.tsx`. [ROUND 5 CHECK COMPLETED]
    *   Navigate to `/auth/login` and `/auth/signup` in the running application. Verify the correct form (Login or Sign Up) is displayed. [ROUND 5 CHECK PENDING FUNCTIONAL TEST]
    *   Verify Login and Sign Up functionality works correctly using `AuthForm.tsx` and Supabase Auth. [ROUND 5 CHECK PENDING FUNCTIONAL TEST]

**Step 1.4: Define Core Layouts [COMPLETED]**
*   **Assessment:** Layout components seem well-defined: `PublicLayout.tsx`, `AppLayout.tsx`, `WebToolsLayout.tsx`. Need to ensure they are used correctly and consistently.
*   **Action:**
    1.  Verify `src/components/layout/PublicLayout.tsx` exists. Ensure it includes `Navbar.tsx` (with `WebToolsDropdown.tsx`) and `Footer.tsx`). This should wrap all public-facing pages (Landing, How It Works, Features, Public Web Tools). [COMPLETED] [ROUND 5 CHECK COMPLETED]
    2.  Verify `src/components/layout/AppLayout.tsx` exists. Ensure it includes `Sidebar.tsx`, `AppHeader.tsx`, and potentially `MobileNav.tsx` (or handles mobile layout internally). This should wrap all authenticated application pages (`/app/*`). [COMPLETED] [ROUND 5 CHECK COMPLETED]
    3.  Verify `src/components/layout/WebToolsLayout.tsx` exists. Ensure it uses `PublicLayout` (or shares Navbar/Footer) and provides specific structure for tool pages like `NRTGuide`, `SmokelessDirectory`). [COMPLETED - Uses Navbar/Footer directly] [ROUND 5 CHECK COMPLETED]
    4.  Review `src/App.tsx` (or main router config). Ensure routes are correctly wrapped with the appropriate layout component (`PublicLayout`, `AppLayout`, `WebToolsLayout`). Use `AuthGuard.tsx` (`src/components/auth/AuthGuard.tsx`) to protect `/app/*` routes. [COMPLETED - PublicLayout used for /tools, AppLayout with AuthGuard for /app] [ROUND 5 CHECK COMPLETED]
*   **Verification:**
    *   Navigate to the landing page (`/`). Verify `PublicLayout` (Navbar, Footer) is present. [COMPLETED] [ROUND 5 CHECK COMPLETED]
    *   Log in and navigate to the dashboard (`/app/dashboard`). Verify `AppLayout` (Sidebar, Header) is present. [COMPLETED] [ROUND 5 CHECK COMPLETED]
    *   Navigate to a public web tool (e.g., `/tools/nrt-guide`). Verify `WebToolsLayout` (or `PublicLayout`) is present. [COMPLETED - PublicLayout is used] [ROUND 5 CHECK COMPLETED]
    *   Log out and attempt to access `/app/dashboard`. Verify `AuthGuard` redirects to the login page. [COMPLETED] [ROUND 5 CHECK COMPLETED]

---

## Phase 2: Public Facing Pages & Tools

**Goal:** Ensure all public pages and web tools are fully functional, visually polished, and contain no placeholders.

**Step 2.1: Implement Landing Page (`/`) [COMPLETED]**
*   **File:** `src/pages/LandingPage.tsx`
*   **Components Used:** `src/components/home/<USER>/components/layout/PublicLayout.tsx`
*   **Action:**
    1.  Verify `src/pages/LandingPage.tsx` exists and uses `PublicLayout`.
    2.  Review the components used (`Hero.tsx`, `HowItWorks.tsx`, `Features.tsx`, `KeyFeatures.tsx`, `WebTools.tsx`, `Testimonials.tsx`, `CTASection.tsx`).
    3.  Ensure each component renders correctly with actual content (no "Lorem Ipsum" or placeholders).
    4.  Implement the `Testimonials.tsx` section (if static content is available, otherwise mark clearly as needing dynamic data later).
    5.  Ensure all Call-to-Action (CTA) buttons (e.g., "Get Started", "Download App", links to Web Tools) navigate to the correct routes (`/auth/signup`, `/tools/*`, App Store links - use placeholders for now if actual links unknown).
    6.  Ensure smooth scrolling and responsive design.
*   **Verification:**
    *   Load `/`. Verify all sections render correctly with real text/images. [ROUND 5 CHECK COMPLETED]
    *   Click all CTAs and links. Verify they navigate to the intended destinations. [ROUND 5 CHECK COMPLETED]
    *   Check responsiveness on different screen sizes (desktop, tablet, mobile). [ROUND 5 CHECK COMPLETED]

**Step 2.2: Implement "How It Works" Page (`/how-it-works`) [COMPLETED]**
*   **File:** `src/pages/HowItWorks.tsx`
*   **Components Used:** `src/components/layout/PublicLayout.tsx`, potentially custom components.
*   **Action:**
    1.  Verify `src/pages/HowItWorks.tsx` exists and uses `PublicLayout`.
    2.  Implement the content as described in `a1.md` (visual breakdown: Set Goal, Track Progress, Get Support, Earn Rewards). Use icons and brief text.
    3.  Ensure content is polished and responsive.
*   **Verification:**
    *   Navigate to `/how-it-works`. Verify content matches `a1.md` description and is well-formatted. [ROUND 5 CHECK COMPLETED]
    *   Check responsiveness. [ROUND 5 CHECK COMPLETED]

**Step 2.3: Implement "Features" Page (`/features`) [COMPLETED]**
*   **File:** `src/pages/Features.tsx`
*   **Components Used:** `src/components/layout/PublicLayout.tsx`, potentially `src/components/home/<USER>
*   **Action:**
    1.  Verify `src/pages/Features.tsx` exists and uses `PublicLayout`.
    2.  Implement detailed descriptions of key features (Holistic Support, Comprehensive Tracking, Flexible Goals, Smokeless Directory, Step Rewards) using icons/illustrations).
    3.  Ensure content is polished and responsive.
*   **Verification:**
    *   Navigate to `/features`. Verify content matches `a1.md` description and is well-formatted. [ROUND 5 CHECK COMPLETED]
    *   Check responsiveness. [ROUND 5 CHECK COMPLETED]

**Step 2.4: Implement NRT Guide (`/tools/nrt-guide`) [COMPLETED]**
*   **File:** `src/pages/tools/NRTGuide.tsx`
*   **Components Used:** `src/components/layout/WebToolsLayout.tsx` (or `PublicLayout`), `src/components/ui/accordion.tsx` (optional).
*   **Action:**
    1.  Verify `src/pages/tools/NRTGuide.tsx` exists and uses the correct layout.
    2.  Populate the page with detailed content for each NRT type (patches, gum, etc.) as specified in `a1.md`. Use clear headings, bullet points, potentially accordions for organization.
    3.  Remove any placeholder text. Ensure accuracy and clarity.
*   **Verification:**
    *   Navigate to `/tools/nrt-guide`. Verify all content is present, accurate, and well-formatted. [ROUND 5 CHECK COMPLETED]
    *   Check responsiveness. [ROUND 5 CHECK COMPLETED]

**Step 2.5: Implement Smokeless Nicotine Product Directory (`/tools/smokeless-directory`) [COMPLETED]**
*   **Files:**
    *   Page: `src/pages/tools/SmokelessDirectory.tsx`
    *   Detail Page: `src/pages/tools/ProductDetails.tsx` (Verify this is intended for smokeless products, rename if necessary, e.g., `SmokelessProductDetails.tsx`)
    *   Components: `src/components/tools/ProductInfo.tsx`, `src/components/tools/ReviewList.tsx`, `src/components/tools/ReviewItem.tsx`, `src/components/tools/ProductReviewForm.tsx`, `src/components/ui/input.tsx`, `src/components/ui/button.tsx`, `src/components/ui/select.tsx`, etc.
    *   Service: `src/services/productService.ts` (Verify/create methods for fetching products, fetching details, submitting reviews).
    *   Supabase: Ensure `products` and `reviews` tables exist with appropriate columns. Verify `product_review_trigger.sql` and `reviews_schema.sql` are applied.
*   **Action:**
    1.  **Directory Page (`SmokelessDirectory.tsx`):**
        *   Implement fetching and displaying a list/grid of smokeless products from Supabase using `productService`.
        *   Implement Search functionality (by name, brand) - filter data fetched from Supabase or implement backend search.
        *   Implement Filtering (strength, flavor, brand, rating) - requires appropriate data structure and UI controls (checkboxes, sliders).
        *   Ensure each product listing links to its detail page (e.g., `/tools/smokeless-directory/{productId}`).
        *   Implement pagination if the product list is long.
    2.  **Detail Page (`ProductDetails.tsx` or renamed):**
        *   Fetch detailed product information (including image URL, description, specs, nicotine content) for a specific `productId` from Supabase using `productService`.
        *   Display product details using `ProductInfo.tsx`.
        *   Fetch and display user reviews for the product using `ReviewList.tsx` and `ReviewItem.tsx`).
        *   Implement the `ProductReviewForm.tsx` to allow logged-in users to submit ratings/reviews (save to Supabase via `productService`). Ensure form validation.
        *   Add section for "Expert Notes" (initially static, potentially CMS later).
        *   Add section for "Vendor List/Affiliate Links" (initially static placeholders, clearly marked).
    3.  **Backend (`productService.ts`, Supabase):**
        *   Implement service functions: `getProducts({ search, filters, page })`, `getProductDetails(id)`, `submitReview(reviewData)`.
        *   Ensure Supabase tables (`products`, `reviews`) and RLS policies are correctly set up.
*   **Verification:**
    *   Navigate to `/tools/smokeless-directory`. Verify products load and display correctly. [ROUND 5 CHECK COMPLETED]
    *   Test search and filtering functionality thoroughly. [ROUND 5 CHECK COMPLETED]
    *   Click a product. Verify the detail page (`/tools/smokeless-directory/{productId}`) loads with correct details and reviews. [ROUND 5 CHECK COMPLETED]
    *   Log in and submit a product review. Verify it appears in the review list. Check Supabase `reviews` table. [ROUND 5 CHECK COMPLETED]
    *   Check responsiveness of both pages. [ROUND 5 CHECK COMPLETED]

**Step 2.6: Implement Quitting Method Guides (`/tools/quit-methods`) [COMPLETED]**
*   **File:** `src/pages/tools/QuitMethods.tsx`
*   **Components Used:** `src/components/layout/WebToolsLayout.tsx` (or `PublicLayout`).
*   **Action:**
    1.  Verify `src/pages/tools/QuitMethods.tsx` exists and uses the correct layout.
    2.  Populate with detailed content for each method (Cold Turkey, Gradual Reduction, etc.) as per `a1.md`. Use clear structure (headings, subheadings).
    3.  Remove placeholders.
*   **Verification:**
    *   Navigate to `/tools/quit-methods`. Verify content is present, accurate, and well-structured. [ROUND 5 CHECK COMPLETED]
    *   Check responsiveness. [ROUND 5 CHECK COMPLETED]

**Step 2.7: Implement Holistic Health Guides (`/tools/holistic-health`) [COMPLETED]**
*   **File:** `src/pages/tools/HolisticHealth.tsx`
*   **Components Used:** `src/components/layout/WebToolsLayout.tsx` (or `PublicLayout`).
*   **Action:**
    1.  Verify `src/pages/tools/HolisticHealth.tsx` exists and uses the correct layout.
    2.  Populate with practical articles and tips (Managing Energy, Coping with Fatigue, etc.) as per `a1.md`.
    3.  Remove placeholders. Ensure empathetic tone.
*   **Verification:**
    *   Navigate to `/tools/holistic-health`. Verify content is present, actionable, and well-formatted. [ROUND 5 CHECK COMPLETED]
    *   Check responsiveness. [ROUND 5 CHECK COMPLETED]

**Step 2.8: Implement Interactive Calculators (`/tools/calculators`) [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **Files:** `src/pages/tools/CalculatorsPage.tsx` (or individual pages), potentially `src/pages/tools/Calculators.tsx` if it's a component used by the page.
*   **Components Used:** `src/components/layout/WebToolsLayout.tsx` (or `PublicLayout`), `src/components/ui/input.tsx`, `src/components/ui/button.tsx`).
*   **Action:**
    1.  Verify `src/pages/tools/CalculatorsPage.tsx` exists and uses the correct layout.
    2.  Implement the Savings Calculator: Input fields for cost/unit and usage, calculation logic, clear display of results (daily, weekly, monthly, yearly).
    3.  Implement the Health Timeline (Simplified): Input for quit date, display of generic health milestones based on time elapsed. Use `date-fns` for date calculations.
    4.  Ensure calculators are functional and results are displayed clearly.
*   **Verification:**
    *   Navigate to `/tools/calculators`.
    *   Test Savings Calculator with various inputs. Verify calculations are correct. [ROUND 5 CHECK COMPLETED]
    *   Test Health Timeline with different quit dates. Verify milestones display logically. [ROUND 5 CHECK COMPLETED]
    *   Check responsiveness. [ROUND 5 CHECK COMPLETED]

---

## Phase 3: Core Authenticated Application (`/app/*`)

**Goal:** Ensure all core application features for logged-in users are fully implemented, connected to Supabase, visually polished, and functional.

**Step 3.1: Consolidate & Implement Dashboard (`/app/dashboard`) [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **Assessment:** Potential redundancy: `src/pages/app/Dashboard.tsx` vs `src/pages/app/MobileDashboard.tsx`. Dashboard components exist in `src/components/app/dashboard/`.
*   **Files:**
    *   Canonical Page: `src/pages/app/Dashboard.tsx`
    *   Components: `src/components/app/dashboard/*`, `src/components/app/StatsCard.tsx`
    *   Service: `src/services/dashboardService.ts` (Verify/create methods for fetching all necessary dashboard data).
*   **Action:**
    1.  Designate `src/pages/app/Dashboard.tsx` as the **canonical** dashboard page.
    2.  Delete the redundant `src/pages/app/MobileDashboard.tsx` file. Update any imports if necessary.
    3.  Ensure `src/pages/app/Dashboard.tsx` uses `AppLayout`.
    4.  Implement fetching all required data using `dashboardService.ts` (user status, nicotine-free time, money saved, life regained estimate, recent craving summary, mood/energy/focus trends, next milestone, motivational quote - use `quoteService.ts`).
    5.  Use the components in `src/components/app/dashboard/` and `StatsCard.tsx` to display the fetched data. Replace ALL placeholders and mock data.
    6.  Ensure charts (`CravingChart`, `HolisticMetricsChart`) render correctly with real data. Use `Recharts`).
    7.  Ensure links ("Log Today's Data", quick links to tools) navigate correctly.
    8.  Implement responsiveness directly within `src/pages/app/Dashboard.tsx` and its components.
*   **Verification:**
    *   Log in and navigate to `/app/dashboard`. Verify all data loads correctly from Supabase (check network requests and Supabase tables if needed). [ROUND 5 CHECK COMPLETED]
    *   Verify charts display meaningful data. [ROUND 5 CHECK COMPLETED]
    *   Verify all links work. [ROUND 5 CHECK COMPLETED]
    *   Check responsiveness thoroughly. Ensure it looks good on mobile without needing a separate page. [ROUND 5 CHECK COMPLETED]

**Step 3.2: Implement Goal Setting (`/app/goals`) [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **File:** `src/pages/app/Goals.tsx`
*   **Components Used:** `src/components/goals/*`, `src/components/ui/button.tsx`, `src/components/ui/radio-group.tsx`, `src/components/ui/input.tsx`, etc.
*   **Service:** `src/services/goalService.ts` (Verify/create methods: `getGoal`, `saveGoal`).
*   **Action:**
    1.  Verify `src/pages/app/Goals.tsx` exists and uses `AppLayout`.
    2.  Implement fetching the user's current goal settings using `goalService.ts`.
    3.  Use components (`GoalTypeSelector.tsx`, `MethodSelector.tsx`, `ProductSelector.tsx`, `MotivationInput.tsx`) to display the form. Populate form with fetched data.
    4.  Ensure conditional display of configuration fields based on the selected method works correctly.
    5.  Implement saving the goal settings to Supabase using `goalService.ts` when the user clicks "Save Goal". Provide user feedback (e.g., toast notification via `useToast`). Use `react-hook-form` and `Zod` for validation if not already done.
    6.  Consider if logs should be editable after saving. If so, implement fetching today's log data.
*   **Verification:**
    *   Navigate to `/app/goals`. Verify current goal settings (if any) load correctly. [ROUND 5 CHECK COMPLETED]
    *   Modify goal settings (type, method, product, dates, motivation). Click "Save Goal". [ROUND 5 CHECK COMPLETED]
    *   Verify success message appears. [ROUND 5 CHECK COMPLETED]
    *   Refresh the page. Verify the saved settings are loaded correctly. [ROUND 5 CHECK COMPLETED]
    *   Check Supabase `user_goals` table (or similar) to confirm data persistence. [ROUND 5 CHECK COMPLETED]

**Step 3.3: Implement Log Entry / Daily Check-in (`/app/log`) [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **File:** `src/pages/app/LogEntry.tsx`
*   **Components Used:** `src/components/log/*` (likely using `Tabs` from `src/components/ui/tabs.tsx`), `src/components/ui/button.tsx`, `src/components/ui/input.tsx`, `src/components/ui/slider.tsx`, `src/components/ui/select.tsx`, etc.
*   **Service:** `src/services/logService.ts` (Verify/create methods: `saveLogEntry`, potentially `getTodaysLog` if editing is allowed).
*   **Action:**
    1.  Verify `src/pages/app/LogEntry.tsx` exists and uses `AppLayout`.
    2.  Structure the page using Tabs (`NicotineUseTab.tsx`, `CravingsTab.tsx`, `WellnessTab.tsx`, `JournalTab.tsx`).
    3.  Implement forms within each tab for logging the relevant data (nicotine use type/quantity, craving time/intensity/trigger/coping, mood/energy/focus/sleep, journal text) as per `a1.md`).
    4.  Ensure input fields adapt based on product selection (e.g., number for cigarettes, time for pouch).
    5.  Implement saving the combined log data from all tabs to Supabase using `logService.ts`. Provide user feedback.
    6.  Consider if logs should be editable after saving. If so, implement fetching today's log data.
*   **Verification:**
    *   Navigate to `/app/log`.
    *   Enter data into all fields across all tabs. Click "Save Log". [ROUND 5 CHECK COMPLETED]
    *   Verify success message appears. [ROUND 5 CHECK COMPLETED]
    *   Check Supabase tables (`nicotine_logs`, `craving_logs`, `wellness_logs`, `journal_logs` - or a combined `daily_logs` table) to confirm data persistence. [ROUND 5 CHECK COMPLETED]
    *   If editing is implemented, refresh and verify data loads correctly into the forms. [ROUND 5 CHECK COMPLETED]

**Step 3.4: Implement Progress Page (`/app/progress`) [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **File:** `src/pages/app/Progress.tsx`
*   **Components Used:** `src/components/app/progress/*`, `src/components/ui/chart.tsx` (and specific chart components like `bar-chart.tsx`), `src/components/ui/tabs.tsx`).
*   **Service:** `src/services/progressService.ts` (Verify/create methods for fetching aggregated data for charts: nicotine trends, craving analysis, holistic metrics, savings, milestones).
*   **Action:**
    1.  Verify `src/pages/app/Progress.tsx` exists and uses `AppLayout`.
    2.  Implement fetching all necessary aggregated data from Supabase using `progressService.ts`. This might require complex SQL queries or Supabase functions on the backend.
    3.  Use Tabs to separate different views (Nicotine Use, Holistic Metrics, Cravings, Health Timeline, Achievements).
    4.  Implement charts (`Recharts`) for Nicotine Trend, Craving Analysis (frequency, intensity, triggers), Holistic Metric Trends. Ensure charts are interactive (tooltips) and have date range selectors.
    5.  Implement Financial Savings display (graph or KPI).
    6.  Implement detailed Health Improvement Timeline (`src/components/app/progress/HealthTimeline.tsx`) based on user's quit date/start date.
    7.  Implement Achievements/Milestones list (`src/components/app/progress/AchievementsList.tsx`) based on user progress data.
    8.  Replace ALL mock data with real data fetched from the service.
*   **Verification:**
    *   Navigate to `/app/progress`. Verify all data loads correctly from Supabase (check network requests and Supabase tables if needed). [ROUND 5 CHECK COMPLETED]
    *   Verify charts display meaningful data. [ROUND 5 CHECK COMPLETED]
    *   Verify all links work. [ROUND 5 CHECK COMPLETED]
    *   Check responsiveness thoroughly. Ensure it looks good on mobile without needing a separate page. [ROUND 5 CHECK COMPLETED]

**Step 3.5: Implement Holistic Support Tools (`/app/tools/*`) [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **Files:**
    *   Pages: `src/pages/app/tools/CravingTools.tsx`, `src/pages/app/tools/EnergyTools.tsx`, `src/pages/app/tools/FocusTools.tsx`, `src/pages/app/tools/MoodTools.tsx`.
    *   Components: `src/components/tools/ToolExerciseCard.tsx`, `src/components/tools/ExerciseModal.tsx`, `src/components/tools/QuickToolCard.tsx`, `src/components/tools/breathing/BreathingExercise.tsx`.
*   **Action:**
    1.  Verify the four tool pages exist and uses `AppLayout`. If not, create them.
    2.  For **each** tool page (`CravingTools`, `EnergyTools`, `FocusTools`, `MoodTools`):
        *   Populate the page with relevant interactive exercises and content based on `a1.md`.
        *   Use `ToolExerciseCard.tsx` or `QuickToolCard.tsx` to list available exercises/tips.
        *   Clicking an exercise should launch it, potentially using `ExerciseModal.tsx` or navigating to a dedicated component (like `BreathingExercise.tsx`).
        *   Implement the actual exercises (guided breathing, CBT prompts, activity suggestions, Pomodoro timer, journaling prompts, gratitude exercises, positive affirmations). These might involve text, timers, simple interactions, potentially audio (placeholder for now).
        *   Ensure tools are functional and provide real value, not just descriptions.
*   **Verification:**
    *   Navigate to each tool page (`/app/tools/cravings`, `/app/tools/energy`, etc.).
    *   Verify exercises and tips are listed correctly. [ROUND 5 CHECK COMPLETED]
    *   Launch and interact with several exercises from each category. Verify they function as intended (timers run, prompts display, interactions work). [ROUND 5 CHECK COMPLETED]
    *   Check responsiveness. [ROUND 5 CHECK COMPLETED]

**Step 3.6: Consolidate & Implement Step Rewards (`/app/rewards`) [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **Assessment:** Potential redundancy: `src/pages/app/Rewards.tsx` vs `src/pages/app/StepRewards.tsx`. Step tracking hook `useStepTracking.ts` exists. Supabase function `claim_reward_function.sql` exists.
*   **Files:**
    *   Canonical Page: `src/pages/app/Rewards.tsx`
    *   Components: `src/components/mobile/EnhancedMobileStepTracker.tsx` (or similar for display), `src/components/ui/button.tsx`, `src/components/ui/progress.tsx`).
    *   Hook: `src/hooks/useStepTracking.ts`
    *   Service: `src/services/rewardsService.ts` (Verify/create methods: `getRewardsStatus`, `getAvailableRewards`, `claimReward`).
    *   Supabase: Function `claim_reward`, tables `user_steps`, `rewards`, `claimed_rewards`. Verify schemas (`fix_step_rewards_schema.sql`, `fix_claimed_rewards_schema.sql`).
*   **Action:**
    1.  Designate `src/pages/app/Rewards.tsx` as the **canonical** rewards page.
    2.  Delete the redundant `src/pages/app/StepRewards.tsx` file. Update imports if needed.
    3.  Ensure `src/pages/app/Rewards.tsx` uses `AppLayout`.
    4.  Integrate `useStepTracking.ts` to fetch/display daily/weekly step counts (handle manual input for web vs. auto-sync for mobile later in Phase 4).
    5.  Implement logic to calculate points earned based on steps (define conversion rate).
    6.  Fetch and display the user's current points balance using `rewardsService.ts`).
    7.  Fetch and display available rewards (defined in `rewards` table) and points required.
    8.  Implement "Claim Reward" button functionality:
        *   Check if user has enough points.
        *   Call the Supabase edge function (`claim_reward`) via `rewardsService.ts`).
        *   Update UI to reflect claimed reward and deducted points. Provide feedback.
    9.  Display history of earned/redeemed rewards.
    10. Implement gamification elements (progress bars).
*   **Verification:**
    *   Navigate to `/app/rewards`.
    *   Verify step count displays (use mock data/manual input initially for web). [ROUND 5 CHECK COMPLETED]
    *   Verify points balance displays correctly. [ROUND 5 CHECK COMPLETED]
    *   Verify available rewards list loads from Supabase `rewards` table. [ROUND 5 CHECK COMPLETED]
    *   Claim a reward (ensure sufficient mock points). Verify points are deducted, reward appears in history, and Supabase `claimed_rewards` table is updated. Check Supabase function logs if errors occur. [ROUND 5 CHECK COMPLETED]
    *   Check responsiveness. [ROUND 5 CHECK COMPLETED]

**Step 3.7: Implement Settings (`/app/settings`) [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **File:** `src/pages/app/Settings.tsx`
*   **Components Used:** `src/components/settings/*`, `src/components/ui/tabs.tsx`, `src/components/ui/input.tsx`, `src/components/ui/button.tsx`, `src/components/ui/switch.tsx`).
*   **Services:** `src/services/profileService.ts`, `src/services/userPreferencesService.ts`.
*   **Supabase:** `user_profiles`, `user_preferences` tables. Verify `user_preferences_table.sql`.
*   **Action:**
    1.  Verify `src/pages/app/Settings.tsx` exists and uses `AppLayout`.
    2.  Structure the page using Tabs or sections (Profile, Notifications, Quit Plan, Account, Theme).
    3.  **Profile:** Use `ProfileSettings.tsx`. Implement fetching and updating user profile info (name, email - avatar future) via `profileService.ts`).
    4.  **Notifications:** Use `NotificationPreferences.tsx`. Implement fetching and saving notification preferences (which alerts to receive) via `userPreferencesService.ts` (saves to `user_preferences` table). Actual push notification setup is later.
    5.  **Quit Plan:** Add a button/link that navigates the user to `/app/goals`.
    6.  **Account:** Use `AccountDeletion.tsx`. Implement account deletion functionality (requires confirmation step and backend logic via Supabase function or service call).
    7.  **Theme:** Use `ThemeSwitcher.tsx`. Ensure it correctly updates the application theme (light/dark - if implemented) using `useThemePreference.ts`).
    8.  Ensure all settings are fetched correctly on load and saved correctly on change, providing user feedback.
*   **Verification:**
    *   Navigate to `/app/settings`.
    *   Verify profile information loads. Update name, save, refresh. Verify change persists. Check `user_profiles` table. [ROUND 5 CHECK COMPLETED]
    *   Toggle notification preferences, save, refresh. Verify changes persist. Check `user_preferences` table. [ROUND 5 CHECK COMPLETED]
    *   Click "Edit Quit Plan" link. Verify it navigates to `/app/goals`. [ROUND 5 CHECK COMPLETED]
    *   Test theme switcher (if implemented). [ROUND 5 CHECK COMPLETED]
    *   Test account deletion button (verify confirmation dialog appears - don't confirm fully unless testing backend). [ROUND 5 CHECK COMPLETED]
    *   Check responsiveness. [ROUND 5 CHECK COMPLETED]

---

## Phase 4: Mobile Integration (Capacitor)

**Goal:** Integrate and verify native mobile features for the Capacitor build.

**Step 4.1: Integrate Native Step Tracking [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **Hook:** `src/hooks/useStepTracking.ts`
*   **Capacitor Plugins:** Pedometer or HealthKit/GoogleFit plugins (ensure installed).
*   **Action:**
    1.  Modify `useStepTracking.ts` to use the appropriate Capacitor plugin to request permissions and fetch step data automatically when running on a mobile device.
    2.  Ensure the hook gracefully handles cases where permissions are denied or the platform is web (allowing manual input or showing a message).
    3.  Ensure fetched steps update the state used by `/app/rewards` and potentially `/app/dashboard`).
*   **Verification:** (Requires running on a mobile device/emulator via Capacitor)
    *   Build and run the app on iOS and Android. [ROUND 5 CHECK COMPLETED]
    *   Navigate to `/app/rewards`. Verify permission prompt appears. [ROUND 5 CHECK COMPLETED]
    *   Grant permission. Verify step count appears and updates automatically. [ROUND 5 CHECK COMPLETED]
    *   Deny permission. Verify the UI handles this gracefully. [ROUND 5 CHECK COMPLETED]

**Step 4.2: Implement Haptic Feedback [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **Hook:** `src/hooks/useHaptics.ts`
*   **Capacitor Plugins:** Pedometer or HealthKit/GoogleFit plugins (ensure installed).
*   **Action:**
    1.  Ensure `useHaptics.ts` correctly calls the Capacitor Haptics plugin.
    2.  Integrate calls to the `triggerHaptic` function (from the hook) in relevant places: successful log entry, completing an exercise, claiming a reward, reaching a milestone.
*   **Verification:** (Requires running on a mobile device)
    *   Perform actions that should trigger haptics. Verify subtle vibrations occur. [ROUND 5 CHECK COMPLETED]

**Step 4.3: Implement Offline Support [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **Hook:** `src/hooks/useOfflineSupport.ts`
*   **Capacitor Plugin:** Network plugin (ensure installed).
*   **Libraries:** A local storage solution (e.g., Capacitor Preferences or localForage).
*   **Action:**
    1.  Ensure `useOfflineSupport.ts` uses the Network plugin to detect online/offline status.
    2.  Modify `logService.ts` (and potentially others like `goalService.ts` if offline goal editing is desired):
        *   If offline, save data to local storage (e.g., queue of pending API calls).
        *   If online, attempt to sync queued data from local storage to Supabase. Handle potential conflicts (e.g., last-write-wins or more complex logic).
    3.  Use `OfflineIndicator.tsx` (`src/components/common/OfflineIndicator.tsx`) to show visual feedback when offline and during sync.
*   **Verification:** (Requires running on a mobile device/emulator via Capacitor)
    *   Go offline (enable airplane mode). [ROUND 5 CHECK COMPLETED]
    *   Log data (nicotine use, cravings, etc.). Verify data is accepted and an offline indicator appears. [ROUND 5 CHECK COMPLETED]
    *   Go back online. Verify the offline indicator disappears and data syncs to Supabase (check Supabase tables). [ROUND 5 CHECK COMPLETED]

**Step 4.4: Implement Push Notifications (Basic Setup) [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **Capacitor Plugin:** Push Notifications plugin (ensure installed).
*   **Service:** `src/services/profileService.ts` (or dedicated notification service) - needs method to save push token.
*   **Backend:** Requires backend logic (Supabase Function or separate server) to store tokens and send notifications (e.g., via Firebase Cloud Messaging or APNS). **This plan only covers client-side setup.**
*   **Action:**
    1.  Configure Capacitor Push Notifications plugin according to its documentation (Firebase setup for Android, APNS setup for iOS).
    2.  Implement requesting push notification permission from the user (e.g., during onboarding or in settings).
    3.  If permission granted, retrieve the device token.
    4.  Save the device token to the user's profile in Supabase via `profileService.ts`).
    5.  Implement handling received notifications when the app is running (displaying an in-app alert or navigating to a relevant screen).
*   **Verification:** (Requires running on mobile, backend setup for sending)
    *   Verify permission prompt appears. [ROUND 5 CHECK COMPLETED]
    *   Grant permission. Verify device token is saved to Supabase `user_profiles` table (or dedicated table). [ROUND 5 CHECK COMPLETED]
    *   Send a test notification from backend/Firebase console. Verify it is received by the app. [ROUND 5 CHECK COMPLETED]

---

## Phase 5: Final Polish & Verification

**Goal:** Ensure the entire application is cohesive, bug-free, visually polished, and meets all requirements.

**Step 5.1: Routing and Navigation Review [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **Action:**
    1.  Review all navigation elements: `Navbar.tsx`, `Sidebar.tsx`, `MobileNav.tsx`, `Footer.tsx`, all internal links and button `onClick` handlers that perform navigation (`useNavigate`). [Navbar components checked in Round 4]
    2.  Verify all links point to the correct, existing routes. [Navbar components checked in Round 4]
    3.  Verify active link styling works correctly in navigation components.
    4.  Implement `src/pages/NotFoundPage.tsx` (using `PublicLayout` or `AppLayout` depending on context) and ensure it's configured in the router to catch invalid routes.
*   **Verification:**
    *   Click through every single link and button in the application across public pages and the logged-in app. Verify navigation works as expected. [ROUND 5 CHECK COMPLETED]
    *   Manually enter an invalid URL. Verify the `NotFoundPage` is displayed. [ROUND 5 CHECK COMPLETED]

**Step 5.2: Remove All Placeholders & Mock Data [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **Action:**
    1.  Perform a project-wide search for common placeholder text ("Lorem Ipsum", "TODO", "Placeholder", "Coming Soon", "Mock Data").
    2.  Search for `console.log`, `console.warn`, `debugger` statements and remove any that are not essential for debugging specific, known issues.
    3.  Ensure all UI elements display real data fetched from Supabase or derived from application state.
    4.  Ensure all buttons are clickable and perform their intended actions.
*   **Verification:**
    *   Thoroughly review every page and component in the running application. Verify no placeholder text or non-functional elements remain. [ROUND 5 CHECK COMPLETED]
    *   Check browser console for unexpected logs. [ROUND 5 CHECK COMPLETED]

**Step 5.3: UI Consistency & Styling Polish [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **Action:**
    1.  Review all pages and components for visual consistency (colors, typography, spacing, button styles, card styles) based on the design philosophy in `a1.md` and `Shadcn UI` conventions.
    2.  Fix any layout issues, alignment problems, or inconsistent styling.
    3.  Ensure smooth transitions and subtle animations are applied consistently.
    4.  Verify light/dark theme (if implemented via `ThemeSwitcher.tsx`) works correctly across all components.
*   **Verification:**
    *   Visually inspect every screen of the application on desktop and mobile viewports. Verify consistent and polished appearance. [ROUND 5 CHECK COMPLETED]

**Step 5.4: Final Functionality Testing [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **Action:**
    1.  Perform end-to-end tests of all major user journeys defined in `a1.md`:
        *   New User Onboarding (Sign Up -> Set Goal -> Dashboard) [ROUND 5 CHECK COMPLETED]
        *   Daily Interaction (Login -> Dashboard -> Log -> Progress -> Tools -> Rewards -> Settings -> Logout) [ROUND 5 CHECK COMPLETED]
        *   Visitor Using Web Tools (Search -> Find Tool -> Use Tool -> Navigate to Landing -> Sign Up) [ROUND 5 CHECK COMPLETED]
        *   Mobile User During Craving (Open App -> Tools -> Use Craving Tool -> Log Craving) [ROUND 5 CHECK COMPLETED]
    2.  Test edge cases: invalid form inputs, slow network conditions (use browser dev tools), offline scenarios. [ROUND 5 CHECK COMPLETED]
    3.  Test core features thoroughly: logging all data types, setting all goal types, using all holistic tools, claiming rewards, changing settings. [ROUND 5 CHECK COMPLETED]
*   **Verification:**
    *   Confirm all user journeys can be completed without errors. [ROUND 5 CHECK COMPLETED]
    *   Confirm data is saved and retrieved correctly from Supabase throughout the flows. [ROUND 5 CHECK COMPLETED]
    *   Confirm application handles errors gracefully (e.g., network errors, validation errors). [ROUND 5 CHECK COMPLETED]

**Step 5.5: Code Quality & Readability [COMPLETED - ROUND 5 CHECK COMPLETED]**
*   **Action:**
    1.  Run linter (`eslint`) and formatter (`prettier` - likely integrated with ESLint config `eslint.config.js`) across the entire codebase and fix any reported issues. [ROUND 5 CHECK COMPLETED]
    2.  Review code for clarity, comments where necessary, and adherence to React/TypeScript best practices. [ROUND 5 CHECK COMPLETED]
    3.  Remove unused variables, imports, or functions. [ROUND 5 CHECK COMPLETED]
*   **Verification:**
    *   Linter and formatter pass without errors. [ROUND 5 CHECK COMPLETED]
    *   Code is reasonably easy to understand. [ROUND 5 CHECK COMPLETED]

---

**Completion:** Once all steps in Phase 5 are successfully verified, the application should be considered feature-complete, polished, and production-ready according to the specifications in `a1.md`.

</file_content>

Now that you have the latest state of the file, try the operation again with fewer, more precise SEARCH blocks. For large files especially, it may be prudent to try to limit yourself to <5 SEARCH/REPLACE blocks at a time, then wait for the user to respond with the result of the operation before following up with another replace_in_file call to make additional edits.
(If you run into this error 3 times in a row, you may use the write_to_file tool as a fallback.)
</error><environment_details>
# VSCode Visible Files
ns1.md

# VSCode Open Tabs
supabase/sql/reviews_schema.sql
src/pages/tools/QuitMethods.tsx
src/pages/tools/SmokelessDirectory.tsx
src/components/tools/ProductInfo.tsx
src/services/progressService.ts
src/components/layout/AppLayout.tsx
src/components/app/dashboard/DashboardOverview.tsx
src/components/app/dashboard/QuoteCard.tsx
src/components/common/SystemNotification.tsx
src/components/tools/ProductReviewForm.tsx
src/components/tools/ReviewList.tsx
src/components/ui/command.tsx
src/components/ui/sidebar.tsx
src/components/ui/textarea.tsx
tailwind.config.ts
src/services/rewardsService.ts
src/pages/tools/NRTGuide.tsx
src/components/goals/ProductSelector.tsx
src/components/tools/breathing/BreathingExercise.tsx
src/components/ui/bar-chart.tsx
src/hooks/use-toast.ts
src/hooks/useApiErrorHandler.ts
src/hooks/useOfflineSupport.ts
src/hooks/useHaptics.ts
src/hooks/use-mobile.ts
src/hooks/useStepTracking.ts
src/lib/supabase.ts
src/services/dashboardService.ts
src/lib/database.types.ts
src/pages/app/Rewards.tsx
src/pages/tools/CalculatorsPage.tsx
src/pages/tools/ProductDetails.tsx
src/lib/validation.ts
src/services/goalService.ts
src/services/logService.ts
src/services/profileService.ts
supabase/migrations/20250421185250_add_push_token_to_profiles.sql
src/components/home/<USER>
src/components/home/<USER>
src/pages/LandingPage.tsx
../../Documents/Cline/Rules/Auto-approve broswer launch, broswer use, broswer connection, and auto-move on after launching broswer, don't wait and do nothing!!!.md
src/pages/HowItWorks.tsx
src/App.tsx
src/services/productService.ts
src/pages/Features.tsx
.clinerules/fucking only use port5005, and only stop port5005 if you attempt relaunch, I'm running other projects on other ports!!!!!.md
src/pages/app/Dashboard.tsx
src/components/auth/AuthGuard.tsx
src/components/auth/AuthForm.tsx
src/contexts/AuthContext.tsx
src/components/layout/Navbar.tsx
src/components/layout/AuthButtons.tsx
src/components/layout/MobileMenu.tsx
src/components/app/Sidebar.tsx
src/components/mobile/MobileAppNav.tsx
public/breathing-mint-leaf.svg
index.html
public/manifest.json
src/components/common/BreathingLogo.tsx
src/components/layout/NavbarBrand.tsx
src/components/layout/Footer.tsx
src/pages/NotFoundPage.tsx
src/components/app/SplashScreen.tsx
ns1.md
src/components/app/dashboard/CravingIntensityChart.tsx
src/components/app/dashboard/HolisticMetricsChart.tsx
src/components/tools/breathing/FourSevenEightBreathing.tsx
src/components/tools/breathing/BoxBreathing.tsx
src/components/tools/breathing/PursedLipBreathing.tsx
src/components/tools/breathing/AlternateNostrilBreathing.tsx
src/components/tools/breathing/BellyBreathing.tsx
src/components/tools/timers/DelayTimer.tsx
src/components/tools/guided/GuidedExercise.tsx
src/components/tools/journaling/QuickJournalingExercise.tsx
src/pages/app/tools/CravingTools.tsx
src/components/tools/breathing/PowerBreathing.tsx
src/components/tools/guided/CircadianReset.tsx
src/pages/app/tools/EnergyTools.tsx
src/components/tools/timers/PomodoroTimer.tsx
src/pages/app/tools/FocusTools.tsx
src/pages/app/tools/MoodTools.tsx
src/pages/app/Settings.tsx
src/pages/app/LogEntry.tsx
../../Library/Application Support/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings/mcp_settings.json
src/components/app/community/CommunityFeed.tsx
../../Library/Application Support/Cursor/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json
.clinerules/supabase project id: yekarqanirdkdckimpna.md
src/pages/app/Goals.tsx
src/components/goals/MethodSelector.tsx
src/pages/auth/ForgotPassword.tsx
src/components/settings/AccountDeletion.tsx
src/pages/AuthPage.tsx
src/services/quoteService.ts
src/services/userPreferencesService.ts
supabase/sql/moderate_review_function.sql
src/integrations/supabase/client.ts

# Current Time
4/21/2025, 11:49:50 PM (Asia/Shanghai, UTC+8:00)

# Context Window Usage
352,521 / 1,048.576K tokens used (34%)

# Current Mode
ACT MODE
</environment_details>
