-- Create craving_logs table
CREATE TABLE IF NOT EXISTS mission_fresh.craving_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  intensity INTEGER NOT NULL CHECK (intensity >= 1 AND intensity <= 10),
  trigger TEXT,
  notes TEXT,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_craving_logs_user_id ON mission_fresh.craving_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_craving_logs_timestamp ON mission_fresh.craving_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_craving_logs_created_at ON mission_fresh.craving_logs(created_at);

-- Enable RLS (Row Level Security)
ALTER TABLE mission_fresh.craving_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for users to access only their own data
CREATE POLICY "Users can access their own craving logs" ON mission_fresh.craving_logs
  FOR ALL USING (auth.uid() = user_id);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION mission_fresh.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_craving_logs_updated_at 
  BEFORE UPDATE ON mission_fresh.craving_logs 
  FOR EACH ROW 
  EXECUTE FUNCTION mission_fresh.update_updated_at_column();