const puppeteer = require('puppeteer');

async function ruthlessInspection() {
    const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Set PC screen size as required
    await page.setViewport({ width: 1920, height: 1080 });
    
    try {
        console.log('🔍 RUTHLESS VISUAL INSPECTION BEGINNING...');
        
        // Navigate to the app on port 5001
        await page.goto('http://localhost:5001', { waitUntil: 'networkidle2', timeout: 15000 });
        
        // Take full page screenshot for ruthless pixel-by-pixel analysis
        await page.screenshot({ 
            path: 'current_app_state_ruthless_inspection.png', 
            fullPage: true 
        });
        
        console.log('📸 RUTHLESS INSPECTION SCREENSHOT CAPTURED: current_app_state_ruthless_inspection.png');
        console.log('🎯 Ready for pixel-by-pixel flaw detection and immediate fixing');
        
    } catch (error) {
        console.error('❌ INSPECTION ERROR:', error.message);
        await page.screenshot({ path: 'inspection_error.png' });
    }
    
    await browser.close();
}

ruthlessInspection();