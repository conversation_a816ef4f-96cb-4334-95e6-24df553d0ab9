const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: {width: 1920, height: 1080}
  });
  
  const page = await browser.newPage();
  await page.setViewport({width: 1920, height: 1080});
  
  await page.goto('http://localhost:5001');
  await page.waitForSelector('body', {timeout: 10000});
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  console.log('🔍 FINDING EMPTY BUTTON...');
  
  const buttons = await page.$$('button');
  
  for (let i = 0; i < buttons.length; i++) {
    const buttonText = await buttons[i].evaluate(el => el.textContent?.trim());
    const buttonHTML = await buttons[i].evaluate(el => el.outerHTML);
    const buttonClass = await buttons[i].evaluate(el => el.className);
    
    console.log(`\nButton ${i+1}:`);
    console.log(`  Text: "${buttonText}"`);
    console.log(`  Classes: ${buttonClass}`);
    
    if (!buttonText || buttonText === '') {
      console.log(`  🚨 EMPTY BUTTON FOUND! HTML: ${buttonHTML}`);
    }
  }
  
  await browser.close();
})();
