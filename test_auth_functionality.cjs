const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: {width: 1920, height: 1080}
  });
  
  const page = await browser.newPage();
  await page.setViewport({width: 1920, height: 1080});
  
  console.log('🔐 TESTING AUTH FUNCTIONALITY');
  
  await page.goto('http://localhost:5001/auth');
  await page.waitForSelector('form', {timeout: 10000});
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test tab switching
  console.log('\n📑 TESTING TAB SWITCHING:');
  
  // Click Sign Up tab
  const signUpTab = await page.$('button:has-text("Sign Up")');
  if (signUpTab) {
    await signUpTab.click();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const submitButton = await page.$('button[type="submit"]');
    const submitText = await submitButton?.evaluate(el => el.textContent);
    console.log('After clicking Sign Up tab, submit button says:', submitText);
  }
  
  // Click Sign In tab
  const signInTab = await page.$('button:has-text("Sign In")');
  if (signInTab) {
    await signInTab.click();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const submitButton = await page.$('button[type="submit"]');
    const submitText = await submitButton?.evaluate(el => el.textContent);
    console.log('After clicking Sign In tab, submit button says:', submitText);
  }
  
  // Test form validation
  console.log('\n✅ TESTING FORM VALIDATION:');
  
  // Try submitting empty form
  const submitButton = await page.$('button[type="submit"]');
  await submitButton?.click();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Check for validation messages
  const validationMessages = await page.$$eval('input:invalid', inputs => 
    inputs.map(input => input.validationMessage)
  );
  console.log('Validation messages:', validationMessages);
  
  // Test invalid email
  await page.fill('input[type="email"]', 'invalid-email');
  await page.fill('input[type="password"]', 'password123');
  await submitButton?.click();
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const errorElement = await page.$('.text-destructive, .error');
  if (errorElement) {
    const errorText = await errorElement.evaluate(el => el.textContent);
    console.log('Email validation error:', errorText);
  }
  
  // Test visual consistency
  console.log('\n🎨 CHECKING VISUAL CONSISTENCY:');
  
  const buttons = await page.$$eval('button', buttons => 
    buttons.map(btn => ({
      text: btn.textContent?.trim(),
      styles: {
        borderRadius: window.getComputedStyle(btn).borderRadius,
        height: window.getComputedStyle(btn).height,
        fontSize: window.getComputedStyle(btn).fontSize
      }
    }))
  );
  
  console.log('Button styles:');
  buttons.forEach((btn, i) => {
    console.log(`  ${i+1}. "${btn.text}": ${btn.styles.height}, ${btn.styles.fontSize}, ${btn.styles.borderRadius}`);
  });
  
  await page.screenshot({path: 'auth_functionality_test.png', fullPage: true});
  console.log('\n📸 Auth functionality test screenshot saved');
  
  await browser.close();
})();
