const puppeteer = require('puppeteer');
const fs = require('fs');

async function comprehensiveAudit() {
  const browser = await puppeteer.launch({ 
    headless: false, 
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    defaultViewport: { width: 1920, height: 1080 }
  });
  
  const page = await browser.newPage();
  await page.setViewport({ width: 1920, height: 1080 });
  
  console.log('🔍 Starting comprehensive audit of 2nrtlist app on port 5002...');
  
  try {
    // Navigate to the app
    console.log('📱 Navigating to http://localhost:5002...');
    await page.goto('http://localhost:5002', { waitUntil: 'networkidle0', timeout: 30000 });
    
    // Take initial screenshot
    await page.screenshot({ path: '2nrtlist_initial_audit.png', fullPage: true });
    console.log('📸 Initial screenshot taken');
    
    // Check if app loaded properly (no build errors)
    const hasErrors = await page.evaluate(() => {
      return document.body.innerText.includes('Error') || 
             document.body.innerText.includes('expected') ||
             document.body.innerText.includes('Cannot') ||
             document.querySelector('.error') !== null;
    });
    
    if (hasErrors) {
      console.log('❌ Build/Runtime errors detected!');
      const errorText = await page.evaluate(() => document.body.innerText);
      console.log('Error details:', errorText.substring(0, 500));
    } else {
      console.log('✅ App loaded without build errors');
    }
    
    // Check if main elements are present
    const mainElements = await page.evaluate(() => {
      const header = document.querySelector('nav');
      const tabs = document.querySelectorAll('button').length;
      const logo = document.querySelector('[class*="Leaf"], .logo, [alt*="logo"]');
      
      return {
        hasHeader: !!header,
        tabCount: tabs,
        hasLogo: !!logo,
        appTitle: document.querySelector('span')?.textContent || 'Not found'
      };
    });
    
    console.log('🏗️ Main Elements Check:', mainElements);
    
    // Check all navigation tabs
    const tabs = ['discover', 'products', 'vendors', 'community', 'my-journey', 'learn'];
    
    for (const tab of tabs) {
      console.log(`\n🔍 Testing ${tab.toUpperCase()} tab...`);
      
      // Click on tab
      const tabButton = await page.$(`button:has-text("${tab}")`) || 
                       await page.$(`[data-tab="${tab}"]`) ||
                       await page.$(`button[class*="${tab}"]`);
      
      if (!tabButton) {
        // Try alternative selector approach
        const clicked = await page.evaluate((tabName) => {
          const buttons = document.querySelectorAll('button');
          for (let btn of buttons) {
            if (btn.textContent.toLowerCase().includes(tabName.toLowerCase())) {
              btn.click();
              return true;
            }
          }
          return false;
        }, tab);
        
        if (!clicked) {
          console.log(`⚠️ Could not find ${tab} tab button`);
          continue;
        }
      } else {
        await tabButton.click();
      }
      
      // Wait for content to load
      await page.waitForTimeout(1000);
      
      // Take screenshot of this tab
      await page.screenshot({ path: `2nrtlist_${tab}_tab.png`, fullPage: true });
      
      // Check for visual consistency issues
      const visualIssues = await page.evaluate(() => {
        const elements = document.querySelectorAll('*');
        let issues = [];
        
        // Check for hardcoded styles
        elements.forEach(el => {
          const style = el.getAttribute('style');
          if (style && (style.includes('color:') || style.includes('background:') || style.includes('font-size:'))) {
            issues.push(`Hardcoded style found: ${el.tagName} - ${style}`);
          }
        });
        
        // Check for inconsistent button sizes
        const buttons = document.querySelectorAll('button');
        const buttonSizes = Array.from(buttons).map(btn => {
          const rect = btn.getBoundingClientRect();
          return { width: rect.width, height: rect.height, text: btn.textContent.trim().substring(0, 20) };
        });
        
        // Check for mock data indicators
        const mockData = document.body.innerText.toLowerCase();
        if (mockData.includes('lorem ipsum') || 
            mockData.includes('placeholder') || 
            mockData.includes('mock') ||
            mockData.includes('test data') ||
            mockData.includes('coming soon')) {
          issues.push('Mock/placeholder data detected');
        }
        
        return {
          hardcodedStyles: issues.filter(i => i.includes('Hardcoded')).length,
          buttonSizes: buttonSizes,
          hasMockData: issues.some(i => i.includes('Mock')),
          totalIssues: issues.length
        };
      });
      
      console.log(`📊 Visual Issues for ${tab}:`, visualIssues);
      
      // Check if features are functional (not just mockups)
      const functionalityCheck = await page.evaluate(() => {
        // Look for interactive elements
        const inputs = document.querySelectorAll('input, select, textarea').length;
        const clickableElements = document.querySelectorAll('button, [onClick], [role="button"]').length;
        const forms = document.querySelectorAll('form').length;
        
        // Check if there are any obvious "fake" elements
        const fakeElements = document.querySelectorAll('[disabled], .disabled, [class*="mock"], [class*="fake"]').length;
        
        return {
          inputs,
          clickableElements,
          forms,
          fakeElements,
          hasRealContent: document.body.innerText.length > 100
        };
      });
      
      console.log(`⚙️ Functionality Check for ${tab}:`, functionalityCheck);
    }
    
    // Test authentication if available
    console.log('\n🔐 Testing Authentication...');
    
    // Look for sign in button
    const signInButton = await page.$('button:has-text("Sign In")') || 
                         await page.$('[class*="signin"], [class*="login"]');
    
    if (signInButton) {
      await signInButton.click();
      await page.waitForTimeout(1000);
      
      // Check if auth modal/form appeared
      const authForm = await page.$('form, [class*="modal"], [class*="auth"]');
      if (authForm) {
        console.log('✅ Auth modal/form detected');
        
        // Try to fill and test auth with provided credentials
        const emailInput = await page.$('input[type="email"], input[name="email"], input[id="email"]');
        const passwordInput = await page.$('input[type="password"], input[name="password"], input[id="password"]');
        
        if (emailInput && passwordInput) {
          // Use the React-aware method from the guide
          await page.evaluate(() => {
            const emailEl = document.querySelector('input[type="email"], input[name="email"], input[id="email"]');
            const passwordEl = document.querySelector('input[type="password"], input[name="password"], input[id="password"]');
            
            if (emailEl && passwordEl) {
              const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, 'value').set;
              
              nativeInputValueSetter.call(emailEl, '<EMAIL>');
              emailEl.dispatchEvent(new Event('input', { bubbles: true }));
              
              nativeInputValueSetter.call(passwordEl, 'J4913836j');
              passwordEl.dispatchEvent(new Event('input', { bubbles: true }));
            }
          });
          
          await page.screenshot({ path: '2nrtlist_auth_filled.png' });
          console.log('✅ Auth form filled with test credentials');
          
          // Try to submit
          const submitButton = await page.$('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")');
          if (submitButton) {
            await submitButton.click();
            await page.waitForTimeout(2000);
            
            // Check if login was successful
            const isLoggedIn = await page.evaluate(() => {
              return document.body.innerText.includes('Welcome') || 
                     document.body.innerText.includes('<EMAIL>') ||
                     document.querySelector('[class*="logout"], [class*="profile"]') !== null;
            });
            
            console.log(isLoggedIn ? '✅ Authentication successful' : '❌ Authentication failed');
            await page.screenshot({ path: '2nrtlist_auth_result.png' });
          }
        }
      }
    }
    
    // Final comprehensive screenshot
    await page.screenshot({ path: '2nrtlist_final_audit.png', fullPage: true });
    
    console.log('\n📋 AUDIT SUMMARY:');
    console.log('================');
    console.log('✅ App successfully loaded on port 5002');
    console.log('✅ All tab navigation tested');
    console.log('✅ Visual consistency checked');
    console.log('✅ Functionality assessment completed');
    console.log('✅ Authentication flow tested');
    console.log('\n📸 Screenshots saved for visual review');
    
  } catch (error) {
    console.error('❌ Audit failed:', error);
    await page.screenshot({ path: '2nrtlist_error_audit.png' });
  } finally {
    await browser.close();
  }
}

comprehensiveAudit();
