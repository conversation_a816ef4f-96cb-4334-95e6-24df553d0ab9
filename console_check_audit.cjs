const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // Capture console
  page.on('console', msg => console.log('Console:', msg.type(), msg.text()));
  page.on('pageerror', err => console.log('Page Error:', err.toString()));
  
  await page.setViewport({ width: 1200, height: 800 });
  
  console.log('Navigating to http://localhost:5001/...');
  await page.goto('http://localhost:5001/', { waitUntil: 'networkidle2' });
  
  // Get page title
  const title = await page.title();
  console.log('Page Title:', title);
  
  // Check if body has content
  const bodyText = await page.evaluate(() => document.body.innerText);
  console.log('Body text length:', bodyText.length);
  console.log('First 200 chars:', bodyText.substring(0, 200));
  
  await page.screenshot({ path: 'landing_audit.png', fullPage: true });
  console.log('Screenshot saved');
  
  await browser.close();
})();