import { test, expect } from '@playwright/test';

test.describe('Application End-to-End Test', () => {
  test('should load the landing page, log in, and navigate to dashboard', async ({ page }) => {
    // 1. Navigate to the landing page
        await page.goto('http://localhost:5006/', { timeout: 60000 });
    await page.waitForLoadState('domcontentloaded');

    // Take a screenshot of the landing page
    await page.screenshot({ path: './test-results/landing_page.png' });

    // Check for console errors on the landing page
    const landingPageConsoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        landingPageConsoleErrors.push(msg.text());
      }
    });
    expect(landingPageConsoleErrors.length).toBe(0);

    // Check for network errors on the landing page
    const landingPageNetworkErrors: string[] = [];
    page.on('requestfailed', request => {
      landingPageNetworkErrors.push(`URL: ${request.url()}, Error: ${request.failure()?.errorText}`);
    });
    expect(landingPageNetworkErrors.length).toBe(0);

    // Capture HTML snapshot of landing page
    const landingPageHtml = await page.evaluate(() => document.documentElement.outerHTML);
    console.log("Landing Page HTML:", landingPageHtml.substring(0, 500) + "..."); // Log first 500 chars

    // 2. Test Public Routes
    const testPublicRoute = async (path: string, screenshotName: string) => {
      console.log(`Navigating to public route: ${path}`);
      await page.goto(`http://localhost:5006${path}`, { timeout: 60000 });
      await page.waitForLoadState('domcontentloaded');
      await page.screenshot({ path: `./test-results/${screenshotName}.png` });

      const consoleErrors: string[] = [];
      page.on('console', msg => {
        if (msg.type() === 'error') {
          consoleErrors.push(msg.text());
        }
      });
      expect(consoleErrors.length).toBe(0);

      const networkErrors: string[] = [];
      page.on('requestfailed', request => {
        networkErrors.push(`URL: ${request.url()}, Error: ${request.failure()?.errorText}`);
      });
      expect(networkErrors.length).toBe(0);

      const htmlContent = await page.evaluate(() => document.documentElement.outerHTML);
      console.log(`${screenshotName} HTML:`, htmlContent.substring(0, 500) + "...");
    };

    await testPublicRoute('/features', 'features_page');
    await testPublicRoute('/how-it-works', 'how_it_works_page');
    await testPublicRoute('/about-us', 'about_us_page');
    await testPublicRoute('/privacy-policy', 'privacy_policy_page');
    await testPublicRoute('/terms-of-service', 'terms_of_service_page');
    await testPublicRoute('/tools', 'web_tools_index_page');
    await testPublicRoute('/tools/nrt-guide', 'nrt_guide_page');
    await testPublicRoute('/tools/smokeless-directory', 'smokeless_directory_page');
    await testPublicRoute('/tools/quit-methods', 'quit_methods_page');
    await testPublicRoute('/tools/calculators', 'calculators_page');
    await testPublicRoute('/tools/holistic-health', 'holistic_health_page');

    // 3. Login to the application
    console.log("Navigating to Auth page for login.");
    await page.goto('http://localhost:5006/auth', { timeout: 60000 });
    await page.waitForLoadState('domcontentloaded');

    // Check if already logged in (e.g., if dashboard is visible)
    const currentUrl = page.url();
    if (currentUrl.includes('/app/dashboard')) {
        console.log("Already logged in, skipping login steps.");
        await page.screenshot({ path: './test-results/dashboard_already_logged_in.png' });
    } else {
        // Fill in login credentials
        console.log("Attempting to log in...");
        await page.fill('input[id=\"email\"]', '<EMAIL>');
        await page.fill('input[id=\"password\"]', 'J4913836j');

        // Click the sign-in button
        await page.click('button[type=\"submit\"]');

        // Wait for navigation to the dashboard or a protected route
        await page.waitForURL('http://localhost:5006/app/dashboard', { timeout: 60000 });
        await page.waitForLoadState('domcontentloaded');
        await page.screenshot({ path: './test-results/dashboard_page.png' });
    }

    // Check for console errors on the dashboard
    const dashboardConsoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        dashboardConsoleErrors.push(msg.text());
      }
    });
    expect(dashboardConsoleErrors.length).toBe(0);

    // Check for network errors on the dashboard
    const dashboardNetworkErrors: string[] = [];
    page.on('requestfailed', request => {
      dashboardNetworkErrors.push(`URL: ${request.url()}, Error: ${request.failure()?.errorText}`);
    });
    expect(dashboardNetworkErrors.length).toBe(0);

    const dashboardHtml = await page.evaluate(() => document.documentElement.outerHTML);
    console.log("Dashboard Page HTML:", dashboardHtml.substring(0, 500) + "...");

    // 4. Test Protected Routes
    const testProtectedRoute = async (path: string, screenshotName: string) => {
      console.log(`Navigating to protected route: ${path}`);
      await page.goto(`http://localhost:5006${path}`, { timeout: 60000 });
      await page.waitForLoadState('domcontentloaded');
      await page.screenshot({ path: `./test-results/${screenshotName}.png` });

      const consoleErrors: string[] = [];
      page.on('console', msg => {
        if (msg.type() === 'error') {
          consoleErrors.push(msg.text());
        }
      });
      expect(consoleErrors.length).toBe(0);

      const networkErrors: string[] = [];
      page.on('requestfailed', request => {
        networkErrors.push(`URL: ${request.url()}, Error: ${request.failure()?.errorText}`);
      });
      expect(networkErrors.length).toBe(0);

      const htmlContent = await page.evaluate(() => document.documentElement.outerHTML);
      console.log(`${screenshotName} HTML:`, htmlContent.substring(0, 500) + "...");
    };

    await testProtectedRoute('/app/goals', 'goals_page');
    await testProtectedRoute('/app/log', 'log_entry_page');
    await testProtectedRoute('/app/rewards', 'rewards_page');
    await testProtectedRoute('/app/profile', 'profile_page');
    await testProtectedRoute('/app/settings', 'settings_page');
    await testProtectedRoute('/app/health-integrations', 'health_integrations_page');
    await testProtectedRoute('/app/progress', 'progress_page');
    await testProtectedRoute('/app/community', 'community_page');
    await testProtectedRoute('/app/community/create-post', 'create_post_page');
    // Note: Skipping community/post/:postId as it requires a specific post ID
    await testProtectedRoute('/app/tools/cravings', 'craving_tools_page');
    await testProtectedRoute('/app/tools/energy', 'energy_tools_page');
    await testProtectedRoute('/app/tools/focus', 'focus_tools_page');
    await testProtectedRoute('/app/tools/mood', 'mood_tools_page');
    await testProtectedRoute('/app/tools/breathing', 'breathing_tools_page');
    await testProtectedRoute('/app/tools/guided-meditation', 'guided_tools_page');
    await testProtectedRoute('/app/tools/fatigue', 'fatigue_tools_page');
    await testProtectedRoute('/app/tools/ai-coach', 'ai_coach_page');
    await testProtectedRoute('/app/progress/timeline', 'health_timeline_page');
    await testProtectedRoute('/app/learn', 'learn_page');
    await testProtectedRoute('/app/journal', 'journal_page');
    await testProtectedRoute('/app/support', 'support_page');
    await testProtectedRoute('/app/relapse-prevention', 'relapse_prevention_page');
    await testProtectedRoute('/app/log-history', 'log_history_page');

    // 5. Test CURD Operations
    // Test Create Goal
    console.log("Testing Create Goal CURD operation...");
    await page.goto('http://localhost:5006/app/goals', { timeout: 60000 });
    await page.waitForLoadState('domcontentloaded');

    // Check if a goal already exists (if 'Update Goal' button is visible)
    const updateGoalButton = page.locator('button:has-text("Update Goal")');
    const saveGoalButton = page.locator('button:has-text("Save Goal")');

    if (await updateGoalButton.isVisible()) {
      console.log("Goal already exists, attempting to update it.");
      // It's possible the input field is not immediately visible, ensure interaction
      await page.fill('input[placeholder="Enter your goal"]', 'Updated Test Goal from Playwright');
      await updateGoalButton.click();
    } else {
      console.log("No existing goal, attempting to create a new one.");
      // For simplicity, directly filling and saving for now. If this fails, then add goal type selection.
      await page.fill('input[placeholder="Enter your goal"]', 'Test Goal from Playwright');
      await saveGoalButton.click();
    }
    await page.screenshot({ path: './test-results/goal_curd_operation.png' });
    const goalCurdHtml = await page.evaluate(() => document.documentElement.outerHTML);
    console.log("Goal CURD HTML:", goalCurdHtml.substring(0, 500) + "...");

    // Test Create Log Entry (Nicotine Use)
    console.log("Testing Create Log Entry (Nicotine Use) CURD operation...");
    await page.goto('http://localhost:5006/app/log', { timeout: 60000 });
    await page.waitForLoadState('domcontentloaded');
    await page.click('text="Nicotine Use"'); // Click on Nicotine Use tab
    await page.fill('input[placeholder="Number of cigarettes"]', '5');
    await page.click('button:has-text("Add Entry")'); // Assuming an add entry button
    await page.waitForSelector('text="Log entry added successfully!"'); // Assuming a success message
    await page.screenshot({ path: './test-results/log_entry_nicotine_curd.png' });
    const logEntryHtml = await page.evaluate(() => document.documentElement.outerHTML);
    console.log("Log Entry Nicotine HTML:", logEntryHtml.substring(0, 500) + "...");
  });
}); 