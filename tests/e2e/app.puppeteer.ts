import puppeteer, { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'puppeteer';

const BASE_URL = 'http://localhost:5175';
const USER_EMAIL = '<EMAIL>';
const USER_PASSWORD = 'J4913836j';

const takeScreenshot = async (page: Page, name: string) => {
  await page.screenshot({ path: `./test-results/${name}.png` });
  console.log(`Screenshot taken: ${name}.png`);
};

const testPublicRoute = async (page: Page, path: string, screenshotName: string) => {
  console.log(`Navigating to public route: ${path}`);
  await page.goto(`${BASE_URL}${path}`, { waitUntil: 'domcontentloaded', timeout: 60000 });
  await takeScreenshot(page, screenshotName);
};

const testProtectedRoute = async (page: Page, path: string, screenshotName: string) => {
    console.log(`Navigating to protected route: ${path}`);
    await page.goto(`${BASE_URL}${path}`, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await takeScreenshot(page, screenshotName);
};


(async () => {
  const browser: Browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox', '--disable-setuid-sandbox'] });
  const page: Page = await browser.newPage();
  await page.setViewport({ width: 1280, height: 800 });

  try {
    // 1. Navigate to landing page
    console.log('Navigating to landing page...');
    await page.goto(BASE_URL, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await takeScreenshot(page, 'landing_page_puppeteer');

    // 2. Test Public Routes
    const publicRoutes = [
        { path: '/features', name: 'features_page' },
        { path: '/how-it-works', name: 'how_it_works_page' },
        { path: '/quit-methods', name: 'quit_methods_page' },
        { path: '/smokeless-directory', name: 'smokeless_directory_page' },
        { path: '/nrt-guide', name: 'nrt_guide_page' },
        { path: '/holistic-health', name: 'holistic_health_page' },
    ];
    for (const route of publicRoutes) {
        await testPublicRoute(page, route.path, route.name);
    }

    // 3. Login
    console.log('Navigating to Auth page for login...');
    await page.goto(`${BASE_URL}/auth`, { waitUntil: 'domcontentloaded', timeout: 60000 });
    
    const dashboardLink = await page.$('a[href="/app/dashboard"]');
    if (dashboardLink) {
        console.log('Already logged in, proceeding...');
        await takeScreenshot(page, 'dashboard_page_autologin_puppeteer');
    } else {
        console.log('Performing login...');
        await page.waitForSelector('input[id="email"]', { timeout: 10000 });
        await page.type('input[id="email"]', USER_EMAIL);
        await page.type('input[id="password"]', USER_PASSWORD);
        await page.click('button[type="submit"]');
        await page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 60000 });
        console.log('Login successful.');
        await takeScreenshot(page, 'dashboard_page_puppeteer');
    }

    // 4. Test Protected Routes
    const protectedRoutes = [
        { path: '/app/dashboard', name: 'dashboard_page' },
        { path: '/app/goals', name: 'goals_page' },
        { path: '/app/log', name: 'log_entry_page' },
        { path: '/app/progress', name: 'progress_page' },
        { path: '/app/rewards', name: 'rewards_page' },
        { path: '/app/community', name: 'community_page' },
        { path: '/app/settings', name: 'settings_page' },
        { path: '/app/profile', name: 'profile_page' },
        { path: '/app/tools/breathing', name: 'breathing_tools_page' },
        { path: '/app/tools/craving-crusher', name: 'craving_crusher_page' },
        { path: '/app/tools/fatigue', name: 'fatigue_tools_page' },
        { path: '/app/tools/ai-coach', name: 'ai_coach_page' },
        { path: '/app/progress/timeline', name: 'health_timeline_page' },
        { path: '/app/learn', name: 'learn_page' },
        { path: '/app/journal', name: 'journal_page' },
        { path: '/app/support', name: 'support_page' },
        { path: '/app/relapse-prevention', name: 'relapse_prevention_page' },
        { path: '/app/log-history', name: 'log_history_page' },
    ];

    for (const route of protectedRoutes) {
        await testProtectedRoute(page, route.path, route.name);
    }

    // 5. Test Goal CURD Operations
    console.log("Testing Goal CURD operations...");
    await page.goto(`${BASE_URL}/app/goals`, { waitUntil: 'networkidle0', timeout: 60000 });
    await takeScreenshot(page, 'goals_page_initial');

    // Helper function to find a button by its text
    const findButtonByText = async (text: string): Promise<ElementHandle<HTMLButtonElement> | null> => {
        const buttonHandle = await page.evaluateHandle((text) => {
            const buttons = Array.from(document.querySelectorAll('button'));
            return buttons.find(button => button.textContent?.includes(text));
        }, text);
        return buttonHandle.asElement() as ElementHandle<HTMLButtonElement> | null;
    };

    // Wait for the page to settle into either 'view' or 'create' mode
    console.log("Waiting for Goals page to load and settle...");
    try {
        await page.waitForFunction(
            () => {
                const buttons = Array.from(document.querySelectorAll('button'));
                const hasEditGoal = buttons.some(btn => btn.textContent?.includes('Edit Goal'));
                const hasSetGoal = buttons.some(btn => btn.textContent?.includes('Set My Goal'));
                return hasEditGoal || hasSetGoal;
            },
            { timeout: 30000 }
        );
        console.log("Goals page has settled.");
    } catch (e) {
        console.error("Page did not settle. Neither 'Edit Goal' nor 'Set My Goal' button found.");
        await takeScreenshot(page, 'error_screenshot_goals_settle');
        throw e;
    }

    // --- 1. Cleanup: Delete goal if it exists ---
    console.log("Checking for existing goal...");
    const editGoalButton = await findButtonByText('Edit Goal');

    if (editGoalButton) {
        console.log("Goal exists. Deleting it first.");
        const deleteTriggerButton = await findButtonByText('Delete');
        if (!deleteTriggerButton) throw new Error("Could not find Delete trigger button.");
        
        await deleteTriggerButton.click();
        await page.waitForSelector('div[role=\"alertdialog\"]', { visible: true });

        const confirmDeleteButton = await page.evaluateHandle(() => {
            const dialog = document.querySelector('div[role="alertdialog"]');
            if (!dialog) return null;
            const buttons = Array.from(dialog.querySelectorAll('button'));
            return buttons.find(button => button.textContent?.trim() === 'Delete Goal');
        });
        const confirmDelete = confirmDeleteButton.asElement() as ElementHandle<HTMLButtonElement> | null;
        if (!confirmDelete) throw new Error("Could not find final Delete button in dialog.");

        if (!confirmDelete) throw new Error("Could not find final Delete button in dialog.");

        await confirmDelete.click();
        await page.waitForSelector('li[data-sonner-toast]', { visible: true });
        console.log("Existing goal deleted successfully.");
        await takeScreenshot(page, 'goals_page_after_cleanup_delete');
    } else {
        console.log("No existing goal found. Proceeding to creation.");
    }

    // --- 2. Create Goal ---
    console.log("Creating a new goal...");
    // Select goal type and method using label text (Radix RadioGroup)
    await page.waitForFunction(
      () => Array.from(document.querySelectorAll('label')).some(l => l.textContent?.includes('Quit Nicotine Entirely')),
      { timeout: 30000 }
    );
    await page.evaluate(() => {
      const clickLabel = (txt: string) => {
        const label = Array.from(document.querySelectorAll('label')).find(l => l.textContent?.trim() === txt);
        if (label) (label as HTMLElement).click();
      };
      clickLabel('Quit Nicotine Entirely');
      clickLabel('Cold Turkey');
    });

    // Open the date picker
    const datePickerTrigger = await findButtonByText('Pick a date');
    if (!datePickerTrigger) throw new Error("Could not find date picker trigger button.");
    await datePickerTrigger.click();
    await takeScreenshot(page, 'debug_after_datepicker_click');

    // Wait for the calendar to appear and then select the date
    console.log("Waiting for calendar popover...");
    await page.waitForSelector('.rdp', { visible: true });
    console.log("Calendar popover found.");

    // --- DEBUG: Log style properties of the 'today' button ---
    const buttonState = await page.evaluate(() => {
        const button = document.querySelector('.rdp-day_today');
        if (!button) {
            return { found: false, message: "Element .rdp-day_today not found in DOM" };
        }
        const style = window.getComputedStyle(button);
        const rect = button.getBoundingClientRect();
        return {
            found: true,
            html: button.outerHTML,
            display: style.display,
            visibility: style.visibility,
            opacity: style.opacity,
            width: style.width,
            height: style.height,
            top: rect.top,
            left: rect.left,
        };
    });
    console.log("DEBUG: 'Today' button state:", buttonState);
    // --- END DEBUG ---

    console.log("Waiting for today's date to be clickable.");
    await page.waitForSelector('.rdp-day_today', { visible: true });
    console.log("Today's date is visible. Clicking it now.");
    await page.click('.rdp-day_today');

    await page.type('textarea[placeholder="For my health, for my family..."]', 'Initial test goal from Puppeteer.');

    const setGoalButton = await findButtonByText('Set My Goal');
    if (!setGoalButton) throw new Error("Could not find 'Set My Goal' button.");
    await setGoalButton.click();
    await page.waitForSelector('li[data-sonner-toast]', { visible: true });
    console.log('Goal created successfully.');
    await takeScreenshot(page, 'goals_page_after_create');

    // --- 3. Update Goal ---
    console.log("Updating the goal...");
    const editButtonForUpdate = await findButtonByText('Edit Goal');
    if (!editButtonForUpdate) throw new Error("Could not find 'Edit Goal' button to start update.");
    await editButtonForUpdate.click();

    await page.waitForSelector('textarea[placeholder="For my health, for my family..."]', { visible: true });
    // Clear the textarea before typing
    await page.evaluate(() => {
        const textarea = document.querySelector('textarea[placeholder="For my health, for my family..."]') as HTMLTextAreaElement | null;
        if (textarea) textarea.value = '';
    });
    await page.type('textarea[placeholder="For my health, for my family..."]', 'Updated test goal from Puppeteer.');

    const saveChangesButton = await findButtonByText('Save Changes');
    if (!saveChangesButton) throw new Error("Could not find 'Save Changes' button.");
    await saveChangesButton.click();
    await page.waitForSelector('li[data-sonner-toast]', { visible: true });
    console.log("Goal updated successfully.");
    await takeScreenshot(page, 'goals_page_after_update');

    // --- 4. Final Delete ---
    console.log("Deleting the created goal for cleanup...");
    const finalDeleteTrigger = await findButtonByText('Delete');
    if (!finalDeleteTrigger) throw new Error("Could not find final Delete trigger button.");

    await finalDeleteTrigger.click();
    await page.waitForSelector('div[role=\"alertdialog\"]', { visible: true });

    const finalConfirmDeleteButton = await page.evaluateHandle(() => {
        const dialog = document.querySelector('div[role="alertdialog"]');
        if (!dialog) return null;
        const buttons = Array.from(dialog.querySelectorAll('button'));
        return buttons.find(button => button.textContent?.trim() === 'Delete Goal');
    });
    const finalConfirmDelete = finalConfirmDeleteButton.asElement() as ElementHandle<HTMLButtonElement> | null;
    if (!finalConfirmDelete) throw new Error("Could not find final confirmation Delete button.");

    await finalConfirmDelete.click();
    await page.waitForSelector('li[data-sonner-toast]', { visible: true });
    console.log("Goal deleted successfully for cleanup.");
    await takeScreenshot(page, 'goals_page_after_final_delete');

    console.log('All tests passed!');
  } catch (error) {
    console.error('Test failed:', error);
    await takeScreenshot(page, 'error_screenshot_puppeteer');
    process.exit(1);
  } finally {
    await browser.close();
  }
})();
