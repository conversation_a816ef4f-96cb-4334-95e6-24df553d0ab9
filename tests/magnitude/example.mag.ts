import { test } from 'magnitude-test';

// Learn more about building test case:
// https://docs.magnitude.run/core-concepts/building-test-cases

const email = process.env.MAGNITUDE_TEST_EMAIL || '<EMAIL>';
const password = process.env.MAGNITUDE_TEST_PASSWORD || 'J4913836j';

test('can log in', { url: 'http://localhost:5005/auth/login' })
    .step('Verify on login page') // Add initial check
        .check('The Sign In button is visible') // Check for a reliable element
    .step(`Enter email address ${email}`)
        .data({ email })
        .check(`Email field value should be ${email}`)
    .step(`Enter password`)
        .data({ password })
        .check('Password field is filled') // Password value might be masked
    .step('Click the Sign In button') // Use exact button text if known
        .check('User is redirected to the dashboard') // Assuming /app/dashboard
        .check('Dashboard shows a welcome message')
