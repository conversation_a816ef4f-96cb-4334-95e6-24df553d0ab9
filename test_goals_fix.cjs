const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // Login
  await page.goto('http://localhost:5001/auth');
  await page.type('input[id="email"]', '<EMAIL>');
  await page.type('input[id="password"]', 'J4913836j');
  await page.click('button[type="submit"]');
  await page.waitForNavigation();
  
  // Test Goals page
  console.log('Testing Goals page after fix...');
  await page.goto('http://localhost:5001/app/goals');
  
  // Wait a bit for any loading
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  const bodyText = await page.evaluate(() => document.body.innerText);
  console.log('Goals content length:', bodyText.length);
  console.log('Contains "Loading":', bodyText.includes('Loading'));
  console.log('Contains "Set Your Goal":', bodyText.includes('Set Your Goal'));
  console.log('First 300 chars:');
  console.log(bodyText.substring(0, 300));
  
  await page.screenshot({ path: 'goals_fixed.png', fullPage: true });
  
  await browser.close();
})();
