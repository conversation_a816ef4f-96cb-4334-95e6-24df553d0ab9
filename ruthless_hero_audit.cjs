const puppeteer = require('puppeteer');

async function ruthlessHeroAudit() {
    const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Set PC screen size as mandated
    await page.setViewport({ width: 1920, height: 1080 });
    
    try {
        console.log('🔍 RUTHLESS HERO SECTION VISUAL AUDIT BEGINNING...');
        
        // Navigate to the app on port 5001
        await page.goto('http://localhost:5001', { 
            waitUntil: 'networkidle2', 
            timeout: 15000 
        });
        
        // Take full page screenshot for ruthless pixel analysis
        await page.screenshot({ 
            path: 'hero_section_ruthless_audit.png', 
            fullPage: true 
        });
        
        console.log('📸 HERO SECTION AUDIT SCREENSHOT CAPTURED: hero_section_ruthless_audit.png');
        
        // Take hero section specific screenshot
        const heroSection = await page.$('section');
        if (heroSection) {
            await heroSection.screenshot({ 
                path: 'hero_section_focused.png' 
            });
            console.log('📸 FOCUSED HERO SECTION CAPTURED: hero_section_focused.png');
        }
        
        console.log('✅ RUTHLESS HERO AUDIT COMPLETE - READY FOR FLAW IDENTIFICATION');
        
    } catch (error) {
        console.error('❌ HERO AUDIT FAILED:', error.message);
    } finally {
        await browser.close();
    }
}

ruthlessHeroAudit();