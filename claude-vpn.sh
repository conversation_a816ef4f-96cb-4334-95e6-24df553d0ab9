#!/bin/bash

# Claude Code VPN Helper Script
# This script configures Claude Code to work with your Geph VPN proxy

echo "🔧 Configuring Claude Code for VPN usage..."

# Test if Geph proxy is running
if ! curl -x http://127.0.0.1:9909 -s -I https://api.anthropic.com/v1/messages --connect-timeout 3 > /dev/null 2>&1; then
    echo "❌ Geph VPN proxy not running on port 9909"
    echo "💡 Please make sure your VPN (Geph) is running and try again"
    exit 1
fi

echo "✅ Geph VPN proxy detected and working"

# Create Claude Code settings directory
mkdir -p ~/.claude

# Configure Claude Code settings
cat > ~/.claude/settings.json << 'EOF'
{
  "env": {
    "HTTPS_PROXY": "http://127.0.0.1:9909",
    "HTTP_PROXY": "http://127.0.0.1:9909",
    "NODE_TLS_REJECT_UNAUTHORIZED": "0"
  }
}
EOF

# Set environment variables for current session
export HTTPS_PROXY=http://127.0.0.1:9909
export HTTP_PROXY=http://127.0.0.1:9909
export NODE_TLS_REJECT_UNAUTHORIZED=0

echo "✅ Claude Code proxy settings configured"
echo "🚀 You can now run: claude"
echo ""
echo "📝 To make this permanent, add these lines to your ~/.zshrc:"
echo "export HTTPS_PROXY=http://127.0.0.1:9909"
echo "export HTTP_PROXY=http://127.0.0.1:9909"
echo "export NODE_TLS_REJECT_UNAUTHORIZED=0"
echo ""
echo "🔄 Or run this script before using Claude Code: ./claude-vpn.sh" 