# Code Check List

**NOTICE: DO NOT FUCKING RECREATE THIS FILE. IT'S TIME TO TAKE ACTION.**

This list outlines the pages, features, components, services, and hooks to be checked for functionality, completeness, user-friendliness, robust Supabase connections (using cloud project ID yekarqanirdkdckimpna, NOT MCP for CRUD), and overall code quality (simple, elegant, maintainable, robust).

Each item will be marked as [COMPLETE] once reviewed and fixed/enhanced.

1.  src/App.tsx [COMPLETE]
2.  src/main.tsx [COMPLETE]
3.  src/components/app/AppHeader.tsx [COMPLETE]
4.  src/components/app/MobileNav.tsx [COMPLETE]
5.  src/components/app/Sidebar.tsx [COMPLETE]
6.  src/components/app/SplashScreen.tsx [COMPLETE]
7.  src/components/app/StatsCard.tsx [COMPLETE]
8.  src/components/auth/AuthForm.tsx [COMPLETE]
9.  src/components/auth/AuthGuard.tsx [COMPLETE]
10. src/components/common/AnimatedNumber.tsx [COMPLETE]
11. src/components/common/BreathingLogo.tsx [COMPLETE]
12. src/components/common/ErrorBoundary.tsx [COMPLETE]
13. src/components/common/ErrorDisplay.tsx [COMPLETE]
14. src/components/common/LoadingSpinner.tsx [COMPLETE]
15. src/components/common/NoGoalSet.tsx [COMPLETE]
16. src/components/common/OfflineIndicator.tsx [COMPLETE]
17. src/components/common/PageBreadcrumb.tsx [COMPLETE]
18. src/components/common/SEOHead.tsx [COMPLETE]
19. src/components/common/SystemNotification.tsx [COMPLETE]
20. src/components/goals/GoalTypeSelector.tsx [COMPLETE]
21. src/components/goals/MethodSelector.tsx [COMPLETE]
22. src/components/goals/MotivationInput.tsx [COMPLETE]
23. src/components/goals/ProductSelector.tsx [COMPLETE]
24. src/components/home/<USER>
25. src/components/home/<USER>
26. src/components/home/<USER>
27. src/components/home/<USER>
28. src/components/home/<USER>
29. src/components/home/<USER>
30. src/components/home/<USER>
31. src/components/home/<USER>
32. src/components/home/<USER>
33. src/components/layout/AppLayout.tsx [COMPLETE]
34. src/components/layout/AuthButtons.tsx [COMPLETE]
35. src/components/layout/Footer.tsx [COMPLETE]
36. src/components/layout/MobileMenu.tsx [COMPLETE]
37. src/components/layout/Navbar.tsx [COMPLETE]
38. src/components/layout/NavbarBrand.tsx [COMPLETE]
39. src/components/layout/PublicLayout.tsx [COMPLETE]
40. src/components/layout/UserMenu.tsx [COMPLETE]
41. src/components/layout/WebToolsDropdown.tsx [COMPLETE]
42. src/components/layout/WebToolsLayout.tsx [COMPLETE]
43. src/components/log/CravingsTab.tsx [COMPLETE]
44. src/components/log/JournalTab.tsx [COMPLETE]
45. src/components/log/NicotineUseTab.tsx [COMPLETE]
46. src/components/log/WellnessTab.tsx [COMPLETE]
47. src/components/mobile/EnhancedMobileStepTracker.tsx [COMPLETE] (Requires backend/native integration for data persistence)
48. src/components/mobile/MobileAppNav.tsx [COMPLETE]
49. src/components/mobile/MobileStepTracker.tsx [COMPLETE] (Requires backend/native integration for data persistence)
50. src/components/settings/AccountDeletion.tsx [COMPLETE]
51. src/components/settings/NotificationPreferences.tsx [COMPLETE] (Pending review of userPreferencesService)
52. src/components/settings/PasswordSettings.tsx [COMPLETE]
53. src/components/ui/form.tsx [COMPLETE]
54. src/contexts/AuthContext.tsx [COMPLETE] (Pending review of pushNotificationService)
55. src/hooks/use-mobile.ts [COMPLETE]
56. src/hooks/use-toast.ts [COMPLETE]
57. src/hooks/useApiErrorHandler.ts [COMPLETE] (Pending review of useOfflineSupport and useHaptics)
58. src/hooks/useFocusTrap.ts [COMPLETE]
59. src/hooks/useHaptics.ts [COMPLETE]
60. src/hooks/useOfflineSupport.ts [COMPLETE] (Pending review of logService and journal log type handling)
61. src/hooks/useStepTracking.ts [COMPLETE] (Pending review of rewardsService and useOfflineSupport issues)
62. src/hooks/useThemePreference.ts [COMPLETE] (Pending review of userPreferencesService and potential enhancement for unauthenticated persistence)
63. src/integrations/supabase/ [COMPLETE] (Contains outdated types, core client and types are in src/lib/)
64. src/lib/database.types.ts [COMPLETE]
65. src/lib/healthUtils.ts [COMPLETE]
66. src/lib/progressUtils.ts [COMPLETE] (Potential issue with chart data sorting across years and mismatch in time_per_unit for calculateLifeRegained)
67. src/lib/savingsUtils.ts [COMPLETE] (Potential null return in calculateSavingsPeriods on insufficient data)
68. src/lib/supabase.ts [COMPLETE]
69. src/lib/utils.ts [COMPLETE]
70. src/lib/validation.ts [COMPLETE] (Uses dynamic goalSchema based on context)
71. src/lib/variants.ts [COMPLETE] (Potentially duplicate 'mint' and 'fresh' button variants)
72. src/pages/AboutUsPage.tsx [COMPLETE]
73. src/pages/AuthPage.tsx [COMPLETE] (Core Supabase auth logic is in AuthContext and AuthForm)
74. src/pages/Features.tsx [COMPLETE] (Missing PublicLayout wrapper, includes TODOs for SEO, accessibility, performance)
75. src/pages/HowItWorks.tsx [COMPLETE] (Missing PublicLayout wrapper)
76. src/pages/Index.tsx [COMPLETE] (Simple redirect component to '/')
77. src/pages/LandingPage.tsx [COMPLETE] (Missing PublicLayout wrapper, includes TODOs for accessibility, performance)
78. src/pages/NotFound.tsx [COMPLETE] (Could potentially use PublicLayout for consistency)
79. src/pages/NotFoundPage.tsx [COMPLETE] (Preferred 404 page, uses PublicLayout and context-aware navigation. src/pages/NotFound.tsx should likely be removed.)
80. src/pages/PrivacyPolicyPage.tsx [COMPLETE] (Content is a basic placeholder and needs to be replaced with a comprehensive, legally compliant policy.)
81. src/pages/TermsOfServicePage.tsx [COMPLETE] (Content is a basic placeholder and needs to be replaced with a comprehensive, legally compliant terms of service.)
82. src/services/communityService.ts [COMPLETE] (Incomplete 'isPrivate' feature for posts, placeholder 'shareCommunityPost', need to verify 'group_messages' table existence, refine error handling.)
83. src/services/dashboardService.ts [COMPLETE] (Orchestrates data fetching. Needs refined error handling, enhanced date validation, potential externalization of AI insight logic, review of hardcoded values.)
84. src/services/gamificationService.ts [COMPLETE] (Incomplete badge/achievement features, need to enhance leaderboard data with profile info, refine error handling, hardcoded points_per_level in RPC.)
85. src/services/goalService.ts [COMPLETE] (Handles goal CRUD. Needs refined error logging, potential reduction of type casting, consider adding basic input validation.)
86. src/services/localResourcesService.ts [COMPLETE] (Fetches nearby facilities via RPC. Needs refined error logging, relies on correct Supabase RPC/database setup.)
87. src/services/logService.ts [COMPLETE] (Comprehensive logging. Needs refined error logging, robust date handling, consider decoupling points earning, configurable craving threshold, verify offline support interaction.)
88. src/services/productService.ts [COMPLETE] (Handles product/review CRUD, affiliate tracking. Needs refined error logging, address type casting for joins, implement backend filtering/pagination, complete review moderation, verify affiliate link tracking, re-implement vendor filtering if needed.)
89. src/services/profileService.ts [COMPLETE] (Handles profile CRUD, push token saving. Needs refined error handling, derive types from database.types.ts, verify push_token column, add auth check in update/save functions.)
90. src/services/progressService.ts [COMPLETE] (Fetches raw progress data. Needs refined error logging, ensure consistent date/timestamp formats, verify step count table, progress calculation logic is elsewhere (review progressUtils.ts).)
91. src/services/pushNotificationService.ts [COMPLETE] (Saves push tokens, invokes Edge Function to send notifications. Needs refined error logging, verify push_tokens table schema (JSON, platform), implement unsubscribe, make platform dynamic, ensure Edge Function security.)
92. src/services/quoteService.ts [COMPLETE] (Fetches random quotes via RPC with fallback. Needs refined error logging, verify 'get_random_quote' RPC function/typing, hardcoded fallback quotes.)
93. src/services/relapseService.ts [COMPLETE] (Handles relapse CRUD. Needs refined error logging, ensure input data validity, consider input validation, relapse impact logic handled elsewhere.)
94. src/services/rewardsService.ts [COMPLETE] (Handles rewards, points, history, step tracking. Needs refined error logging, verify 'activity_points_log' table/schema, review point calculation logic, verify reward claiming/point deduction mechanism, remove redundant RewardType.)
95. src/services/userPreferencesService.ts [COMPLETE] (Handles user preferences CRUD. Needs refined error logging, derive UserPreferences type from database.types.ts, verify defaults/schema, potential update optimization, consider removing redundant saveUserPreferences.)
96. supabase/config.toml [COMPLETE] (Configured with correct cloud project ID 'yekarqanirdkdckimpna', no conflicting local Supabase settings found.)
97. supabase/functions/ [COMPLETE] (Directory checked, individual functions within will be reviewed next.)
98. supabase/functions/delete-user/
99. supabase/functions/send-push-notification/
100. supabase/migrations/
101. supabase/migrations/20250428030032_create_push_tokens_table.sql
102. supabase/migrations/20250428041800_create_relapses_table.sql
103. supabase/migrations/create_private_messaging_tables.sql
104. supabase/sql/
105. tests/
106. tests/magnitude/
107. tests/magnitude/example.mag.ts
108. tests/magnitude/magnitude.config.ts
