const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: {width: 1920, height: 1080}
  });
  
  const page = await browser.newPage();
  await page.setViewport({width: 1920, height: 1080});
  
  console.log('🔐 TESTING AUTH FUNCTIONALITY (FIXED)');
  
  await page.goto('http://localhost:5001/auth');
  await page.waitForSelector('form', {timeout: 10000});
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Get all buttons and identify them
  const buttons = await page.$$eval('button', buttons => 
    buttons.map((btn, i) => ({
      index: i,
      text: btn.textContent?.trim(),
      type: btn.type,
      className: btn.className
    }))
  );
  
  console.log('\n🔘 ALL BUTTONS IDENTIFIED:');
  buttons.forEach(btn => {
    console.log(`  ${btn.index + 1}. "${btn.text}" (type: ${btn.type})`);
  });
  
  // Test tab switching functionality
  console.log('\n📑 TESTING TAB SWITCHING:');
  
  // Find and click the Sign Up button (not submit)
  const signUpTabIndex = buttons.findIndex(btn => 
    btn.text === 'Sign Up' && btn.type !== 'submit'
  );
  
  if (signUpTabIndex !== -1) {
    const buttonElements = await page.$$('button');
    await buttonElements[signUpTabIndex].click();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check submit button text after switching
    const updatedButtons = await page.$$eval('button', buttons => 
      buttons.map(btn => ({
        text: btn.textContent?.trim(),
        type: btn.type
      }))
    );
    
    const submitButton = updatedButtons.find(btn => btn.type === 'submit');
    console.log('After clicking Sign Up tab, submit button says:', submitButton?.text);
  }
  
  // Test visual consistency - check button heights, border radius, fonts
  console.log('\n🎨 VISUAL CONSISTENCY CHECK:');
  
  const buttonStyles = await page.$$eval('button', buttons => 
    buttons.map((btn, i) => ({
      index: i + 1,
      text: btn.textContent?.trim(),
      height: window.getComputedStyle(btn).height,
      borderRadius: window.getComputedStyle(btn).borderRadius,
      fontSize: window.getComputedStyle(btn).fontSize,
      padding: window.getComputedStyle(btn).padding,
      backgroundColor: window.getComputedStyle(btn).backgroundColor
    }))
  );
  
  buttonStyles.forEach(btn => {
    console.log(`Button ${btn.index} "${btn.text}": ${btn.height} | ${btn.borderRadius} | ${btn.fontSize}`);
  });
  
  // Check for any styling inconsistencies
  const heights = [...new Set(buttonStyles.map(btn => btn.height))];
  const borderRadii = [...new Set(buttonStyles.map(btn => btn.borderRadius))];
  const fontSizes = [...new Set(buttonStyles.map(btn => btn.fontSize))];
  
  console.log('\n📊 CONSISTENCY ANALYSIS:');
  console.log(`Unique heights: ${heights.length} (${heights.join(', ')})`);
  console.log(`Unique border radius: ${borderRadii.length} (${borderRadii.join(', ')})`);
  console.log(`Unique font sizes: ${fontSizes.length} (${fontSizes.join(', ')})`);
  
  if (heights.length > 2) {
    console.log('❌ TOO MANY DIFFERENT BUTTON HEIGHTS - INCONSISTENCY!');
  }
  if (borderRadii.length > 2) {
    console.log('❌ TOO MANY DIFFERENT BORDER RADIUS VALUES - INCONSISTENCY!');
  }
  
  await page.screenshot({path: 'auth_consistency_check.png', fullPage: true});
  console.log('\n📸 Auth consistency check screenshot saved');
  
  await browser.close();
})();
