const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // Login
  await page.goto('http://localhost:5001/auth');
  await page.type('input[id="email"]', '<EMAIL>');
  await page.type('input[id="password"]', 'J4913836j');
  await page.click('button[type="submit"]');
  await page.waitForNavigation();
  
  console.log('🔍 INVESTIGATING GOALS PAGE REVERSION...\n');
  
  await page.goto('http://localhost:5001/app/goals');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  const bodyText = await page.evaluate(() => document.body.innerText);
  console.log('Goals content length:', bodyText.length);
  console.log('Full content:');
  console.log(bodyText);
  
  await browser.close();
})();
