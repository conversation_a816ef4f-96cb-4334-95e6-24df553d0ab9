# ULTRA PERFECTIONIST AUDIT - <PERSON>EVE JOBS STANDARD
## "If you haven't found 5-20 flaws per page, you're not looking hard enough"

### SACRED COMMANDMENTS
1. **FIND 5-20 FLAWS PER PAGE** - This is the SUCCESS METRIC
2. **CLICK EVERY BUTTON** - Test every single interactive element
3. **SUBMIT EVERY FORM** - Verify all CRUD operations work
4. **CHECK EVERY CONSOLE ERROR** - Zero tolerance for any errors
5. **PIXEL-LEVEL VISUAL INSPECTION** - <PERSON> would fire me for any imperfection
6. **ZERO MOCK DATA** - All data must be real from database
7. **SCREENSHOT BEFORE/AFTER EVERY EDIT** - Visual proof required

### TEST CREDENTIALS
- Email: <EMAIL>
- Password: J4913836j

### STARTING SYSTEMATIC ULTRA-PERFECTIONIST AUDIT NOW!
