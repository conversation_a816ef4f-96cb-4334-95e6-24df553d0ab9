
# Mission Fresh - Project Transition Document

## Project Overview

Mission Fresh is a holistic smoking cessation application that helps users quit or reduce their nicotine consumption through multiple approaches. Unlike traditional quit smoking apps that focus solely on cold turkey quitting, Mission Fresh supports various reduction methods and provides comprehensive tools for managing the physical and mental challenges associated with nicotine withdrawal.

## Tech Stack

### Frontend
- **Framework**: React with TypeScript
- **Build Tool**: Vite
- **UI Components**: Shadcn UI (built on Radix UI)
- **Styling**: Tailwind CSS
- **Routing**: React Router v6
- **State Management**: React Context API, React Query
- **Charts/Visualizations**: Recharts
- **Mobile Framework**: Capacitor for native mobile builds

### Backend
- **Platform**: Supabase (serverless)
- **Database**: PostgreSQL (hosted by Supabase)
- **Authentication**: Supabase Auth
- **API**: Supabase REST API
- **Storage**: Supabase Storage
- **Functions**: Supabase Edge Functions (serverless)

## Project Structure

### Core Files
- `src/App.tsx` - Main application component with routing
- `src/main.tsx` - Application entry point
- `src/components` - Reusable UI components
- `src/contexts` - React context providers
- `src/hooks` - Custom React hooks
- `src/lib` - Utility functions and helpers
- `src/pages` - Page components
- `src/services` - API service functions
- `src/integrations` - Integration with external services (Supabase)

### Key Components

#### Layout Components
- `src/components/layout/Navbar.tsx` - Top navigation bar
- `src/components/layout/Footer.tsx` - Site footer
- `src/components/layout/WebToolsLayout.tsx` - Layout for public tools
- `src/components/layout/AppLayout.tsx` - Layout for authenticated app

#### App-specific Components
- `src/components/app/Sidebar.tsx` - App sidebar navigation
- `src/components/app/MobileNav.tsx` - Mobile navigation bar
- `src/components/app/SplashScreen.tsx` - Mobile app splash screen
- `src/components/app/*` - Various app-specific components

#### Auth Components
- `src/components/auth/AuthForm.tsx` - Authentication form
- `src/components/auth/AuthGuard.tsx` - Protected route wrapper

#### Home/Landing Page Components
- `src/components/home/<USER>
- `src/components/home/<USER>
- `src/components/home/<USER>
- `src/components/home/<USER>

#### Tools Components
- `src/components/tools/ExerciseModal.tsx` - Modal for tool exercises
- `src/components/tools/QuickToolCard.tsx` - Card for quick tools
- `src/components/tools/ToolExerciseCard.tsx` - Card for tool exercises

## Page Structure

### Public Pages
- `/` - Landing page
- `/auth` - Authentication page (login/signup)
- `/tools/*` - Public tools pages:
  - `/tools/nrt-guide` - NRT Guide
  - `/tools/smokeless-directory` - Smokeless products directory
  - `/tools/quit-methods` - Quit methods guide
  - `/tools/calculators` - Calculators
  - `/tools/holistic-health` - Holistic health guide

### Application Pages (Authenticated)
- `/app/dashboard` - User dashboard
- `/app/log` - Daily log entry
- `/app/goals` - Goals setting and tracking
- `/app/progress` - Progress visualization
- `/app/rewards` - Step rewards
- `/app/settings` - User settings

#### Tools Pages (Authenticated)
- `/app/tools/cravings` - Craving management tools
- `/app/tools/energy` - Energy enhancing tools
- `/app/tools/mood` - Mood improvement tools
- `/app/tools/focus` - Focus improvement tools

## Database Structure (Supabase)

### Primary Tables

#### profiles
- `id` (UUID, PK) - Linked to auth.users
- `first_name` (text)
- `last_name` (text) 
- `email` (text)
- `avatar_url` (text)
- `role` (text)
- `created_at` (timestamp)
- `updated_at` (timestamp)

#### nicotine_logs
- `id` (UUID, PK)
- `user_id` (UUID, FK to auth.users)
- `date` (date)
- `used_nicotine` (boolean)
- `product_type` (text)
- `quantity` (integer)
- `mood` (integer) - Scale 1-5
- `energy` (integer) - Scale 1-5
- `focus` (integer) - Scale 1-5
- `sleep_hours` (numeric)
- `sleep_quality` (integer) - Scale 1-5
- `craving_intensity` (integer) - Scale 1-5
- `craving_trigger` (text)
- `journal` (text)
- `created_at` (timestamp)

#### user_goals
- `id` (UUID, PK)
- `user_id` (UUID, FK to auth.users)
- `goal_type` (text) - 'afresh' (quit) or 'fresher' (reduce)
- `method` (text) - 'cold-turkey', 'gradual-reduction', 'tapering', 'nrt', 'harm-reduction'
- `product_type` (text)
- `quit_date` (date)
- `reduction_percent` (integer)
- `timeline_days` (integer)
- `motivation` (text)
- `created_at` (timestamp)
- `updated_at` (timestamp)

#### step_rewards
- `id` (UUID, PK)
- `user_id` (UUID, FK to auth.users)
- `date` (date)
- `steps` (integer)
- `points_earned` (integer)
- `created_at` (timestamp)

#### rewards
- `id` (UUID, PK)
- `name` (text)
- `description` (text)
- `points_required` (integer)
- `active` (boolean)
- `created_at` (timestamp)

#### claimed_rewards
- `id` (UUID, PK)
- `user_id` (UUID, FK to auth.users)
- `reward_id` (UUID, FK to rewards)
- `claimed_at` (timestamp)
- `status` (text) - 'pending' or 'fulfilled'

## Authentication Flow

The app uses Supabase Auth for authentication:

1. User navigates to `/auth` page
2. User signs up or logs in using email/password
3. On successful authentication, user is redirected to `/app/dashboard`
4. Protected routes are wrapped with `AuthGuard` component
5. Session persistence is handled by Supabase client and `AuthContext`

## Key Features & Functionality

### 1. Goal Setting
- Users can set quit or reduction goals
- Multiple quitting methods supported
- Timeline and tracking implementation

### 2. Daily Logging
- Track nicotine usage
- Monitor mood, energy, focus, sleep
- Journal entries
- Craving intensity and triggers

### 3. Progress Tracking
- Visualize quit progress
- Track streaks and milestones
- View holistic metrics (mood, energy, etc.)

### 4. Step Rewards
- Mobile step tracking
- Points system for rewards
- Reward redemption

### 5. Support Tools
- **Craving Tools**: Exercises to manage cravings
- **Energy Tools**: Activities to boost energy
- **Mood Tools**: Techniques to improve mood
- **Focus Tools**: Methods to enhance focus

### 6. Public Resources
- **NRT Guide**: Information on nicotine replacement therapy
- **Smokeless Directory**: Comprehensive directory of smokeless nicotine alternatives
- **Quit Methods**: Information on various quitting approaches
- **Calculators**: Health and financial calculators
- **Holistic Health**: Guide to overall wellbeing while quitting

## Mobile App (Capacitor)

The mobile app is built with Capacitor to provide native functionality:

- Step tracking
- Push notifications for reminders
- Offline functionality
- Native UI elements

Mobile configuration is in `capacitor.config.ts`

## API Integration

### Supabase
- API client setup in `src/lib/supabase.ts` and `src/integrations/supabase/client.ts`
- Authentication handled by `AuthContext`
- Data services in `src/services/*`

## Best Practices for Continuing Development

### State Management
- Use React Context for global state (auth, theme)
- Use React Query for data fetching and caching
- Use local state for component-specific state

### Component Structure
- Keep components small and focused
- Extract reusable logic to custom hooks
- Use TypeScript for type safety

### Styling
- Use Tailwind CSS utilities
- Use shadcn/ui components
- Use responsive design patterns with mobile-first approach

### Data Fetching
- Use React Query for data fetching and management
- Create service functions for API calls
- Handle loading and error states

### Performance
- Use React.memo for expensive renders
- Use useMemo and useCallback for optimizations
- Implement pagination and virtualization for large lists

## Deployment

The app is deployed on Lovable's infrastructure. The deployment process is:

1. Code is committed to the repository
2. Automatic builds are triggered
3. Frontend is deployed as a static site
4. Supabase Edge Functions are deployed automatically

## Project Philosophy

Mission Fresh differentiates itself through:

1. **Holistic Approach**: Addressing not just nicotine cravings but also energy, mood, focus, and overall wellbeing
2. **Non-Judgmental Support**: Supporting all paths to reduction, not just cold turkey
3. **Comprehensive Tools**: Providing the most complete set of resources for quitting or reducing nicotine
4. **Fresh Branding**: Positive messaging around being "fresh" rather than stigmatizing smoking
5. **Multi-Platform**: Supporting web and mobile users equally
6. **Complete Resource**: Being the definitive guide for smokeless alternatives while being transparent about health considerations

## Roadmap & Future Enhancements

1. AI-powered personalized recommendations
2. Social community features
3. Advanced analytics and insights
4. Enhanced mobile health integrations
5. Expanded smokeless directory with vendor integration
6. International localization
7. Subscription tiers and premium features

---

This document provides a comprehensive overview of the Mission Fresh project structure, architecture, and functionality. Anyone continuing development should review the codebase in conjunction with this document to understand the project's organization and approach.
