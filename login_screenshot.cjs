const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  await page.setViewport({ width: 1200, height: 800 });
  
  // Login to the app
  await page.goto('http://localhost:5001/auth', { waitUntil: 'domcontentloaded', timeout: 60000 });
  
  // Check if already logged in
  const dashboardLink = await page.$('a[href="/app/dashboard"]');
  if (dashboardLink) {
    console.log('Already logged in');
    await page.goto('http://localhost:5001/app/dashboard', { waitUntil: 'domcontentloaded', timeout: 60000 });
  } else {
    console.log('Logging in...');
    await page.waitForSelector('input[id="email"]', { timeout: 10000 });
    await page.type('input[id="email"]', '<EMAIL>');
    await page.type('input[id="password"]', 'J4913836j');
    await page.click('button[type="submit"]');
    await page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 60000 });
  }
  
  await page.screenshot({ path: 'dashboard_current.png' });
  console.log('Dashboard screenshot taken');
  await browser.close();
})();
