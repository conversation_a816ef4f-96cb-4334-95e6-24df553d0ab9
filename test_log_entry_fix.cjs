const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // Login
  await page.goto('http://localhost:5001/auth');
  await page.type('input[id="email"]', '<EMAIL>');
  await page.type('input[id="password"]', 'J4913836j');
  await page.click('button[type="submit"]');
  await page.waitForNavigation();
  
  // Test Log Entry page
  console.log('Testing Log Entry page after fix...');
  await page.goto('http://localhost:5001/app/log');
  
  // Wait for loading
  await new Promise(resolve => setTimeout(resolve, 4000));
  
  const bodyText = await page.evaluate(() => document.body.innerText);
  console.log('Log Entry content length:', bodyText.length);
  console.log('Contains "Wellness":', bodyText.includes('Wellness'));
  console.log('Contains "Save Entry":', bodyText.includes('Save Entry'));
  console.log('Contains tabs:', bodyText.includes('Nicotine') && bodyText.includes('Cravings'));
  console.log('Loading indicator present:', bodyText.includes('Loading') || bodyText.includes('loading'));
  console.log('First 400 chars:');
  console.log(bodyText.substring(0, 400));
  
  await page.screenshot({ path: 'log_entry_fixed.png', fullPage: true });
  
  await browser.close();
})();
