const puppeteer = require('puppeteer');

async function testAuth() {
  let browser;
  try {
    console.log('🚀 Starting 2nrtlist authentication test...');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Navigate to the app
    console.log('📱 Navigating to http://localhost:5002...');
    await page.goto('http://localhost:5002', { waitUntil: 'networkidle0', timeout: 30000 });
    
    // Take initial screenshot
    await page.screenshot({ path: '2nrtlist_auth_test_initial.png', fullPage: true });
    console.log('📸 Initial screenshot taken');
    
    // Check if app loaded properly (no build errors)
    const hasErrors = await page.evaluate(() => {
      return document.body.innerText.includes('Error') || 
             document.body.innerText.includes('expected') ||
             document.body.innerText.includes('Cannot') ||
             document.querySelector('.error') !== null;
    });
    
    if (hasErrors) {
      console.log('❌ Build/Runtime errors detected!');
      const errorText = await page.evaluate(() => document.body.innerText);
      console.log('Error details:', errorText.substring(0, 500));
      return;
    }
    
    console.log('✅ App loaded without build errors');
    
    // Check if main elements are present
    const mainElements = await page.evaluate(() => {
      const header = document.querySelector('nav');
      const logo = document.querySelector('[class*="Leaf"], .logo, [alt*="logo"]');
      const signInButton = document.querySelector('button:contains("Sign In"), [role="button"]:contains("Sign In")');
      
      return {
        hasHeader: !!header,
        hasLogo: !!logo,
        hasSignInButton: !!signInButton,
        appTitle: document.querySelector('span')?.textContent || 'Not found'
      };
    });
    
    console.log('🔍 Main elements check:', mainElements);
    
    // Look for Sign In button more specifically
    const signInButton = await page.$('button:contains("Sign In")') || 
                         await page.$x('//button[contains(text(), "Sign In")]');
    
    if (signInButton.length > 0) {
      console.log('✅ Sign In button found');
      
      // Click the Sign In button
      await signInButton[0].click();
      await page.waitForTimeout(1000);
      
      // Take screenshot after clicking Sign In
      await page.screenshot({ path: '2nrtlist_auth_modal_opened.png', fullPage: true });
      console.log('📸 Auth modal screenshot taken');
      
      // Check if auth modal appeared
      const modalVisible = await page.evaluate(() => {
        const modal = document.querySelector('[role="dialog"], .modal, [class*="modal"]');
        return !!modal;
      });
      
      if (modalVisible) {
        console.log('✅ Auth modal opened successfully');
        
        // Fill in the test credentials
        const emailInput = await page.$('input[type="email"], input[placeholder*="email"]');
        const passwordInput = await page.$('input[type="password"], input[placeholder*="password"]');
        
        if (emailInput && passwordInput) {
          console.log('✅ Email and password inputs found');
          
          await emailInput.type('<EMAIL>');
          await passwordInput.type('J4913836j');
          
          console.log('✅ Test credentials entered');
          
          // Look for submit button
          const submitButton = await page.$('button[type="submit"], button:contains("Sign In"), button:contains("Login")') ||
                              await page.$x('//button[contains(text(), "Sign In") or contains(text(), "Login")]');
          
          if (submitButton.length > 0) {
            console.log('✅ Submit button found, attempting login...');
            
            await submitButton[0].click();
            await page.waitForTimeout(3000); // Wait for auth response
            
            // Take screenshot after login attempt
            await page.screenshot({ path: '2nrtlist_auth_after_login.png', fullPage: true });
            console.log('📸 After login screenshot taken');
            
            // Check if login was successful
            const loginSuccess = await page.evaluate(() => {
              const welcomeText = document.body.innerText.toLowerCase();
              return welcomeText.includes('welcome') || 
                     welcomeText.includes('profile') ||
                     welcomeText.includes('dashboard') ||
                     !document.querySelector('[role="dialog"], .modal, [class*="modal"]');
            });
            
            if (loginSuccess) {
              console.log('🎉 LOGIN SUCCESSFUL!');
            } else {
              console.log('❌ Login may have failed - checking for error messages');
              
              const errorMessage = await page.evaluate(() => {
                const errorEl = document.querySelector('.error, [class*="error"], .text-red, [class*="destructive"]');
                return errorEl ? errorEl.textContent : 'No specific error found';
              });
              
              console.log('Error message:', errorMessage);
            }
          } else {
            console.log('❌ Submit button not found');
          }
        } else {
          console.log('❌ Email or password input not found');
        }
      } else {
        console.log('❌ Auth modal did not appear');
      }
    } else {
      console.log('❌ Sign In button not found');
      
      // Let's see what buttons are available
      const availableButtons = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        return buttons.map(btn => btn.textContent?.trim()).filter(text => text);
      });
      
      console.log('Available buttons:', availableButtons);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testAuth();
